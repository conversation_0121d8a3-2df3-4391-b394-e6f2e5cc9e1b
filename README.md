# dealer-drive-backend

Prerequisites:
`ruby version - 3.4.4`

`rails version - 8.0.2`

`mysql - 9.3.0`

Note: For older versions of docker compose use `docker-compose`
Steps:
Ensure you have installed docker,

For the first time build, run
1. `docker compose build`
2. `docker compose exec web rails db:prepare`(also seeds data)
3. `docker compose up`

For future builds
1. `docker compose up` would run the app

To access app `http://0.0.0.0:3000/`

To check migration status
`docker compose exec web rails db:migrate:status`

To run console
`docker compose exec web rails c`

## Local Setup

> Before you start you will need a copy of the **master.key** file, this will be placed in the **config** directory. This is required to run any Rails commands and also to view or edit the encrypted credentials.

### VSCode Setup

Using VSCode for development purposes is recommended as it "lets you use a Docker container as a full-featured development environment", allowing you to connect and attach to any running container. The specific settings for this are contained in `.devcontainer/devcontainer.json`. Including a list of VSCode extensions that should be enabled on the Docker container. The integrated terminal in VSCode will run directly inside the attached Docker container.
If you need to run any commands (rspec, migrations etc) these should be done from the VSCode integrated terminal when it is connected to the container. This means that all commands run from VSCode will be run on the container, avoiding the need to prefix the existing command with `docker compose exec web` e.g. `docker compose exec web rails console` can be run as `rails console`.

### Running RSpec

> Specs will take longer to run, avoid running the full suite unless it is required.

You will need to specify the `test` Rails environment (the default is `development`). The usual commands are available.
From the **VSCode terminal**:

- `RAILS_ENV=test rspec` will run all specs
- `RAILS_ENV=test rspec spec/models` to run all model specs
- `RAILS_ENV=test rspec spec/requests` to run all request specs etc. etc.

From outside the **VSCode terminal**:

- `docker compose run -e "RAILS_ENV=test" web rspec` will run all specs
- `docker compose run -e "RAILS_ENV=test" web rspec spec/models` will run model specs etc.

From the **github action**:
Click on `Run workflow` button on the [Rspec action page](https://github.com/imotor-code/dealer-drive-backend/actions/workflows/test.yml) and choose branch and click `Run workflow` button.

#### Best Practices for Writing Specs

For best practices on writing RSpec tests, refer to [Better Specs](https://www.betterspecs.org/). This resource provides guidelines and tips to write clean, efficient, and maintainable tests.

### Running Jobs

> There will be an increase in the time taken to run jobs.

The usual commands are available. From the **VSCode terminal**:

- `rails -T` to list all available jobs/tasks
- `rails db:migrate:legacy` to run the **Legacy migration** job

### Editing encrypted credentials and secrets

> The encrypted credentials file in **config/credentials.yml.enc** can only be decrypted using the matching **config/master.key** file, this file is not kept under source control.

The usual commands are available. From the **VSCode terminal** run `rails credentials:edit` to decrypt and edit the credentials using Vim inside the **VSCode terminal**. When the file is saved and the the VIM session is quit (`:wq`), source control will pick up any changes to the encrypted file.

The credentials can also be edited "natively" inside VSCode (rather than a Vim session), from the **VSCode terminal** run `EDITOR='code --wait' rails credentials:edit` to decrypt and edit the credentials in a new tab inside the current VSCode window, you will need to save and close the file for source control to pick up any changes to the encrypted file.

### API documentation

> We use [Swagger](https://github.com/swagger-api/swagger-ui) for API documentation. The documentation is only available in the development environment, and uses the local development database to generate its results.

The usual commands are available. From the **VSCode terminal**:

To generate or update the documentation.

`RAILS_ENV=test rails rswag:specs:swaggerize`

`docker compose exec web bash -c "RAILS_ENV=test bundle exec rails rswag:specs:swaggerize"`


The documentation will then be available at [http://localhost:3000/api-docs/index.html](http://localhost:3000/api-docs/index.html).

### Rubocop

To run Rubocop on all ruby files changed in a feature branch, run the following command from the **VSCode terminal**:

`git diff --name-only feature/<feature-branch-name>..main | xargs rubocop --force-exclusion`

To run it against the **current branch** (without typing in the branch name manually), run the following command from the VSCode terminal:

`git diff --name-only main...$(git rev-parse --abbrev-ref HEAD) | xargs rubocop --force-exclusion`

If you append `--fix` at the end of the above command, then it will fix some of the issues automatically.

### Code coverage
`simplecov` has been added to RSpec, which generates coverage report everytime specs are run (e.g. `RAILS_ENV=test rspec`). The report is available at `coverage/index.html`.

### Changelog
For every PR, please update the `CHANGELOG.md` file with the changes made.

### Github Actions
There is a github action setup for this repo (`.github/workflows/main.yml`)
- `Rails PR Code Quality Checks` - This runs on every PR and runs `brakeman` and `rubocop` on the changed files.
- `Rails Tests` - This runs on every PR and runs `rspec` on the changed files.

### Github Templates
There are three templates setup for this repo (under `.github/`)
- `PULL_REQUEST_TEMPLATE.md` - This is the template for creating a PR.
- `ISSUE_TEMPLATE.md` - This is the template for creating an issue.
- `feature-request.md` - This is the template for creating a feature request.

### Github Rules
There are two rules setup for this repo
- `Require pull request reviews before merging` - This requires 2 reviews before a PR can be merged.
- `Require status checks to pass before merging` - This requires the github actions to pass before a PR can be merged.
- `Restrict who can push to matching branches` - This restricts who can push to the `main` branch.
- `Delete Protection Branches` - This protects the `main` branch from being deleted.
- `Request pull request review from Copilot` - Automatically request review from Copilot for new pull requests, if the author has access to Copilot code review.

