-- Adminer 4.7.7 MySQL dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

USE `dealerdrive`;

DELIMITER ;;

DROP PROCEDURE IF EXISTS `deleteDriverLicenseOlderThan12month`;;
CREATE PROCEDURE `deleteDriverLicenseOlderThan12month`()
BEGIN
	declare x INT default 0;
  WHILE x <= 200000 DO
    DELETE FROM `DriverLicense`
	WHERE idDriverLicense BETWEEN x AND x+999
	AND createTimestamp < '2015-09-01 00:00:00';
    SET x = x + 1000;
  END WHILE;
END;;

DROP PROCEDURE IF EXISTS `deleteDriverSignatureOlderThan12month`;;
CREATE PROCEDURE `deleteDriverSignatureOlderThan12month`()
BEGIN
	declare x INT default 0;
  WHILE x <= 360000 DO
    DELETE FROM `DriverSignature`
	WHERE idDriverSignature BETWEEN x AND x+999;
    SET x = x + 1000;
  END WHILE;
END;;

DROP PROCEDURE IF EXISTS `GetDealershipStats`;;
CREATE PROCEDURE `GetDealershipStats`(
    IN reportYear INT,
    IN reportMonth INT
)
BEGIN

    DECLARE messageType INT DEFAULT 2;
    DECLARE ppsrCallType INT DEFAULT 0;
    DECLARE appraisalStatus INT DEFAULT 2;
    DECLARE appraisal TINYINT DEFAULT 2;
    DECLARE liveMarketIntegrationSystem INT DEFAULT 9;
    DECLARE liveMarketIntegrationTypeMapping INT DEFAULT 0;


    DECLARE sydneyStart DATETIME;
    DECLARE sydneyEnd DATETIME;
    DECLARE fromDate DATETIME;
    DECLARE toDate DATETIME;


    SET sydneyStart = STR_TO_DATE(CONCAT(reportYear, '-', reportMonth, '-01'), '%Y-%m-%d');
    SET sydneyEnd = LAST_DAY(sydneyStart) + INTERVAL 1 DAY;


    SET fromDate = CONVERT_TZ(sydneyStart, 'Australia/Sydney', 'UTC');
    SET toDate = CONVERT_TZ(sydneyEnd, 'Australia/Sydney', 'UTC');

    select fromDate, toDate;


    SELECT dealership.IdDealership AS IdDealership,
        dealership.Name AS DealershipName,
        dealership.CostOfPPSR as CostOfPPSR,
        dealership.CostOfLookup as CostOfLookup,
        dealership.IncludedLookup as IncludedLookup,
        (
            SELECT COUNT(*)
            FROM AppraisalVehicleLookupCache
            WHERE
                AppraisalVehicleLookupCache.DealershipId = dealership.IdDealership AND
                AppraisalVehicleLookupCache.RequestDateUtc >= fromDate AND
                AppraisalVehicleLookupCache.RequestDateUtc <= toDate
        ) AS LookUpCount,
        (
            SELECT COUNT(*)
            FROM Appraisal
            WHERE
                Appraisal.DealershipId = dealership.IdDealership AND
                Appraisal.Status = appraisalStatus AND
                Appraisal.CreationDateUtc >= fromDate AND
                Appraisal.CreationDateUtc <= toDate
        ) AS AwardedCount,
        (
            SELECT COUNT(*)
            FROM AppraisalCommunicationLog
            LEFT JOIN Appraisal ON Appraisal.AppraisalId = AppraisalCommunicationLog.AppraisalId
            WHERE
                Appraisal.DealershipId = dealership.IdDealership AND
                AppraisalCommunicationLog.MessageType = messageType AND
                AppraisalCommunicationLog.LogDateUtc >= fromDate AND
                AppraisalCommunicationLog.LogDateUtc <= toDate
        ) AS SMSCount,
        (
            SELECT COUNT(*)
            FROM Appraisal
            LEFT JOIN AppraisalPpsrLog ON Appraisal.AppraisalId = AppraisalPpsrLog.AppraisalId
            WHERE
                Appraisal.DealershipId = dealership.IdDealership AND
                Success = 1 AND
                PpsrCallType = ppsrCallType AND
                AppraisalPpsrLog.LogDateUtc >= fromDate AND
                AppraisalPpsrLog.LogDateUtc <= toDate
        ) As PpsrCount,
        (
            SELECT COUNT(DISTINCT Appraisal.AppraisalId)
            FROM Appraisal
            LEFT JOIN AppraisalIntegrationStorage ON Appraisal.AppraisalId = AppraisalIntegrationStorage.AppraisalId
            WHERE
                Appraisal.DealershipId = dealership.IdDealership AND
                AppraisalIntegrationStorage.IntegrationSystem = liveMarketIntegrationSystem AND
                AppraisalIntegrationStorage.IntegrationType = liveMarketIntegrationTypeMapping AND
                AppraisalIntegrationStorage.InsertDateTime >= fromDate AND
                AppraisalIntegrationStorage.InsertDateTime <= toDate
        ) As LiveMarketCount
    FROM Dealership dealership
    INNER JOIN DealershipSubscription subscription ON subscription.DealershipId = dealership.IdDealership
    WHERE (subscription.ActiveSubscription = appraisal)
    GROUP BY dealership.IdDealership;

END;;

DELIMITER ;

DROP TABLE IF EXISTS `AdditionalDriveInfoField`;
CREATE TABLE `AdditionalDriveInfoField` (
  `idAdditionalDriveInfoField` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) DEFAULT NULL,
  `Dealership_idDealership` int(11) NOT NULL,
  `category` tinyint(4) NOT NULL,
  `type` tinyint(4) NOT NULL,
  `driveTypes` int(11) DEFAULT NULL,
  `order` int(11) NOT NULL,
  `options` text,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updateTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`idAdditionalDriveInfoField`),
  KEY `idx_Dealership_idDealership` (`Dealership_idDealership`),
  CONSTRAINT `FK_AdditionalDriveInfoField_Dealership` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AdditionalDriveInfoValue`;
CREATE TABLE `AdditionalDriveInfoValue` (
  `idAdditionalDriveInfoValue` int(11) NOT NULL AUTO_INCREMENT,
  `Drive_idDrive` int(11) NOT NULL,
  `AdditionalDriveInfoField_idAdditionalDriveInfoField` int(11) NOT NULL,
  `searchable` varchar(1000) DEFAULT NULL,
  `value` text,
  `value2` text,
  PRIMARY KEY (`idAdditionalDriveInfoValue`),
  KEY `idx_Drive_idDrive` (`Drive_idDrive`),
  KEY `idx_AdditionalDriveInfoField_idAdditionalDriveInfoField` (`AdditionalDriveInfoField_idAdditionalDriveInfoField`),
  CONSTRAINT `FK_AdditionalDriveInfoValue_AdditionalDriveInfoField` FOREIGN KEY (`AdditionalDriveInfoField_idAdditionalDriveInfoField`) REFERENCES `AdditionalDriveInfoField` (`idAdditionalDriveInfoField`) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT `FK_AdditionalDriveInfoValue_Drive` FOREIGN KEY (`Drive_idDrive`) REFERENCES `Drive` (`idDrive`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AdditionalVehicleInfoValue`;
CREATE TABLE `AdditionalVehicleInfoValue` (
  `idAdditionalVehicleInfoValue` int(11) NOT NULL AUTO_INCREMENT,
  `Car_idCar` int(11) NOT NULL,
  `AdditionalDriveInfoField_idAdditionalDriveInfoField` int(11) NOT NULL,
  `searchable` varchar(1000) DEFAULT NULL,
  `value` text,
  `updateTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`idAdditionalVehicleInfoValue`),
  KEY `idx_Car_idCar` (`Car_idCar`),
  KEY `idx_AdditionalDriveInfoField_idAdditionalDriveInfoField` (`AdditionalDriveInfoField_idAdditionalDriveInfoField`),
  CONSTRAINT `FK_AdditionalVehicleInfoValue_AdditionalDriveInfoField` FOREIGN KEY (`AdditionalDriveInfoField_idAdditionalDriveInfoField`) REFERENCES `AdditionalDriveInfoField` (`idAdditionalDriveInfoField`) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT `FK_AdditionalVehicleInfoValue_Car` FOREIGN KEY (`Car_idCar`) REFERENCES `Car` (`idCar`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `Appraisal`;
CREATE TABLE `Appraisal` (
  `AppraisalId` int(11) NOT NULL AUTO_INCREMENT,
  `CreationDateUtc` datetime(6) NOT NULL,
  `DealershipId` int(11) NOT NULL,
  `AppraisalCustomerId` int(11) NOT NULL,
  `StartDateUtc` datetime(6) NOT NULL,
  `UserId` int(11) NOT NULL,
  `StorageKey` varchar(255),
  `AwardedPrice` int(11),
  `AwardedTime` datetime(6) DEFAULT NULL,
  `Status` int(11) NOT NULL DEFAULT '1',
  `ProjectedArrivalDate` datetime(6) DEFAULT NULL,
  `HighestOfferAmount` int(11),
  `MarkedAs` int(11) DEFAULT NULL,
  `LastModifiedUtc` datetime(6) NOT NULL,
  `LastModifierId` int(11) NOT NULL DEFAULT '0',
  `HasSignature` bit(1) NOT NULL DEFAULT b'0',
  `SubmittedUtc` datetime(6) DEFAULT NULL,
  `SubmitterId` int(11) DEFAULT NULL,
  `SearchableStaffFirstName` varchar(45) DEFAULT NULL,
  `SearchableStaffLastName` varchar(45) DEFAULT NULL,
  `RetailLinkKey` longtext,
  `IsArchived` bit(1) NOT NULL DEFAULT b'0',
  `IsDeleted` bit(1) NOT NULL DEFAULT b'0',
  `DuplicatedAppraisalId` int(11) DEFAULT NULL,
  `PdfLinkKey` longtext,
  `WholesalePdfLinkKey` longtext,
  `AwardByUserId` int(11) DEFAULT NULL,
  `HasDisplayPrice` bit(1) NOT NULL DEFAULT b'0',
  `GivenPrice` int(11) DEFAULT NULL,
  `RedbookDescription` longtext,
  `RedbookId` longtext,
  `RedbookUpdateDateTime` datetime(6) DEFAULT NULL,
  `RedbookUpdateUserId` int(11) DEFAULT NULL,
  `AnonymousEditKey` varchar(20) DEFAULT NULL,
  `LiveMarketInsightsStatus` int(11) DEFAULT NULL,
  `ArchivedDateUtc` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`AppraisalId`),
  KEY `IX_Appraisal_DealershipId` (`DealershipId`),
  KEY `IX_Appraisal_UserId` (`UserId`),
  KEY `IX_Appraisal_AppraisalCustomerId` (`AppraisalCustomerId`),
  KEY `IX_Appraisal_Status` (`Status`),
  KEY `IX_Appraisal_LastModifierId` (`LastModifierId`),
  KEY `IX_Appraisal_SubmitterId` (`SubmitterId`),
  KEY `IX_Appraisal_AwardByUserId` (`AwardByUserId`),
  CONSTRAINT `FK_Appraisal_AppraisalCustomer_AppraisalCustomerId` FOREIGN KEY (`AppraisalCustomerId`) REFERENCES `AppraisalCustomer` (`AppraisalCustomerId`) ON DELETE CASCADE,
  CONSTRAINT `FK_Appraisal_AppraisalStatusSortName_Status` FOREIGN KEY (`Status`) REFERENCES `AppraisalStatusSortName` (`AppraisalStatusKey`) ON DELETE CASCADE,
  CONSTRAINT `FK_Appraisal_Dealership_DealershipId` FOREIGN KEY (`DealershipId`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE,
  CONSTRAINT `FK_Appraisal_User_AwardByUserId` FOREIGN KEY (`AwardByUserId`) REFERENCES `User` (`idUser`) ON DELETE NO ACTION,
  CONSTRAINT `FK_Appraisal_User_LastModifierId` FOREIGN KEY (`LastModifierId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE,
  CONSTRAINT `FK_Appraisal_User_SubmitterId` FOREIGN KEY (`SubmitterId`) REFERENCES `User` (`idUser`) ON DELETE NO ACTION,
  CONSTRAINT `FK_Appraisal_User_UserId` FOREIGN KEY (`UserId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalCommunicationLog`;
CREATE TABLE `AppraisalCommunicationLog` (
  `LogId` int(11) NOT NULL AUTO_INCREMENT,
  `AppraisalId` int(11) DEFAULT NULL,
  `CategoryType` int(11) NOT NULL,
  `ContactInfo` longtext,
  `LogDateUtc` datetime(6) NOT NULL,
  `MessageType` int(11) NOT NULL,
  `UserId` int(11) DEFAULT NULL,
  PRIMARY KEY (`LogId`),
  KEY `IX_AppraisalCommunicationLog_AppraisalId` (`AppraisalId`),
  KEY `IX_AppraisalCommunicationLog_UserId` (`UserId`),
  CONSTRAINT `FK_AppraisalCommunicationLog_Appraisal_AppraisalId` FOREIGN KEY (`AppraisalId`) REFERENCES `Appraisal` (`AppraisalId`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalCommunicationLog_User_UserId` FOREIGN KEY (`UserId`) REFERENCES `User` (`idUser`) ON DELETE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalConditionMap`;
CREATE TABLE `AppraisalConditionMap` (
  `AppraisalId` int(11) NOT NULL,
  `Bonnet` int(11) DEFAULT NULL,
  `Boot` int(11) DEFAULT NULL,
  `FrontBumper` int(11) DEFAULT NULL,
  `FrontFenderSkirt` int(11) DEFAULT NULL,
  `FrontPanel` int(11) DEFAULT NULL,
  `FrontWindshield` int(11) DEFAULT NULL,
  `LastModifiedUtc` datetime(6) NOT NULL,
  `LastModifierId` int(11) NOT NULL,
  `LeftFrontDoor` int(11) DEFAULT NULL,
  `LeftFrontFender` int(11) DEFAULT NULL,
  `LeftFrontHeadlamp` int(11) DEFAULT NULL,
  `LeftFrontTyre` int(11) DEFAULT NULL,
  `LeftFrontWheel` int(11) DEFAULT NULL,
  `LeftFrontWheelPhotos` longtext,
  `LeftFrontWindow` int(11) DEFAULT NULL,
  `LeftRearDoor` int(11) DEFAULT NULL,
  `LeftRearFender` int(11) DEFAULT NULL,
  `LeftRearHeadlamp` int(11) DEFAULT NULL,
  `LeftRearTyre` int(11) DEFAULT NULL,
  `LeftRearWheel` int(11) DEFAULT NULL,
  `LeftRearWheelPhotos` longtext,
  `LeftRearWindow` int(11) DEFAULT NULL,
  `LeftRunningBoard` int(11) DEFAULT NULL,
  `RearBumper` int(11) DEFAULT NULL,
  `RearFenderSkirt` int(11) DEFAULT NULL,
  `RearGrill` int(11) DEFAULT NULL,
  `RearWindshield` int(11) DEFAULT NULL,
  `RightFrontDoor` int(11) DEFAULT NULL,
  `RightFrontFender` int(11) DEFAULT NULL,
  `RightFrontHeadlamp` int(11) DEFAULT NULL,
  `RightFrontTyre` int(11) DEFAULT NULL,
  `RightFrontWheel` int(11) DEFAULT NULL,
  `RightFrontWheelPhotos` longtext,
  `RightFrontWindow` int(11) DEFAULT NULL,
  `RightRearDoor` int(11) DEFAULT NULL,
  `RightRearFender` int(11) DEFAULT NULL,
  `RightRearHeadlamp` int(11) DEFAULT NULL,
  `RightRearTyre` int(11) DEFAULT NULL,
  `RightRearWheel` int(11) DEFAULT NULL,
  `RightRearWheelPhotos` longtext,
  `RightRearWindow` int(11) DEFAULT NULL,
  `RightRunningBoard` int(11) DEFAULT NULL,
  `Roof` int(11) DEFAULT NULL,
  PRIMARY KEY (`AppraisalId`),
  KEY `IX_AppraisalConditionMap_LastModifierId` (`LastModifierId`),
  CONSTRAINT `FK_AppraisalConditionMap_Appraisal_AppraisalId` FOREIGN KEY (`AppraisalId`) REFERENCES `Appraisal` (`AppraisalId`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalConditionMap_User_LastModifierId` FOREIGN KEY (`LastModifierId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalConditionReport`;
CREATE TABLE `AppraisalConditionReport` (
  `AppraisalId` int(11) NOT NULL,
  `HasSignsOfRepairs` bit(1) DEFAULT NULL,
  `InteriorPhotos` longtext,
  `InteriorRating` smallint(6) DEFAULT NULL,
  `IsRoadTested` bit(1) DEFAULT NULL,
  `IsVehicleClean` bit(1) DEFAULT NULL,
  `IsVehicleDry` bit(1) DEFAULT NULL,
  `LastModifiedUtc` datetime(6) NOT NULL,
  `LastModifierId` int(11) NOT NULL,
  `MechanicalPhotos` longtext,
  `MechanicalRating` smallint(6) DEFAULT NULL,
  `Notes` longtext,
  `PaintWorkPhotos` longtext,
  `PaintWorkRating` smallint(6) DEFAULT NULL,
  `PanelWorkPhotos` longtext,
  `PanelWorkRating` smallint(6) DEFAULT NULL,
  `WindscreenPhotos` longtext,
  `WindscreenRating` smallint(6) DEFAULT NULL,
  PRIMARY KEY (`AppraisalId`),
  KEY `IX_AppraisalConditionReport_LastModifierId` (`LastModifierId`),
  CONSTRAINT `FK_AppraisalConditionReport_Appraisal_AppraisalId` FOREIGN KEY (`AppraisalId`) REFERENCES `Appraisal` (`AppraisalId`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalConditionReport_User_LastModifierId` FOREIGN KEY (`LastModifierId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalCustomer`;
CREATE TABLE `AppraisalCustomer` (
  `AppraisalCustomerId` int(11) NOT NULL AUTO_INCREMENT,
  `Address` varchar(180) DEFAULT NULL,
  `City` varchar(45) DEFAULT NULL,
  `Company` varchar(45) DEFAULT NULL,
  `EmailAddress` varchar(254),
  `FirstName` varchar(45) DEFAULT NULL,
  `LastName` varchar(45) DEFAULT NULL,
  `LicenceFrontPhotoLocalKey` varchar(500),
  `LicenceFrontPhotoStorageKey` varchar(255) DEFAULT NULL,
  `LicenceRearPhotoLocalKey` varchar(500),
  `LicenceRearPhotoStorageKey` varchar(255) DEFAULT NULL,
  `PhoneNumber` varchar(20) DEFAULT NULL,
  `PostCode` varchar(10) DEFAULT NULL,
  `StorageKey` varchar(255) DEFAULT NULL,
  `HasGivenDataCollectionConsent` bit(1) NOT NULL DEFAULT b'0',
  `HasGivenMarketingConsent` bit(1) NOT NULL DEFAULT b'0',
  `LastModifiedUtc` datetime(6) NOT NULL,
  `LastModifierId` int(11) NOT NULL DEFAULT '0',
  `DealershipId` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`AppraisalCustomerId`),
  KEY `IX_AppraisalCustomer_LastModifierId` (`LastModifierId`),
  KEY `IX_AppraisalCustomer_DealershipId` (`DealershipId`),
  CONSTRAINT `FK_AppraisalCustomer_Dealership_DealershipId` FOREIGN KEY (`DealershipId`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalCustomer_User_LastModifierId` FOREIGN KEY (`LastModifierId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalDealershipConfiguration`;
CREATE TABLE `AppraisalDealershipConfiguration` (
  `DealershipId` int(11) NOT NULL,
  `ValidationFields` longtext,
  `GroupShareMode` int(11) NOT NULL DEFAULT '0',
  `WholesalePDFSettingToShowVinAndRego` bit(1) DEFAULT NULL,
  `PushNotificationConfigs` text,
  `AllowPriceUpdatesOnWonAppraisals` bit(1) NOT NULL DEFAULT b'1',
  PRIMARY KEY (`DealershipId`),
  CONSTRAINT `FK_AppraisalDealershipConfiguration_Dealership_DealershipId` FOREIGN KEY (`DealershipId`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalDealershipSendEmailConfig`;
CREATE TABLE `AppraisalDealershipSendEmailConfig` (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `Config` longtext,
  `DealershipId` int(11) NOT NULL,
  `Enabled` bit(1) NOT NULL,
  PRIMARY KEY (`Id`),
  KEY `IX_AppraisalDealershipSendEmailConfig_DealershipId` (`DealershipId`),
  CONSTRAINT `FK_AppraisalDealershipSendEmailConfig_Dealership_DealershipId` FOREIGN KEY (`DealershipId`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalExternalApiCall`;
CREATE TABLE `AppraisalExternalApiCall` (
  `AppraisalExternalApiCallId` int(11) NOT NULL AUTO_INCREMENT,
  `IntegrationSystem` smallint(6) NOT NULL,
  `DealershipId` int(11) DEFAULT NULL,
  `AppraisalExternalSyncId` int(11) DEFAULT NULL,
  `Url` text,
  `RequestInfo` longtext,
  `CreatedAtUtc` datetime NOT NULL,
  PRIMARY KEY (`AppraisalExternalApiCallId`),
  KEY `IX_AppraisalExternalApiCall_DealershipId` (`DealershipId`),
  KEY `IX_AppraisalExternalApiCall_AppraisalExternalSyncId` (`AppraisalExternalSyncId`),
  CONSTRAINT `FK_AppraisalExternalApiCall_AppraisalExternalSync_Id` FOREIGN KEY (`AppraisalExternalSyncId`) REFERENCES `AppraisalExternalSync` (`AppraisalExternalSyncId`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalExternalApiCall_Dealership_DealershipId` FOREIGN KEY (`DealershipId`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalExternalSync`;
CREATE TABLE `AppraisalExternalSync` (
  `AppraisalExternalSyncId` int(11) NOT NULL AUTO_INCREMENT,
  `IntegrationSystem` smallint(6) NOT NULL,
  `ExternalRecordId` varchar(255) DEFAULT NULL,
  `DealershipId` int(11) DEFAULT NULL,
  `AppraisalId` int(11) DEFAULT NULL,
  `Status` int(11) NOT NULL,
  `CreatedAtUtc` datetime NOT NULL,
  `UpdatedAtUtc` datetime NOT NULL,
  `UpdatedByUserId` int(11) DEFAULT NULL,
  `EffectiveAppraisalExternalApiCallId` int(11) DEFAULT NULL,
  PRIMARY KEY (`AppraisalExternalSyncId`),
  KEY `IX_AppraisalExternalSync_DealershipId` (`DealershipId`),
  KEY `IX_AppraisalExternalSync_AppraisalId` (`AppraisalId`),
  KEY `IX_AppraisalExternalSync_UpdatedByUserId` (`UpdatedByUserId`),
  KEY `IX_AppraisalExternalSync_EffectiveExternalApiCallId` (`EffectiveAppraisalExternalApiCallId`),
  KEY `IX_AppraisalExternalSync_ExternalRecordId` (`ExternalRecordId`),
  CONSTRAINT `FK_AppraisalExternalSync_AppraisalExternalApiCall_Id` FOREIGN KEY (`EffectiveAppraisalExternalApiCallId`) REFERENCES `AppraisalExternalApiCall` (`AppraisalExternalApiCallId`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalExternalSync_Appraisal_AppraisalId` FOREIGN KEY (`AppraisalId`) REFERENCES `Appraisal` (`AppraisalId`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalExternalSync_Dealership_DealershipId` FOREIGN KEY (`DealershipId`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalExternalSync_User_UpdatedByUserId` FOREIGN KEY (`UpdatedByUserId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalFavorite`;
CREATE TABLE `AppraisalFavorite` (
  `AppraisalId` int(11) NOT NULL,
  `UserId` int(11) NOT NULL,
  PRIMARY KEY (`AppraisalId`,`UserId`),
  KEY `IX_AppraisalFavorite_UserId` (`UserId`),
  CONSTRAINT `FK_AppraisalFavorite_Appraisal_AppraisalId` FOREIGN KEY (`AppraisalId`) REFERENCES `Appraisal` (`AppraisalId`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalFavorite_User_UserId` FOREIGN KEY (`UserId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalFinance`;
CREATE TABLE `AppraisalFinance` (
  `AppraisalId` int(11) NOT NULL,
  `BalloonPayments` int(11),
  `CompanyName` varchar(45) DEFAULT NULL,
  `CurrentMonthlyRepayments` int(11),
  `HasIndicatedClearTitle` bit(1) DEFAULT NULL,
  `IsCurrentlyUnderFinance` int(11) DEFAULT NULL,
  `LastModifiedUtc` datetime(6) NOT NULL,
  `LastModifierId` int(11) NOT NULL,
  `PayoutAmount` int(11),
  `TermInMonths` smallint(6) DEFAULT NULL,
  `NextPaymentDueAt` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`AppraisalId`),
  KEY `IX_AppraisalFinance_LastModifierId` (`LastModifierId`),
  CONSTRAINT `FK_AppraisalFinance_Appraisal_AppraisalId` FOREIGN KEY (`AppraisalId`) REFERENCES `Appraisal` (`AppraisalId`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalFinance_User_LastModifierId` FOREIGN KEY (`LastModifierId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalHistory`;
CREATE TABLE `AppraisalHistory` (
  `AppraisalId` int(11) NOT NULL,
  `BookAndHistoryPhotos` longtext,
  `HasDashWarningLights` bit(1) DEFAULT NULL,
  `HistoryType` int(11) DEFAULT NULL,
  `LastModifiedUtc` datetime(6) NOT NULL,
  `LastModifierId` int(11) NOT NULL,
  `LastServiceDate` datetime(6) DEFAULT NULL,
  `LastServiceOdometerReading` int(11) DEFAULT NULL,
  `NextServiceDueDate` datetime(6) DEFAULT NULL,
  `Notes` longtext,
  `OwnersCount` smallint(6) DEFAULT NULL,
  PRIMARY KEY (`AppraisalId`),
  KEY `IX_AppraisalHistory_LastModifierId` (`LastModifierId`),
  CONSTRAINT `FK_AppraisalHistory_Appraisal_AppraisalId` FOREIGN KEY (`AppraisalId`) REFERENCES `Appraisal` (`AppraisalId`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalHistory_User_LastModifierId` FOREIGN KEY (`LastModifierId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalIntegratedAPICallLog`;
CREATE TABLE `AppraisalIntegratedAPICallLog` (
  `AppraisalIntegratedApiCallLogId` int(11) NOT NULL AUTO_INCREMENT,
  `AppraisalId` int(11),
  `At` datetime(6) NOT NULL,
  `CalledMethod` longtext,
  `DealershipId` int(11) NOT NULL,
  `IntegrationSystem` int(11) NOT NULL,
  `RequestInfo` longtext,
  `ResponseInfo` longtext,
  `UserId` int(11),
  `WasSuccessful` bit(1) NOT NULL,
  `LogData` longtext,
  PRIMARY KEY (`AppraisalIntegratedApiCallLogId`),
  KEY `IX_AppraisalIntegratedAPICallLog_AppraisalId` (`AppraisalId`),
  KEY `IX_AppraisalIntegratedAPICallLog_DealershipId` (`DealershipId`),
  KEY `IX_AppraisalIntegratedAPICallLog_UserId` (`UserId`),
  CONSTRAINT `FK_AppraisalIntegratedAPICallLog_Appraisal_AppraisalId` FOREIGN KEY (`AppraisalId`) REFERENCES `Appraisal` (`AppraisalId`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalIntegratedAPICallLog_Dealership_DealershipId` FOREIGN KEY (`DealershipId`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalIntegratedAPICallLog_User_UserId` FOREIGN KEY (`UserId`) REFERENCES `User` (`idUser`) ON DELETE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalIntegrationCache`;
CREATE TABLE `AppraisalIntegrationCache` (
  `AppraisalIntegrationCacheId` int(11) NOT NULL AUTO_INCREMENT,
  `IntegrationSystem` int(11) NOT NULL,
  `AdditionalKey` varchar(255) DEFAULT NULL,
  `CacheType` int(11) NOT NULL,
  `CacheValue` longtext,
  `UpdatedAtUtc` datetime(6) NOT NULL,
  `DealershipId` int(11) DEFAULT NULL,
  PRIMARY KEY (`AppraisalIntegrationCacheId`),
  KEY `IX_AppraisalIntegrationCache_IntegrationSystem` (`IntegrationSystem`),
  KEY `IX_AppraisalIntegrationCache_DealershipId` (`DealershipId`),
  CONSTRAINT `FK_AppraisalIntegrationCache_Dealership_DealershipId` FOREIGN KEY (`DealershipId`) REFERENCES `Dealership` (`idDealership`) ON DELETE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalIntegrationConfiguration`;
CREATE TABLE `AppraisalIntegrationConfiguration` (
  `AppraisalIntegrationConfigurationId` int(11) NOT NULL AUTO_INCREMENT,
  `ConfigurationAsJson` longtext,
  `IntegrationSystem` int(11) NOT NULL,
  `IsEnabled` bit(1) NOT NULL,
  `LastSyncDateTime` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`AppraisalIntegrationConfigurationId`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalIntegrationDealershipConfiguration`;
CREATE TABLE `AppraisalIntegrationDealershipConfiguration` (
  `AppraisalIntegrationDealershipConfigurationId` int(11) NOT NULL AUTO_INCREMENT,
  `ConfigurationAsJson` longtext,
  `DealershipId` int(11) NOT NULL,
  `IntegrationSystem` int(11) NOT NULL,
  `IsEnabled` bit(1) NOT NULL,
  PRIMARY KEY (`AppraisalIntegrationDealershipConfigurationId`),
  KEY `IX_AppraisalIntegrationDealershipConfiguration_DealershipId` (`DealershipId`),
  CONSTRAINT `FK_AppraisalIntegrationDealershipConfiguration_Dealership_Dealer` FOREIGN KEY (`DealershipId`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalIntegrationStorage`;
CREATE TABLE `AppraisalIntegrationStorage` (
  `AppraisalIntegrationStorageId` int(11) NOT NULL AUTO_INCREMENT,
  `AppraisalId` int(11) NOT NULL,
  `InsertDateTime` datetime(6) NOT NULL,
  `IntegrationSystem` int(11) NOT NULL,
  `IntegrationType` int(11) NOT NULL,
  `MappingDataAsJson` longtext,
  PRIMARY KEY (`AppraisalIntegrationStorageId`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalLog`;
CREATE TABLE `AppraisalLog` (
  `AppraisalLogId` int(11) NOT NULL AUTO_INCREMENT,
  `AppraisalId` int(11) NOT NULL,
  `ChangeDateUtc` datetime(6) NOT NULL,
  `ModifierId` int(11),
  `OldNewValue` longtext NOT NULL,
  PRIMARY KEY (`AppraisalLogId`),
  KEY `IX_AppraisalLog_AppraisalId` (`AppraisalId`),
  KEY `IX_AppraisalLog_ModifierId` (`ModifierId`),
  CONSTRAINT `FK_AppraisalLog_Appraisal_AppraisalId` FOREIGN KEY (`AppraisalId`) REFERENCES `Appraisal` (`AppraisalId`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalLog_User_ModifierId` FOREIGN KEY (`ModifierId`) REFERENCES `User` (`idUser`) ON DELETE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalNote`;
CREATE TABLE `AppraisalNote` (
  `AppraisalNoteId` int(11) NOT NULL AUTO_INCREMENT,
  `AppraisalId` int(11) NOT NULL,
  `Body` longtext,
  `CreatedAtUtc` datetime(6) NOT NULL,
  `By` varchar(45) DEFAULT NULL,
  `PriceObfuscatedBody` longtext,
  PRIMARY KEY (`AppraisalNoteId`),
  KEY `IX_AppraisalNote_AppraisalId` (`AppraisalId`),
  CONSTRAINT `FK_AppraisalNote_Appraisal_AppraisalId` FOREIGN KEY (`AppraisalId`) REFERENCES `Appraisal` (`AppraisalId`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalOfferRequest`;
CREATE TABLE `AppraisalOfferRequest` (
  `AppraisalOfferRequestId` int(11) NOT NULL AUTO_INCREMENT,
  `AppraisalId` int(11) NOT NULL,
  `BusinessName` varchar(45) NOT NULL,
  `DecisionTakenTime` datetime(6) DEFAULT NULL,
  `Email` varchar(254),
  `FirstName` varchar(45) NOT NULL,
  `IsWinningBid` bit(1) NOT NULL,
  `LastName` varchar(45) NOT NULL,
  `OfferAmount` int(11),
  `Status` int(11) NOT NULL,
  `UniqueCode` varchar(20) DEFAULT NULL,
  `CommunicateBy` int(11) NOT NULL DEFAULT '0',
  `Mobile` longtext,
  `OfferRequestType` int(11) DEFAULT NULL,
  `StaffId` int(11) DEFAULT NULL,
  `Note` varchar(1000) DEFAULT NULL,
  `AppraisalTenderListValuerId` int(11) DEFAULT NULL,
  PRIMARY KEY (`AppraisalOfferRequestId`),
  KEY `IX_AppraisalOfferRequest_AppraisalId` (`AppraisalId`),
  KEY `IX_AppraisalOfferRequest_StaffId` (`StaffId`),
  KEY `IX_AppraisalOfferRequest_AppraisalTenderListValuerId` (`AppraisalTenderListValuerId`),
  CONSTRAINT `FK_AppraisalOfferRequest_AppraisalTenderListValuer_AppraisalTend` FOREIGN KEY (`AppraisalTenderListValuerId`) REFERENCES `AppraisalTenderListValuer` (`AppraisalTenderListValuerId`) ON DELETE SET NULL,
  CONSTRAINT `FK_AppraisalOfferRequest_Appraisal_AppraisalId` FOREIGN KEY (`AppraisalId`) REFERENCES `Appraisal` (`AppraisalId`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalOfferRequest_User_StaffId` FOREIGN KEY (`StaffId`) REFERENCES `User` (`idUser`) ON DELETE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalOptionsFitted`;
CREATE TABLE `AppraisalOptionsFitted` (
  `AppraisalId` int(11) NOT NULL,
  `CanopyType` int(11) DEFAULT NULL,
  `ExtendedWarrantyExpiration` datetime(6) DEFAULT NULL,
  `HasAftermarketWheels` bit(1) DEFAULT NULL,
  `HasBluetooth` bit(1) DEFAULT NULL,
  `HasBullBar` bit(1) DEFAULT NULL,
  `HasCanopy` bit(1) DEFAULT NULL,
  `HasExtendedWarranty` bit(1) DEFAULT NULL,
  `HasHeatedSeats` bit(1) DEFAULT NULL,
  `HasKeylessEntry` bit(1) DEFAULT NULL,
  `HasPersonalPropertySecurityReport` bit(1) DEFAULT NULL,
  `HasSunroof` bit(1) DEFAULT NULL,
  `HasTintedWindows` bit(1) DEFAULT NULL,
  `HasTonneauCover` bit(1) DEFAULT NULL,
  `HasTowbar` bit(1) DEFAULT NULL,
  `HasTray` bit(1) DEFAULT NULL,
  `IsOnTotalWriteOffRegister` int(11) DEFAULT NULL,
  `KeysCount` smallint(6) DEFAULT NULL,
  `LastModifiedUtc` datetime(6) NOT NULL,
  `LastModifierId` int(11) NOT NULL,
  `Notes` longtext,
  `Photos` longtext,
  `SunroofType` int(11) DEFAULT NULL,
  `HasCargoBlind` bit(1) DEFAULT NULL,
  `HasPersonalPropertySecurityReportLastModifiedUtc` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`AppraisalId`),
  KEY `IX_AppraisalOptionsFitted_LastModifierId` (`LastModifierId`),
  CONSTRAINT `FK_AppraisalOptionsFitted_Appraisal_AppraisalId` FOREIGN KEY (`AppraisalId`) REFERENCES `Appraisal` (`AppraisalId`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalOptionsFitted_User_LastModifierId` FOREIGN KEY (`LastModifierId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalPendingUpload`;
CREATE TABLE `AppraisalPendingUpload` (
  `AppraisalPendingUploadId` int(11) NOT NULL AUTO_INCREMENT,
  `DealershipId` int(11) DEFAULT NULL,
  `StorageKey` varchar(255) NOT NULL,
  `ExpiryUtc` datetime(6) NOT NULL,
  `CreatedAtUtc` datetime(6) NOT NULL,
  PRIMARY KEY (`AppraisalPendingUploadId`),
  KEY `IX_AppraisalPendingUpload_DealershipId` (`DealershipId`),
  CONSTRAINT `FK_AppraisalPendingUpload_Dealership_DealershipId` FOREIGN KEY (`DealershipId`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalPermission`;
CREATE TABLE `AppraisalPermission` (
  `UserId` int(11) NOT NULL,
  `Permissions` longtext,
  PRIMARY KEY (`UserId`),
  CONSTRAINT `FK_AppraisalPermission_User_UserId` FOREIGN KEY (`UserId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalPpsrLog`;
CREATE TABLE `AppraisalPpsrLog` (
  `AppraisalPpsrLogId` int(11) NOT NULL AUTO_INCREMENT,
  `AppraisalId` int(11) DEFAULT NULL,
  `LogDateUtc` datetime(6) NOT NULL,
  `PpsrCallType` int(11) NOT NULL,
  `RequestText` longtext,
  `ResponseText` longtext,
  `Success` bit(1) NOT NULL,
  `UserId` int(11),
  `Data` longtext,
  `ParentAppraisalPpsrLogId` int(11) DEFAULT NULL,
  `Status` int(11) NOT NULL DEFAULT '0',
  `Code` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`AppraisalPpsrLogId`),
  KEY `IX_AppraisalPpsrLog_AppraisalId` (`AppraisalId`),
  KEY `IX_AppraisalPpsrLog_UserId` (`UserId`),
  KEY `IX_PpsrCallType_Status` (`PpsrCallType`,`Status`),
  KEY `IX_PpsrCallType_Code` (`Code`),
  CONSTRAINT `FK_AppraisalPpsrLog_Appraisal_AppraisalId` FOREIGN KEY (`AppraisalId`) REFERENCES `Appraisal` (`AppraisalId`) ON DELETE NO ACTION,
  CONSTRAINT `FK_AppraisalPpsrLog_User_UserId` FOREIGN KEY (`UserId`) REFERENCES `User` (`idUser`) ON DELETE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalRefurbCost`;
CREATE TABLE `AppraisalRefurbCost` (
  `AppraisalId` int(11) NOT NULL,
  `LastModifiedUtc` datetime(6) NOT NULL,
  `LastModifierId` int(11) NOT NULL,
  `Mechanical` int(11) DEFAULT NULL,
  `Other` int(11) DEFAULT NULL,
  `PaintAndPanel` int(11) DEFAULT NULL,
  `Registration` int(11) DEFAULT NULL,
  `WheelsAndTyres` int(11) DEFAULT NULL,
  `Windscreen` int(11) DEFAULT NULL,
  PRIMARY KEY (`AppraisalId`),
  KEY `IX_AppraisalRefurbCost_LastModifierId` (`LastModifierId`),
  CONSTRAINT `FK_AppraisalRefurbCost_Appraisal_AppraisalId` FOREIGN KEY (`AppraisalId`) REFERENCES `Appraisal` (`AppraisalId`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalRefurbCost_User_LastModifierId` FOREIGN KEY (`LastModifierId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalSettingItem`;
CREATE TABLE `AppraisalSettingItem` (
  `AppraisalSettingItemId` int(11) NOT NULL AUTO_INCREMENT,
  `Key` longtext,
  `Value` longtext,
  PRIMARY KEY (`AppraisalSettingItemId`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalStatusSortName`;
CREATE TABLE `AppraisalStatusSortName` (
  `AppraisalStatusKey` int(11) NOT NULL,
  `SortName` varchar(40) DEFAULT NULL,
  PRIMARY KEY (`AppraisalStatusKey`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalTenderList`;
CREATE TABLE `AppraisalTenderList` (
  `AppraisalTenderListId` int(11) NOT NULL AUTO_INCREMENT,
  `Title` varchar(200) NOT NULL,
  `DealershipId` int(11) NOT NULL,
  `UserId` int(11) NOT NULL,
  `CreatedAtUtc` datetime(6) NOT NULL,
  `IsArchived` bit(1) NOT NULL DEFAULT b'0',
  PRIMARY KEY (`AppraisalTenderListId`),
  KEY `IX_AppraisalTenderList_UserId` (`UserId`),
  KEY `IX_AppraisalTenderList_DealershipId` (`DealershipId`),
  CONSTRAINT `FK_AppraisalTenderList_Dealership_DealershipId` FOREIGN KEY (`DealershipId`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalTenderList_User_UserId` FOREIGN KEY (`UserId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalTenderListItem`;
CREATE TABLE `AppraisalTenderListItem` (
  `AppraisalTenderListItemId` int(11) NOT NULL AUTO_INCREMENT,
  `AppraisalTenderListId` int(11) NOT NULL,
  `AppraisalId` int(11) NOT NULL,
  `CreatedAtUtc` datetime(6) NOT NULL,
  `StaffId` int(11) DEFAULT NULL,
  PRIMARY KEY (`AppraisalTenderListItemId`),
  KEY `FK_AppraisalTenderListItem_Appraisal_AppraisalId` (`AppraisalId`),
  KEY `IX_AppraisalTenderListItem_AppraisalTenderListId` (`AppraisalTenderListId`),
  KEY `IX_AppraisalTenderListItem_StaffId` (`StaffId`),
  CONSTRAINT `FK_AppraisalTenderListItem_AppraisalTenderList_AppraisalTenderLi` FOREIGN KEY (`AppraisalTenderListId`) REFERENCES `AppraisalTenderList` (`AppraisalTenderListId`) ON DELETE NO ACTION,
  CONSTRAINT `FK_AppraisalTenderListItem_Appraisal_AppraisalId` FOREIGN KEY (`AppraisalId`) REFERENCES `Appraisal` (`AppraisalId`) ON DELETE NO ACTION,
  CONSTRAINT `FK_AppraisalTenderListItem_User_StaffId` FOREIGN KEY (`StaffId`) REFERENCES `User` (`idUser`) ON DELETE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalTenderListValuer`;
CREATE TABLE `AppraisalTenderListValuer` (
  `AppraisalTenderListValuerId` int(11) NOT NULL AUTO_INCREMENT,
  `AppraisalTenderListId` int(11) NOT NULL,
  `BusinessName` varchar(45) NOT NULL,
  `Email` varchar(254) DEFAULT NULL,
  `Mobile` varchar(20) DEFAULT NULL,
  `CommunicateBy` int(11) NOT NULL DEFAULT '0',
  `FirstName` varchar(45) NOT NULL,
  `LastName` varchar(45) NOT NULL,
  `OfferRequestType` int(11) DEFAULT NULL,
  `StaffId` int(11) DEFAULT NULL,
  `UniqueCode` varchar(20) DEFAULT NULL,
  `CreatedAtUtc` datetime(6) NOT NULL,
  PRIMARY KEY (`AppraisalTenderListValuerId`),
  KEY `IX_AppraisalTenderListValuer_AppraisalTenderListId` (`AppraisalTenderListId`),
  KEY `IX_AppraisalTenderListValuer_StaffId` (`StaffId`),
  CONSTRAINT `FK_AppraisalTenderListValuer_AppraisalTenderList_AppraisalTender` FOREIGN KEY (`AppraisalTenderListId`) REFERENCES `AppraisalTenderList` (`AppraisalTenderListId`) ON DELETE NO ACTION,
  CONSTRAINT `FK_AppraisalTenderListValuer_User_StaffId` FOREIGN KEY (`StaffId`) REFERENCES `User` (`idUser`) ON DELETE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalThirdPartySync`;
CREATE TABLE `AppraisalThirdPartySync` (
  `AppraisalThirdPartySyncId` int(11) NOT NULL AUTO_INCREMENT,
  `IntegrationSystem` int(11) NOT NULL,
  `AppraisalId` int(11) NOT NULL,
  `Status` int(11) NOT NULL DEFAULT '0',
  `FailureCount` int(11) NOT NULL DEFAULT '0',
  `CreatedAtUtc` datetime(6) NOT NULL,
  `SyncLastSucceedAtUtc` datetime(6) DEFAULT NULL,
  `SyncLastFailedAtUtc` datetime(6) DEFAULT NULL,
  PRIMARY KEY (`AppraisalThirdPartySyncId`),
  KEY `IX_AppraisalThirdPartySync_AppraisalId` (`AppraisalId`),
  KEY `IX_AppraisalThirdPartySync_IntegrationSystem` (`IntegrationSystem`),
  CONSTRAINT `FK_AppraisalThirdPartySync_Appraisal_AppraisalId` FOREIGN KEY (`AppraisalId`) REFERENCES `Appraisal` (`AppraisalId`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalUser`;
CREATE TABLE `AppraisalUser` (
  `UserId` int(11) NOT NULL,
  `PushNotificationToken` varchar(255),
  `PushNotificationTokenLastModifiedAt` datetime(6) DEFAULT NULL,
  `PushNotificationEnabled` bit(1) NOT NULL DEFAULT b'1',
  `LatestAppVersion` longtext,
  `LatestAppVersionUpdateAt` datetime(6) DEFAULT NULL,
  `DmsIdentifier` longtext,
  PRIMARY KEY (`UserId`),
  CONSTRAINT `FK_AppraisalUser_User_UserId` FOREIGN KEY (`UserId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalUserDevice`;
CREATE TABLE `AppraisalUserDevice` (
  `AppraisalUserDeviceId` int(11) NOT NULL AUTO_INCREMENT,
  `DeviceId` longtext,
  `DeviceName` longtext,
  `LatestAppVersion` longtext,
  `LatestAppVersionUpdateAt` datetime(6) DEFAULT NULL,
  `PushNotificationToken` varchar(255) DEFAULT NULL,
  `PushNotificationTokenLastModifiedAt` datetime(6) DEFAULT NULL,
  `UserId` int(11) NOT NULL,
  PRIMARY KEY (`AppraisalUserDeviceId`),
  KEY `IX_AppraisalUserDevice_UserId` (`UserId`),
  CONSTRAINT `FK_AppraisalUserDevice_User_UserId` FOREIGN KEY (`UserId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalValuer`;
CREATE TABLE `AppraisalValuer` (
  `AppraisalValuerId` int(11) NOT NULL AUTO_INCREMENT,
  `BusinessName` varchar(45) NOT NULL,
  `DealershipId` int(11) NOT NULL,
  `Email` varchar(254),
  `FirstName` varchar(45) NOT NULL,
  `LastName` varchar(45) NOT NULL,
  `CommunicateBy` int(11) NOT NULL DEFAULT '0',
  `Mobile` varchar(20) DEFAULT NULL,
  `IsActive` bit(1) NOT NULL DEFAULT b'1',
  `IsMandatory` bit(1) NOT NULL DEFAULT b'0',
  PRIMARY KEY (`AppraisalValuerId`),
  KEY `IX_AppraisalValuer_DealershipId` (`DealershipId`),
  CONSTRAINT `FK_AppraisalValuer_Dealership_DealershipId` FOREIGN KEY (`DealershipId`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalVehicle`;
CREATE TABLE `AppraisalVehicle` (
  `BodyType` int(11) DEFAULT NULL,
  `BuildYear` smallint(6) DEFAULT NULL,
  `ComplianceDate` datetime(6) DEFAULT NULL,
  `DoorsCount` smallint(6) DEFAULT NULL,
  `ExteriorColour` varchar(45) DEFAULT NULL,
  `FuelType` int(11) DEFAULT NULL,
  `InteriorColour` varchar(45) DEFAULT NULL,
  `InteriorType` int(11) DEFAULT NULL,
  `IsVehiclePresent` bit(1) DEFAULT NULL,
  `MainPhotoLocalKey` varchar(500),
  `MainPhotoStorageKey` varchar(255),
  `Make` varchar(255) DEFAULT NULL,
  `Model` varchar(255) DEFAULT NULL,
  `OdometerReading` int(11) DEFAULT NULL,
  `OdometerReadingDate` datetime(6) DEFAULT NULL,
  `OdometerReadingPhotoLocalKey` varchar(500),
  `OdometerReadingPhotoStorageKey` varchar(255),
  `Registration` varchar(50) DEFAULT NULL,
  `RegistrationExpiry` datetime(6) DEFAULT NULL,
  `SeatsCount` smallint(6) DEFAULT NULL,
  `TransmissionStyle` int(11) DEFAULT NULL,
  `Vin` varchar(17) DEFAULT NULL,
  `LastModifiedUtc` datetime(6) NOT NULL,
  `LastModifierId` int(11) NOT NULL DEFAULT '0',
  `DrivingWheel` int(11) DEFAULT NULL,
  `SpareType` int(11) DEFAULT NULL,
  `WheelSizeFront` int(11) DEFAULT NULL,
  `WheelSizeRear` int(11) DEFAULT NULL,
  `AppraisalId` int(11) NOT NULL DEFAULT '0',
  `RegistrationState` int(11) DEFAULT NULL,
  `EngineDecilitre` int(11) DEFAULT NULL,
  `EngineLitre` int(11) DEFAULT NULL,
  `AirbagRecallCheckDate` datetime(6) DEFAULT NULL,
  `AirbagRecallCheckParameters` varchar(256) DEFAULT NULL,
  `AirbagRecallStatus` int(11) DEFAULT NULL,
  `BuildMonth` smallint(6) DEFAULT NULL,
  `AverageKM` double DEFAULT NULL,
  `DealerRetailMax` double DEFAULT NULL,
  `DealerRetailMin` double DEFAULT NULL,
  `DealerTradeMax` double DEFAULT NULL,
  `DealerTradeMin` double DEFAULT NULL,
  `PrivateSaleMax` double DEFAULT NULL,
  `PrivateSaleMin` double DEFAULT NULL,
  `RRP` double DEFAULT NULL,
  `EngineNumber` longtext,
  `LastLookupTime` datetime(6) DEFAULT NULL,
  `StolenDate` datetime(6) DEFAULT NULL,
  `StolenJurisdiction` longtext,
  `StolenSummary` longtext,
  `WovDamage` longtext,
  `WovDate` datetime(6) DEFAULT NULL,
  `WovIncident` longtext,
  `WovJurisdiction` longtext,
  `WovType` longtext,
  `NvicCode` longtext,
  `VinLocationName` longtext,
  `CompliancePlateLocation` longtext,
  `EngineKW` int(11) DEFAULT NULL,
  `PriceGuideSource` int(11) DEFAULT NULL,
  KEY `IX_AppraisalVehicle_FuelType` (`FuelType`),
  KEY `IX_AppraisalVehicle_LastModifierId` (`LastModifierId`),
  KEY `FK_AppraisalVehicle_Appraisal_AppraisalId` (`AppraisalId`),
  CONSTRAINT `FK_AppraisalVehicle_Appraisal_AppraisalId` FOREIGN KEY (`AppraisalId`) REFERENCES `Appraisal` (`AppraisalId`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalVehicle_FuelTypeSortName_FuelType` FOREIGN KEY (`FuelType`) REFERENCES `FuelTypeSortName` (`FuelTypeKey`) ON DELETE NO ACTION,
  CONSTRAINT `FK_AppraisalVehicle_User_LastModifierId` FOREIGN KEY (`LastModifierId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `AppraisalVehicleLookupCache`;
CREATE TABLE `AppraisalVehicleLookupCache` (
  `LookupCacheId` int(11) NOT NULL AUTO_INCREMENT,
  `ApiAccessConfig` longtext,
  `DealershipId` int(11) NOT NULL,
  `Plate` longtext,
  `RequestDateUtc` datetime(6) NOT NULL,
  `State` int(11) DEFAULT NULL,
  `UserId` int(11) NOT NULL,
  `VehicleDetailsResponse` longtext,
  `Categories` longtext,
  `BuildYear` int(11) DEFAULT NULL,
  `VIN` longtext,
  `AppraisalId` int(11) DEFAULT NULL,
  PRIMARY KEY (`LookupCacheId`),
  KEY `IX_AppraisalVehicleLookupCache_DealershipId` (`DealershipId`),
  KEY `IX_AppraisalVehicleLookupCache_UserId` (`UserId`),
  KEY `IX_AppraisalVehicleLookupCache_AppraisalId` (`AppraisalId`),
  CONSTRAINT `FK_AppraisalVehicleLookupCache_Appraisal_AppraisalId` FOREIGN KEY (`AppraisalId`) REFERENCES `Appraisal` (`AppraisalId`) ON DELETE NO ACTION,
  CONSTRAINT `FK_AppraisalVehicleLookupCache_Dealership_DealershipId` FOREIGN KEY (`DealershipId`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE,
  CONSTRAINT `FK_AppraisalVehicleLookupCache_User_UserId` FOREIGN KEY (`UserId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `BackgroundImport`;
CREATE TABLE `BackgroundImport` (
  `idImport` int(11) NOT NULL AUTO_INCREMENT,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `User_idUser` int(11) DEFAULT NULL,
  `Dealership_idDealership` int(11) NOT NULL,
  `importStatus` int(11) NOT NULL,
  `importType` int(11) NOT NULL,
  `totalItems` int(11) NOT NULL,
  `processedItems` int(11) NOT NULL,
  `jsonData` mediumtext NOT NULL,
  `endTimestamp` timestamp NULL DEFAULT NULL,
  `errorMessage` mediumtext NOT NULL,
  `idSyncConfig` int(11) DEFAULT NULL,
  PRIMARY KEY (`idImport`),
  KEY `idx_BackgroundImport_importStatus` (`importStatus`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `Car`;
CREATE TABLE `Car` (
  `idCar` int(11) NOT NULL AUTO_INCREMENT,
  `carStatus` tinyint(3) unsigned NOT NULL,
  `Dealership_idDealership` int(11) NOT NULL,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `vin` varchar(50) DEFAULT NULL,
  `stockNumber` varchar(50) DEFAULT NULL,
  `rego` varchar(50) DEFAULT NULL,
  `make` varchar(255) NOT NULL,
  `model` varchar(255) NOT NULL,
  `color` varchar(45) DEFAULT NULL,
  `year` smallint(5) unsigned DEFAULT NULL,
  `regoExpiry` date DEFAULT NULL,
  `isTradePlateUsed` tinyint(1) DEFAULT '0',
  `lastKnownOdometerKm` int(11) DEFAULT NULL,
  `lastKnownFuelGaugeLevel` int(11) DEFAULT '-1',
  `warningEmailFlags` int(11) NOT NULL DEFAULT '0',
  `updateTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `lastKnownLocation` varchar(255) DEFAULT NULL,
  `systemMake` varchar(255) DEFAULT NULL,
  `systemModel` varchar(255) DEFAULT NULL,
  `lastSystemInspectionTimestamp` timestamp NULL DEFAULT NULL,
  `lastSendingRegoExpiryNotificationDate` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`idCar`),
  KEY `fk_Car_Dealership1_idx` (`Dealership_idDealership`),
  KEY `idx_idDealership_stockNumber` (`Dealership_idDealership`,`stockNumber`),
  KEY `idx_vin` (`vin`),
  KEY `idx_stockNumber` (`stockNumber`),
  CONSTRAINT `fk_Car_Dealership1` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DELIMITER ;;

CREATE TRIGGER `updateLastSendingRegoExpiryNotificationDate` BEFORE UPDATE ON `Car` FOR EACH ROW
BEGIN
			IF (NEW.regoExpiry <> OLD.regoExpiry) OR (OLD.regoExpiry IS NOT NULL AND NEW.regoExpiry IS NULL) THEN
				SET NEW.lastSendingRegoExpiryNotificationDate = NULL;
			END IF;
		END;;

DELIMITER ;

DROP TABLE IF EXISTS `CarLog`;
CREATE TABLE `CarLog` (
  `idCarLog` int(11) NOT NULL AUTO_INCREMENT,
  `Car_idCar` int(11) NOT NULL,
  `User_idUser` int(11) DEFAULT NULL,
  `ChangedColumn` varchar(60) DEFAULT NULL,
  `OldValue` varchar(255) DEFAULT NULL,
  `NewValue` varchar(255) DEFAULT NULL,
  `ChangeTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `AdditionalDriveInfoField_idAdditionalDriveInfoField` int(11) DEFAULT NULL,
  PRIMARY KEY (`idCarLog`),
  KEY `idx_Car_idCar` (`Car_idCar`),
  KEY `idx_User_idUser` (`User_idUser`),
  KEY `idx_AdditionalDriveInfoField_idAdditionalDriveInfoField` (`AdditionalDriveInfoField_idAdditionalDriveInfoField`),
  CONSTRAINT `FK_CarLog_Car` FOREIGN KEY (`Car_idCar`) REFERENCES `Car` (`idCar`) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT `FK_CarLog_User` FOREIGN KEY (`User_idUser`) REFERENCES `User` (`idUser`) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT `fk_AdditionalDriveInfoField_idAdditionalDriveInfoField` FOREIGN KEY (`AdditionalDriveInfoField_idAdditionalDriveInfoField`) REFERENCES `AdditionalDriveInfoField` (`idAdditionalDriveInfoField`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP VIEW IF EXISTS `CarsalesBookingReport`;
CREATE TABLE `CarsalesBookingReport` (`externalRecordId` varchar(255), `requestSatus` varchar(18), `requestDateTimeUtc` datetime, `confirmedDateTimeUtc` timestamp, `isBookingTimeChaged` int(1), `driveStatus` varchar(11), `createDateTimeUtc` timestamp, `confirmDateTimeUtc` timestamp, `responseTimeInMinutes` bigint(21));


DROP TABLE IF EXISTS `Country`;
CREATE TABLE `Country` (
  `idCountry` int(11) NOT NULL AUTO_INCREMENT,
  `countryName` varchar(90) NOT NULL,
  `defaultDistanceUnit` tinyint(3) unsigned NOT NULL,
  `defaultDateFormat` tinyint(3) unsigned NOT NULL,
  `alpha2` varchar(2) DEFAULT NULL,
  `currencyUnit` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`idCountry`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `CustomerThirdPartySync`;
CREATE TABLE `CustomerThirdPartySync` (
  `idCustomerThirdPartySync` int(11) NOT NULL AUTO_INCREMENT,
  `Dealership_idDealership` int(11) NOT NULL,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `idThirdPartySync` tinyint(3) unsigned NOT NULL,
  `idExternalRecord` varchar(100) NOT NULL,
  `firstName` varchar(45) NOT NULL,
  `lastName` varchar(45) NOT NULL,
  `age` varchar(10) DEFAULT NULL,
  `telephone` varchar(20) DEFAULT NULL,
  `email` varchar(254) DEFAULT NULL,
  `gender` varchar(1) DEFAULT NULL,
  `postCode` varchar(10) DEFAULT NULL,
  `suburb` varchar(100) DEFAULT NULL,
  `unit` varchar(100) DEFAULT NULL,
  `streetNumber` varchar(100) DEFAULT NULL,
  `streetName` varchar(100) DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `state` varchar(100) DEFAULT NULL,
  `country` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`idCustomerThirdPartySync`),
  KEY `idDealership_idExternalRecord_idThirdPartySync_idx` (`Dealership_idDealership`,`idExternalRecord`,`idThirdPartySync`),
  CONSTRAINT `CustomerThirdPartySync_ibfk_1` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DamageReport`;
CREATE TABLE `DamageReport` (
  `idDamageReport` int(11) NOT NULL AUTO_INCREMENT,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `description` text,
  PRIMARY KEY (`idDamageReport`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DamageReportPhoto`;
CREATE TABLE `DamageReportPhoto` (
  `idDamageReportPhoto` int(11) NOT NULL AUTO_INCREMENT,
  `DamageReport_idDamageReport` int(11) NOT NULL,
  `photoFilename` varchar(45) DEFAULT NULL,
  `imageData` longblob,
  `imageType` varchar(10) DEFAULT NULL,
  PRIMARY KEY (`idDamageReportPhoto`),
  KEY `fk_DamageReportPhoto_DamageReport1_idx` (`DamageReport_idDamageReport`),
  CONSTRAINT `fk_DamageReportPhoto_DamageReport1` FOREIGN KEY (`DamageReport_idDamageReport`) REFERENCES `DamageReport` (`idDamageReport`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `Dealership`;
CREATE TABLE `Dealership` (
  `idDealership` int(11) NOT NULL AUTO_INCREMENT,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `dealershipStatus` tinyint(3) unsigned DEFAULT NULL,
  `name` varchar(45) NOT NULL,
  `address1` varchar(45) NOT NULL,
  `address2` varchar(45) DEFAULT NULL COMMENT '	',
  `suburb` varchar(45) NOT NULL COMMENT '	',
  `state` varchar(45) NOT NULL,
  `country` varchar(45) DEFAULT NULL,
  `postcode` varchar(10) NOT NULL,
  `telephone` varchar(20) NOT NULL,
  `email` varchar(254) DEFAULT NULL,
  `abn` int(10) unsigned DEFAULT NULL,
  `testDriveTermConditions` text,
  `carLoanTermConditions` text,
  `logoFilename` varchar(45) DEFAULT NULL,
  `overdraftType` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `overdraftValue` float DEFAULT NULL,
  `planType` tinyint(3) unsigned NOT NULL,
  `planEndDate` datetime DEFAULT NULL,
  `numTripCredit` int(11) NOT NULL DEFAULT '0',
  `logoVersion` smallint(5) unsigned NOT NULL DEFAULT '0',
  `DealershipGroup_idDealershipGroup` int(11) DEFAULT NULL,
  `dealershipGroupLastUpdated` timestamp NULL DEFAULT NULL,
  `ageOutNotifyState` tinyint(3) unsigned DEFAULT '0',
  `ageOutLastActivityId` int(11) DEFAULT NULL,
  `DealershipLogo_idDealershipLogo` int(11) DEFAULT NULL,
  `Country_idCountry` int(11) DEFAULT NULL,
  `settingDistanceUnit` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `settingDateFormat` tinyint(3) unsigned DEFAULT '1',
  `settingTimezone` varchar(90) DEFAULT NULL,
  `insuranceWaiverEnabled` tinyint(1) NOT NULL DEFAULT '0',
  `insuranceWaiverText` text,
  `loanOdometerTrackingEnabled` tinyint(1) DEFAULT '0',
  `contractEndDate` datetime DEFAULT NULL,
  `isSendLoanCarReviewEmail` tinyint(1) DEFAULT '0',
  `isBookingFeatureEnabled` tinyint(1) DEFAULT '0',
  `loanVehicleNumber` smallint(5) unsigned DEFAULT '0',
  `bookingConfirmationEmailOption` tinyint(3) unsigned DEFAULT '1',
  `bookingUpdateEmailOption` tinyint(1) unsigned DEFAULT '1',
  `skipBookingEmailWithinMinutes` smallint(5) unsigned DEFAULT '0',
  `bookingConsideredNoShowAfterMinutes` smallint(5) unsigned DEFAULT '60',
  `bookingEmailBccSalesOption` tinyint(3) unsigned DEFAULT '1',
  `isEnquiryEnabled` tinyint(1) DEFAULT '1',
  `isTestDriveEnabled` tinyint(1) DEFAULT '1',
  `isLoanEnabled` tinyint(1) DEFAULT '1',
  `isSelfLoanEnabled` tinyint(1) DEFAULT '1',
  `level2OdometerWarningKm` int(11) DEFAULT NULL,
  `level2OdometerWarningEmail` varchar(255) DEFAULT NULL,
  `level1OdometerWarningKm` int(11) DEFAULT NULL,
  `level1OdometerWarningEmail` varchar(255) DEFAULT NULL,
  `xeroIdentifier` varchar(255) DEFAULT NULL,
  `selfLoanNotificationMon` tinyint(1) DEFAULT '1',
  `selfLoanNotificationTue` tinyint(1) DEFAULT '1',
  `selfLoanNotificationWed` tinyint(1) DEFAULT '1',
  `selfLoanNotificationThu` tinyint(1) DEFAULT '1',
  `selfLoanNotificationFri` tinyint(1) DEFAULT '1',
  `selfLoanNotificationSat` tinyint(1) DEFAULT '1',
  `selfLoanNotificationSun` tinyint(1) DEFAULT '1',
  `selfLoanNotificationTime` varchar(10) DEFAULT '06:00 PM',
  `settingRecentCustomerAge` int(11) NOT NULL DEFAULT '30',
  `emailDisplayName` varchar(254) DEFAULT NULL,
  `emailFromAddress` varchar(254) DEFAULT NULL,
  `isSendTestDriveReviewEmail` tinyint(1) DEFAULT '1',
  `isSendTestDriveTermsEmail` tinyint(1) DEFAULT '1',
  `isSendLoanCarTermsEmail` tinyint(1) DEFAULT '1',
  `longName` varchar(255) DEFAULT NULL,
  `loanLocationTrackingEnabled` tinyint(1) DEFAULT '0',
  `selfLoanReturnNotificationTime` varchar(10) DEFAULT '',
  `deleteLicenceImagesSetting` tinyint(3) unsigned NOT NULL DEFAULT '6',
  `lockDriverLicenceImages` tinyint(3) unsigned DEFAULT '1',
  `carsLeftWarningNumbers` int(11) DEFAULT NULL,
  `carsLeftWarningEmail` varchar(254) DEFAULT NULL,
  `excludeFromCleanUp` tinyint(3) unsigned DEFAULT '0',
  `costOfPPSR` decimal(10,2) NOT NULL DEFAULT '0.00',
  `costOfLookup` decimal(10,2) NOT NULL DEFAULT '0.00',
  `includedLookup` int(11) NOT NULL DEFAULT '0',
  `forceMFA` tinyint(3) unsigned DEFAULT '0',
  `hardDeleteLicenceImagesAfterMonths` tinyint(3) unsigned NOT NULL DEFAULT '12',
  `isGeoblockingEnabled` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `geoblockingWhiteList` text NOT NULL,
  `driverLicenceImagesUnlockMethod` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `exportAccess` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `exportAccessWhiteList` text NOT NULL,
  `securityAccess` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `securityAccessWhiteList` text NOT NULL,
  `isInfringementFeatureEnabled` tinyint(1) DEFAULT '0',
  `imagesWatermark` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `cameraMode` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `retainDamageReport` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `fuelGaugeLevelEntryMode` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `vehicleRegoExpiryWarningDays` int(11) DEFAULT NULL,
  `vehicleRegoExpiryWarningEmail` varchar(254) DEFAULT NULL,
  `forceHardDeleteLicenceImagesAfterMonths` tinyint(3) unsigned NOT NULL DEFAULT '24',
  `selfLoanConfirmation` tinyint(1) DEFAULT '0',
  `selfLoanConfirmationText` text,
  `appraisalsImagesMode` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `appraisalsShootStyle` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `isLicenceTextRecognitionFeatureEnabled` tinyint(1) DEFAULT '0',
  `duplicateRegoMode` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `vehicleLocationEditMode` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `consentsOptions` text,
  `driveAccompanimentMode` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `deleteCustomerDataMode` tinyint(1) DEFAULT '0',
  `autoFinishUnaccompaniedTestDrive` tinyint(3) unsigned DEFAULT '0',
  PRIMARY KEY (`idDealership`),
  KEY `fk_Dealership_DealershipGroup1_idx` (`DealershipGroup_idDealershipGroup`),
  KEY `fk_Dealership_DealershipLogo1_idx` (`DealershipLogo_idDealershipLogo`),
  KEY `fk_Dealership_Country1_idx` (`Country_idCountry`),
  CONSTRAINT `fk_Dealership_Country1` FOREIGN KEY (`Country_idCountry`) REFERENCES `Country` (`idCountry`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `fk_Dealership_DealershipGroup1` FOREIGN KEY (`DealershipGroup_idDealershipGroup`) REFERENCES `DealershipGroup` (`idDealershipGroup`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `fk_Dealership_DealershipLogo1` FOREIGN KEY (`DealershipLogo_idDealershipLogo`) REFERENCES `DealershipLogo` (`idDealershipLogo`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DELIMITER ;;

CREATE TRIGGER `checkForceHardDeleteLicenceImagesAfterMonths` BEFORE UPDATE ON `Dealership` FOR EACH ROW
BEGIN
                IF (NEW.forceHardDeleteLicenceImagesAfterMonths <> OLD.forceHardDeleteLicenceImagesAfterMonths) AND (NEW.forceHardDeleteLicenceImagesAfterMonths < NEW.hardDeleteLicenceImagesAfterMonths) THEN
                    SET NEW.forceHardDeleteLicenceImagesAfterMonths = NEW.hardDeleteLicenceImagesAfterMonths;
                END IF;
			END;;

DELIMITER ;

DROP TABLE IF EXISTS `DealershipAlert`;
CREATE TABLE `DealershipAlert` (
  `idDealershipAlert` int(11) NOT NULL AUTO_INCREMENT,
  `Dealership_idDealership` int(11) NOT NULL,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `alertType` tinyint(3) unsigned NOT NULL,
  `threshold` tinyint(3) unsigned NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`idDealershipAlert`),
  UNIQUE KEY `unique_dealership_alertType` (`Dealership_idDealership`,`alertType`),
  KEY `fk_DealershipAlert_Dealership1_idx` (`Dealership_idDealership`),
  CONSTRAINT `fk_DealershipAlert_Dealership1` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DealershipCustomer`;
CREATE TABLE `DealershipCustomer` (
  `idDealershipCustomer` int(11) NOT NULL AUTO_INCREMENT,
  `Dealership_idDealership` int(11) NOT NULL,
  `firstName` varchar(45) NOT NULL,
  `lastName` varchar(45) NOT NULL,
  `age` varchar(10) DEFAULT NULL,
  `telephone` varchar(20) DEFAULT NULL,
  `email` varchar(254) DEFAULT NULL,
  `gender` varchar(1) DEFAULT NULL,
  `postcode` varchar(10) DEFAULT NULL,
  `suburb` varchar(100) DEFAULT NULL,
  `unit` varchar(100) DEFAULT NULL,
  `streetNumber` varchar(100) DEFAULT NULL,
  `streetName` varchar(100) DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `state` varchar(100) DEFAULT NULL,
  `country` varchar(100) DEFAULT NULL,
  `licenceNumber` varchar(20) DEFAULT NULL,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`idDealershipCustomer`),
  KEY `fk_SecurityLog_Dealership_idx` (`Dealership_idDealership`),
  CONSTRAINT `DealershipCustomer_ibfk_1` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DealershipEav`;
CREATE TABLE `DealershipEav` (
  `idDealershipEav` int(11) NOT NULL AUTO_INCREMENT,
  `Dealership_idDealership` int(11) NOT NULL,
  `attribute` varchar(40) NOT NULL,
  `value` text NOT NULL,
  PRIMARY KEY (`idDealershipEav`),
  KEY `fk_DealershipEav_Dealership_idx` (`Dealership_idDealership`),
  KEY `fk_DealershipEav_attribute_idx` (`attribute`),
  CONSTRAINT `DealershipEav_ibfk_1` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DealershipFile`;
CREATE TABLE `DealershipFile` (
  `idDealershipFile` int(11) NOT NULL AUTO_INCREMENT,
  `Dealership_idDealership` int(11) NOT NULL,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `User_idUserCreator` int(11) NOT NULL,
  `fileData` longblob NOT NULL,
  `fileType` varchar(16) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `fileSizeBytes` int(11) NOT NULL,
  PRIMARY KEY (`idDealershipFile`),
  KEY `fk_DealershipFile_Dealership1_idx` (`Dealership_idDealership`),
  KEY `fk_DealershipFile_User1_idx` (`User_idUserCreator`),
  CONSTRAINT `fk_DealershipFile_Dealership1` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `fk_DealershipFile_User1` FOREIGN KEY (`User_idUserCreator`) REFERENCES `User` (`idUser`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DealershipGroup`;
CREATE TABLE `DealershipGroup` (
  `idDealershipGroup` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(45) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `signupCode` varchar(45) NOT NULL,
  `createTimestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `groupStatus` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `contractEndDate` datetime NOT NULL,
  `apiKey` varchar(40) DEFAULT NULL,
  `publicApiConfig` text,
  `appraisalsApiKey` varchar(40) DEFAULT NULL,
  `appraisalsPublicApiConfig` text,
  PRIMARY KEY (`idDealershipGroup`),
  UNIQUE KEY `groupSignupCode_UNIQUE` (`signupCode`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DealershipGroupFile`;
CREATE TABLE `DealershipGroupFile` (
  `idDealershipGroupFile` int(11) NOT NULL AUTO_INCREMENT,
  `DealershipGroup_idDealershipGroup` int(11) NOT NULL,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `User_idUserCreator` int(11) NOT NULL,
  `fileData` longblob NOT NULL,
  `fileType` varchar(16) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `fileSizeBytes` int(10) unsigned NOT NULL,
  PRIMARY KEY (`idDealershipGroupFile`,`DealershipGroup_idDealershipGroup`),
  KEY `fk_DealershipGroupFile_DealershipGroup1_idx` (`DealershipGroup_idDealershipGroup`),
  KEY `fk_DealershipGroupFile_User1_idx` (`User_idUserCreator`),
  CONSTRAINT `fk_DealershipGroupFile_DealershipGroup1` FOREIGN KEY (`DealershipGroup_idDealershipGroup`) REFERENCES `DealershipGroup` (`idDealershipGroup`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `fk_DealershipGroupFile_User1` FOREIGN KEY (`User_idUserCreator`) REFERENCES `User` (`idUser`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DealershipIntegrationStatus`;
CREATE TABLE `DealershipIntegrationStatus` (
  `idDealershipIntegrationStatus` int(11) NOT NULL AUTO_INCREMENT,
  `IntegrationStatus_idIntegrationStatus` int(11) DEFAULT NULL,
  `Dealership_idDealership` int(11) DEFAULT NULL,
  `successStreak` int(11) NOT NULL DEFAULT '0',
  `lastSuccessDate` datetime DEFAULT NULL,
  `failureStreak` int(11) NOT NULL DEFAULT '0',
  `lastFailureDate` datetime DEFAULT NULL,
  `lastFailureMessage` text,
  PRIMARY KEY (`idDealershipIntegrationStatus`),
  KEY `FK_DealershipIntegrationStatus_IntegrationStatus` (`IntegrationStatus_idIntegrationStatus`),
  KEY `FK_DealershipIntegrationStatus_Dealership` (`Dealership_idDealership`),
  CONSTRAINT `FK_DealershipIntegrationStatus_Dealership` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `FK_DealershipIntegrationStatus_IntegrationStatus` FOREIGN KEY (`IntegrationStatus_idIntegrationStatus`) REFERENCES `IntegrationStatus` (`idIntegrationStatus`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DealershipLogo`;
CREATE TABLE `DealershipLogo` (
  `idDealershipLogo` int(11) NOT NULL AUTO_INCREMENT,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `imageData` longblob NOT NULL,
  `imageType` varchar(10) NOT NULL,
  PRIMARY KEY (`idDealershipLogo`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DealershipSignUpSurvey`;
CREATE TABLE `DealershipSignUpSurvey` (
  `idDealershipSignUpSurvey` int(11) NOT NULL AUTO_INCREMENT,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Dealership_idDealership` int(11) NOT NULL,
  `referralMethod` tinyint(3) unsigned NOT NULL,
  `referralTextOther` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`idDealershipSignUpSurvey`),
  KEY `fk_SignUpSurvey_Dealership1_idx` (`Dealership_idDealership`),
  CONSTRAINT `fk_SignUpSurvey_Dealership1` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DealershipStats`;
CREATE TABLE `DealershipStats` (
  `idDealershipStats` int(11) NOT NULL AUTO_INCREMENT,
  `Dealership_idDealership` int(11) NOT NULL,
  `numTestDrive` int(11) NOT NULL DEFAULT '0',
  `numLoan` int(11) NOT NULL DEFAULT '0',
  `numSelfLoan` int(11) NOT NULL DEFAULT '0',
  `numEnquiryNonStock` int(11) NOT NULL DEFAULT '0',
  `numEnquiryStock` int(11) NOT NULL DEFAULT '0',
  `numSoldTestDrive` int(11) NOT NULL DEFAULT '0',
  `numSoldEnquiry` int(11) NOT NULL DEFAULT '0',
  `numTotal` int(11) NOT NULL DEFAULT '0',
  `inDate` date NOT NULL,
  PRIMARY KEY (`idDealershipStats`),
  KEY `fk_DealershipStats_Dealership_idx` (`Dealership_idDealership`),
  KEY `idx_DealershipStats_inDate` (`inDate`),
  CONSTRAINT `DealershipStats_ibfk_1` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DealershipSubscription`;
CREATE TABLE `DealershipSubscription` (
  `DealershipId` int(11) NOT NULL,
  `ActiveSubscription` int(11) NOT NULL,
  PRIMARY KEY (`DealershipId`),
  CONSTRAINT `FK_DealershipSubscription_Dealership_DealershipId` FOREIGN KEY (`DealershipId`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DealershipThirdPartyApiAccess`;
CREATE TABLE `DealershipThirdPartyApiAccess` (
  `DealershipThirdPartyApiAccessId` int(11) NOT NULL AUTO_INCREMENT,
  `AccessibleApis` text,
  `DealershipId` int(11) NOT NULL,
  `AppraisalsVehicleLookupSource` tinyint(4) DEFAULT NULL,
  PRIMARY KEY (`DealershipThirdPartyApiAccessId`),
  UNIQUE KEY `IX_DealershipThirdPartyApiAccess_DealershipId` (`DealershipId`),
  CONSTRAINT `FK_DealershipThirdPartyApiAccess_Dealership_DealershipId` FOREIGN KEY (`DealershipId`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DealershipThirdPartySyncConfig`;
CREATE TABLE `DealershipThirdPartySyncConfig` (
  `idDealershipThirdPartySyncConfig` int(11) NOT NULL AUTO_INCREMENT,
  `createTimestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `Dealership_idDealership` int(11) NOT NULL,
  `idThirdPartySync` tinyint(3) unsigned NOT NULL,
  `lastUpdateTimestamp` timestamp NULL DEFAULT NULL,
  `numSyncFailuresSinceLastSuccess` int(11) NOT NULL DEFAULT '0',
  `lastSuccessSyncTimestamp` timestamp NULL DEFAULT NULL,
  `syncStatus` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `username` varchar(64) DEFAULT NULL,
  `username2` varchar(65000) DEFAULT NULL,
  `password` varchar(64) DEFAULT NULL,
  `lastSyncErrorNotificationTimestamp` timestamp NULL DEFAULT NULL,
  `enabledTimestamp` timestamp NULL DEFAULT NULL,
  `lastSyncAttemptTimestamp` timestamp NULL DEFAULT NULL,
  `lastChecksum` varchar(64) DEFAULT NULL,
  `extraConfig` text,
  PRIMARY KEY (`idDealershipThirdPartySyncConfig`),
  KEY `fk_DealershipThirdPartySyncConfig_Dealership1_idx` (`Dealership_idDealership`),
  CONSTRAINT `fk_DealershipThirdPartySyncConfig_Dealership1` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `Dealership_has_User`;
CREATE TABLE `Dealership_has_User` (
  `Dealership_idDealership` int(11) NOT NULL,
  `User_idUser` int(11) NOT NULL,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `userRole` tinyint(3) unsigned NOT NULL,
  PRIMARY KEY (`Dealership_idDealership`,`User_idUser`),
  KEY `fk_Dealership_has_User_User1_idx` (`User_idUser`),
  KEY `fk_Dealership_has_User_Dealership_idx` (`Dealership_idDealership`),
  CONSTRAINT `fk_Dealership_has_User_Dealership` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT `fk_Dealership_has_User_User1` FOREIGN KEY (`User_idUser`) REFERENCES `User` (`idUser`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `Drive`;
CREATE TABLE `Drive` (
  `idDrive` int(11) NOT NULL AUTO_INCREMENT,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `driveStatus` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `driveType` tinyint(3) unsigned NOT NULL,
  `Car_idCar` int(11) DEFAULT NULL,
  `User_idUser` int(11) NOT NULL,
  `Driver_idDriver` int(11) DEFAULT NULL,
  `startTimestamp` timestamp NULL DEFAULT NULL,
  `endTimestamp` timestamp NULL DEFAULT NULL,
  `DamageReport_idDamageReportBefore` int(11) DEFAULT NULL,
  `DamageReport_idDamageReportAfter` int(11) DEFAULT NULL,
  `imageSignatureFilenameStart` varchar(45) DEFAULT NULL,
  `imageSignatureFilenameEnd` varchar(45) DEFAULT NULL,
  `driveNotes` text,
  `routeDistance` float DEFAULT NULL,
  `driverReviewCarStars` tinyint(3) unsigned DEFAULT NULL,
  `driverReviewSalesPersonStars` tinyint(3) unsigned DEFAULT NULL,
  `driverReviewTimestamp` timestamp NULL DEFAULT NULL,
  `photoLicenseFrontFilename` varchar(45) DEFAULT NULL,
  `photoLicenseBackFilename` varchar(45) DEFAULT NULL,
  `routePolyline` text,
  `averageSpeed` float DEFAULT NULL,
  `startPositionLat` float(10,6) DEFAULT NULL,
  `startPositionLng` float(10,6) DEFAULT NULL,
  `startOdometerKm` int(11) DEFAULT NULL,
  `endOdometerKm` int(11) DEFAULT NULL,
  `purpose` varchar(255) DEFAULT NULL,
  `DriverLicense_idDriverLicense` int(11) DEFAULT NULL,
  `DriverSignature_idDriverSignatureStart` int(11) DEFAULT NULL,
  `DriverSignature_idDriverSignatureEnd` int(11) DEFAULT NULL,
  `insuranceWaiverChecked` tinyint(1) NOT NULL DEFAULT '0',
  `accompaniedDrive` tinyint(1) NOT NULL DEFAULT '0',
  `User_idUserReturn` int(11) DEFAULT NULL,
  `roNumber` varchar(30) DEFAULT NULL,
  `tradePlateReg` varchar(30) DEFAULT NULL,
  `tcEmail` varchar(254) DEFAULT NULL,
  `isStaffAssignRequired` tinyint(1) DEFAULT '0',
  `overrideStatus` tinyint(3) unsigned DEFAULT '0',
  `overrideUserId` int(11) DEFAULT NULL,
  `startFuelGaugeLevel` int(11) DEFAULT '-1',
  `endFuelGaugeLevel` int(11) DEFAULT '-1',
  `Drive_idDealership` int(11) DEFAULT NULL,
  `isVehicleAssignRequired` tinyint(1) DEFAULT '0',
  `financeIntroAdvisedDate` timestamp NULL DEFAULT NULL,
  `financeIntroAdvisedBy` int(11) DEFAULT NULL,
  `isFinanceIntroAdvised` tinyint(1) NOT NULL DEFAULT '0',
  `regoLog` varchar(50) DEFAULT NULL,
  `Car_idCarOldBooked` int(11) DEFAULT NULL,
  `returnLocation` varchar(255) DEFAULT NULL,
  `DamageReport_idCustomerVehicleDamageReportBefore` int(11) DEFAULT NULL,
  `DamageReport_idCustomerVehicleDamageReportAfter` int(11) DEFAULT NULL,
  PRIMARY KEY (`idDrive`),
  KEY `fk_TestDrive_Car1_idx` (`Car_idCar`),
  KEY `fk_TestDrive_User1_idx` (`User_idUser`),
  KEY `fk_TestDrive_Client1_idx` (`Driver_idDriver`),
  KEY `fk_TestDrive_DamageReport1_idx` (`DamageReport_idDamageReportBefore`),
  KEY `fk_TestDrive_DamageReport2_idx` (`DamageReport_idDamageReportAfter`),
  KEY `fk_Drive_DriverLicense1_idx` (`DriverLicense_idDriverLicense`),
  KEY `fk_Drive_User2` (`User_idUserReturn`),
  KEY `idx_idDealership_driveStatus_idDrive` (`Drive_idDealership`,`driveStatus`,`idDrive`),
  KEY `idx_idDealership_driveStatus_driveType_startTimestamp_idDrive` (`Drive_idDealership`,`driveStatus`,`driveType`,`startTimestamp`,`idDrive`),
  KEY `fk_Drive_CarOldBooked` (`Car_idCarOldBooked`),
  KEY `fk_TestDrive_DamageReport3_idx` (`DamageReport_idCustomerVehicleDamageReportBefore`),
  KEY `fk_TestDrive_DamageReport4_idx` (`DamageReport_idCustomerVehicleDamageReportAfter`),
  KEY `fk_Drive_DriverSignature1_idx` (`DriverSignature_idDriverSignatureStart`),
  KEY `fk_Drive_DriverSignature2_idx` (`DriverSignature_idDriverSignatureEnd`),
  KEY `idx_regoLog` (`regoLog`),
  KEY `idx_tradePlateReg` (`tradePlateReg`),
  CONSTRAINT `fk_Drive_Car1` FOREIGN KEY (`Car_idCar`) REFERENCES `Car` (`idCar`) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT `fk_Drive_CarOldBooked` FOREIGN KEY (`Car_idCarOldBooked`) REFERENCES `Car` (`idCar`),
  CONSTRAINT `fk_Drive_DamageReport1` FOREIGN KEY (`DamageReport_idDamageReportBefore`) REFERENCES `DamageReport` (`idDamageReport`) ON DELETE SET NULL ON UPDATE NO ACTION,
  CONSTRAINT `fk_Drive_DamageReport2` FOREIGN KEY (`DamageReport_idDamageReportAfter`) REFERENCES `DamageReport` (`idDamageReport`) ON DELETE SET NULL ON UPDATE NO ACTION,
  CONSTRAINT `fk_Drive_Driver1` FOREIGN KEY (`Driver_idDriver`) REFERENCES `Driver` (`idDriver`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `fk_Drive_DriverLicense1` FOREIGN KEY (`DriverLicense_idDriverLicense`) REFERENCES `DriverLicense` (`idDriverLicense`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `fk_Drive_DriverSignature1` FOREIGN KEY (`DriverSignature_idDriverSignatureStart`) REFERENCES `DriverSignature` (`idDriverSignature`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `fk_Drive_DriverSignature2` FOREIGN KEY (`DriverSignature_idDriverSignatureEnd`) REFERENCES `DriverSignature` (`idDriverSignature`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `fk_Drive_User1` FOREIGN KEY (`User_idUser`) REFERENCES `User` (`idUser`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `fk_Drive_User2` FOREIGN KEY (`User_idUserReturn`) REFERENCES `User` (`idUser`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DriveComment`;
CREATE TABLE `DriveComment` (
  `idDriveComment` int(11) NOT NULL AUTO_INCREMENT,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Drive_idDrive` int(11) NOT NULL,
  `comments` text NOT NULL,
  `User_idUser` int(11) DEFAULT NULL,
  PRIMARY KEY (`idDriveComment`),
  KEY `fk_DriveComment_Drive1_idx` (`Drive_idDrive`),
  KEY `fk_DriveComment_User1_idx` (`User_idUser`),
  CONSTRAINT `fk_DriveComment_Drive1` FOREIGN KEY (`Drive_idDrive`) REFERENCES `Drive` (`idDrive`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `fk_DriveComment_User1` FOREIGN KEY (`User_idUser`) REFERENCES `User` (`idUser`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP VIEW IF EXISTS `DriveCommentSummary`;
CREATE TABLE `DriveCommentSummary` (`Drive_idDrive` int(11), `comments` text);


DROP TABLE IF EXISTS `DriveFile`;
CREATE TABLE `DriveFile` (
  `idDriveFile` int(11) NOT NULL AUTO_INCREMENT,
  `Drive_idDrive` int(11) NOT NULL,
  `createTimestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `User_idUserCreator` int(11) NOT NULL,
  `fileData` longblob NOT NULL,
  `fileType` varchar(16) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `fileSizeBytes` int(10) unsigned NOT NULL,
  PRIMARY KEY (`idDriveFile`),
  KEY `fk_DriveFile_Drive1_idx` (`Drive_idDrive`),
  KEY `fk_DriveFile_User1_idx` (`User_idUserCreator`),
  CONSTRAINT `fk_DriveFile_Drive1` FOREIGN KEY (`Drive_idDrive`) REFERENCES `Drive` (`idDrive`) ON DELETE NO ACTION ON UPDATE NO ACTION,
  CONSTRAINT `fk_DriveFile_User1` FOREIGN KEY (`User_idUserCreator`) REFERENCES `User` (`idUser`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `Driver`;
CREATE TABLE `Driver` (
  `idDriver` int(11) NOT NULL AUTO_INCREMENT,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `firstName` varchar(45) NOT NULL,
  `lastName` varchar(45) NOT NULL,
  `age` varchar(10) DEFAULT NULL,
  `telephone` varchar(20) DEFAULT NULL,
  `email` varchar(254) DEFAULT NULL,
  `consentFlags` int(10) unsigned NOT NULL DEFAULT '0',
  `gender` varchar(1) DEFAULT NULL,
  `postcode` varchar(10) DEFAULT NULL,
  `leadSourceId` int(11) DEFAULT NULL,
  `suburb` varchar(100) DEFAULT NULL,
  `unit` varchar(100) DEFAULT NULL,
  `streetNumber` varchar(100) DEFAULT NULL,
  `streetName` varchar(100) DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `state` varchar(100) DEFAULT NULL,
  `country` varchar(100) DEFAULT NULL,
  `extId` varchar(100) DEFAULT NULL,
  `licenceNumber` varchar(20) DEFAULT NULL,
  PRIMARY KEY (`idDriver`),
  KEY `gender` (`gender`),
  KEY `age` (`age`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DriverDocument`;
CREATE TABLE `DriverDocument` (
  `idDriverDocument` int(11) NOT NULL AUTO_INCREMENT,
  `Driver_idDriver` int(11) NOT NULL,
  `s3Id` varchar(255) DEFAULT NULL,
  `fileType` varchar(10) DEFAULT NULL,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`idDriverDocument`),
  KEY `fk_DriverDocument_Driver_idx` (`Driver_idDriver`),
  CONSTRAINT `DriverDocument_ibfk_1` FOREIGN KEY (`Driver_idDriver`) REFERENCES `Driver` (`idDriver`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP VIEW IF EXISTS `DriveReport`;
CREATE TABLE `DriveReport` (`idDriveReport` int(11), `Drive_accompaniedDrive` tinyint(1), `Drive_driverReviewSalesPersonStars` tinyint(3) unsigned, `Drive_driveType` tinyint(3) unsigned, `Drive_driveStatus` tinyint(3) unsigned, `Drive_endTimestamp` timestamp, `Drive_idDrive` int(11), `Drive_insuranceWaiverChecked` tinyint(1), `Drive_startTimestamp` timestamp, `Drive_roNumber` varchar(30), `Drive_distance` bigint(12), `Drive_startFuelGaugeLevel` int(11), `Drive_endFuelGaugeLevel` int(11), `Drive_averageSpeed` float, `Drive_tradePlateReg` varchar(30), `Drive_startOdometerKm` int(11), `Drive_endOdometerKm` int(11), `Drive_routeDistance` float, `Drive_driverReviewCarStars` tinyint(3) unsigned, `Drive_damageReport_idDamageReportBefore` int(11), `Drive_damageReport_idDamageReportAfter` int(11), `Drive_car_idCar` int(11), `Drive_financeIntroAdvisedDate` timestamp, `Drive_financeIntroAdvisedBy` int(11), `Drive_isFinanceIntroAdvised` tinyint(1), `Drive_regoLog` varchar(50), `Car_make` varchar(255), `Car_model` varchar(255), `Car_year` smallint(5) unsigned, `Car_stockNumber` varchar(50), `Car_vin` varchar(50), `Car_rego` varchar(103), `Car_color` varchar(45), `Car_isTradePlateUsed` int(1), `Car_lastKnownLocation` varchar(255), `Dealership_idDealership` int(11), `Dealership_name` varchar(45), `Dealership_suburb` varchar(45), `Dealership_postcode` varchar(10), `Dealership_state` varchar(45), `Dealership_country` varchar(45), `Driver_firstName` varchar(45), `Driver_lastName` varchar(45), `Driver_age` varchar(10), `Driver_gender` varchar(1), `Driver_idDriver` int(11), `Driver_leadSourceId` int(11), `Driver_consentFlags` int(10) unsigned, `Driver_postcode` varchar(10), `Driver_email` varchar(254), `Driver_telephone` varchar(20), `Driver_licenceNumber` varchar(20), `Driver_suburb` varchar(100), `Driver_unit` varchar(100), `Driver_streetNumber` varchar(100), `Driver_streetName` varchar(100), `Driver_city` varchar(100), `Driver_state` varchar(100), `Driver_country` varchar(100), `LeadSourceDetail_description` varchar(45), `LeadSourceDetail_leadSourceId` int(11), `User_idUser` int(11), `User_firstName` varchar(45), `User_lastName` varchar(45), `User_telephone` varchar(20), `User_email` varchar(254), `SoldCar_soldCarType` varchar(19), `SoldCar_Drive_idDrive` int(11), `SoldCar_soldDate` varchar(40));


DROP TABLE IF EXISTS `DriveReportOld`;
CREATE TABLE `DriveReportOld` (
  `idDriveReport` int(11) NOT NULL AUTO_INCREMENT,
  `Car_make` varchar(45) NOT NULL,
  `Car_model` varchar(45) NOT NULL,
  `Car_year` smallint(5) unsigned DEFAULT NULL,
  `Car_stockNumber` varchar(50) DEFAULT NULL,
  `Car_vin` varchar(50) DEFAULT NULL,
  `Car_rego` varchar(50) DEFAULT NULL,
  `Car_color` varchar(50) DEFAULT NULL,
  `Car_isTradePlateUsed` tinyint(1) DEFAULT NULL,
  `Dealership_idDealership` int(11) NOT NULL,
  `Dealership_name` varchar(45) NOT NULL,
  `Dealership_suburb` varchar(45) DEFAULT NULL,
  `Dealership_postcode` varchar(10) DEFAULT NULL,
  `Dealership_state` varchar(45) NOT NULL,
  `Dealership_country` varchar(45) DEFAULT NULL,
  `Drive_accompaniedDrive` tinyint(3) unsigned NOT NULL,
  `Drive_driverReviewSalesPersonStars` tinyint(3) unsigned DEFAULT NULL,
  `Drive_driveType` tinyint(3) unsigned NOT NULL,
  `Drive_driveStatus` tinyint(3) unsigned DEFAULT NULL,
  `Drive_endTimestamp` timestamp NULL DEFAULT NULL,
  `Drive_idDrive` int(11) NOT NULL,
  `Drive_insuranceWaiverChecked` tinyint(3) unsigned DEFAULT NULL,
  `Drive_startTimestamp` timestamp NULL DEFAULT NULL,
  `Drive_roNumber` varchar(45) DEFAULT NULL,
  `Drive_distance` smallint(5) unsigned DEFAULT NULL,
  `Drive_averageSpeed` float DEFAULT NULL,
  `Drive_tradePlateReg` varchar(30) DEFAULT NULL,
  `Drive_startOdometerKm` int(11) DEFAULT NULL,
  `Drive_endOdometerKm` int(11) DEFAULT NULL,
  `Drive_routeDistance` float DEFAULT NULL,
  `Drive_driverReviewCarStars` smallint(5) unsigned DEFAULT NULL,
  `Drive_damageReport_idDamageReportBefore` int(11) DEFAULT NULL,
  `Drive_damageReport_idDamageReportAfter` int(11) DEFAULT NULL,
  `Driver_firstName` varchar(45) DEFAULT NULL,
  `Driver_lastName` varchar(45) DEFAULT NULL,
  `Driver_age` varchar(10) DEFAULT NULL,
  `Driver_gender` varchar(1) DEFAULT NULL,
  `Driver_idDriver` int(11) NOT NULL,
  `Driver_leadSourceId` int(11) DEFAULT NULL,
  `Driver_consentFlags` int(10) unsigned DEFAULT NULL,
  `Driver_postcode` varchar(10) DEFAULT NULL,
  `Driver_email` varchar(45) DEFAULT NULL,
  `Driver_telephone` varchar(20) DEFAULT NULL,
  `SoldCar_soldCarType` enum('Sold (New Order)','Sold (Demonstrator)','Sold (Used)') NOT NULL,
  `User_idUser` int(11) NOT NULL,
  `User_firstName` varchar(45) NOT NULL,
  `User_lastName` varchar(45) NOT NULL,
  `User_telephone` varchar(20) DEFAULT NULL,
  `User_email` varchar(45) DEFAULT NULL,
  `Drive_car_idCar` int(11) DEFAULT NULL,
  `Driver_suburb` varchar(100) DEFAULT NULL,
  `Driver_unit` varchar(100) DEFAULT NULL,
  `Driver_streetNumber` varchar(100) DEFAULT NULL,
  `Driver_streetName` varchar(100) DEFAULT NULL,
  `Driver_city` varchar(100) DEFAULT NULL,
  `Driver_state` varchar(100) DEFAULT NULL,
  `Driver_country` varchar(100) DEFAULT NULL,
  `Drive_financeIntroAdvisedDate` timestamp NULL DEFAULT NULL,
  `Drive_financeIntroAdvisedBy` int(11) DEFAULT NULL,
  `Drive_isFinanceIntroAdvised` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`idDriveReport`),
  KEY `fk_DriveReport_Car_make_idx` (`Car_make`),
  KEY `fk_DriveReport_Car_model_idx` (`Car_model`),
  KEY `fk_DriveReport_Dealership_idDealership_idx` (`Dealership_idDealership`),
  KEY `fk_DriveReport_Dealership_state_idx` (`Dealership_state`),
  KEY `fk_DriveReport_Drive_accompaniedDrive_idx` (`Drive_accompaniedDrive`),
  KEY `fk_DriveReport_Drive_driverReviewSalesPersonStars_idx` (`Drive_driverReviewSalesPersonStars`),
  KEY `fk_DriveReport_Drive_driveType_idx` (`Drive_driveType`),
  KEY `fk_DriveReport_Drive_endTimestamp_idx` (`Drive_endTimestamp`),
  KEY `fk_DriveReport_Drive_insuranceWaiverChecked_idx` (`Drive_insuranceWaiverChecked`),
  KEY `fk_DriveReport_Drive_startTimestamp_idx` (`Drive_startTimestamp`),
  KEY `fk_DriveReport_Driver_age_idx` (`Driver_age`),
  KEY `fk_DriveReport_Driver_gender_idx` (`Driver_gender`),
  KEY `fk_DriveReport_Driver_leadSourceId_idx` (`Driver_leadSourceId`),
  KEY `fk_DriveReport_Driver_postcode_idx` (`Driver_postcode`),
  KEY `fk_DriveReport_SoldCar_soldCarType_idx` (`SoldCar_soldCarType`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DriverLicense`;
CREATE TABLE `DriverLicense` (
  `idDriverLicense` int(11) NOT NULL AUTO_INCREMENT,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `frontImageData` longblob,
  `licenseFrontS3Id` varchar(255) DEFAULT NULL,
  `frontImageType` varchar(10) NOT NULL,
  `backImageData` longblob,
  `licenseBackS3Id` varchar(255) DEFAULT NULL,
  `backImageType` varchar(10) NOT NULL,
  `isAsync` tinyint(1) DEFAULT '0',
  `bucketType` tinyint(1) unsigned NOT NULL DEFAULT '0',
  `isBackedUp` tinyint(1) NOT NULL DEFAULT '0',
  `recognitionStatus` int(11) DEFAULT '0',
  `recognitionData` mediumtext,
  PRIMARY KEY (`idDriverLicense`),
  KEY `frontImageType` (`frontImageType`),
  KEY `recognitionStatus` (`recognitionStatus`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DriverSignature`;
CREATE TABLE `DriverSignature` (
  `idDriverSignature` int(11) NOT NULL AUTO_INCREMENT,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `imageData` longblob NOT NULL,
  `imageType` varchar(10) NOT NULL,
  `verbalConfirmation` tinyint(1) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`idDriverSignature`),
  KEY `imageType` (`imageType`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `DriveThirdPartySync`;
CREATE TABLE `DriveThirdPartySync` (
  `idDriveThirdPartySync` int(11) NOT NULL AUTO_INCREMENT,
  `createTimestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `idThirdPartySync` tinyint(3) unsigned NOT NULL,
  `Drive_idDrive` int(11) NOT NULL,
  `idExternalRecord` varchar(64) DEFAULT NULL,
  `dedupKey` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`idDriveThirdPartySync`),
  KEY `fk_DriveThirdPartySync_Drive1_idx` (`Drive_idDrive`),
  KEY `idx_idThirdPartySync_dedupKey` (`idThirdPartySync`,`dedupKey`),
  CONSTRAINT `fk_DriveThirdPartySync_Drive1` FOREIGN KEY (`Drive_idDrive`) REFERENCES `Drive` (`idDrive`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `EmailValidation`;
CREATE TABLE `EmailValidation` (
  `idEmailValidation` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(1024) NOT NULL,
  `regex` tinyint(3) unsigned DEFAULT NULL,
  `mailgun` tinyint(3) unsigned DEFAULT NULL,
  `delivery` tinyint(3) unsigned DEFAULT NULL,
  `valid` tinyint(3) unsigned DEFAULT NULL,
  PRIMARY KEY (`idEmailValidation`),
  KEY `email` (`email`(767)),
  KEY `valid` (`valid`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `ExternalApiCallLog`;
CREATE TABLE `ExternalApiCallLog` (
  `idExternalApiCallLog` int(11) NOT NULL AUTO_INCREMENT,
  `ExternalApiDriveSync_idExternalApiDriveSync` int(11) DEFAULT NULL,
  `url` text,
  `requestContent` text,
  `responseContent` text,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`idExternalApiCallLog`),
  KEY `idx_ExternalApiDriveSync_idExternalApiDriveSync` (`ExternalApiDriveSync_idExternalApiDriveSync`),
  CONSTRAINT `FK_ExternalApiCallLog_ExternalApiDriveSync` FOREIGN KEY (`ExternalApiDriveSync_idExternalApiDriveSync`) REFERENCES `ExternalApiDriveSync` (`idExternalApiDriveSync`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `ExternalApiDriveSync`;
CREATE TABLE `ExternalApiDriveSync` (
  `idExternalApiDriveSync` int(11) NOT NULL AUTO_INCREMENT,
  `Dealership_idDealership` int(11) NOT NULL,
  `idThirdPartySync` int(11) NOT NULL,
  `Drive_idDrive` int(11) DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT '0',
  `externalRecordId` varchar(255) NOT NULL,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updateTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ExternalApiCallLog_effectiveExternalApiCallLogId` int(11) DEFAULT NULL,
  `User_idAssignedUser` int(11) DEFAULT NULL,
  PRIMARY KEY (`idExternalApiDriveSync`),
  KEY `idx_externalRecordId` (`externalRecordId`),
  KEY `idx_Dealership_idDealership` (`Dealership_idDealership`),
  KEY `idx_Drive_idDrive` (`Drive_idDrive`),
  KEY `idx_ExternalApiCallLog_effectiveExternalApiCallLogId` (`ExternalApiCallLog_effectiveExternalApiCallLogId`),
  KEY `idx_User_idAssignedUser` (`User_idAssignedUser`),
  CONSTRAINT `FK_ExternalApiDriveSync_Dealership` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT `FK_ExternalApiDriveSync_Drive` FOREIGN KEY (`Drive_idDrive`) REFERENCES `Drive` (`idDrive`) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT `FK_ExternalApiDriveSync_effectiveExternalApiCallLog` FOREIGN KEY (`ExternalApiCallLog_effectiveExternalApiCallLogId`) REFERENCES `ExternalApiCallLog` (`idExternalApiCallLog`) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT `fk_User_idAssignedUser` FOREIGN KEY (`User_idAssignedUser`) REFERENCES `User` (`idUser`) ON DELETE SET NULL ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `e_background_job`;
CREATE TABLE `e_background_job` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `progress` int(11) DEFAULT NULL,
  `status` int(11) DEFAULT NULL,
  `start_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_time` timestamp NULL DEFAULT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `request` text,
  `status_text` text,
  PRIMARY KEY (`id`),
  KEY `end_time` (`end_time`),
  KEY `start_time` (`start_time`,`end_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_end_time_status` (`end_time`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `FuelTypeSortName`;
CREATE TABLE `FuelTypeSortName` (
  `FuelTypeKey` int(11) NOT NULL,
  `SortName` varchar(40) DEFAULT NULL,
  PRIMARY KEY (`FuelTypeKey`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `Infringement`;
CREATE TABLE `Infringement` (
  `idInfringement` int(11) NOT NULL AUTO_INCREMENT,
  `User_idUserCreator` int(11) NOT NULL,
  `User_idUserModifier` int(11) NOT NULL,
  `Drive_idDrive` int(11) DEFAULT NULL,
  `infringementType` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `status` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `files` text,
  `data` mediumtext,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `editTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `InfringementBatch_idInfringementBatch` int(11) DEFAULT NULL,
  PRIMARY KEY (`idInfringement`),
  KEY `User_idUserModifier` (`User_idUserModifier`),
  KEY `fk_Infringement_User_Creator_idx` (`User_idUserCreator`),
  KEY `fk_Infringement_Drive_idx` (`Drive_idDrive`),
  KEY `idx_InfringementBatch_idInfringementBatch` (`InfringementBatch_idInfringementBatch`),
  CONSTRAINT `Infringement_ibfk_1` FOREIGN KEY (`User_idUserCreator`) REFERENCES `User` (`idUser`),
  CONSTRAINT `Infringement_ibfk_2` FOREIGN KEY (`User_idUserModifier`) REFERENCES `User` (`idUser`),
  CONSTRAINT `Infringement_ibfk_3` FOREIGN KEY (`Drive_idDrive`) REFERENCES `Drive` (`idDrive`),
  CONSTRAINT `fk_InfringementBatch_idInfringementBatch` FOREIGN KEY (`InfringementBatch_idInfringementBatch`) REFERENCES `InfringementBatch` (`idInfringementBatch`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `InfringementBatch`;
CREATE TABLE `InfringementBatch` (
  `idInfringementBatch` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) DEFAULT NULL,
  `User_idUser` int(11) NOT NULL,
  `creationMode` tinyint(4) NOT NULL,
  `files` text,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updateTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `inputType` tinyint(3) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`idInfringementBatch`),
  KEY `idx_User_idUser` (`User_idUser`),
  CONSTRAINT `FK_InfringementBatch_ExternalApiDriveSync` FOREIGN KEY (`User_idUser`) REFERENCES `User` (`idUser`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `IntegrationStatus`;
CREATE TABLE `IntegrationStatus` (
  `idIntegrationStatus` int(11) NOT NULL AUTO_INCREMENT,
  `integrationKey` varchar(200) NOT NULL,
  `successStreak` int(11) NOT NULL DEFAULT '0',
  `lastSuccessDate` datetime DEFAULT NULL,
  `failureStreak` int(11) NOT NULL DEFAULT '0',
  `lastFailureDate` datetime DEFAULT NULL,
  `lastFailureMessage` text,
  PRIMARY KEY (`idIntegrationStatus`),
  UNIQUE KEY `integrationKey` (`integrationKey`),
  UNIQUE KEY `IX_integrationKey` (`integrationKey`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `LeadSource`;
CREATE TABLE `LeadSource` (
  `idLeadSource` int(11) NOT NULL AUTO_INCREMENT,
  `Dealership_idDealership` int(11) NOT NULL,
  `leadSourceId` int(11) NOT NULL,
  PRIMARY KEY (`idLeadSource`),
  UNIQUE KEY `dealership_leadSource` (`Dealership_idDealership`,`leadSourceId`),
  KEY `fk_LeadSource_Dealership_idx` (`Dealership_idDealership`),
  CONSTRAINT `LeadSource_ibfk_1` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `LeadSourceDetail`;
CREATE TABLE `LeadSourceDetail` (
  `idLeadSourceDetail` int(11) NOT NULL AUTO_INCREMENT,
  `leadSourceId` int(11) NOT NULL,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `description` varchar(45) NOT NULL,
  PRIMARY KEY (`idLeadSourceDetail`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP VIEW IF EXISTS `LivemarketUsageStats`;
CREATE TABLE `LivemarketUsageStats` (`name` varchar(45), `n` bigint(21));


DROP TABLE IF EXISTS `Location`;
CREATE TABLE `Location` (
  `idLocation` int(11) NOT NULL AUTO_INCREMENT,
  `Drive_idDrive` int(11) NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `lat` float(10,6) NOT NULL,
  `lng` float(10,6) NOT NULL,
  PRIMARY KEY (`idLocation`),
  KEY `fk_Location_TestDrive1_idx` (`Drive_idDrive`),
  CONSTRAINT `fk_Location_Drive1` FOREIGN KEY (`Drive_idDrive`) REFERENCES `Drive` (`idDrive`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `Log`;
CREATE TABLE `Log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level` varchar(128) DEFAULT NULL,
  `category` varchar(128) DEFAULT NULL,
  `logtime` int(11) DEFAULT NULL,
  `message` text,
  PRIMARY KEY (`id`),
  KEY `level` (`level`),
  KEY `category` (`category`),
  KEY `logtime` (`logtime`),
  KEY `message` (`message`(767))
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `LoginSession`;
CREATE TABLE `LoginSession` (
  `idLoginSession` int(11) NOT NULL AUTO_INCREMENT,
  `User_idUser` int(11) NOT NULL,
  `token` varchar(72) DEFAULT NULL,
  `impersonateSession` int(11) DEFAULT NULL,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `createIp` varchar(100) DEFAULT NULL,
  `device` varchar(200) DEFAULT NULL,
  `appVersion` varchar(45) DEFAULT NULL,
  `lastUse` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `lastIp` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`idLoginSession`),
  KEY `fk_LoginSession_User_idx` (`User_idUser`),
  KEY `token_idx` (`token`),
  CONSTRAINT `LoginSession_ibfk_1` FOREIGN KEY (`User_idUser`) REFERENCES `User` (`idUser`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `PhpSession`;
CREATE TABLE `PhpSession` (
  `id` char(32) NOT NULL,
  `expire` int(11) DEFAULT NULL,
  `data` longblob,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `RedBookCipherKeys`;
CREATE TABLE `RedBookCipherKeys` (
  `idRedbookCipherKey` int(11) NOT NULL AUTO_INCREMENT,
  `startTimestamp` timestamp NOT NULL,
  `endTimestamp` timestamp NOT NULL,
  `cipherKey` varchar(100) NOT NULL,
  PRIMARY KEY (`idRedbookCipherKey`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `RedBookFamilies`;
CREATE TABLE `RedBookFamilies` (
  `RedBookFamilyId` int(11) NOT NULL AUTO_INCREMENT,
  `Description` longtext,
  `FamilyCode` longtext,
  `LatestYear` int(11) DEFAULT NULL,
  `MakeCode` longtext,
  `StartYear` int(11) DEFAULT NULL,
  `VehicleTypeCode` longtext,
  PRIMARY KEY (`RedBookFamilyId`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `RedBookMakes`;
CREATE TABLE `RedBookMakes` (
  `RedBookMakeId` int(11) NOT NULL AUTO_INCREMENT,
  `Description` longtext,
  `LatestYear` int(11) DEFAULT NULL,
  `MakeCode` longtext,
  `StartYear` int(11) DEFAULT NULL,
  PRIMARY KEY (`RedBookMakeId`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `RedBookVehicles`;
CREATE TABLE `RedBookVehicles` (
  `RedBookVehicleId` int(11) NOT NULL AUTO_INCREMENT,
  `BadgeDescription` longtext,
  `BadgeSecondaryDescription` longtext,
  `BodyConfigDescription` longtext,
  `BodyStyleDescription` longtext,
  `CurrentRelease` longtext,
  `Cylinders` longtext,
  `Description` longtext,
  `DoorNum` longtext,
  `DriveDescription` longtext,
  `EngineDescription` longtext,
  `EngineSize` longtext,
  `ExtraIdentification` longtext,
  `FamilyCode` longtext,
  `FuelTypeDescription` longtext,
  `GearLocationDescription` longtext,
  `GearNum` longtext,
  `GearTypeDescription` longtext,
  `ImportFlag` longtext,
  `InductionDescription` longtext,
  `LimitedEdition` longtext,
  `MakeCode` longtext,
  `MonthGroup` int(11) DEFAULT NULL,
  `OptionCategory` longtext,
  `Roofline` longtext,
  `SeatCapacity` longtext,
  `SequenceNum` int(11) DEFAULT NULL,
  `SeriesModelYear` longtext,
  `SeriesPublic` longtext,
  `VehicleKey` longtext,
  `VehicleTypeCode` longtext,
  `WheelBaseConfig` longtext,
  `YearGroup` int(11) DEFAULT NULL,
  PRIMARY KEY (`RedBookVehicleId`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `RedBookYears`;
CREATE TABLE `RedBookYears` (
  `RedBookYearId` int(11) NOT NULL AUTO_INCREMENT,
  `Description` longtext,
  `FamilyCode` longtext,
  `MakeCode` longtext,
  `MonthGroup` longtext,
  `VehicleTypeCode` longtext,
  `YearGroup` longtext,
  PRIMARY KEY (`RedBookYearId`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `RemoteSignature`;
CREATE TABLE `RemoteSignature` (
  `idRemoteSignature` int(11) NOT NULL AUTO_INCREMENT,
  `User_idUser` int(11) NOT NULL,
  `Dealership_idDealership` int(11) NOT NULL,
  `driveType` tinyint(3) unsigned NOT NULL,
  `mobileNumber` varchar(20) DEFAULT NULL,
  `code` varchar(8) NOT NULL,
  `imageData` longblob,
  `imageType` varchar(10) DEFAULT NULL,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `signTimestamp` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`idRemoteSignature`),
  UNIQUE KEY `RemoteSignature_unique_code_idx` (`code`),
  KEY `fk_RemoteSignature_User_idx` (`User_idUser`),
  KEY `fk_RemoteSignature_Dealership_idx` (`Dealership_idDealership`),
  CONSTRAINT `RemoteSignature_ibfk_1` FOREIGN KEY (`User_idUser`) REFERENCES `User` (`idUser`),
  CONSTRAINT `RemoteSignature_ibfk_2` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `Report`;
CREATE TABLE `Report` (
  `idReport` int(11) NOT NULL AUTO_INCREMENT,
  `User_idUser` int(11) NOT NULL,
  `reportType` int(11) NOT NULL,
  `request` text,
  PRIMARY KEY (`idReport`),
  KEY `fk_Report_User_idx` (`User_idUser`),
  CONSTRAINT `Report_ibfk_1` FOREIGN KEY (`User_idUser`) REFERENCES `User` (`idUser`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `ReportSchedule`;
CREATE TABLE `ReportSchedule` (
  `idReportSchedule` int(11) NOT NULL AUTO_INCREMENT,
  `User_idUser` int(11) NOT NULL,
  `Report_idReport` int(11) NOT NULL,
  `recurrenceType` varchar(10) DEFAULT NULL,
  `dayTime` varchar(5) DEFAULT NULL,
  `weekDay` varchar(10) DEFAULT NULL,
  `monthDay` varchar(10) DEFAULT NULL,
  `recipients` text,
  `lastSuccessSendTimestamp` timestamp NULL DEFAULT NULL,
  `dayTimeUtc` varchar(5) DEFAULT NULL,
  `weekDayUtc` varchar(10) DEFAULT NULL,
  `monthDayUtc` varchar(10) DEFAULT NULL,
  `dayUtcOffset` varchar(5) DEFAULT NULL,
  PRIMARY KEY (`idReportSchedule`),
  KEY `Report_idReport` (`Report_idReport`),
  KEY `fk_Report_User_idx` (`User_idUser`),
  KEY `fk_Report_dayTime_idx` (`dayTime`),
  KEY `fk_Report_RecurrenceType_idx` (`recurrenceType`),
  KEY `fk_Report_WeekDay_idx` (`weekDay`),
  KEY `fk_Report_MonthDay_idx` (`monthDay`),
  KEY `fk_Report_Timestamp_idx` (`lastSuccessSendTimestamp`),
  CONSTRAINT `ReportSchedule_ibfk_1` FOREIGN KEY (`User_idUser`) REFERENCES `User` (`idUser`),
  CONSTRAINT `ReportSchedule_ibfk_2` FOREIGN KEY (`Report_idReport`) REFERENCES `Report` (`idReport`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `ResetPasswordSecret`;
CREATE TABLE `ResetPasswordSecret` (
  `idResetPasswordSecret` int(11) NOT NULL AUTO_INCREMENT,
  `User_idUser` int(11) NOT NULL,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `resetSecret` varchar(40) NOT NULL,
  `expiryTimestamp` timestamp NOT NULL DEFAULT '2000-01-01 12:00:00',
  `usedTimestamp` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`idResetPasswordSecret`),
  KEY `fk_ResetPasswordSecret_User1_idx` (`User_idUser`),
  CONSTRAINT `fk_ResetPasswordSecret_User1` FOREIGN KEY (`User_idUser`) REFERENCES `User` (`idUser`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `SecurityAlert`;
CREATE TABLE `SecurityAlert` (
  `idSecurityAlert` int(11) NOT NULL AUTO_INCREMENT,
  `SecurityAlertTrigger_idSecurityAlertTrigger` int(11) NOT NULL,
  `User_idUser` int(11) NOT NULL,
  `count` int(11) NOT NULL DEFAULT '1',
  `status` tinyint(1) NOT NULL DEFAULT '0',
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `lastOccurrenceTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`idSecurityAlert`),
  KEY `FK_SecurityAlert_User` (`User_idUser`),
  KEY `idx_idSecurityAlertTrigger_idUser_status` (`SecurityAlertTrigger_idSecurityAlertTrigger`,`User_idUser`,`status`),
  CONSTRAINT `FK_SecurityAlert_SecurityAlertTrigger` FOREIGN KEY (`SecurityAlertTrigger_idSecurityAlertTrigger`) REFERENCES `SecurityAlertTrigger` (`idSecurityAlertTrigger`) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT `FK_SecurityAlert_User` FOREIGN KEY (`User_idUser`) REFERENCES `User` (`idUser`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `SecurityAlertTrigger`;
CREATE TABLE `SecurityAlertTrigger` (
  `idSecurityAlertTrigger` int(11) NOT NULL AUTO_INCREMENT,
  `securityLogOperation` int(11) NOT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `condition` text NOT NULL,
  `name` varchar(200) NOT NULL,
  `emailAndPhoneNumbers` varchar(1000) DEFAULT NULL,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `enableTimestamp` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`idSecurityAlertTrigger`),
  KEY `idx_securityLogOperation_status` (`securityLogOperation`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `SecurityLog`;
CREATE TABLE `SecurityLog` (
  `idSecurityLog` int(11) NOT NULL AUTO_INCREMENT,
  `User_idUser` int(11) NOT NULL,
  `User_idUserImpersonator` int(11) DEFAULT NULL,
  `Dealership_idDealership` int(11) DEFAULT NULL,
  `logLevel` int(11) NOT NULL DEFAULT '0',
  `operation` int(11) NOT NULL,
  `ip` varchar(45) DEFAULT NULL,
  `sessionId` varchar(128) DEFAULT NULL,
  `userAgent` text,
  `data` mediumtext,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `country` varchar(100) DEFAULT NULL,
  `isProcessed` bit(1) NOT NULL DEFAULT b'1',
  PRIMARY KEY (`idSecurityLog`),
  KEY `fk_SecurityLog_User_idx` (`User_idUser`),
  KEY `fk_SecurityLog_UserImpersonator_idx` (`User_idUserImpersonator`),
  KEY `fk_SecurityLog_Dealership_idx` (`Dealership_idDealership`),
  KEY `idx_SecurityLog_isProcessed` (`isProcessed`),
  CONSTRAINT `SecurityLog_ibfk_1` FOREIGN KEY (`User_idUser`) REFERENCES `User` (`idUser`),
  CONSTRAINT `SecurityLog_ibfk_2` FOREIGN KEY (`User_idUserImpersonator`) REFERENCES `User` (`idUser`),
  CONSTRAINT `SecurityLog_ibfk_3` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP VIEW IF EXISTS `SimpleDriveList`;
CREATE TABLE `SimpleDriveList` (`idDrive` int(11), `driveType` varchar(19), `startTimestamp` timestamp, `endTimestamp` timestamp, `dealershipName` varchar(45), `driverFirstName` varchar(45), `driverLastName` varchar(45), `driverTelephone` varchar(20), `driverEmail` varchar(254), `carRego` varchar(50), `carMake` varchar(255), `carModel` varchar(255), `carYear` smallint(5) unsigned);


DROP TABLE IF EXISTS `SiteConfig`;
CREATE TABLE `SiteConfig` (
  `idSiteConfig` int(11) NOT NULL AUTO_INCREMENT,
  `exportsEnabled` tinyint(4) DEFAULT '1',
  `licenseImageEnabled` tinyint(4) DEFAULT '1',
  `cookieNoticeEnabled` tinyint(4) DEFAULT '0',
  `mfaEnabled` tinyint(4) DEFAULT '0',
  `financeIntroEnabled` tinyint(4) DEFAULT '0',
  `selfLoanNotificationEnabled` tinyint(4) DEFAULT '0',
  `videoAcademyEnabled` tinyint(4) DEFAULT '0',
  `forceMfaForPortalAccessEnabled` tinyint(4) DEFAULT '0',
  `observedServerIp` varchar(45) DEFAULT NULL,
  PRIMARY KEY (`idSiteConfig`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `SoldCar`;
CREATE TABLE `SoldCar` (
  `idSoldCar` int(11) NOT NULL AUTO_INCREMENT,
  `Drive_idDrive` int(11) NOT NULL,
  `soldCarType` enum('Sold (New Order)','Sold (Demonstrator)','Sold (Used)') NOT NULL,
  `soldDate` varchar(40) NOT NULL,
  PRIMARY KEY (`idSoldCar`),
  UNIQUE KEY `Drive_idDrive` (`Drive_idDrive`),
  KEY `fk_SoldCar_Drive_idx` (`Drive_idDrive`),
  CONSTRAINT `SoldCar_ibfk_1` FOREIGN KEY (`Drive_idDrive`) REFERENCES `Drive` (`idDrive`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `StockReport`;
CREATE TABLE `StockReport` (
  `Id` int(11) NOT NULL AUTO_INCREMENT,
  `Title` varchar(255) DEFAULT NULL,
  `DealershipName` varchar(255) DEFAULT NULL,
  `DealershipId` int(11) NOT NULL,
  `XmlContent` mediumtext,
  `JsonContent` mediumtext,
  `ReportDateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `RowsCount` int(11) NOT NULL DEFAULT '0',
  `DealershipCode` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`Id`),
  KEY `idx_StockReport_Id_DealershipId` (`Id`,`DealershipId`),
  KEY `StockReport_DealershipCode_Id` (`DealershipCode`,`Id`),
  KEY `ReportDateTime` (`ReportDateTime`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `SuburbPostcode`;
CREATE TABLE `SuburbPostcode` (
  `idSuburbPostcode` int(11) NOT NULL AUTO_INCREMENT,
  `postcode` varchar(10) NOT NULL,
  `suburb` varchar(100) NOT NULL,
  `Country_idCountry` int(11) NOT NULL,
  PRIMARY KEY (`idSuburbPostcode`),
  KEY `postcode_county_idx` (`postcode`,`Country_idCountry`),
  KEY `Country_idCountry` (`Country_idCountry`),
  CONSTRAINT `SuburbPostcode_ibfk_1` FOREIGN KEY (`Country_idCountry`) REFERENCES `Country` (`idCountry`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `tbl_migration`;
CREATE TABLE `tbl_migration` (
  `version` varchar(255) NOT NULL,
  `apply_time` int(11) DEFAULT NULL,
  PRIMARY KEY (`version`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `Temp_DealershipStatsPeriods`;
CREATE TABLE `Temp_DealershipStatsPeriods` (
  `dateStart` date NOT NULL,
  `dateEnd` date NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `TermsAndConditionsHistory`;
CREATE TABLE `TermsAndConditionsHistory` (
  `idTermsAndConditionsHistory` int(11) NOT NULL AUTO_INCREMENT,
  `expireAt` timestamp NOT NULL,
  `testDriveTermsAndCondition` text,
  `loanCarDriveTermsAndCondition` text,
  `Dealership_idDealership` int(11) NOT NULL,
  PRIMARY KEY (`idTermsAndConditionsHistory`),
  KEY `Dealership_idDealership` (`Dealership_idDealership`),
  CONSTRAINT `TermsAndConditionsHistory_ibfk_1` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `ThirdPartyApiCallLogs`;
CREATE TABLE `ThirdPartyApiCallLogs` (
  `ThridPartyApiCallLogId` int(11) NOT NULL AUTO_INCREMENT,
  `At` datetime NOT NULL,
  `DealershipId` int(11) NOT NULL,
  `LogInfo` text,
  `RequestInfo` text,
  `ThirdPartyApiCategory` int(11) NOT NULL,
  `UserId` int(11) NOT NULL,
  `WasSuccessful` int(1) NOT NULL,
  PRIMARY KEY (`ThridPartyApiCallLogId`),
  KEY `IX_ThirdPartyApiCallLogs_DealershipId` (`DealershipId`),
  KEY `IX_ThirdPartyApiCallLogs_UserId` (`UserId`),
  CONSTRAINT `FK_ThirdPartyApiCallLogs_Dealership_DealershipId` FOREIGN KEY (`DealershipId`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE,
  CONSTRAINT `FK_ThirdPartyApiCallLogs_User_UserId` FOREIGN KEY (`UserId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `TripPackPlan`;
CREATE TABLE `TripPackPlan` (
  `idTripPackPlan` int(11) NOT NULL AUTO_INCREMENT,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `planStatus` tinyint(3) unsigned NOT NULL,
  `numTrips` int(10) unsigned NOT NULL,
  `costExGst` float NOT NULL,
  `costGst` float NOT NULL,
  `name` varchar(255) NOT NULL,
  `description1` text,
  `orderIndex` tinyint(3) unsigned DEFAULT NULL,
  `description2` text,
  PRIMARY KEY (`idTripPackPlan`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `TripPackPurchase`;
CREATE TABLE `TripPackPurchase` (
  `idTripPackPurchase` int(11) NOT NULL AUTO_INCREMENT,
  `purchaseType` tinyint(3) unsigned NOT NULL,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `numTripCredits` int(10) unsigned DEFAULT NULL,
  `Dealership_idDealership` int(11) NOT NULL,
  `idExternalTransaction` varchar(45) DEFAULT NULL,
  `amountExGst` float NOT NULL,
  `amountGst` float NOT NULL,
  `transactionMetaData` text,
  `User_idUserPurchaser` int(11) NOT NULL,
  PRIMARY KEY (`idTripPackPurchase`),
  KEY `fk_TripPackPurchase_Dealership1_idx` (`Dealership_idDealership`),
  KEY `fk_TripPackPurchase_User1_idx` (`User_idUserPurchaser`),
  CONSTRAINT `fk_TripPackPurchase_Dealership1` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE ON UPDATE NO ACTION,
  CONSTRAINT `fk_TripPackPurchase_User1` FOREIGN KEY (`User_idUserPurchaser`) REFERENCES `User` (`idUser`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `UnverifiedSignUp`;
CREATE TABLE `UnverifiedSignUp` (
  `UnverifiedSignUpId` int(11) NOT NULL AUTO_INCREMENT,
  `Data` text NOT NULL,
  `createTimestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`UnverifiedSignUpId`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `User`;
CREATE TABLE `User` (
  `idUser` int(11) NOT NULL AUTO_INCREMENT,
  `userType` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `userStatus` tinyint(3) unsigned NOT NULL DEFAULT '0',
  `firstName` varchar(45) NOT NULL,
  `lastName` varchar(45) NOT NULL,
  `email` varchar(254) NOT NULL,
  `passwordHash` varchar(40) NOT NULL,
  `createTimestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `lastLoginTimestamp` timestamp NULL DEFAULT NULL,
  `telephone` varchar(20) DEFAULT NULL,
  `jobTitle` varchar(45) DEFAULT NULL COMMENT '	',
  `photoFilename` varchar(45) DEFAULT NULL,
  `lockoutEndTimestamp` timestamp NULL DEFAULT NULL,
  `numConsecutiveBadPassword` smallint(5) unsigned DEFAULT NULL,
  `driveReviewEmailMessage` text,
  `emailVerifySecret` varchar(40) DEFAULT NULL,
  `ageOutNotifyState` tinyint(3) unsigned DEFAULT '0',
  `profileImageType` varchar(10) DEFAULT NULL,
  `profileImageData` longblob,
  `testDriveTarget` int(11) NOT NULL DEFAULT '0',
  `salesTarget` int(11) NOT NULL DEFAULT '0',
  `language` varchar(10) DEFAULT NULL,
  `latestAppVersion` varchar(45) DEFAULT NULL,
  `allowImpersonation` tinyint(1) DEFAULT '0',
  `mfaMode` tinyint(4) DEFAULT '0',
  `mfaToken` varchar(255) DEFAULT NULL,
  `enableSelfLoanNotification` tinyint(1) DEFAULT '0',
  `licenseFrontS3Id` varchar(255) DEFAULT NULL,
  `licenseBackS3Id` varchar(255) DEFAULT NULL,
  `licenseImageUpdateTimestamp` timestamp NULL DEFAULT NULL,
  `licenseExpiryTimestamp` timestamp NULL DEFAULT NULL,
  `profileImageUpdateTimestamp` timestamp NULL DEFAULT NULL,
  `secondEmail` varchar(254) NOT NULL,
  `lastPasswordUpdate` timestamp NULL DEFAULT NULL,
  `forcePasswordReset` bit(1) NOT NULL DEFAULT b'0',
  `permissions` text,
  `otpSecretKey` varbinary(250) DEFAULT NULL,
  PRIMARY KEY (`idUser`),
  UNIQUE KEY `email_UNIQUE` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `Video`;
CREATE TABLE `Video` (
  `idVideo` int(11) NOT NULL AUTO_INCREMENT,
  `title` text NOT NULL,
  `category` text,
  `description` text,
  `videoURL` text NOT NULL,
  `imageURL` text NOT NULL,
  `order` int(11) DEFAULT NULL,
  `pageURL` text,
  `system` int(11) DEFAULT NULL,
  PRIMARY KEY (`idVideo`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `VinLookupLog`;
CREATE TABLE `VinLookupLog` (
  `VinLookupLogId` int(11) NOT NULL AUTO_INCREMENT,
  `At` datetime(6) NOT NULL,
  `DealershipId` int(11) NOT NULL,
  `UserId` int(11) NOT NULL,
  `Vin` varchar(17) DEFAULT NULL,
  `Year` smallint(6) NOT NULL DEFAULT '0',
  PRIMARY KEY (`VinLookupLogId`),
  KEY `IX_VinLookupLog_UserId` (`UserId`),
  KEY `IX_VinLookupLog_DealershipId` (`DealershipId`),
  CONSTRAINT `FK_VinLookupLog_Dealership_DealershipId` FOREIGN KEY (`DealershipId`) REFERENCES `Dealership` (`idDealership`) ON DELETE CASCADE,
  CONSTRAINT `FK_VinLookupLog_User_UserId` FOREIGN KEY (`UserId`) REFERENCES `User` (`idUser`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `XtimeEmail`;
CREATE TABLE `XtimeEmail` (
  `idXtimeEmail` int(11) NOT NULL AUTO_INCREMENT,
  `messageId` varchar(200) DEFAULT NULL,
  `timestamp` int(11) NOT NULL,
  `recipient` varchar(1000) NOT NULL,
  `sender` varchar(1000) DEFAULT NULL,
  `subject` varchar(1000) DEFAULT NULL,
  `body` text,
  `useragent` varchar(1000) DEFAULT NULL,
  `ip` varchar(50) DEFAULT NULL,
  `processedData` text,
  `confirmationCode` varchar(100) DEFAULT NULL,
  `isCancellation` tinyint(4) DEFAULT NULL,
  `Dealership_idDealership` int(11) DEFAULT NULL,
  `Drive_idDrive` int(11) DEFAULT NULL,
  PRIMARY KEY (`idXtimeEmail`),
  KEY `FK_XtimeEmail_Dealership_idDealership` (`Dealership_idDealership`),
  KEY `FK_XtimeEmail_Drive_idDrive` (`Drive_idDrive`),
  KEY `FK_XtimeEmail_messageId` (`messageId`),
  CONSTRAINT `FK_XtimeEmail_Dealership_idDealership` FOREIGN KEY (`Dealership_idDealership`) REFERENCES `Dealership` (`idDealership`),
  CONSTRAINT `FK_XtimeEmail_Drive_idDrive` FOREIGN KEY (`Drive_idDrive`) REFERENCES `Drive` (`idDrive`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


DROP TABLE IF EXISTS `CarsalesBookingReport`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `CarsalesBookingReport` AS select `ExternalApiDriveSync`.`externalRecordId` AS `externalRecordId`,(case `ExternalApiDriveSync`.`status` when 1 then 'confirmed (auto)' when 2 then 'confirmed (manual)' when 3 then 'pending (create)' when 4 then 'pending (update)' when 5 then 'pending (cancel)' when 6 then 'rejected' else 'n/a' end) AS `requestSatus`,cast(json_unquote(json_extract(`ExternalApiCallLog`.`requestContent`,'$.startDateTimeUtc')) as datetime) AS `requestDateTimeUtc`,`Drive`.`startTimestamp` AS `confirmedDateTimeUtc`,(case when (isnull(`Drive`.`startTimestamp`) or isnull(cast(json_unquote(json_extract(`ExternalApiCallLog`.`requestContent`,'$.startDateTimeUtc')) as datetime)) or (`Drive`.`startTimestamp` = cast(json_unquote(json_extract(`ExternalApiCallLog`.`requestContent`,'$.startDateTimeUtc')) as datetime))) then 0 else 1 end) AS `isBookingTimeChaged`,(case `Drive`.`driveStatus` when 0 then 'enquiry' when 1 then 'in-progress' when 2 then 'completed' when 3 then 'cancelled' when 4 then 'booked' else 'n/a' end) AS `driveStatus`,`ExternalApiDriveSync`.`createTimestamp` AS `createDateTimeUtc`,`Drive`.`createTimestamp` AS `confirmDateTimeUtc`,timestampdiff(MINUTE,`ExternalApiDriveSync`.`createTimestamp`,`Drive`.`createTimestamp`) AS `responseTimeInMinutes` from ((`ExternalApiDriveSync` left join `Drive` on((`ExternalApiDriveSync`.`Drive_idDrive` = `Drive`.`idDrive`))) left join `ExternalApiCallLog` on((`ExternalApiCallLog`.`idExternalApiCallLog` = `ExternalApiDriveSync`.`ExternalApiCallLog_effectiveExternalApiCallLogId`))) where (`ExternalApiDriveSync`.`Dealership_idDealership` <> 2983);

DROP TABLE IF EXISTS `DriveCommentSummary`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `DriveCommentSummary` AS select `DriveComment`.`Drive_idDrive` AS `Drive_idDrive`,group_concat(`DriveComment`.`comments` separator '; ') AS `comments` from `DriveComment` group by `DriveComment`.`Drive_idDrive`;

DROP TABLE IF EXISTS `DriveReport`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `DriveReport` AS select `Drive`.`idDrive` AS `idDriveReport`,`Drive`.`accompaniedDrive` AS `Drive_accompaniedDrive`,`Drive`.`driverReviewSalesPersonStars` AS `Drive_driverReviewSalesPersonStars`,`Drive`.`driveType` AS `Drive_driveType`,`Drive`.`driveStatus` AS `Drive_driveStatus`,`Drive`.`endTimestamp` AS `Drive_endTimestamp`,`Drive`.`idDrive` AS `Drive_idDrive`,`Drive`.`insuranceWaiverChecked` AS `Drive_insuranceWaiverChecked`,`Drive`.`startTimestamp` AS `Drive_startTimestamp`,`Drive`.`roNumber` AS `Drive_roNumber`,(`Drive`.`endOdometerKm` - `Drive`.`startOdometerKm`) AS `Drive_distance`,`Drive`.`startFuelGaugeLevel` AS `Drive_startFuelGaugeLevel`,`Drive`.`endFuelGaugeLevel` AS `Drive_endFuelGaugeLevel`,`Drive`.`averageSpeed` AS `Drive_averageSpeed`,`Drive`.`tradePlateReg` AS `Drive_tradePlateReg`,`Drive`.`startOdometerKm` AS `Drive_startOdometerKm`,`Drive`.`endOdometerKm` AS `Drive_endOdometerKm`,`Drive`.`routeDistance` AS `Drive_routeDistance`,`Drive`.`driverReviewCarStars` AS `Drive_driverReviewCarStars`,`Drive`.`DamageReport_idDamageReportBefore` AS `Drive_damageReport_idDamageReportBefore`,`Drive`.`DamageReport_idDamageReportAfter` AS `Drive_damageReport_idDamageReportAfter`,`Drive`.`Car_idCar` AS `Drive_car_idCar`,`Drive`.`financeIntroAdvisedDate` AS `Drive_financeIntroAdvisedDate`,`Drive`.`financeIntroAdvisedBy` AS `Drive_financeIntroAdvisedBy`,`Drive`.`isFinanceIntroAdvised` AS `Drive_isFinanceIntroAdvised`,ifnull(`Drive`.`regoLog`,'') AS `Drive_regoLog`,`Car`.`make` AS `Car_make`,`Car`.`model` AS `Car_model`,`Car`.`year` AS `Car_year`,`Car`.`stockNumber` AS `Car_stockNumber`,`Car`.`vin` AS `Car_vin`,ifnull(if(((`Drive`.`regoLog` = `Car`.`rego`) or (ifnull(`Car`.`rego`,'') = '')),`Drive`.`regoLog`,concat(`Drive`.`regoLog`,' [',`Car`.`rego`,']')),'') AS `Car_rego`,`Car`.`color` AS `Car_color`,if((coalesce(`Drive`.`tradePlateReg`,'') = ''),0,1) AS `Car_isTradePlateUsed`,`Car`.`lastKnownLocation` AS `Car_lastKnownLocation`,`Dealership`.`idDealership` AS `Dealership_idDealership`,`Dealership`.`name` AS `Dealership_name`,`Dealership`.`suburb` AS `Dealership_suburb`,`Dealership`.`postcode` AS `Dealership_postcode`,`Dealership`.`state` AS `Dealership_state`,`Dealership`.`country` AS `Dealership_country`,`Driver`.`firstName` AS `Driver_firstName`,`Driver`.`lastName` AS `Driver_lastName`,`Driver`.`age` AS `Driver_age`,`Driver`.`gender` AS `Driver_gender`,`Driver`.`idDriver` AS `Driver_idDriver`,`Driver`.`leadSourceId` AS `Driver_leadSourceId`,`Driver`.`consentFlags` AS `Driver_consentFlags`,`Driver`.`postcode` AS `Driver_postcode`,`Driver`.`email` AS `Driver_email`,`Driver`.`telephone` AS `Driver_telephone`,`Driver`.`licenceNumber` AS `Driver_licenceNumber`,`Driver`.`suburb` AS `Driver_suburb`,`Driver`.`unit` AS `Driver_unit`,`Driver`.`streetNumber` AS `Driver_streetNumber`,`Driver`.`streetName` AS `Driver_streetName`,`Driver`.`city` AS `Driver_city`,`Driver`.`state` AS `Driver_state`,`Driver`.`country` AS `Driver_country`,`LeadSourceDetail`.`description` AS `LeadSourceDetail_description`,`LeadSourceDetail`.`leadSourceId` AS `LeadSourceDetail_leadSourceId`,`User`.`idUser` AS `User_idUser`,`User`.`firstName` AS `User_firstName`,`User`.`lastName` AS `User_lastName`,`User`.`telephone` AS `User_telephone`,`User`.`email` AS `User_email`,ifnull(`SoldCar`.`soldCarType`,'') AS `SoldCar_soldCarType`,`SoldCar`.`Drive_idDrive` AS `SoldCar_Drive_idDrive`,`SoldCar`.`soldDate` AS `SoldCar_soldDate` from ((((((`Drive` left join `Car` on((`Car`.`idCar` = `Drive`.`Car_idCar`))) left join `Dealership` on((`Dealership`.`idDealership` = `Drive`.`Drive_idDealership`))) left join `Driver` on((`Driver`.`idDriver` = `Drive`.`Driver_idDriver`))) left join `LeadSourceDetail` on((`Driver`.`leadSourceId` = `LeadSourceDetail`.`leadSourceId`))) left join `User` on((`User`.`idUser` = `Drive`.`User_idUser`))) left join `SoldCar` on((`SoldCar`.`Drive_idDrive` = `Drive`.`idDrive`)));

DROP TABLE IF EXISTS `LivemarketUsageStats`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `LivemarketUsageStats` AS select `Dealership`.`name` AS `name`,count(0) AS `n` from ((`AppraisalIntegrationStorage` join `Appraisal` on((`Appraisal`.`AppraisalId` = `AppraisalIntegrationStorage`.`AppraisalId`))) join `Dealership` on((`Dealership`.`idDealership` = `Appraisal`.`DealershipId`))) where ((`AppraisalIntegrationStorage`.`IntegrationSystem` = '9') and (`AppraisalIntegrationStorage`.`IntegrationType` = '3')) group by `Dealership`.`name`;

DROP TABLE IF EXISTS `SimpleDriveList`;
CREATE ALGORITHM=UNDEFINED SQL SECURITY DEFINER VIEW `SimpleDriveList` AS select `Drive`.`idDrive` AS `idDrive`,(case `Drive`.`driveType` when 1 then 'TestDrive' when 2 then 'Loan' when 3 then 'SelfLoan' when 4 then 'Enquiry (Stock)' when 5 then 'Enquiry (Non-Stock)' when 6 then 'TestDrive (Sold)' when 7 then 'Enquiry (Sold)' end) AS `driveType`,`Drive`.`startTimestamp` AS `startTimestamp`,`Drive`.`endTimestamp` AS `endTimestamp`,`Dealership`.`name` AS `dealershipName`,`Driver`.`firstName` AS `driverFirstName`,`Driver`.`lastName` AS `driverLastName`,`Driver`.`telephone` AS `driverTelephone`,`Driver`.`email` AS `driverEmail`,`Car`.`rego` AS `carRego`,`Car`.`make` AS `carMake`,`Car`.`model` AS `carModel`,`Car`.`year` AS `carYear` from (((`Drive` left join `Dealership` on((`Drive`.`Drive_idDealership` = `Dealership`.`idDealership`))) left join `Driver` on((`Drive`.`Driver_idDriver` = `Driver`.`idDriver`))) left join `Car` on((`Drive`.`Car_idCar` = `Car`.`idCar`)));

-- 2025-06-27 11:48:00
