//= require froala_editor.min
//= require plugins/code_view.min
//= require plugins/code_beautifier.min
//= require plugins/entities.min
//= require plugins/url.min
//= require plugins/special_characters.min
//= require plugins/paragraph_format.min
//= require plugins/quote.min
//= require plugins/align.min
//= require plugins/lists.min
//= require plugins/link.min
//= require plugins/table.min
//= require plugins/image.min
//= require plugins/draggable.min
//= require plugins/quick_insert.min
//= require plugins/video.min
//= require plugins/char_counter.min
//= require plugins/colors.min
//= require plugins/file.min
//= require plugins/font_size.min
//= require plugins/fullscreen.min
//= require plugins/inline_style.min
//= require plugins/save.min
//= require plugins/help.min
//= require plugins/print.min
//= require plugins/word_paste.min

$.FroalaEditor.DEFAULTS.key = "ZB3F4C2B10aD6C4B2G2G3E2D2C9C3D4tjgxF4dA10bE-13eD-11aB1vyzB3xo==";

// read more about froala image upload impl here: https://froala.com/wysiwyg-editor/docs/concepts/image/upload/
// and toolbar buttons here: https://froala.com/wysiwyg-editor/docs/options/#toolbarButtons

var froalaToolbarButtons = [
  "bold",
  "italic",
  "underline",
  "strikethrough",
  "subscript",
  "superscript",
  "|",
  "fontSize",
  "paragraphFormat",
  "align",
  "formatOL",
  "formatUL",
  "|",
  "insertLink",
  "insertImage",
  "insertVideo",
  "specialCharacters",
  "insertTable",
  "insertHR",
  "html",
  "clearFormatting",
  "fullscreen",
];
var froalaParagraphFormat = { N: "Normal", H2: "&lt;h2&gt;", H3: "&lt;h3&gt;", H4: "&lt;h4&gt;", H5: "&lt;h5&gt;" };

$.FroalaEditor.DEFAULTS.imageUploadURL = "/content/editor_upload_image";
$.FroalaEditor.DEFAULTS.videoUploadURL = "/content/editor_upload_image";
$.FroalaEditor.DEFAULTS.imageMaxSize = 1024 * 1024 * 5;
$.FroalaEditor.DEFAULTS.videoMaxSize = 1024 * 1024 * 10;
$.FroalaEditor.DEFAULTS.paragraphFormat = froalaParagraphFormat;
$.FroalaEditor.DEFAULTS.toolbarButtons = $.FroalaEditor.DEFAULTS.toolbarButtonsMD = $.FroalaEditor.DEFAULTS.toolbarButtonsSM = $.FroalaEditor.DEFAULTS.toolbarButtonsXS = froalaToolbarButtons;
$.FroalaEditor.DEFAULTS.heightMax = 300;
$.FroalaEditor.DEFAULTS.codeMirror = false;
$.FroalaEditor.DEFAULTS.iconsTemplate = "font_awesome_5";
// Link helper options
$.FroalaEditor.DEFAULTS.linkEditButtons = ["linkEdit", "linkRemove"];
$.FroalaEditor.DEFAULTS.linkInsertButtons = ["linkBack"];
$.FroalaEditor.DEFAULTS.linkList = [];
$.FroalaEditor.DEFAULTS.linkStyles = {};
