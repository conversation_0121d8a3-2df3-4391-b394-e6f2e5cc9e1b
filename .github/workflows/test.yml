name: Rails Tests

on:
  workflow_dispatch:
  pull_request:
    branches: [main, develop]
    types: [opened]
  push:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:9.3
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: dealer-drive-backend_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

      redis:
        image: redis:latest
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: "3.4.4"
          bundler-cache: true

      - name: Setup test database
        env:
          RAILS_ENV: test
          DATABASE_URL: mysql2://root:password@127.0.0.1:3306/dealer-drive-backend_test
        run: |
          bundle exec rails db:create
          bundle exec rails db:schema:load

      - name: Run tests
        env:
          RAILS_ENV: test
          DATABASE_URL: mysql2://root:password@127.0.0.1:3306/dealer-drive-backend_test
          REDIS_URL: redis://localhost:6379/0
          RAILS_TEST_KEY: test
        run: bundle exec rspec

      - name: Upload coverage report
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: coverage/
