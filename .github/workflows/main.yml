name: Rails PR Code Quality Checks

on:
  workflow_dispatch:
  pull_request:
    branches: [main, develop]
    types: [opened, synchronize, reopened]

jobs:
  code_quality:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          # Fetch full history for better blame information
          fetch-depth: 0
          # Use a token that can push changes back
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: "3.4.4" # Adjust to your Ruby version
          bundler-cache: true # Runs 'bundle install' and caches gems

      - name: Run brakeman with reviewdog
        uses: reviewdog/action-brakeman@v2.19.2
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}

      - name: Run rubocop with reviewdog
        uses: reviewdog/action-rubocop@v2.21.3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          reporter: github-pr-check

      # - name: Run rails_best_practices with reviewdog
      #   uses: blooper05/action-rails_best_practices@v2.2.0
      #   with:
      #     github_token: ${{ secrets.GITHUB_TOKEN }}
