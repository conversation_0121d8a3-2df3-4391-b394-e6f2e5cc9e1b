## Description

This PR does adds this functionality to `this thing`. Add a thorough explanation to give the code reviewers a good picture what these changes are supposed to achieve. This PR fixes `this issue`.

- And does `this` and `that` for these reasons…
- And also `this_thing` because…

## Review Instructions

1. Switch to this branch.
2. Run `db:migrate` when relevant.
3. Run the rake task `rails db:data:update:some_thing` **ONCE**
4. Click [here](http://localhost:3000/) fill this out...

## Post-Deploy Instructions

1. Run the rake task `rails db:data:update:some_thing` **ONCE** (include rake tasks if it should be executed after a deployment in staging and production)

## Checklist

Make sure the following tasks have been completed before you submit this pull request:

- [ ] Added new specs as required
- [ ] The full spec suite is passing
- [ ] Fixed any RuboCop issues raised in code you added *(Run `git diff --name-only main...$(git rev-parse --abbrev-ref HEAD) | xargs rubocop --force-exclusion --autocorrect` to auto fix some issues)* or `rubocop -A` to auto fix all issues in whole project
- [ ] Tested in Chrome
- [ ] Added seed data to `seeds.rb` as needed
- [ ] Ran `RAILS_ENV=test rails rswag:specs:swaggerize` to update `swagger.yaml`
- [ ] Tested cURL request in Postman and copy-pasted sample request from Postman into PR description
- [ ] Merged **main** into your feature branch, multiple times as needed
- [ ] Update the changelog
- [ ] Invite at least 2 reviewers and from Copilot if possible
- [ ] Put your name in Assignees on the PR
- [ ] Put label(s) on the PR 
- [ ] Put the PR in the right Project (Dealer Drive) and change status to `In Review`
- [ ] Tag the issue(s) which this PR fixes (if any) under `Development` section. Successfully merging this pull request may close these issues.

 
> Notes
>
> - You can remove sections that are not relevant. E.g.: `Post Deployment Instructions`.
> - You must complete the **checklist**.
> - You can strike-through checklist items that are not `~~relevant~~`.
