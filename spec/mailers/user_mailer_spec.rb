require "rails_helper"

RSpec.describe UserMailer, type: :mailer do
  describe '#send_otp' do
    let(:user) { create(:user, email: '<EMAIL>', first_name: 'TestUser') }
    let(:otp) { '123456' }
    let(:action) { :login }
    let(:mail) { described_class.send_otp(user, otp, action) }

    it 'renders the subject with translation' do
      expect(mail.subject).to eq(I18n.t("mail.login.subject", action: action))
    end

    it 'sends to the correct recipient' do
      expect(mail.to).to eq([ user.email ])
    end

    it 'renders the body with OTP and action' do
      expect(mail.body.encoded).to include(otp)
      expect(mail.body.encoded).to include(I18n.t("mail.login.name"))
      expect(mail.body.encoded).to include("Hello #{user.first_name}")
    end
  end
end
