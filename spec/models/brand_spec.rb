require "rails_helper"

RSpec.describe Brand, type: :model do
  describe "associations" do
    it { should have_one_attached(:logo) }
    it { should have_many(:dealerships).dependent(:restrict_with_error) }
  end

  describe "traits" do
    describe "with_logo" do
      it "attaches a logo to the brand" do
        brand = create(:brand, :with_logo)
        expect(brand.logo).to be_attached
      end
    end

    describe "without_logo" do
      it "is valid without a logo" do
        brand = create(:brand, :without_logo)
        expect(brand).to be_valid
      end
    end
  end

  describe "validations" do
    subject { create(:brand) }

    it { is_expected.to validate_presence_of(:name) }
    it { is_expected.to validate_uniqueness_of(:name).case_insensitive }

    describe "name uniqueness" do
      it "prevents exact duplicate names" do
        create(:brand, name: "hyundai")
        new_brand = build(:brand, name: "hyundai")
        expect(new_brand).not_to be_valid
        expect(new_brand.errors[:name]).to include("has already been taken")
      end

      it "enforces case-insensitive uniqueness" do
        create(:brand, name: "hyundai")
        new_brand = build(:brand, name: "Hyundai")
        expect(new_brand).not_to be_valid
        expect(new_brand.errors[:name]).to include("has already been taken")
      end
    end

    describe "uuid" do
      it "automatically sets a UUID as before_hook before doing validation on create" do
        brand = create(:brand)
        expect(brand).to be_valid
        expect(brand.uuid).to be_present
        expect(brand.uuid).to match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)
      end

      it "does not change UUID on update" do
        brand = create(:brand)
        original_uuid = brand.uuid
        brand.name = "New Name"
        expect(brand).to be_valid
        expect(brand.uuid).to eq(original_uuid)
      end

      it "enforces uniqueness of uuid" do
        existing_brand = create(:brand)
        new_brand = build(:brand, uuid: existing_brand.uuid)
        expect(new_brand).not_to be_valid
        expect(new_brand.errors[:uuid]).to include("has already been taken")
      end
    end

    describe "logo attachment" do
      it "is valid with a proper image file" do
        brand = build(:brand, :with_logo)
        expect(brand).to be_valid
      end

      it "is invalid with an improper file type" do
        brand = build(:brand, :with_invalid_logo_type)
        expect(brand).not_to be_valid
        expect(brand.errors[:logo]).to include("has an invalid content type (authorized content types are PNG, JPG)")
      end

      it "is invalid with a file larger than 5MB" do
        brand = build(:brand, :with_large_logo)
        expect(brand).not_to be_valid
        expect(brand.errors[:logo]).to include("file size must be less than 5 MB (current size is 6 MB)")
      end
    end
  end
end
