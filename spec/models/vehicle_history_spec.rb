require 'rails_helper'

RSpec.describe Vehicle::VehicleHistory, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:customer_vehicle) }
  end

  describe 'validations' do
    subject { build(:vehicle_history) }

    it { is_expected.to validate_numericality_of(:number_of_owners).only_integer.allow_nil }
    it { is_expected.to validate_numericality_of(:last_service_odometer).only_integer.allow_nil }
    it { is_expected.to allow_value(true).for(:has_accident_history) }
    it { is_expected.to allow_value(false).for(:has_accident_history) }
    it { is_expected.to allow_value(true).for(:has_dash_warning_lights) }
    it { is_expected.to allow_value(false).for(:has_dash_warning_lights) }
  end

  describe 'uuid' do
    it 'generates a uuid on create' do
      history = create(:vehicle_history)
      expect(history.uuid).to be_present
      expect(history.uuid.length).to eq(36)
    end
    it 'is unique' do
      h1 = create(:vehicle_history)
      h2 = create(:vehicle_history)
      expect(h1.uuid).not_to eq(h2.uuid)
    end
  end

  describe 'attachments' do
    it 'has many attached service_book_images' do
      history = create(:vehicle_history)
      expect(history.service_book_images).to be_an_instance_of(ActiveStorage::Attached::Many)
    end

    it 'validates content type of service_book_images' do
      history = build(:vehicle_history)
      # Valid content type
      history.service_book_images.attach(
        io: StringIO.new('fake image content'),
        filename: 'test.png',
        content_type: 'image/png'
      )
      expect(history).to be_valid

      # Invalid content type
      history.service_book_images.detach
      history.service_book_images.attach(
        io: StringIO.new('fake text content'),
        filename: 'test.txt',
        content_type: 'text/plain'
      )
      expect(history).not_to be_valid
      expect(history.errors[:service_book_images]).to include('has an invalid content type (authorized content types are PNG, JPG)')
    end

    it 'validates size limit of service_book_images' do
      history = build(:vehicle_history)
      large_file = StringIO.new('x' * (6.megabytes))
      history.service_book_images.attach(
        io: large_file,
        filename: 'large.png',
        content_type: 'image/png'
      )
      expect(history).not_to be_valid
      expect(history.errors[:service_book_images]).to include('file size must be less than 5 MB (current size is 6 MB)')
    end

    it 'validates count limit of service_book_images' do
      history = build(:vehicle_history)
      6.times do |i|
        history.service_book_images.attach(
          io: StringIO.new('fake image content'),
          filename: "test#{i}.png",
          content_type: 'image/png'
        )
      end
      expect(history).not_to be_valid
      expect(history.errors[:service_book_images]).to include('too many files attached (maximum is 5 files, got 6)')
    end
  end

  describe 'notes field' do
    it 'can set and retrieve notes' do
      notes_text = 'This is a test note.'
      history = build(:vehicle_history, notes: notes_text)
      expect(history.notes).to eq(notes_text)
    end
  end

  describe 'vehicle_history_status enum' do
    it 'defines the correct enum values' do
      expect(described_class.vehicle_history_statuses).to eq({
        'no_history' => 0,
        'partial_history' => 1,
        'full_mixed_history' => 2,
        'full_oem_history' => 3
      })
    end

    it 'is valid with each enum value' do
      described_class.vehicle_history_statuses.keys.each do |status|
        history = build(:vehicle_history, vehicle_history_status: status)
        expect(history).to be_valid
      end
    end

    it 'defaults to no_history' do
      history = build(:vehicle_history)
      expect(history.vehicle_history_status).to eq('no_history')
    end

    it 'can be set to each value and queried with predicate methods' do
      history = build(:vehicle_history, vehicle_history_status: :partial_history)
      expect(history.partial_history?).to be true
      history.vehicle_history_status = :full_mixed_history
      expect(history.full_mixed_history?).to be true
      history.vehicle_history_status = :full_oem_history
      expect(history.full_oem_history?).to be true
    end
  end
end
