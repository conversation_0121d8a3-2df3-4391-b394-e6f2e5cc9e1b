require 'rails_helper'

RSpec.describe Vehicle::FinanceDetails, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:customer_vehicle) }
  end

  describe 'validations' do
    subject { build(:finance_details) }

    describe 'is_financed' do
      it 'allows true when required fields are provided' do
        finance_details = build(:finance_details, :financed)
        expect(finance_details).to be_valid
      end

      it 'allows false' do
        finance_details = build(:finance_details, is_financed: false)
        expect(finance_details).to be_valid
      end
    end

    describe 'has_clear_title' do
      it 'allows true' do
        finance_details = build(:finance_details, has_clear_title: true)
        expect(finance_details).to be_valid
      end

      it 'allows false' do
        finance_details = build(:finance_details, has_clear_title: false)
        expect(finance_details).to be_valid
      end
    end

    describe 'current_repayment_amount' do
      it { is_expected.to validate_numericality_of(:current_repayment_amount).is_greater_than(0).allow_nil }

      it 'allows positive values' do
        finance_details = build(:finance_details, current_repayment_amount: 500.50)
        expect(finance_details).to be_valid
      end

      it 'rejects zero' do
        finance_details = build(:finance_details, current_repayment_amount: 0)
        expect(finance_details).not_to be_valid
        expect(finance_details.errors[:current_repayment_amount]).to include('must be greater than 0')
      end

      it 'rejects negative values' do
        finance_details = build(:finance_details, current_repayment_amount: -100)
        expect(finance_details).not_to be_valid
        expect(finance_details.errors[:current_repayment_amount]).to include('must be greater than 0')
      end

      it 'allows nil' do
        finance_details = build(:finance_details, current_repayment_amount: nil)
        expect(finance_details).to be_valid
      end
    end

    describe 'terms_months' do
      it { is_expected.to validate_numericality_of(:terms_months).is_greater_than(0).only_integer.allow_nil }

      it 'allows positive integers' do
        finance_details = build(:finance_details, terms_months: 60)
        expect(finance_details).to be_valid
      end

      it 'rejects zero' do
        finance_details = build(:finance_details, terms_months: 0)
        expect(finance_details).not_to be_valid
        expect(finance_details.errors[:terms_months]).to include('must be greater than 0')
      end

      it 'rejects negative values' do
        finance_details = build(:finance_details, terms_months: -12)
        expect(finance_details).not_to be_valid
        expect(finance_details.errors[:terms_months]).to include('must be greater than 0')
      end

      it 'rejects decimals' do
        finance_details = build(:finance_details, terms_months: 12.5)
        expect(finance_details).not_to be_valid
        expect(finance_details.errors[:terms_months]).to include('must be an integer')
      end

      it 'allows nil' do
        finance_details = build(:finance_details, terms_months: nil)
        expect(finance_details).to be_valid
      end
    end

    describe 'interest_rate' do
      it { is_expected.to validate_numericality_of(:interest_rate).is_greater_than(0).is_less_than_or_equal_to(100).allow_nil }

      it 'allows values between 0 and 100' do
        finance_details = build(:finance_details, interest_rate: 5.25)
        expect(finance_details).to be_valid
      end

      it 'allows exactly 100' do
        finance_details = build(:finance_details, interest_rate: 100)
        expect(finance_details).to be_valid
      end

      it 'rejects zero' do
        finance_details = build(:finance_details, interest_rate: 0)
        expect(finance_details).not_to be_valid
        expect(finance_details.errors[:interest_rate]).to include('must be greater than 0')
      end

      it 'rejects negative values' do
        finance_details = build(:finance_details, interest_rate: -1)
        expect(finance_details).not_to be_valid
        expect(finance_details.errors[:interest_rate]).to include('must be greater than 0')
      end

      it 'rejects values over 100' do
        finance_details = build(:finance_details, interest_rate: 101)
        expect(finance_details).not_to be_valid
        expect(finance_details.errors[:interest_rate]).to include('must be less than or equal to 100')
      end

      it 'allows nil' do
        finance_details = build(:finance_details, interest_rate: nil)
        expect(finance_details).to be_valid
      end
    end

    describe 'next_due_date' do
      context 'when is_financed is true' do
        subject { build(:finance_details, :financed) }

        it { is_expected.to validate_presence_of(:next_due_date) }

        it 'requires next_due_date when financed' do
          finance_details = build(:finance_details, :financed, next_due_date: nil)
          expect(finance_details).not_to be_valid
          expect(finance_details.errors[:next_due_date]).to include("can't be blank")
        end
      end

      context 'when is_financed is false' do
        subject { build(:finance_details, is_financed: false) }

        it 'allows nil next_due_date when not financed' do
          finance_details = build(:finance_details, is_financed: false, next_due_date: nil)
          expect(finance_details).to be_valid
        end
      end
    end

    describe 'finance_company' do
      context 'when is_financed is true' do
        subject { build(:finance_details, :financed) }

        it { is_expected.to validate_presence_of(:finance_company) }

        it 'requires finance_company when financed' do
          finance_details = build(:finance_details, :financed, finance_company: nil)
          expect(finance_details).not_to be_valid
          expect(finance_details.errors[:finance_company]).to include("can't be blank")
        end
      end

      context 'when is_financed is false' do
        subject { build(:finance_details, is_financed: false) }

        it 'allows nil finance_company when not financed' do
          finance_details = build(:finance_details, is_financed: false, finance_company: nil)
          expect(finance_details).to be_valid
        end
      end
    end

    describe 'payout_amount' do
      it { is_expected.to validate_numericality_of(:payout_amount).is_greater_than_or_equal_to(0).allow_nil }

      it 'allows nil' do
        finance_details = build(:finance_details, payout_amount: nil)
        expect(finance_details).to be_valid
      end

      it 'allows zero' do
        finance_details = build(:finance_details, payout_amount: 0.0)
        expect(finance_details).to be_valid
      end

      it 'allows positive decimal values' do
        finance_details = build(:finance_details, payout_amount: 12345.67)
        expect(finance_details).to be_valid
      end

      it 'rejects negative values' do
        finance_details = build(:finance_details, payout_amount: -100.0)
        expect(finance_details).not_to be_valid
        expect(finance_details.errors[:payout_amount]).to include('must be greater than or equal to 0')
      end

      it 'allows up to 2 decimal places' do
        finance_details = build(:finance_details, payout_amount: 123.45)
        expect(finance_details).to be_valid
      end

      it 'rounds values with more than 2 decimal places' do
        finance_details = build(:finance_details, payout_amount: 123.456)
        expect(finance_details).to be_valid
        expect(finance_details.payout_amount.round(2)).to eq(123.46)
      end
    end
  end

  describe 'methods' do
    describe '#next_payment_due?' do
      it 'returns false when next_due_date is nil' do
        finance_details = build(:finance_details, next_due_date: nil)
        expect(finance_details.next_payment_due?).to be false
      end

      it 'returns true when next_due_date is today' do
        finance_details = build(:finance_details, next_due_date: Date.current)
        expect(finance_details.next_payment_due?).to be true
      end

      it 'returns true when next_due_date is in the past' do
        finance_details = build(:finance_details, next_due_date: Date.current - 1.day)
        expect(finance_details.next_payment_due?).to be true
      end

      it 'returns false when next_due_date is in the future' do
        finance_details = build(:finance_details, next_due_date: Date.current + 1.day)
        expect(finance_details.next_payment_due?).to be false
      end
    end

    describe '#days_until_next_payment' do
      it 'returns nil when next_due_date is nil' do
        finance_details = build(:finance_details, next_due_date: nil)
        expect(finance_details.days_until_next_payment).to be_nil
      end

      it 'returns negative number when payment is overdue' do
        finance_details = build(:finance_details, next_due_date: Date.current - 5.days)
        expect(finance_details.days_until_next_payment).to eq(-5)
      end

      it 'returns zero when payment is due today' do
        finance_details = build(:finance_details, next_due_date: Date.current)
        expect(finance_details.days_until_next_payment).to eq(0)
      end

      it 'returns positive number when payment is due in the future' do
        finance_details = build(:finance_details, next_due_date: Date.current + 10.days)
        expect(finance_details.days_until_next_payment).to eq(10)
      end
    end
  end

  describe 'factory' do
    it 'has a valid factory' do
      expect(build(:finance_details)).to be_valid
    end

    it 'creates a financed record with all required fields' do
      finance_details = create(:finance_details, :financed)
      expect(finance_details).to be_valid
      expect(finance_details.is_financed).to be true
      expect(finance_details.has_clear_title).to be false
      expect(finance_details.current_repayment_amount).to be_present
      expect(finance_details.terms_months).to be_present
      expect(finance_details.interest_rate).to be_present
      expect(finance_details.next_due_date).to be_present
      expect(finance_details.finance_company).to be_present
    end

    it 'creates a clear title record' do
      finance_details = create(:finance_details, :clear_title)
      expect(finance_details).to be_valid
      expect(finance_details.is_financed).to be false
      expect(finance_details.has_clear_title).to be true
    end

    it 'creates a not financed record' do
      finance_details = create(:finance_details, :not_financed)
      expect(finance_details).to be_valid
      expect(finance_details.is_financed).to be false
      expect(finance_details.has_clear_title).to be false
    end

    it 'creates a record with payment due' do
      finance_details = create(:finance_details, :with_payment_due)
      expect(finance_details).to be_valid
      expect(finance_details.next_payment_due?).to be true
      expect(finance_details.days_until_next_payment).to be < 0
    end

    it 'creates a record with payment due soon' do
      finance_details = create(:finance_details, :with_payment_due_soon)
      expect(finance_details).to be_valid
      expect(finance_details.next_payment_due?).to be false
      expect(finance_details.days_until_next_payment).to be > 0
      expect(finance_details.days_until_next_payment).to be <= 7
    end

    it 'creates a record with high interest rate' do
      finance_details = create(:finance_details, :with_high_interest)
      expect(finance_details).to be_valid
      expect(finance_details.interest_rate).to eq(15.5)
    end

    it 'creates a record with low interest rate' do
      finance_details = create(:finance_details, :with_low_interest)
      expect(finance_details).to be_valid
      expect(finance_details.interest_rate).to eq(3.25)
    end

    it 'creates a record with short term' do
      finance_details = create(:finance_details, :with_short_term)
      expect(finance_details).to be_valid
      expect(finance_details.terms_months).to eq(12)
    end

    it 'creates a record with long term' do
      finance_details = create(:finance_details, :with_long_term)
      expect(finance_details).to be_valid
      expect(finance_details.terms_months).to eq(84)
    end

    it 'creates a record with high payment' do
      finance_details = create(:finance_details, :with_high_payment)
      expect(finance_details).to be_valid
      expect(finance_details.current_repayment_amount).to eq(1500.00)
    end

    it 'creates a record with low payment' do
      finance_details = create(:finance_details, :with_low_payment)
      expect(finance_details).to be_valid
      expect(finance_details.current_repayment_amount).to eq(250.00)
    end
  end

  describe 'edge cases' do
    describe 'interest rate precision' do
      it 'handles decimal interest rates correctly' do
        finance_details = build(:finance_details, interest_rate: 5.125)
        expect(finance_details).to be_valid
      end

      it 'handles very low interest rates' do
        finance_details = build(:finance_details, interest_rate: 0.01)
        expect(finance_details).to be_valid
      end

      it 'handles maximum interest rate' do
        finance_details = build(:finance_details, interest_rate: 100.0)
        expect(finance_details).to be_valid
      end
    end

    describe 'repayment amount precision' do
      it 'handles decimal repayment amounts correctly' do
        finance_details = build(:finance_details, current_repayment_amount: 1234.56)
        expect(finance_details).to be_valid
      end

      it 'handles very small repayment amounts' do
        finance_details = build(:finance_details, current_repayment_amount: 0.01)
        expect(finance_details).to be_valid
      end
    end

    describe 'terms months edge cases' do
      it 'handles minimum valid term' do
        finance_details = build(:finance_details, terms_months: 1)
        expect(finance_details).to be_valid
      end

      it 'handles large terms' do
        finance_details = build(:finance_details, terms_months: 120)
        expect(finance_details).to be_valid
      end
    end
  end
end
