require 'rails_helper'

RSpec.describe Vehicle::VehicleCondition, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:customer_vehicle) }
    it { is_expected.to have_many_attached(:photos) }
    it { is_expected.to have_many(:component_ratings).dependent(:destroy) }
  it { is_expected.to have_many(:reconditioning_costs).dependent(:destroy) }
  end

  describe 'validations' do
    subject { FactoryBot.build(:vehicle_condition) }

    # Removed shoulda-matchers for :uuid presence and uniqueness due to HasUuid concern
    # Removed validations for old rating fields

    it 'does not allow duplicate uuids' do
      existing = FactoryBot.create(:vehicle_condition, uuid: 'abc-123')
      duplicate = FactoryBot.build(:vehicle_condition, uuid: 'abc-123')
      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:uuid]).to include('has already been taken')
    end
  end

  describe 'component_ratings' do
    let(:vehicle_condition) { FactoryBot.create(:vehicle_condition) }

    it 'can have many component_ratings' do
      expect {
        FactoryBot.create(:component_rating, vehicle_condition: vehicle_condition, name: :front_wheels)
        FactoryBot.create(:component_rating, vehicle_condition: vehicle_condition, name: :rear_wheels)
        FactoryBot.create(:component_rating, vehicle_condition: vehicle_condition, name: :panel_work)
      }.to change { vehicle_condition.component_ratings.count }.by(3)
    end

    it 'destroys associated component_ratings when destroyed' do
      FactoryBot.create(:component_rating, vehicle_condition: vehicle_condition)
      expect {
        vehicle_condition.destroy
      }.to change { Vehicle::ComponentRating.count }.by(-1)
    end
  end

  describe 'photo attachments' do
    let(:vehicle_condition) { FactoryBot.build(:vehicle_condition) }

    it 'is valid with up to 5 valid image files' do
      5.times do |i|
        vehicle_condition.photos.attach(
          io: StringIO.new('image content'),
          filename: "photo_#{i}.jpg",
          content_type: 'image/jpeg'
        )
      end
      expect(vehicle_condition).to be_valid
    end

    it 'is invalid with more than 5 images' do
      6.times do |i|
        vehicle_condition.photos.attach(
          io: StringIO.new('image content'),
          filename: "photo_#{i}.jpg",
          content_type: 'image/jpeg'
        )
      end
      expect(vehicle_condition).not_to be_valid
      expect(vehicle_condition.errors[:photos]).to include(a_string_matching(/too many files/i))
    end

    it 'is invalid with a non-image file' do
      vehicle_condition.photos.attach(
        io: StringIO.new('not an image'),
        filename: 'file.txt',
        content_type: 'text/plain'
      )
      expect(vehicle_condition).not_to be_valid
      expect(vehicle_condition.errors[:photos]).to include(a_string_matching(/invalid content type/i))
    end

    it 'is invalid with an image larger than 5MB' do
      vehicle_condition.photos.attach(
        io: StringIO.new('a' * (5.megabytes + 1)),
        filename: 'large_image.jpg',
        content_type: 'image/jpeg'
      )
      expect(vehicle_condition).not_to be_valid
      expect(vehicle_condition.errors[:photos]).to include(a_string_matching(/less than 5 MB/i))
    end
  end

  describe 'defaults' do
    let(:condition) { described_class.new }
    it 'defaults is_clean to true' do
      expect(condition.is_clean).to eq(true)
    end
    it 'defaults is_wet to false' do
      expect(condition.is_wet).to eq(false)
    end
    it 'defaults is_road_tested to false' do
      expect(condition.is_road_tested).to eq(false)
    end
    it 'defaults has_signs_of_repair to false' do
      expect(condition.has_signs_of_repair).to eq(false)
    end
  end
end
