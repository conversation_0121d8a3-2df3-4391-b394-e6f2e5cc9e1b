require "rails_helper"

RSpec.describe DealershipGroup, type: :model do
  describe "validations" do
    it { is_expected.to validate_presence_of(:name) }
  end

  describe "enums" do
    it { is_expected.to define_enum_for(:status).with_values(active: 0, suspended: 1).backed_by_column_of_type(:integer) }
  end

  describe "factory" do
    it "has a valid factory" do
      expect(build(:dealership_group)).to be_valid
    end

    it "generates unique names" do
      group1 = create(:dealership_group)
      group2 = create(:dealership_group)
      expect(group1.name).not_to eq(group2.name)
    end
  end

  describe "traits" do
    it "creates a suspended group" do
      group = create(:dealership_group, :suspended)
      expect(group.status).to eq("suspended")
    end
  end

  describe "associations" do
    it { is_expected.to have_many(:dealerships) }
  end

  describe "#suspend!" do
    it "changes the status to suspended" do
      group = create(:dealership_group)
      group.suspended!
      expect(group.status).to eq("suspended")
    end
  end
end
