require "rails_helper"

RSpec.describe DealershipFeaturesSetting, type: :model do
  describe "associations" do
    it { should belong_to(:dealership) }
  end

  describe "default values" do
    let(:setting) { create(:dealership_features_setting) }

    it "sets default values for boolean fields" do
      expect(setting.advance_booking_enabled).to be false
      expect(setting.insurance_waiver_enabled).to be false
      expect(setting.dealer_drive_subscription).to be false
      expect(setting.appraisals_subscription).to be false
      expect(setting.fuel_level_in_test_drive).to be false
      expect(setting.fuel_level_in_loan).to be false
    end

    it "sets default value for setting_recent_customer_age to nil" do
      expect(setting.setting_recent_customer_age).to be_nil
    end
  end

  describe "validations" do
    it { expect(subject).to validate_numericality_of(:setting_recent_customer_age).only_integer.allow_nil.is_greater_than(0) }

    describe "setting_recent_customer_age" do
      it "is valid when nil" do
        setting = build(:dealership_features_setting, setting_recent_customer_age: nil)
        expect(setting).to be_valid
      end

      it "is valid with a positive integer" do
        setting = build(:dealership_features_setting, setting_recent_customer_age: 30)
        expect(setting).to be_valid
      end

      it "is invalid with zero" do
        setting = build(:dealership_features_setting, setting_recent_customer_age: 0)
        expect(setting).not_to be_valid
        expect(setting.errors[:setting_recent_customer_age]).to include("must be greater than 0")
      end

      it "is invalid with a negative number" do
        setting = build(:dealership_features_setting, setting_recent_customer_age: -1)
        expect(setting).not_to be_valid
        expect(setting.errors[:setting_recent_customer_age]).to include("must be greater than 0")
      end

      it "is invalid with a decimal" do
        setting = build(:dealership_features_setting, setting_recent_customer_age: 1.5)
        expect(setting).not_to be_valid
        expect(setting.errors[:setting_recent_customer_age]).to include("must be an integer")
      end
    end
  end

  describe "factory" do
    it "has a valid factory" do
      expect(build(:dealership_features_setting)).to be_valid
    end

    it "creates a setting with advance booking enabled" do
      setting = create(:dealership_features_setting, :with_advance_booking_enabled)
      expect(setting.advance_booking_enabled).to be true
    end

    it "creates a setting with advance booking disabled" do
      setting = create(:dealership_features_setting, :with_advance_booking_disabled)
      expect(setting.advance_booking_enabled).to be false
    end

    it "creates a setting with insurance waiver enabled" do
      setting = create(:dealership_features_setting, :with_insurance_waiver_enabled)
      expect(setting.insurance_waiver_enabled).to be true
    end

    it "creates a setting with insurance waiver disabled" do
      setting = create(:dealership_features_setting, :with_insurance_waiver_disabled)
      expect(setting.insurance_waiver_enabled).to be false
    end

    it "creates a setting with dealer drive subscription enabled" do
      setting = create(:dealership_features_setting, :with_dealer_drive_subscription_enabled)
      expect(setting.dealer_drive_subscription).to be true
    end

    it "creates a setting with dealer drive subscription disabled" do
      setting = create(:dealership_features_setting, :with_dealer_drive_subscription_disabled)
      expect(setting.dealer_drive_subscription).to be false
    end

    it "creates a setting with appraisals subscription enabled" do
      setting = create(:dealership_features_setting, :with_appraisals_subscription_enabled)
      expect(setting.appraisals_subscription).to be true
    end

    it "creates a setting with appraisals subscription disabled" do
      setting = create(:dealership_features_setting, :with_appraisals_subscription_disabled)
      expect(setting.appraisals_subscription).to be false
    end

    it "creates a setting with recent customer age" do
      setting = create(:dealership_features_setting, :with_recent_customer_age)
      expect(setting.setting_recent_customer_age).to eq(30)
    end
  end
end
