require 'rails_helper'

RSpec.describe CustomerVehicle, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:customer) }
    it { is_expected.to belong_to(:dealership) }
    it { is_expected.to have_one(:finance_details).dependent(:destroy) }
    it { is_expected.to have_one(:options_fitted).dependent(:destroy) }
    it { is_expected.to belong_to(:brand).optional }
    it { is_expected.to have_one(:vehicle_condition).dependent(:destroy) }
    it { is_expected.to belong_to(:appraisal) }
  end

  describe 'validations' do
    subject { build(:customer_vehicle) }

    # VehicleBase validations
    it { is_expected.to validate_presence_of(:make) }
    it { is_expected.to validate_presence_of(:model) }
    it { is_expected.to validate_presence_of(:build_year) }
    it { is_expected.to validate_numericality_of(:build_year).is_greater_than(1900) }
    it { is_expected.to validate_numericality_of(:build_year).is_less_than_or_equal_to(Date.current.year + 1) }
    it { is_expected.to validate_numericality_of(:build_month).is_greater_than(0) }
    it { is_expected.to validate_numericality_of(:build_month).is_less_than(13) }

    it { is_expected.to validate_length_of(:rego).is_at_most(20) }
    it { is_expected.to validate_length_of(:vin).is_at_most(17) }
    it { is_expected.to validate_numericality_of(:build_year).is_greater_than(1900) }
    it { is_expected.to validate_numericality_of(:build_year).is_less_than_or_equal_to(Date.current.year + 1) }

    # CustomerVehicle specific validations
    context 'is_vehicle_present' do
      it 'allows true' do
        vehicle = build(:customer_vehicle, is_vehicle_present: true)
        expect(vehicle).to be_valid
      end

      it 'allows false' do
        vehicle = build(:customer_vehicle, is_vehicle_present: false)
        expect(vehicle).to be_valid
      end

      it 'allows nil' do
        vehicle = build(:customer_vehicle, is_vehicle_present: nil)
        expect(vehicle).to be_valid
      end

      # Note: Boolean fields automatically convert string values to booleans in Rails
      # 'maybe' becomes true, 'false' becomes false, etc.
      # The inclusion validation ensures only true/false are accepted
    end

    context 'compliance_month' do
      it 'allows valid months (1-12)' do
        (1..12).each do |month|
          vehicle = build(:customer_vehicle, compliance_month: month)
          expect(vehicle).to be_valid
        end
      end

      it 'rejects month 0' do
        vehicle = build(:customer_vehicle, compliance_month: 0)
        expect(vehicle).not_to be_valid
        expect(vehicle.errors[:compliance_month]).to include('must be greater than 0')
      end

      it 'rejects month 13' do
        vehicle = build(:customer_vehicle, compliance_month: 13)
        expect(vehicle).not_to be_valid
        expect(vehicle.errors[:compliance_month]).to include('must be less than 13')
      end

      it 'allows nil' do
        vehicle = build(:customer_vehicle, compliance_month: nil)
        expect(vehicle).to be_valid
      end
    end

    context 'compliance_year' do
      it 'allows years after 1900' do
        vehicle = build(:customer_vehicle, compliance_year: 1901)
        expect(vehicle).to be_valid
      end

      it 'rejects year 1900' do
        vehicle = build(:customer_vehicle, compliance_year: 1900)
        expect(vehicle).not_to be_valid
        expect(vehicle.errors[:compliance_year]).to include('must be greater than 1900')
      end

      it 'allows nil' do
        vehicle = build(:customer_vehicle, compliance_year: nil)
        expect(vehicle).to be_valid
      end
    end

    context 'odometer_reading' do
      it 'allows zero' do
        vehicle = build(:customer_vehicle, odometer_reading: 0)
        expect(vehicle).to be_valid
      end

      it 'allows positive values' do
        vehicle = build(:customer_vehicle, odometer_reading: 50000)
        expect(vehicle).to be_valid
      end

      it 'rejects negative values' do
        vehicle = build(:customer_vehicle, odometer_reading: -1)
        expect(vehicle).not_to be_valid
        expect(vehicle.errors[:odometer_reading]).to include('must be greater than or equal to 0')
      end

      it 'allows nil' do
        vehicle = build(:customer_vehicle, odometer_reading: nil)
        expect(vehicle).to be_valid
      end
    end

    context 'number_of_doors' do
      it 'allows positive values' do
        vehicle = build(:customer_vehicle, number_of_doors: 4)
        expect(vehicle).to be_valid
      end

      it 'rejects zero' do
        vehicle = build(:customer_vehicle, number_of_doors: 0)
        expect(vehicle).not_to be_valid
        expect(vehicle.errors[:number_of_doors]).to include('must be greater than 0')
      end

      it 'rejects negative values' do
        vehicle = build(:customer_vehicle, number_of_doors: -1)
        expect(vehicle).not_to be_valid
        expect(vehicle.errors[:number_of_doors]).to include('must be greater than 0')
      end

      it 'allows nil' do
        vehicle = build(:customer_vehicle, number_of_doors: nil)
        expect(vehicle).to be_valid
      end
    end

    context 'number_of_seats' do
      it 'allows positive values' do
        vehicle = build(:customer_vehicle, number_of_seats: 5)
        expect(vehicle).to be_valid
      end

      it 'rejects zero' do
        vehicle = build(:customer_vehicle, number_of_seats: 0)
        expect(vehicle).not_to be_valid
        expect(vehicle.errors[:number_of_seats]).to include('must be greater than 0')
      end

      it 'rejects negative values' do
        vehicle = build(:customer_vehicle, number_of_seats: -1)
        expect(vehicle).not_to be_valid
        expect(vehicle.errors[:number_of_seats]).to include('must be greater than 0')
      end

      it 'allows nil' do
        vehicle = build(:customer_vehicle, number_of_seats: nil)
        expect(vehicle).to be_valid
      end
    end

    context 'engine_kilowatts' do
      it 'allows positive values' do
        vehicle = build(:customer_vehicle, engine_kilowatts: 100)
        expect(vehicle).to be_valid
      end
      it 'rejects zero or negative values' do
        vehicle = build(:customer_vehicle, engine_kilowatts: 0)
        expect(vehicle).not_to be_valid
        expect(vehicle.errors[:engine_kilowatts]).to include('must be greater than 0')
        vehicle = build(:customer_vehicle, engine_kilowatts: -10)
        expect(vehicle).not_to be_valid
        expect(vehicle.errors[:engine_kilowatts]).to include('must be greater than 0')
      end
      it 'allows nil' do
        vehicle = build(:customer_vehicle, engine_kilowatts: nil)
        expect(vehicle).to be_valid
      end
    end
    context 'wheel_size_front' do
      it 'allows values >= 13' do
        vehicle = build(:customer_vehicle, wheel_size_front: 13)
        expect(vehicle).to be_valid
        vehicle = build(:customer_vehicle, wheel_size_front: 18)
        expect(vehicle).to be_valid
      end
      it 'rejects values < 13' do
        vehicle = build(:customer_vehicle, wheel_size_front: 12)
        expect(vehicle).not_to be_valid
        expect(vehicle.errors[:wheel_size_front]).to include('must be greater than or equal to 13')
      end
      it 'allows nil' do
        vehicle = build(:customer_vehicle, wheel_size_front: nil)
        expect(vehicle).to be_valid
      end
    end
    context 'wheel_size_rear' do
      it 'allows values >= 13' do
        vehicle = build(:customer_vehicle, wheel_size_rear: 13)
        expect(vehicle).to be_valid
        vehicle = build(:customer_vehicle, wheel_size_rear: 20)
        expect(vehicle).to be_valid
      end
      it 'rejects values < 13' do
        vehicle = build(:customer_vehicle, wheel_size_rear: 12)
        expect(vehicle).not_to be_valid
        expect(vehicle.errors[:wheel_size_rear]).to include('must be greater than or equal to 13')
      end
      it 'allows nil' do
        vehicle = build(:customer_vehicle, wheel_size_rear: nil)
        expect(vehicle).to be_valid
      end
    end
  end

  describe 'enums' do
    it { is_expected.to define_enum_for(:seat_type).with_values(leather: 0, cloth: 1, mixed: 2).backed_by_column_of_type(:integer) }
    it { is_expected.to define_enum_for(:fuel_type).with_values(petrol: 0, diesel: 1, electric: 2, hybrid: 3, plugin_hybrid: 4, lpg: 5, other: 6).backed_by_column_of_type(:integer) }
    it { is_expected.to define_enum_for(:driving_wheels).with_values(fwd: 0, rwd: 1, awd: 2, four_wd: 3).backed_by_column_of_type(:integer) }
    it { is_expected.to define_enum_for(:spare_wheel_type).with_values(full_size: 0, space_saver: 1, run_flat: 2, repair_kit: 3, no_spare_wheel: 4).backed_by_column_of_type(:integer) }
    it { is_expected.to define_enum_for(:transmission).with_values(manual: 0, automatic: 1, cvt: 2, semi_automatic: 3, dual_clutch: 4).backed_by_column_of_type(:integer) }
    it { is_expected.to define_enum_for(:body_type).with_values(sedan: 0, hatchback: 1, wagon: 2, suv: 3, coupe: 4, convertible: 5, ute: 6, van: 7, truck: 8, unknown_body: 9).backed_by_column_of_type(:integer) }
  end

  describe 'scopes' do
    let(:customer) { create(:customer) }
    let!(:present_vehicle) { create(:customer_vehicle, customer: customer, is_vehicle_present: true) }
    let!(:absent_vehicle) { create(:customer_vehicle, customer: customer, is_vehicle_present: false) }

    describe '.present' do
      it 'returns only vehicles where is_vehicle_present is true' do
        expect(CustomerVehicle.present).to include(present_vehicle)
        expect(CustomerVehicle.present).not_to include(absent_vehicle)
      end
    end

    describe '.absent' do
      it 'returns only vehicles where is_vehicle_present is false' do
        expect(CustomerVehicle.absent).to include(absent_vehicle)
        expect(CustomerVehicle.absent).not_to include(present_vehicle)
      end
    end
  end

  describe '#display_name' do
    it 'returns formatted vehicle name with build year, make, and model' do
      vehicle = build(:customer_vehicle, build_year: 2020, make: 'Toyota', model: 'Camry')
      expect(vehicle.display_name).to eq('2020 Toyota Camry')
    end

    it 'handles missing build year' do
      vehicle = build(:customer_vehicle, build_year: nil, make: 'Toyota', model: 'Camry')
      expect(vehicle.display_name).to eq('Toyota Camry')
    end

    it 'handles missing make' do
      vehicle = build(:customer_vehicle, build_year: 2020, make: nil, model: 'Camry')
      expect(vehicle.display_name).to eq('2020 Camry')
    end

    it 'handles missing model' do
      vehicle = build(:customer_vehicle, build_year: 2020, make: 'Toyota', model: nil)
      expect(vehicle.display_name).to eq('2020 Toyota')
    end
  end

  describe '#to_param' do
    it 'returns the uuid' do
      vehicle = create(:customer_vehicle)
      expect(vehicle.to_param).to eq(vehicle.uuid)
    end
  end

  describe 'photo attachments' do
    it 'validates photo content types' do
      vehicle = build(:customer_vehicle)

      # Valid content types
      vehicle.photos.attach(
        io: StringIO.new('fake image content'),
        filename: 'test.png',
        content_type: 'image/png'
      )
      expect(vehicle).to be_valid

      # Invalid content type
      vehicle.photos.detach
      vehicle.photos.attach(
        io: StringIO.new('fake text content'),
        filename: 'test.txt',
        content_type: 'text/plain'
      )
      expect(vehicle).not_to be_valid
      expect(vehicle.errors[:photos]).to include('has an invalid content type (authorized content types are PNG, JPG)')
    end

    it 'validates photo size limit' do
      vehicle = build(:customer_vehicle)

      # Create a file larger than 5MB
      large_file = StringIO.new('x' * (6.megabytes))
      vehicle.photos.attach(
        io: large_file,
        filename: 'large.png',
        content_type: 'image/png'
      )

      expect(vehicle).not_to be_valid
      expect(vehicle.errors[:photos]).to include('file size must be less than 5 MB (current size is 6 MB)')
    end

    it 'validates photo count limit' do
      vehicle = build(:customer_vehicle)

      # Attach 6 photos (limit is 5)
      6.times do |i|
        vehicle.photos.attach(
          io: StringIO.new('fake image content'),
          filename: "test#{i}.png",
          content_type: 'image/png'
        )
      end

      expect(vehicle).not_to be_valid
      expect(vehicle.errors[:photos]).to include('too many files attached (maximum is 5 files, got 6)')
    end
  end

  describe 'finance_details association' do
    it { is_expected.to have_one(:finance_details).dependent(:destroy) }
    it { is_expected.to accept_nested_attributes_for(:finance_details) }

    context 'when a customer vehicle is destroyed' do
      it 'destroys the associated finance_details' do
        vehicle = create(:customer_vehicle)
        finance = create(:finance_details, customer_vehicle: vehicle)
        expect { vehicle.destroy }.to change { Vehicle::FinanceDetails.count }.by(-1)
      end
    end

    context 'with nested attributes' do
      let(:dealership) { create(:dealership) }
      let(:customer) { create(:customer, dealership: dealership) }
      let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer) }
      let(:brand) { create(:brand) }
      let(:vehicle_attrs) do
        attributes_for(:customer_vehicle, customer: customer, dealership: dealership, brand: brand).merge(
          finance_details_attributes: attributes_for(:finance_details, current_repayment_amount: 123.45, is_financed: false, has_clear_title: true)
        )
      end

      it 'creates finance_details when nested attributes are provided' do
        vehicle = CustomerVehicle.create!(vehicle_attrs.merge(customer: customer, brand: create(:brand), dealership: dealership, appraisal: appraisal))
        expect(vehicle.finance_details).to be_present
        expect(vehicle.finance_details.current_repayment_amount).to eq(123.45)
        expect(vehicle.finance_details.has_clear_title).to be true
      end

      it 'updates finance_details via nested attributes' do
        vehicle = create(:customer_vehicle)
        finance = create(:finance_details, customer_vehicle: vehicle, current_repayment_amount: 100)
        vehicle.update!(finance_details_attributes: { id: finance.id, current_repayment_amount: 555.55 })
        expect(vehicle.finance_details.reload.current_repayment_amount).to eq(555.55)
      end
    end

    context 'factory integration' do
      it 'can create a customer vehicle with associated finance_details' do
        vehicle = create(:customer_vehicle)
        finance = create(:finance_details, customer_vehicle: vehicle, current_repayment_amount: 999.99)
        expect(vehicle.finance_details).to eq(finance)
        expect(vehicle.finance_details).to be_a(Vehicle::FinanceDetails)
        expect(vehicle.finance_details.current_repayment_amount).to eq(999.99)
      end
    end
  end

  describe 'vehicle_history association' do
    it { is_expected.to have_one(:vehicle_history).class_name('Vehicle::VehicleHistory').dependent(:destroy) }
    it { is_expected.to accept_nested_attributes_for(:vehicle_history) }

    it 'destroys associated vehicle_history when customer_vehicle is destroyed' do
      vehicle = create(:customer_vehicle)
      history = create(:vehicle_history, customer_vehicle: vehicle)
      expect { vehicle.destroy }.to change { Vehicle::VehicleHistory.count }.by(-1)
    end

    it 'can create vehicle_history via nested attributes' do
      vehicle = build(:customer_vehicle, vehicle_history_attributes: attributes_for(:vehicle_history))
      expect(vehicle.vehicle_history).to be_present
      expect(vehicle.vehicle_history).to be_a(Vehicle::VehicleHistory)
    end
  end

  describe 'vehicle_condition association' do
    it { is_expected.to accept_nested_attributes_for(:vehicle_condition) }

    context 'when a customer vehicle is destroyed' do
      it 'destroys the associated vehicle_condition' do
        vehicle = create(:customer_vehicle)
        condition = create(:vehicle_condition, customer_vehicle: vehicle)
        expect { vehicle.destroy }.to change { Vehicle::VehicleCondition.count }.by(-1)
      end
    end

    context 'with nested attributes' do
      let(:customer) { create(:customer) }
      let(:brand) { create(:brand) }
      let(:dealership) { create(:dealership) }
      let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer) }
      let(:vehicle_attrs) do
        attributes_for(:customer_vehicle, customer: customer, dealership: dealership, brand: brand).merge(
          vehicle_condition_attributes: attributes_for(:vehicle_condition)
        )
      end

      it 'creates vehicle_condition when nested attributes are provided' do
        vehicle = CustomerVehicle.create!(vehicle_attrs.merge(customer: customer, brand: create(:brand), dealership: dealership, appraisal: appraisal))
        expect(vehicle.vehicle_condition).to be_present
      end

      it 'updates vehicle_condition via nested attributes' do
        vehicle = create(:customer_vehicle)
        condition = create(:vehicle_condition, customer_vehicle: vehicle)
        vehicle.update!(vehicle_condition_attributes: { id: condition.id })
      end
    end

    context 'factory integration' do
      it 'can create a customer vehicle with associated vehicle_condition' do
        vehicle = create(:customer_vehicle)
        condition = create(:vehicle_condition, customer_vehicle: vehicle)
        expect(vehicle.vehicle_condition).to eq(condition)
        expect(vehicle.vehicle_condition).to be_a(Vehicle::VehicleCondition)
      end
    end
  end

  describe 'factory traits' do
    describe ':electric trait' do
      it 'creates a vehicle with electric fuel type' do
        vehicle = create(:customer_vehicle, :electric)
        expect(vehicle.fuel_type).to eq('electric')
      end
    end

    describe ':hybrid trait' do
      it 'creates a vehicle with hybrid fuel type' do
        vehicle = create(:customer_vehicle, :hybrid)
        expect(vehicle.fuel_type).to eq('hybrid')
      end
    end

    describe ':automatic trait' do
      it 'creates a vehicle with automatic transmission' do
        vehicle = create(:customer_vehicle, :automatic)
        expect(vehicle.transmission).to eq('automatic')
      end
    end

    describe ':manual trait' do
      it 'creates a vehicle with manual transmission' do
        vehicle = create(:customer_vehicle, :manual)
        expect(vehicle.transmission).to eq('manual')
      end
    end

    describe ':suv trait' do
      it 'creates a vehicle with SUV body type' do
        vehicle = create(:customer_vehicle, :suv)
        expect(vehicle.body_type).to eq('suv')
      end
    end

    describe ':sedan trait' do
      it 'creates a vehicle with sedan body type' do
        vehicle = create(:customer_vehicle, :sedan)
        expect(vehicle.body_type).to eq('sedan')
      end
    end

    describe ':with_photos trait' do
      it 'creates a vehicle with attached photos' do
        vehicle = create(:customer_vehicle, :with_photos)
        expect(vehicle.photos).to be_attached
        expect(vehicle.photos.count).to eq(2)
        expect(vehicle.photos.first.filename.to_s).to eq('car_1.png')
        expect(vehicle.photos.second.filename.to_s).to eq('car_2.jpg')
      end

      it 'attaches photos with correct content types' do
        vehicle = create(:customer_vehicle, :with_photos)
        expect(vehicle.photos.first.content_type).to eq('image/png')
        expect(vehicle.photos.second.content_type).to eq('image/jpeg')
      end
    end
  end

  describe 'uuid generation' do
    it 'automatically generates a uuid on creation' do
      vehicle = create(:customer_vehicle)
      expect(vehicle.uuid).to be_present
      expect(vehicle.uuid).to match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)
    end

    it 'ensures uuid uniqueness' do
      vehicle1 = create(:customer_vehicle)
      vehicle2 = build(:customer_vehicle, uuid: vehicle1.uuid)
      expect(vehicle2).not_to be_valid
      expect(vehicle2.errors[:uuid]).to include('has already been taken')
    end
  end

  describe 'nested attributes' do
    describe 'options_fitted_attributes' do
      it 'accepts nested attributes for options_fitted' do
        customer = create(:customer)
        customer_vehicle = build(:customer_vehicle,  options_fitted_attributes: {
          has_sunroof: true,
          has_tinted_windows: true,
          has_extended_warranty: true,
          extended_warranty_expiry: 1.year.from_now.to_date
        })

        # The nested attributes should be present but validation will fail until parent is saved
        expect(customer_vehicle.options_fitted).to be_present
        expect(customer_vehicle.options_fitted.has_sunroof).to be true
        expect(customer_vehicle.options_fitted.has_tinted_windows).to be true
        expect(customer_vehicle.options_fitted.has_extended_warranty).to be true
      end

      it 'creates options_fitted when customer_vehicle is saved' do
        customer = create(:customer)
        customer_vehicle = build(:customer_vehicle,  options_fitted_attributes: {
          has_sunroof: true,
          has_bluetooth: true,
          additional_options: { "custom_paint" => "Red" }
        })

        customer_vehicle.save!

        expect(customer_vehicle.options_fitted).to be_present
        expect(customer_vehicle.options_fitted.has_sunroof).to be true
        expect(customer_vehicle.options_fitted.has_bluetooth).to be true
        expect(customer_vehicle.options_fitted.additional_options).to eq({ "custom_paint" => "Red" })
      end

      it 'updates existing options_fitted when customer_vehicle is updated' do
        customer_vehicle = create(:customer_vehicle)
        options_fitted = create(:options_fitted, customer_vehicle: customer_vehicle, has_sunroof: false)

        customer_vehicle.update!(options_fitted_attributes: {
          id: options_fitted.id,
          has_sunroof: true,
          has_towbar: true
        })

        options_fitted.reload
        expect(options_fitted.has_sunroof).to be true
        expect(options_fitted.has_towbar).to be true
      end

      it 'validates nested options_fitted attributes' do
        customer = create(:customer)
        customer_vehicle = build(:customer_vehicle,  options_fitted_attributes: {
          has_extended_warranty: true,
          extended_warranty_expiry: nil # This should be invalid
        })

        # The nested attributes validation should work even before parent is saved
        expect(customer_vehicle.options_fitted).not_to be_valid
        expect(customer_vehicle.options_fitted.errors[:extended_warranty_expiry]).to include("can't be blank")
      end
    end

    describe 'finance_details_attributes' do
      it 'accepts nested attributes for finance_details' do
        customer = create(:customer)
        customer_vehicle = build(:customer_vehicle,  finance_details_attributes: {
          is_financed: true,
          has_clear_title: false,
          current_repayment_amount: 500.00,
          terms_months: 60,
          next_due_date: 1.month.from_now.to_date,
          finance_company: "Test Finance Co"
        })

        expect(customer_vehicle).to be_valid
        expect(customer_vehicle.finance_details).to be_present
        expect(customer_vehicle.finance_details.is_financed).to be true
        expect(customer_vehicle.finance_details.current_repayment_amount).to eq(500.00)
      end
    end
  end
end
