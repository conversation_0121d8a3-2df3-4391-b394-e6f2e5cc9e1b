require 'rails_helper'

RSpec.describe DeviceRegistration, type: :model do
  describe 'associations' do
    it { should belong_to(:user) }
  end

  describe 'validations' do
    subject { build(:device_registration) }

    it { should validate_presence_of(:device_id) }
    it { should validate_presence_of(:refresh_token) }
    it { should validate_presence_of(:refresh_token_expires_at) }
    it { should validate_length_of(:device_id).is_at_most(255) }
    it { should allow_value("", nil).for(:device_name) }
    it { should validate_length_of(:device_name).is_at_most(255) }
    it { should validate_length_of(:device_os_version).is_at_most(255) }
    it { should validate_length_of(:last_login_ip).is_at_most(45) }
    it { should validate_length_of(:app_version).is_at_most(30) }
    it { should validate_length_of(:app_build_number).is_at_most(30) }
    it { should validate_length_of(:fcm_token).is_at_most(512) }
    it { should validate_presence_of(:app_version) }
    it { should validate_presence_of(:app_build_number) }

    it { should define_enum_for(:device_os).with_values(ios: 0, android: 1, ipad: 2, other: 3).with_prefix }

    it { should validate_uniqueness_of(:user_id).scoped_to(%i[device_id]).case_insensitive }
    it 'validates uniqueness of refresh_token' do
      create(:device_registration)
      should validate_uniqueness_of(:refresh_token)
    end
  end

  describe 'callbacks' do
    it 'captures the last login IP before save' do
      Thread.current[:request_ip] = '***********'
      device_registration = build(:device_registration)
      device_registration.save
      expect(device_registration.last_login_ip).to eq('***********')
      Thread.current[:request_ip] = nil
    end
  end

  describe '#generate_tokens' do
    let(:device_registration) { create(:device_registration) }

    it 'generates access and refresh tokens' do
      tokens = Auth::TokenService.new(device_registration).generate_tokens(true, nil)

      expect(tokens).to have_key(:access_token)
      expect(tokens).to have_key(:refresh_token)
      expect(tokens).to have_key(:expires_at)
      expect(tokens[:access_token]).to be_present
      expect(tokens[:refresh_token]).to be_present
      expect(tokens[:expires_at]).to be_present
    end

    it 'includes JTI claim in access token' do
      tokens = Auth::TokenService.new(device_registration).generate_tokens
      decoded_token = JWT.decode(
        tokens[:access_token],
        Rails.application.credentials.devise_jwt_secret_key,
        true,
        { algorithm: 'HS256' }
      )

      expect(decoded_token[0]).to have_key('jti')
      expect(decoded_token[0]['jti']).to be_present
    end

    it 'forces refresh when requested' do
      original_refresh_token = device_registration.refresh_token
      tokens = Auth::TokenService.new(device_registration).generate_tokens(true)

      device_registration.reload
      expect(device_registration.refresh_token).not_to eq(original_refresh_token)
    end
  end

  describe '#invalidate!' do
    let(:device_registration) { create(:device_registration, active: true) }

    it 'deactivates the device registration' do
      device_registration.invalidate!
      expect(device_registration.active).to be_falsey
    end
  end

  describe "uuid" do
    it "automatically sets a UUID as before_hook before doing validation on create" do
      device = create(:device_registration)
      expect(device).to be_valid
      expect(device.uuid).to be_present
      expect(device.uuid).to match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)
    end

    it "enforces uniqueness of uuid" do
      existing_device = create(:device_registration)
      new_device = build(:device_registration, uuid: existing_device.uuid)
      expect(new_device).not_to be_valid
      expect(new_device.errors[:uuid]).to include("has already been taken")
    end
  end
end
