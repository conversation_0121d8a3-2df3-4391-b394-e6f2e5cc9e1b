require 'rails_helper'

RSpec.describe FavouriteAppraisal, type: :model do
  describe 'associations' do
    it { should belong_to(:appraisal) }
    it { should belong_to(:user) }
  end

  describe 'validations' do
    subject { build(:favourite_appraisal) }
    it { should validate_uniqueness_of(:appraisal_id).scoped_to(:user_id) }
  end

  describe 'uniqueness validation' do
    let(:user) { create(:user) }
    let(:appraisal) { create(:appraisal) }

    it 'prevents duplicate user-appraisal combinations' do
      create(:favourite_appraisal, user: user, appraisal: appraisal)
      duplicate = build(:favourite_appraisal, user: user, appraisal: appraisal)

      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:appraisal_id]).to include('has already been taken')
    end

    it 'allows same appraisal to be favourited by different users' do
      user2 = create(:user)
      create(:favourite_appraisal, user: user, appraisal: appraisal)
      favourite2 = build(:favourite_appraisal, user: user2, appraisal: appraisal)

      expect(favourite2).to be_valid
    end

    it 'allows same user to favourite different appraisals' do
      appraisal2 = create(:appraisal)
      create(:favourite_appraisal, user: user, appraisal: appraisal)
      favourite2 = build(:favourite_appraisal, user: user, appraisal: appraisal2)

      expect(favourite2).to be_valid
    end
  end
end
