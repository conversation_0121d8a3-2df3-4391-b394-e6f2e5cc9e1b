require "rails_helper"

RSpec.describe DealershipTermsSetting, type: :model do
  describe "associations" do
    it { should belong_to(:dealership) }
  end

  describe "default values" do
    let(:setting) { create(:dealership_terms_setting) }

    it "sets default values to nil" do
      expect(setting.test_drive_terms_text).to be_nil
      expect(setting.car_loan_terms_text).to be_nil
      expect(setting.insurance_waiver_text).to be_nil
    end
  end

  describe "factory" do
    it "has a valid factory" do
      expect(build(:dealership_terms_setting)).to be_valid
    end

    it "creates a setting with test drive terms" do
      setting = create(:dealership_terms_setting, :with_test_drive_terms)
      expect(setting.test_drive_terms_text).to eq("Standard test drive terms and conditions apply.")
    end

    it "creates a setting with car loan terms" do
      setting = create(:dealership_terms_setting, :with_car_loan_terms)
      expect(setting.car_loan_terms_text).to eq("Standard car loan terms and conditions apply.")
    end

    it "creates a setting with insurance waiver text" do
      setting = create(:dealership_terms_setting, :with_insurance_waiver_text)
      expect(setting.insurance_waiver_text).to eq("Standard insurance waiver terms and conditions apply.")
    end
  end
end
