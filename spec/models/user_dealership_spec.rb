require 'rails_helper'

RSpec.describe UserDealership, type: :model do
  describe 'validations' do
    subject { build(:user_dealership) }

    it { should validate_presence_of(:role) }
    it { should validate_uniqueness_of(:user_id).scoped_to(:dealership_id) }
  end

  describe 'associations' do
    it { should belong_to(:user) }
    it { should belong_to(:dealership) }
  end

  describe 'enums' do
    it do
      should define_enum_for(:role)
        .with_values(dealership_admin: 0, sales_person: 1,  staff: 2)
    end
  end

  describe 'uniqueness validation' do
    let(:user) { create(:user) }
    let(:dealership) { create(:dealership) }

    it 'prevents duplicate user-dealership combinations' do
      create(:user_dealership, user: user, dealership: dealership)
      duplicate = build(:user_dealership, user: user, dealership: dealership)

      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:user_id]).to include('has already been taken')
    end
  end
end
