require 'rails_helper'

RSpec.describe DealershipVehicle, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:dealership) }
    it { is_expected.to have_many(:drives).dependent(:destroy) }
    it { is_expected.to have_one(:last_damage_report).class_name("DamageReport").optional.dependent(:destroy) }
    it { is_expected.to have_one(:last_known_location).class_name("GpsLocation").dependent(:destroy) }
    it { is_expected.to belong_to(:brand).optional }
  end

  describe 'validations' do
    it { is_expected.to validate_presence_of(:make) }
    it { is_expected.to validate_presence_of(:model) }
    it { is_expected.to validate_length_of(:rego).is_at_most(20) }
    it { is_expected.to validate_length_of(:vin).is_at_most(17) }

    it { should validate_numericality_of(:build_year).is_greater_than(1900) }
    it { should validate_numericality_of(:build_year).is_less_than_or_equal_to(Date.current.year + 1) }
    it { should validate_presence_of(:build_year) }
  end

  describe 'enums' do
    it { is_expected.to define_enum_for(:status).with_values(available: 0, in_use: 1, out_of_service: 2, deleted: 3, sold: 4, enquiry: 5).backed_by_column_of_type(:integer) }
    it { is_expected.to define_enum_for(:vehicle_type).with_values(new_vehicle: 0, demo: 1, old: 2).backed_by_column_of_type(:integer) }
  end

   describe 'scopes' do
    let(:dealership) { create(:dealership) }
    let!(:available_vehicle) { create(:vehicle, dealership: dealership, status: :available) }
    let!(:maintenance_vehicle) { create(:vehicle, dealership: dealership, status: :out_of_service) }

    describe '.available_for_test_drive' do
      it 'returns only available vehicles' do
        expect(DealershipVehicle.available_for_test_drive).to include(available_vehicle)
        expect(DealershipVehicle.available_for_test_drive).not_to include(maintenance_vehicle)
      end
    end
  end
  describe '#display_name' do
    it 'returns formatted vehicle name' do
      vehicle = build(:vehicle, build_year: 2023, make: 'Toyota', model: 'Camry')
      expect(vehicle.display_name).to eq('2023 Toyota Camry')
    end
  end

  describe 'photo attachments' do
    it 'is valid with multiple image files' do
      vehicle = build(:vehicle, :with_photos)
      expect(vehicle).to be_valid
    end

    it 'is invalid with an image larger than 5MB' do
      vehicle = build(:vehicle, :with_photos)
      vehicle.photos.attach(
        io: Rails.root.join("spec/fixtures/files/large_car_image.png").open,
        filename: "large_car_image.png",
        content_type: "image/png"
      )
      expect(vehicle).not_to be_valid
      expect(vehicle.errors[:photos]).to include(a_string_matching(/file size must be less than 5 MB/))
    end

    it 'is invalid with an invalid file type' do
      vehicle = build(:vehicle)
      vehicle.photos.attach(
        {
          io: StringIO.new("some text content"),
          filename: "test.txt",
          content_type: "text/plain"
        }
      )
      expect(vehicle).not_to be_valid
      expect(vehicle.errors[:photos]).to include("has an invalid content type (authorized content types are PNG, JPG)")
    end

    it 'is invalid with more than 5 photos' do
      vehicle = build(:vehicle)
      6.times do |i|
        vehicle.photos.attach(
          {
            io: StringIO.new("image content"),
            filename: "photo_#{i}.jpg",
            content_type: "image/jpeg"
          }
        )
      end
      expect(vehicle).not_to be_valid
      expect(vehicle.errors[:photos]).to include("too many files attached (maximum is 5 files, got 6)")
    end
  end

  describe 'build_year validation' do
    it 'accepts current year' do
      vehicle = build(:vehicle, build_year: Date.current.year)
      expect(vehicle).to be_valid
    end

    it 'accepts next year' do
      vehicle = build(:vehicle, build_year: Date.current.year + 1)
      expect(vehicle).to be_valid
    end

    it 'rejects years too far in future' do
      vehicle = build(:vehicle, build_year: Date.current.year + 2)
      expect(vehicle).not_to be_valid
    end

    it 'rejects years too far in past' do
      vehicle = build(:vehicle, build_year: 1899)
      expect(vehicle).not_to be_valid
    end
  end

  describe '.not_deleted' do
    let(:dealership) { create(:dealership) }
    let!(:deleted_vehicle) { create(:vehicle, dealership: dealership, status: :deleted) }
    let!(:active_vehicle) { create(:vehicle, dealership: dealership, status: :available) }

    it 'returns vehicles that are not deleted' do
      expect(DealershipVehicle.not_deleted).to include(active_vehicle)
      expect(DealershipVehicle.not_deleted).not_to include(deleted_vehicle)
    end
  end

  describe '.ordered_by_name' do
    let(:dealership) { create(:dealership) }
    let!(:vehicle1) { create(:vehicle, dealership: dealership, make: 'Toyota', model: 'Camry', build_year: 2023) }
    let!(:vehicle2) { create(:vehicle, dealership: dealership, make: 'Honda', model: 'Civic', build_year: 2022) }
    let!(:vehicle3) { create(:vehicle, dealership: dealership, make: 'Toyota', model: 'Corolla', build_year: 2024) }

    it 'orders vehicles by make, model, then build_year' do
      ordered_vehicles = dealership.vehicles.ordered_by_name
      expect(ordered_vehicles.to_a).to eq([ vehicle2, vehicle1, vehicle3 ])
    end
  end

  describe '.search_by_term' do
    let(:dealership) { create(:dealership) }
    let!(:vehicle1) { create(:vehicle, dealership: dealership, make: 'Toyota', model: 'Camry', build_year: 2023, stock_number: 'T001', rego: 'ABC123', color: 'Red') }
    let!(:vehicle2) { create(:vehicle, dealership: dealership, make: 'Honda', model: 'Civic', build_year: 2022, stock_number: 'H002', rego: 'DEF456', color: 'Blue') }
    let!(:vehicle3) { create(:vehicle, dealership: dealership, make: 'Ford', model: 'Focus', build_year: 2021, stock_number: 'F003', rego: 'GHI789', color: 'Green') }

    it 'finds vehicles by stock_number' do
      results = DealershipVehicle.search_by_term('T00')
      expect(results).to include(vehicle1)
      expect(results).not_to include(vehicle2, vehicle3)
    end

    it 'finds vehicles by rego' do
      results = DealershipVehicle.search_by_term('DEF4')
      expect(results).to include(vehicle2)
      expect(results).not_to include(vehicle1, vehicle3)
    end

    it 'finds vehicles by make' do
      results = DealershipVehicle.search_by_term('Toy')
      expect(results).to include(vehicle1)
      expect(results).not_to include(vehicle2, vehicle3)
    end

    it 'finds vehicles by model' do
      results = DealershipVehicle.search_by_term('Civic')
      expect(results).to include(vehicle2)
      expect(results).not_to include(vehicle1, vehicle3)
    end

    it 'finds vehicles by color' do
      results = DealershipVehicle.search_by_term('Gre')
      expect(results).to include(vehicle3)
      expect(results).not_to include(vehicle1, vehicle2)
    end

    it 'finds vehicles by build_year' do
      results = DealershipVehicle.search_by_term('023')
      expect(results).to include(vehicle1)
      expect(results).not_to include(vehicle2, vehicle3)
    end

    it 'is case insensitive' do
      results = DealershipVehicle.search_by_term('YOTA')
      expect(results).to include(vehicle1)
    end

    it 'returns none for blank term' do
      expect(DealershipVehicle.search_by_term('')).to be_empty
      expect(DealershipVehicle.search_by_term(nil)).to be_empty
      expect(DealershipVehicle.search_by_term('   ')).to be_empty
    end

    it 'returns none for terms less than 3 characters' do
      expect(DealershipVehicle.search_by_term('To')).to be_empty
    end

    it 'returns none for terms more than 20 characters' do
      long_term = 'a' * 21
      expect(DealershipVehicle.search_by_term(long_term)).to be_empty
    end

    it 'respects the limit parameter' do
      # Create more vehicles to test limit
      create_list(:vehicle, 25, dealership: dealership, make: 'Test')
      results = DealershipVehicle.search_by_term('test', limit: 10)
      expect(results.count).to eq(10)
    end

    it 'orders results by make, model, build_year' do
      create(:vehicle, dealership: dealership, make: 'Audi', model: 'A4', build_year: 2023)
      create(:vehicle, dealership: dealership, make: 'Audi', model: 'A3', build_year: 2022)

      results = DealershipVehicle.search_by_term('audi')
      expect(results.first.model).to eq('A3')
      expect(results.last.model).to eq('A4')
    end
  end

  describe '#display_name with missing values' do
    it 'handles missing build_year gracefully' do
      vehicle = build(:vehicle, build_year: nil, make: 'Toyota', model: 'Camry')
      expect(vehicle.display_name).to eq('Toyota Camry')
    end

    it 'handles missing make gracefully' do
      vehicle = build(:vehicle, build_year: 2023, make: nil, model: 'Camry')
      expect(vehicle.display_name).to eq('2023 Camry')
    end

    it 'handles missing model gracefully' do
      vehicle = build(:vehicle, build_year: 2023, make: 'Toyota', model: nil)
      expect(vehicle.display_name).to eq('2023 Toyota')
    end

    it 'handles all missing values gracefully' do
      vehicle = build(:vehicle, build_year: nil, make: nil, model: nil)
      expect(vehicle.display_name).to eq('')
    end
  end

  describe '#currently_on_test_drive?' do
    let(:dealership) { create(:dealership) }
    let(:vehicle) { create(:vehicle, dealership: dealership) }

    context 'when vehicle has active drives' do
      before do
        create(:drive, vehicle: vehicle, status: :in_progress)
      end

      it 'returns true' do
        expect(vehicle.currently_on_test_drive?).to be true
      end
    end

    context 'when vehicle has no active drives' do
      before do
        create(:drive, vehicle: vehicle, status: :completed)
      end

      it 'returns false' do
        expect(vehicle.currently_on_test_drive?).to be false
      end
    end

    context 'when vehicle has no drives' do
      it 'returns false' do
        expect(vehicle.currently_on_test_drive?).to be false
      end
    end
  end

  describe '#to_param' do
    it 'returns the uuid' do
      vehicle = create(:vehicle)
      expect(vehicle.to_param).to eq(vehicle.uuid)
    end
  end

  describe 'photo validations edge cases' do
    it 'is valid with exactly 5 photos' do
      vehicle = build(:vehicle)
      5.times do |i|
        vehicle.photos.attach(
          {
            io: StringIO.new("image content"),
            filename: "photo_#{i}.jpg",
            content_type: "image/jpeg"
          }
        )
      end
      expect(vehicle).to be_valid
    end

    it 'is valid with no photos' do
      vehicle = build(:vehicle)
      expect(vehicle).to be_valid
    end

    it 'accepts JPEG content type' do
      vehicle = build(:vehicle)
      vehicle.photos.attach(
        {
          io: StringIO.new("image content"),
          filename: "photo.jpg",
          content_type: "image/jpeg"
        }
      )
      expect(vehicle).to be_valid
    end
  end

  describe 'vin and rego validations' do
    it 'accepts valid VIN length' do
      vehicle = build(:vehicle, vin: '12345678901234567')
      expect(vehicle).to be_valid
    end

    it 'accepts valid rego length' do
      vehicle = build(:vehicle, rego: 'ABC123')
      expect(vehicle).to be_valid
    end

    it 'accepts nil VIN' do
      vehicle = build(:vehicle, vin: nil)
      expect(vehicle).to be_valid
    end

    it 'accepts nil rego' do
      vehicle = build(:vehicle, rego: nil)
      expect(vehicle).to be_valid
    end
  end
end
