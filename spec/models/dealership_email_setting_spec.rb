require "rails_helper"

RSpec.describe DealershipEmailSetting, type: :model do
  describe "associations" do
    it { should belong_to(:dealership) }
  end

  describe "validations" do
    subject { build(:dealership_email_setting) }

    it { should validate_numericality_of(:level1_odometer_warning_km).only_integer.allow_nil.is_greater_than(0) }
    it { should validate_numericality_of(:level2_odometer_warning_km).only_integer.allow_nil.is_greater_than(0) }
    it { should allow_value("<EMAIL>").for(:level1_odometer_warning_email) }
    it { should allow_value("<EMAIL>").for(:level2_odometer_warning_email) }
    it { should allow_value("<EMAIL>").for(:email_from_address) }
    it { should_not allow_value("invalid-email").for(:level1_odometer_warning_email) }
    it { should_not allow_value("invalid-email").for(:level2_odometer_warning_email) }
    it { should_not allow_value("invalid-email").for(:email_from_address) }
  end

  describe "default values" do
    let(:setting) { create(:dealership_email_setting) }

    it "sets default values" do
      expect(setting.send_email_for_bookings).to eq("none")
      expect(setting.email_from_address).to be_nil
      expect(setting.email_display_name).to be_nil
      expect(setting.send_test_drive_review_email).to be false
      expect(setting.send_test_drive_terms_email).to be false
      expect(setting.send_loan_terms_email).to be false
      expect(setting.loan_review_email_enabled).to be false
    end
  end

  describe "enums" do
    it { should define_enum_for(:send_email_for_bookings).with_values(none: 0, test_drive: 1, car_loan: 2, both: 3).with_prefix }
  end

  describe "factory" do
    it "has a valid factory" do
      expect(build(:dealership_email_setting)).to be_valid
    end

    it "creates a setting with send email for bookings none" do
      setting = create(:dealership_email_setting, :with_send_email_for_bookings_none)
      expect(setting.send_email_for_bookings).to eq("none")
    end

    it "creates a setting with send email for bookings test drive" do
      setting = create(:dealership_email_setting, :with_send_email_for_bookings_test_drive)
      expect(setting.send_email_for_bookings).to eq("test_drive")
    end

    it "creates a setting with send email for bookings car loan" do
      setting = create(:dealership_email_setting, :with_send_email_for_bookings_car_loan)
      expect(setting.send_email_for_bookings).to eq("car_loan")
    end

    it "creates a setting with send email for bookings both" do
      setting = create(:dealership_email_setting, :with_send_email_for_bookings_both)
      expect(setting.send_email_for_bookings).to eq("both")
    end

    it "creates a setting with level1 odometer settings" do
      setting = create(:dealership_email_setting, :level1_odometer_settings)
      expect(setting.level1_odometer_warning_km).to eq(10000)
      expect(setting.level1_odometer_warning_email).to eq("<EMAIL>")
    end

    it "creates a setting with level2 odometer settings" do
      setting = create(:dealership_email_setting, :level2_odometer_settings)
      expect(setting.level2_odometer_warning_km).to eq(20000)
      expect(setting.level2_odometer_warning_email).to eq("<EMAIL>")
    end

    it "creates a setting with email from address" do
      setting = create(:dealership_email_setting, :with_email_from_address)
      expect(setting.email_from_address).to eq("<EMAIL>")
    end

    it "creates a setting with email display name" do
      setting = create(:dealership_email_setting, :with_email_display_name)
      expect(setting.email_display_name).to eq("Test Dealership")
    end
  end
end
