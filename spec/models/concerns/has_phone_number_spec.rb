require "rails_helper"

RSpec.describe HasPhoneNumber do
  before(:all) do
    ActiveRecord::Base.connection.create_table :model_with_country_phone_required do |t|
      t.string :phone, null: false
      t.string :country, null: false
      t.timestamps
    end

    ActiveRecord::Base.connection.create_table :model_with_country_phone_optional do |t|
      t.string :phone
      t.string :country
      t.timestamps
    end

    ActiveRecord::Base.connection.create_table :model_without_country_phone_required do |t|
      t.string :phone, null: false
      t.timestamps
    end

    ActiveRecord::Base.connection.create_table :model_without_country_phone_optional do |t|
      t.string :phone
      t.timestamps
    end
  end

  after(:all) do
    ActiveRecord::Base.connection.drop_table :model_with_country_phone_required
    ActiveRecord::Base.connection.drop_table :model_with_country_phone_optional
    ActiveRecord::Base.connection.drop_table :model_without_country_phone_required
    ActiveRecord::Base.connection.drop_table :model_without_country_phone_optional
  end

  let(:has_country_phone_required_model) do
    Class.new(ApplicationRecord) do
      def self.name
        "ModelWithCountryPhoneRequired"
      end
      self.table_name = "model_with_country_phone_required"
      include HasPhoneNumber
      has_phone_number(field_name: :phone, required: true)
    end
  end

  let(:has_country_phone_optional_model) do
    Class.new(ApplicationRecord) do
      def self.name
        "ModelWithCountryPhoneOptional"
      end
      self.table_name = "model_with_country_phone_optional"
      include HasPhoneNumber
      has_phone_number(field_name: :phone, required: false)
    end
  end

  let(:no_country_phone_required_model) do
    Class.new(ApplicationRecord) do
      def self.name
        "ModelWithoutCountryPhoneRequired"
      end
      self.table_name = "model_without_country_phone_required"
      include HasPhoneNumber
      has_phone_number(field_name: :phone, required: true)
    end
  end

  let(:no_country_phone_optional_model) do
    Class.new(ApplicationRecord) do
      def self.name
        "ModelWithoutCountryPhoneOptional"
      end
      self.table_name = "model_without_country_phone_optional"
      include HasPhoneNumber
      has_phone_number(field_name: :phone, required: false)
    end
  end

  describe "model with country field and has phone required" do
    let(:model) { has_country_phone_required_model.new(country: "AU") }

    describe "phone=" do
      it "formats AU phone numbers correctly" do
        model.phone = "0415555267"
        expect(model.phone).to eq("+61415555267")
      end

      it "handles international numbers" do
        model.phone = "+44 20 7123 4567"
        expect(model.phone).to eq("+442071234567")
      end

      it "handles nil values" do
        model.phone = nil
        expect(model.phone).to be_nil
      end

      it "handles empty strings" do
        model.phone = ""
        expect(model.phone).to be_nil
      end
    end

    describe "formatted_phone" do
      it "returns national format for AU numbers" do
        model.phone = "+61415555267"
        expect(model.formatted_phone).to eq("0415 555 267")
      end

      it "returns national format for UK numbers" do
        model.phone = "+442071234567"
        expect(model.formatted_phone).to eq("020 7123 4567")
      end

      it "returns nil for nil phone numbers" do
        model.phone = nil
        expect(model.formatted_phone).to be_nil
      end
    end

    describe "validations" do
      it "does not accept empty phone numbers" do
        model.phone = ""
        expect(model).not_to be_valid
        expect(model.errors[:phone]).to include("can't be blank")
      end

      it "does not accept nil values" do
        model.phone = nil
        expect(model.formatted_phone).to be_nil
      end

      it "does not accept invalid phone numbers" do
        model.phone = "234234234234"
        expect(model).not_to be_valid
        expect(model.errors[:phone]).to include("is invalid")
      end
    end
  end

  describe "model with country field and has phone optional" do
    let(:model) { has_country_phone_optional_model.new(country: "AU") }

    describe "phone=" do
      it "formats AU phone numbers correctly" do
        model.phone = "0415555267"
        expect(model.phone).to eq("+61415555267")
      end
    end

    describe "formatted_phone" do
      it "returns national format for AU numbers" do
        model.phone = "+61415555267"
        expect(model.formatted_phone).to eq("0415 555 267")
      end
    end

    describe "validations" do
      it "accepts empty phone numbers" do
        model.phone = ""
        expect(model).to be_valid
        expect(model.phone).to be_nil
      end

      it "accepts nil values" do
        model.phone = nil
        expect(model).to be_valid
        expect(model.phone).to be_nil
      end

      it "does not accept invalid phone numbers" do
        model.phone = "234234234234"
        expect(model).not_to be_valid
        expect(model.errors[:phone]).to include("is invalid")
      end
    end
  end

  describe "model without country field and has phone required" do
    let(:model) { no_country_phone_required_model.new }

    describe "phone=" do
      it "formats phone numbers correctly (AU)" do
        model.phone = "0415555267"
        expect(model.phone).to eq("+61415555267")
      end

      it "formats international numbers correctly (UK)" do
        model.phone = "+44 20 7123 4567"
        expect(model.phone).to eq("+442071234567")
      end
    end

    describe "formatted_phone" do
      it "returns national format for AU numbers" do
        model.phone = "+61415555267"
        expect(model.formatted_phone).to eq("0415 555 267")
      end

      it "returns national format for UK numbers" do
        model.phone = "+442071234567"
        expect(model.formatted_phone).to eq("020 7123 4567")
      end

      it "returns nil for nil phone numbers" do
        model.phone = nil
        expect(model.formatted_phone).to be_nil
      end
    end

    describe "validations" do
      it "does not accept empty phone numbers" do
        model.phone = ""
        expect(model).not_to be_valid
        expect(model.errors[:phone]).to include("can't be blank")
      end

      it "does not accept nil values" do
        model.phone = nil
        expect(model).not_to be_valid
        expect(model.errors[:phone]).to include("can't be blank")
      end

      it "does not accept invalid phone numbers" do
        model.phone = "234234234234"
        expect(model).not_to be_valid
        expect(model.errors[:phone]).to include("is invalid")
      end
    end
  end

  describe "model without country field and has phone optional" do
    let(:model) { no_country_phone_optional_model.new }

    describe "phone=" do
      it "formats phone numbers correctly (AU)" do
        model.phone = "0415555267"
        expect(model.phone).to eq("+61415555267")
      end

      it "formats international numbers correctly (UK)" do
        model.phone = "+44 20 7123 4567"
        expect(model.phone).to eq("+442071234567")
      end
    end

    describe "formatted_phone" do
      it "returns national format for AU numbers" do
        model.phone = "+61415555267"
        expect(model.formatted_phone).to eq("0415 555 267")
      end

      it "returns national format for UK numbers" do
        model.phone = "+442071234567"
        expect(model.formatted_phone).to eq("020 7123 4567")
      end

      it "returns nil for nil phone numbers" do
        model.phone = nil
        expect(model.formatted_phone).to be_nil
      end
    end

    describe "validations" do
      it "accepts empty phone numbers" do
        model.phone = ""
        expect(model).to be_valid
        expect(model.phone).to be_nil
      end

      it "accepts nil values" do
        model.phone = nil
        expect(model).to be_valid
        expect(model.phone).to be_nil
      end

      it "does not accept invalid phone numbers" do
        model.phone = "234234234234"
        expect(model).not_to be_valid
        expect(model.errors[:phone]).to include("is invalid")
      end
    end
  end
end
