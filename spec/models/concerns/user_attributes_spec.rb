require 'rails_helper'

RSpec.describe UserAttributes, type: :model do
  describe '#name' do
    it 'returns full name when first and last names are present' do
      user = build(:user, first_name: "<PERSON>", last_name: "<PERSON>")
      expect(user.name).to eq("<PERSON>")
    end

    it 'returns first name when last name is nil' do
      user = build(:user, first_name: "<PERSON>", last_name: nil)
      expect(user.name).to eq("<PERSON>")
    end

    it 'returns empty string when both names are nil' do
      user = build(:user, first_name: nil, last_name: nil)
      expect(user.name).to eq("")
    end
  end

  describe '#full_name' do
    it 'returns the same as name method' do
      user = build(:user, first_name: "<PERSON>", last_name: "<PERSON>")
      expect(user.full_name).to eq(user.name)
      expect(user.full_name).to eq("<PERSON>")
    end

    it 'handles nil last name' do
      user = build(:user, first_name: "<PERSON>", last_name: nil)
      expect(user.full_name).to eq("<PERSON>")
    end
  end

  describe '#masked_destination' do
    it 'masks email when preferred_2fa is email' do
      user = build(:user, email: "<EMAIL>", preferred_2fa: "email")
      expect(user.masked_destination).to eq("a***<EMAIL>")
    end

    it 'masks phone when preferred_2fa is sms' do
      user = build(:user, phone: "+919876543210", preferred_2fa: "sms")
      expect(user.masked_destination).to eq("*********3210")
    end

    it 'returns "totp" when preferred_2fa is totp' do
      user = build(:user, preferred_2fa: :totp)
      expect(user.masked_destination).to eq("totp")
    end
  end
end
