require "rails_helper"

RSpec.describe HasUuid do
  before(:all) do
    ActiveRecord::Base.connection.create_table :mock_model do |t|
      t.string :uuid
      t.timestamps
    end
    ActiveRecord::Base.connection.add_index :mock_model, :uuid, unique: true
  end

  after(:all) do
    ActiveRecord::Base.connection.drop_table :mock_model
  end

  let(:mock_model_class) do
    Class.new(ApplicationRecord) do
      def self.name
        "MockModel"
      end
      self.table_name = "mock_model"
      include HasUuid
    end
  end

  describe "behavior" do
    it "takes care of setting a valid uuid" do
      model_class_without_uuid_concern = Class.new(ApplicationRecord) do
        def self.name
          "ValidationTestModel"
        end
        self.table_name = "mock_model"
        validates :uuid, presence: true
      end

      model = model_class_without_uuid_concern.new
      expect(model).not_to be_valid
      expect(model.uuid).to be_nil
      expect(model.errors[:uuid]).to include("can't be blank")

      # Include the HasUuid concern
      model_class_without_uuid_concern.include(HasUuid)
      model = model_class_without_uuid_concern.new
      # Now the model will be valid as the concern will set a valid uuid
      expect(model).to be_valid
      expect(model.uuid).to be_present
    end
  end

  describe "validations" do
    it "validates uniqueness of uuid" do
      existing_model = mock_model_class.create!
      new_model = mock_model_class.new(uuid: existing_model.uuid)
      expect(new_model).not_to be_valid
      expect(new_model.errors[:uuid]).to include("has already been taken")
    end
  end

  describe "uuid generation" do
    it "generates a uuid before validation on create" do
      model = mock_model_class.new
      expect(model.uuid).to be_nil
      model.valid?
      expect(model.uuid).to be_present
      expect(model.uuid).to match(/\A[\da-f]{8}-([\da-f]{4}-){3}[\da-f]{12}\z/i)
    end

    it "does not change existing uuid" do
      model = mock_model_class.create!
      original_uuid = model.uuid
      model.valid?
      expect(model.uuid).to eq(original_uuid)
    end

    it "generates unique uuids for multiple records" do
      model1 = mock_model_class.create!
      model2 = mock_model_class.create!
      expect(model1.uuid).not_to eq(model2.uuid)
    end

    it "handles uuid collisions by generating a new uuid" do
      # Create a model with a specific uuid
      existing_model = mock_model_class.create!
      original_uuid = existing_model.uuid

      # Create a new model and force a collision
      new_model = mock_model_class.new
      allow(SecureRandom).to receive(:uuid).and_return(original_uuid, "new-uuid-123")
      new_model.valid? # This will trigger the before_validation callback

      # Verify that the new model got a different uuid
      expect(new_model.uuid).not_to eq(original_uuid)
      expect(new_model.uuid).to eq("new-uuid-123")
    end
  end

  describe "uuid format" do
    it "generates uuids in the correct format" do
      model = mock_model_class.create!
      expect(model.uuid).to match(/\A[\da-f]{8}-([\da-f]{4}-){3}[\da-f]{12}\z/i)
    end

    it "generates uuids with the correct length" do
      model = mock_model_class.create!
      expect(model.uuid.length).to eq(36) # 32 hex chars + 4 hyphens
    end
  end

  describe "database constraints" do
    it "enforces uuid uniqueness at the database level" do
      # Create first record
      model1 = mock_model_class.create!

      # Try to create second record with same UUID
      model2 = mock_model_class.new(uuid: model1.uuid)

      expect {
        model2.save(validate: false)
      }.to raise_error(ActiveRecord::RecordNotUnique)
    end
  end
end
