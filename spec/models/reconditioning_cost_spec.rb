require 'rails_helper'

RSpec.describe Vehicle::ReconditioningCost, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:vehicle_condition).class_name('Vehicle::VehicleCondition') }
  end

  describe 'enums' do
    it do
      should define_enum_for(:cost_type).
        with_values(
          paint_and_panel: 0,
          wheels_and_tyres: 1,
          windscreen: 2,
          mechanical: 3,
          registration: 4,
          other: 5
        )
    end
  end

  describe 'validations' do
    subject { FactoryBot.build(:reconditioning_cost) }

    it { is_expected.to validate_presence_of(:cost_type) }
    it { is_expected.to validate_presence_of(:amount) }
    it { is_expected.to validate_numericality_of(:amount).is_greater_than_or_equal_to(0) }
    it { is_expected.to validate_presence_of(:currency) }

    it 'validates uniqueness of cost_type scoped to vehicle_condition_id' do
      vehicle_condition = create(:vehicle_condition)
      create(:reconditioning_cost, vehicle_condition: vehicle_condition, cost_type: :paint_and_panel)

      duplicate = build(:reconditioning_cost, vehicle_condition: vehicle_condition, cost_type: :paint_and_panel)
      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:cost_type]).to include('has already been taken')
    end

    context 'uniqueness validation' do
      let(:vehicle_condition) { create(:vehicle_condition) }
      let!(:existing_cost) { create(:reconditioning_cost, vehicle_condition: vehicle_condition, cost_type: :paint_and_panel, amount: 500, currency: 'AUD') }

      it 'prevents duplicate cost types for the same vehicle condition' do
        duplicate_cost = build(:reconditioning_cost, vehicle_condition: vehicle_condition, cost_type: :paint_and_panel, amount: 750, currency: 'AUD')
        expect(duplicate_cost).not_to be_valid
        expect(duplicate_cost.errors[:cost_type]).to include('has already been taken')
      end

      it 'allows same cost type for different vehicle conditions' do
        other_vehicle_condition = create(:vehicle_condition)
        other_cost = build(:reconditioning_cost, vehicle_condition: other_vehicle_condition, cost_type: :paint_and_panel, amount: 300, currency: 'AUD')
        expect(other_cost).to be_valid
      end

      it 'allows different cost types for the same vehicle condition' do
        different_cost = build(:reconditioning_cost, vehicle_condition: vehicle_condition, cost_type: :mechanical, amount: 200, currency: 'AUD')
        expect(different_cost).to be_valid
      end
    end
  end
end
