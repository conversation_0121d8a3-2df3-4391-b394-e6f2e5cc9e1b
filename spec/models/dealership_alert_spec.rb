require "rails_helper"

RSpec.describe DealershipAlert, type: :model do
  describe "associations" do
    it { is_expected.to belong_to(:dealership) }
  end

  describe "validations" do
    subject { build(:dealership_alert) }

    it { is_expected.to validate_presence_of(:alert_type) }
    it { is_expected.to validate_presence_of(:threshold) }
    it { is_expected.to validate_presence_of(:emails) }
    it { is_expected.to validate_numericality_of(:threshold).is_greater_than(0) }
    it { is_expected.to allow_value("<EMAIL>,<EMAIL>").for(:emails) }
    it { is_expected.not_to allow_value("<EMAIL>,invalid-email").for(:emails) }
    it { is_expected.not_to allow_value(",").for(:emails) }
    it { is_expected.to define_enum_for(:alert_type).with_values(info: 0, warning: 1, error: 2).backed_by_column_of_type(:integer) }
    it { is_expected.to define_enum_for(:status).with_values(active: 0, inactive: 1).backed_by_column_of_type(:integer) }
  end

  describe "factory" do
    it "has a valid factory" do
      alert = build(:dealership_alert, dealership: create(:dealership))
      expect(alert).to be_valid
    end

    it "generates unique emails" do
      alert1 = create(:dealership_alert)
      alert2 = create(:dealership_alert)
      expect(alert1.emails).not_to eq(alert2.emails)
    end
  end

  describe "traits" do
    it "creates a warning alert" do
      alert = create(:dealership_alert, :warning)
      expect(alert.alert_type).to eq("warning")
    end

    it "creates an error alert" do
      alert = create(:dealership_alert, :error)
      expect(alert.alert_type).to eq("error")
    end

    it "creates an info alert by default" do
      alert = create(:dealership_alert)
      expect(alert.alert_type).to eq("info")
    end

    it "creates an inactive alert" do
      alert = create(:dealership_alert, :inactive)
      expect(alert.status).to eq("inactive")
    end

    it "is invalid with invalid emails" do
      alert = build(:dealership_alert, :with_invalid_emails)
      expect(alert).not_to be_valid
      expect(alert.errors[:emails]).to include("random_string is not a valid email")
    end
  end

  describe "email validation" do
    it "adds error for each invalid email" do
      alert = build(:dealership_alert, emails: "<EMAIL>,random@,random_string")
      expect(alert).not_to be_valid
      expect(alert.errors[:emails]).to include("random@ is not a valid email")
      expect(alert.errors[:emails]).to include("random_string is not a valid email")
    end

    it "validates email length" do
      long_email = "a" * 256 + "@example.com"
      alert = build(:dealership_alert, emails: "<EMAIL>,#{long_email}")
      expect(alert).not_to be_valid
      expect(alert.errors[:emails]).to include("#{long_email} must be under 255 characters")
    end

    it "handles whitespace in email list" do
      alert = build(:dealership_alert, emails: "  <EMAIL>  ,  <EMAIL>  ")
      expect(alert).to be_valid
    end
  end
end
