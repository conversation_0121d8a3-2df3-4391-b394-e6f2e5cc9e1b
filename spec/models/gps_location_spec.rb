require 'rails_helper'

RSpec.describe GpsLocation, type: :model do
  describe "associations" do
    it { is_expected.to belong_to(:trackable) }
  end

  describe "validations" do
    it { is_expected.to validate_presence_of(:latitude) }
    it { is_expected.to validate_presence_of(:longitude) }
  end

  describe "sequence numbers" do
    let(:vehicle) { create(:vehicle) }
    let(:last_known_location) { create(:gps_location, trackable: vehicle) }

    let(:drive) { create(:drive) }
    let(:drive_location1) { create(:gps_location, trackable: drive) }
    let(:drive_location2) { create(:gps_location, trackable: drive) }
    let(:drive_location3) { create(:gps_location, trackable: drive) }

    it "Saves locations in the correct order" do
      last_known_location
      expect(vehicle.last_known_location).to eq(last_known_location)

      drive_location1
      drive_location2
      drive_location3
      expect(drive.waypoints.map(&:id)).to eq([ drive_location1.id, drive_location2.id, drive_location3.id ])
    end
  end
end
