require "rails_helper"

RSpec.describe PasswordService do
  let(:user) { create(:user, reset_code_resend_count: 0) }
  let(:service) { described_class.new(user) }

  describe "#generate_and_send_reset_code" do
    it "generates a 6-digit code, stores it, increments count, and sends email" do
      mailer = double("Mailer", deliver_later: true)
      expect(UserMailer).to receive(:send_otp)
        .with(user, kind_of(String), :forgot_password)
        .and_return(mailer)

      reset_code = service.generate_and_send_reset_code

      user.reload
      expect(reset_code).to match(/\A\d{6}\z/)
      expect(user.password_reset_code.to_s).to eq(reset_code)
      expect(user.reset_code_generated_at).to be_within(1.second).of(Time.current)
      expect(user.reset_code_resend_count).to eq(1)
    end

    it "sends the code for a custom action like :change_password" do
      mailer = double("Mailer", deliver_later: true)
      expect(UserMailer).to receive(:send_otp)
        .with(user, kind_of(String), :change_password)
        .and_return(mailer)

      service.generate_and_send_reset_code(:change_password)
    end

    it "raises TooManyRequestError if limit is exceeded" do
      allow(service).to receive(:can_send_code?).and_return(false)

      expect {
        service.generate_and_send_reset_code
      }.to raise_error(Errors::TooManyRequestError)
    end
  end

  describe "#clear_code!" do
    before do
      user.update!(
        password_reset_code: "123456",
        reset_code_generated_at: Time.current,
        reset_code_resend_count: 2
      )
    end

    it "clears the reset code and timestamp and resets resend count" do
      service.clear_code!
      user.reload

      expect(user.password_reset_code).to be_nil
      expect(user.reset_code_generated_at).to be_nil
      expect(user.reset_code_resend_count).to eq(0)
    end
  end

  describe "#validate_security_code" do
    before do
      user.update!(
        password_reset_code: "123456",
        reset_code_generated_at: Time.current
      )
    end

    it "returns true for valid code within expiry time" do
      expect(service.validate_security_code("123456")).to be true
    end

    it "returns false for incorrect code" do
      expect(service.validate_security_code("000000")).to be false
    end

    it "returns false if code is expired" do
      user.update!(reset_code_generated_at: 20.minutes.ago)
      expect(service.validate_security_code("123456")).to be false
    end

    it "returns false if code or timestamp is nil" do
      user.update!(password_reset_code: nil)
      expect(service.validate_security_code("123456")).to be false

      user.update!(password_reset_code: "123456", reset_code_generated_at: nil)
      expect(service.validate_security_code("123456")).to be false
    end
  end

  describe "#request_limit_exceeded?" do
    it "returns true if time window has expired" do
      user.update!(reset_code_generated_at: 2.hours.ago, reset_code_resend_count: 5)
      expect(service.send(:can_send_code?)).to be true
    end

    it "returns true if resend count is within limit" do
      user.update!(reset_code_generated_at: Time.current, reset_code_resend_count: 2)
      expect(service.send(:can_send_code?)).to be true
    end

    it "returns false if resend count is over limit and within time window" do
      user.update!(reset_code_generated_at: Time.current, reset_code_resend_count: 5)
      expect(service.send(:can_send_code?)).to be false
    end
  end

  describe "#time_window_expired?" do
    it "returns true if no timestamp is set" do
      user.update!(reset_code_generated_at: nil)
      expect(service.send(:time_window_expired?)).to be true
    end

    it "returns true if timestamp is outside time window" do
      user.update!(reset_code_generated_at: 2.hours.ago)
      expect(service.send(:time_window_expired?)).to be true
    end

    it "returns false if timestamp is recent" do
      user.update!(reset_code_generated_at: 5.minutes.ago)
      expect(service.send(:time_window_expired?)).to be false
    end
  end
end
