require 'rails_helper'

RSpec.describe SmsService do
  describe '.deliver' do
    let(:phone_number) { '+***********' }
    let(:message) { 'Test message' }
    let(:twilio_client) { instance_double(Twilio::REST::Client) }
    let(:messages) { instance_double('Twilio::REST::Api::V2010::AccountContext::MessageList') }
    let(:account_context) { instance_double('Twilio::REST::Api::V2010::AccountContext') }

    before do
      allow(Twilio::REST::Client).to receive(:new).and_return(twilio_client)
      allow(twilio_client).to receive_message_chain(:api, :v2010, :account, :messages).and_return(messages)

      # Stub TWILIO_SENDER to be a phone number for these tests
      stub_const('TWILIO_SENDER', '+**********')
    end

    it 'sends an SMS using Twilio' do
      expect(messages).to receive(:create).with(
        from: TWILIO_SENDER,
        to: phone_number,
        body: message
      )

      described_class.deliver(to: phone_number, message: message)
    end

    context 'when <PERSON><PERSON><PERSON> raises an error' do
      let(:twilio_error) do
        response = double('Twi<PERSON>::Response')
        allow(response).to receive(:status_code).and_return(400)
        allow(response).to receive(:body).and_return({ message: 'Invalid phone number' })

        Twilio::REST::RestError.new(
          'Failed to send message',
          response
        )
      end

      before do
        allow(messages).to receive(:create).and_raise(twilio_error)
      end

      it 'raises an SmsDeliveryError' do
        expect {
          described_class.deliver(to: phone_number, message: message)
        }.to raise_error(Errors::SmsDeliveryError, /Failed to send SMS/)
      end
    end
  end
end
