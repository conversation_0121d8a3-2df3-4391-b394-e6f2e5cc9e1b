require 'rails_helper'

RSpec.describe UserInvitationService do
  let(:dealership) { create(:dealership) }
  let(:inviting_user) { create(:user) }
  let(:service) { described_class.new(inviting_user) }

  let(:valid_params) do
    {
      email: "<EMAIL>",
      first_name: "<PERSON>",
      last_name: "<PERSON><PERSON>",
      phone: "+61777777777",
      user_type: "dealership_user",
      dealership_uuid: dealership.uuid,
      role: "sales_person"
    }
  end

  describe '#invite_user' do
    context 'when inviting user is a super_admin' do
      before { inviting_user.update!(user_type: :super_admin) }

      it 'can invite another super_admin' do
        params = valid_params.merge(user_type: 'super_admin', dealership_uuid: nil, role: nil)
        expect { service.invite_user(**params) }.to change(User, :count).by(1)

        new_user = User.last
        expect(new_user.super_admin?).to be true
        expect(new_user.dealerships).to be_empty
      end

      it 'can invite staff' do
        params = valid_params.merge(user_type: 'staff', dealership_uuid: nil, role: nil)
        expect { service.invite_user(**params) }.to change(User, :count).by(1)

        new_user = User.last
        expect(new_user.staff?).to be true
        expect(new_user.dealerships).to be_empty
      end

      it 'can invite dealership_user as admin' do
        params = valid_params.merge(role: 'dealership_admin')
        expect { service.invite_user(**params) }.to change(User, :count).by(1)

        new_user = User.last
        expect(new_user.dealership_user?).to be true
        expect(new_user.dealerships).to include(dealership)
        expect(new_user.user_dealerships.first.role).to eq('dealership_admin')
      end

      it 'can invite dealership_user as sales person' do
        params = valid_params.merge(role: 'sales_person')
        expect { service.invite_user(**params) }.to change(User, :count).by(1)

        new_user = User.last
        expect(new_user.dealership_user?).to be true
        expect(new_user.dealerships).to include(dealership)
        expect(new_user.user_dealerships.first.role).to eq('sales_person')
      end
    end

    context 'when inviting user is a staff' do
      before { inviting_user.update!(user_type: :staff) }

      it 'can invite dealership_user with valid roles' do
        [ 'sales_person', 'staff', 'dealership_admin' ].each do |role|
          params = valid_params.merge(
            email: "newuser_#{role}@example.com",
            role: role
          )
          expect { service.invite_user(**params) }.to change(User, :count).by(1)

          new_user = User.last
          expect(new_user.dealership_user?).to be true
          expect(new_user.dealerships).to include(dealership)
          expect(new_user.user_dealerships.first.role).to eq(role)
        end
      end

      it 'cannot invite super_admin' do
        params = valid_params.merge(user_type: 'super_admin', dealership_uuid: nil)
        expect { service.invite_user(**params) }
          .to raise_error(Errors::ForbiddenError, "Staff members can only invite dealership users")
      end

      it 'cannot invite staff' do
        params = valid_params.merge(user_type: 'staff', dealership_uuid: nil)
        expect { service.invite_user(**params) }
          .to raise_error(Errors::ForbiddenError, "Staff members can only invite dealership users")
      end
    end

    context 'when inviting user is a dealership_user' do
      before do
        inviting_user.update!(user_type: :dealership_user)
        create(:user_dealership, user: inviting_user, dealership: dealership, role: :dealership_admin)
      end

      it 'can invite dealership_user with sales_person role' do
        params = valid_params.merge(role: 'sales_person')
        expect { service.invite_user(**params) }.to change(User, :count).by(1)

        new_user = User.last
        expect(new_user.dealership_user?).to be true
        expect(new_user.dealerships).to include(dealership)
        expect(new_user.user_dealerships.first.role).to eq('sales_person')
      end

      it 'can invite dealership_user with staff role' do
        params = valid_params.merge(role: 'staff')
        expect { service.invite_user(**params) }.to change(User, :count).by(1)

        new_user = User.last
        expect(new_user.dealership_user?).to be true
        expect(new_user.dealerships).to include(dealership)
        expect(new_user.user_dealerships.first.role).to eq('staff')
      end

      it 'cannot invite dealership_user with admin role' do
        params = valid_params.merge(role: 'dealership_admin')
        expect { service.invite_user(**params) }
          .to raise_error(Errors::ForbiddenError, "Only super admins and staff can assign dealership admin role")
      end

      it 'cannot invite user to another dealership' do
        other_dealership = create(:dealership)
        params = valid_params.merge(dealership_uuid: other_dealership.uuid)

        expect { service.invite_user(**params) }
          .to raise_error(Errors::ForbiddenError, "Users can only invite users to dealerships where they are an admin")
      end

      it 'cannot invite super_admin' do
        params = valid_params.merge(user_type: 'super_admin', dealership_uuid: nil)
        expect { service.invite_user(**params) }
          .to raise_error(Errors::ForbiddenError, "Dealership users can only invite other dealership users")
      end

      it 'cannot invite staff' do
        params = valid_params.merge(user_type: 'staff', dealership_uuid: nil)
        expect { service.invite_user(**params) }
          .to raise_error(Errors::ForbiddenError, "Dealership users can only invite other dealership users")
      end
    end

    context 'validations' do
      before { inviting_user.update!(user_type: :super_admin) }

      it 'validates user_type is valid' do
        params = valid_params.merge(user_type: 'invalid_type')
        expect { service.invite_user(**params) }
          .to raise_error(Errors::InvalidInput, "Invalid user type. Valid types are: dealership_user, staff, super_admin")
      end

      it 'requires dealership_uuid for dealership_user' do
        params = valid_params.merge(dealership_uuid: nil)
        expect { service.invite_user(**params) }
          .to raise_error(Errors::InvalidInput, "Dealership UUID is required for dealership user invitations")
      end

      it 'validates email format' do
        params = valid_params.merge(email: 'invalid_email')
        expect { service.invite_user(**params) }
          .to raise_error(ActiveRecord::RecordInvalid)
      end

      it 'validates phone format' do
        params = valid_params.merge(phone: 'invalid_phone')
        expect { service.invite_user(**params) }
          .to raise_error(ActiveRecord::RecordInvalid)
      end

      it 'validates email uniqueness' do
        existing_user = create(:user, email: valid_params[:email])
        expect { service.invite_user(**valid_params) }
          .to raise_error(ActiveRecord::RecordInvalid)
      end

      it 'validates role for dealership_user based on permission level' do
        inviting_user.update!(user_type: :dealership_user)
        create(:user_dealership, user: inviting_user, dealership: dealership, role: :dealership_admin)

        params = valid_params.merge(role: 'invalid_role')
        expect { service.invite_user(**params) }
          .to raise_error(Errors::InvalidInput, "Invalid role. Valid roles for your permission level are: sales_person, staff")
      end

      it 'allows role to be nil for non-dealership users' do
        params = valid_params.merge(user_type: 'super_admin', dealership_uuid: nil, role: nil)
        expect { service.invite_user(**params) }.to change(User, :count).by(1)
      end
    end

    context 'temporary password' do
      before { inviting_user.update!(user_type: :super_admin) }

      it 'generates a valid temporary password' do
        user = service.invite_user(**valid_params)
        temp_password = service.temp_password

        # Password should be 12 characters
        expect(temp_password.length).to eq(12)

        # Should contain required characters
        expect(temp_password).to match(/[A-Z]/) # uppercase
        expect(temp_password).to match(/[a-z]/) # lowercase
        expect(temp_password).to match(/[0-9]/) # number
        expect(temp_password).to match(/[!@#$%^&*-]/) # special character
      end

      it 'marks user as password_change_required' do
        user = service.invite_user(**valid_params)
        expect(user.password_change_required).to be true
      end
    end

    context 'email notification', type: :mailer do
      before do
        inviting_user.update!(user_type: :super_admin)
        clear_enqueued_jobs
        clear_performed_jobs
        ActionMailer::Base.deliveries.clear
      end

      it 'enqueues invitation email' do
        expect {
          service.invite_user(**valid_params)
        }.to have_enqueued_mail(UserInvitationMailer, :invitation_email)
      end

      it 'sends email with correct content' do
        perform_enqueued_jobs do
          @user = service.invite_user(**valid_params)
        end

        mail = ActionMailer::Base.deliveries.last
        expect(mail.to).to include(@user.email)
        expect(mail.subject).to eq('Welcome to Dealer Drive - Your Account Invitation')

        # Convert special characters to HTML entities for comparison
        temp_password_html = CGI.escapeHTML(service.temp_password)
        expect(mail.body.to_s).to include(temp_password_html)
      end
    end

    context 'role parameter validations' do
      before { inviting_user.update!(user_type: :super_admin) }

      it 'raises error if role is missing for dealership_user' do
        params = valid_params.merge(role: nil)
        expect { service.invite_user(**params) }
          .to raise_error(Errors::InvalidInput, /Role is required/)
      end

      it 'raises error if role is blank for dealership_user' do
        params = valid_params.merge(role: '')
        expect { service.invite_user(**params) }
          .to raise_error(Errors::InvalidInput, /Role is required/)
      end

      it 'raises error if role is invalid for dealership_user' do
        params = valid_params.merge(role: 'invalid_role')
        expect { service.invite_user(**params) }
          .to raise_error(Errors::InvalidInput, /Invalid role/)
      end

      it 'allows valid roles for super_admin' do
        %w[sales_person staff dealership_admin].each do |role|
          params = valid_params.merge(role: role, email: "superadmin_#{role}@example.com")
          expect { service.invite_user(**params) }.to change(User, :count).by(1)
        end
      end

      it 'allows valid roles for staff' do
        inviting_user.update!(user_type: :staff)
        %w[sales_person staff dealership_admin].each do |role|
          params = valid_params.merge(role: role, email: "staff_#{role}@example.com")
          expect { service.invite_user(**params) }.to change(User, :count).by(1)
        end
      end

      it 'allows only sales_person and staff for dealership_admin' do
        inviting_user.update!(user_type: :dealership_user)
        create(:user_dealership, user: inviting_user, dealership: dealership, role: :dealership_admin)
        %w[sales_person staff].each do |role|
          params = valid_params.merge(role: role, email: "dealershipadmin_#{role}@example.com")
          expect { service.invite_user(**params) }.to change(User, :count).by(1)
        end
      end

      it 'forbids dealership_admin role for dealership_user inviter' do
        inviting_user.update!(user_type: :dealership_user)
        create(:user_dealership, user: inviting_user, dealership: dealership, role: :dealership_admin)
        params = valid_params.merge(role: 'dealership_admin')
        expect { service.invite_user(**params) }
          .to raise_error(Errors::ForbiddenError, "Only super admins and staff can assign dealership admin role")
      end
    end
  end
end
