require 'rails_helper'
RSpec.describe Auth::DeviceLoginService do
  let(:user) { create(:user) }
  let(:device_id) { 'test-device-123' }
  let(:app_version) { '1.0.0' }
  let(:app_build_number) { '100' }
  let(:device_os) { 'ios' }

  describe '#call' do
    context 'when user has no existing device' do
      it 'creates a new device session and returns tokens' do
        result = described_class.call(user, device_id, app_version, app_build_number, device_os)

        expect(result).to have_key(:access_token)
        expect(result).to have_key(:refresh_token)
        expect(result).to have_key(:expires_at)
        expect(result).to have_key(:device_registration)

        device = result[:device_registration]
        expect(device.device_id).to eq(device_id)
      end
    end

    context 'when user has existing device session' do
      let!(:existing_device) { create(:device_registration, user: user, device_id: device_id) }

      it 'deactivates existing session and creates new one' do
        described_class.call(user, device_id, app_version, app_build_number, device_os)

        existing_device.reload
        expect(existing_device.logged_out_at).to be_present
        expect(existing_device.active).to be true
      end
    end
  end
end
