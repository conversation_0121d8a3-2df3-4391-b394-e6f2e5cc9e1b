require 'rails_helper'

RSpec.describe Auth::TokenService do
  let(:user) { create(:user) }
  let(:device) { create(:device_registration, user: user, device_id: 'test-device-123') }
  let(:device_id) { device.device_id }

  describe '#decode_and_verify_token' do
    context 'with valid token' do
      let(:token) do
        payload = {
          sub: user.id.to_s,
          scp: "user",
          jti: SecureRandom.uuid,
          device_id: device_id,
          exp: 1.day.from_now.to_i,
          token_type: Auth::TokenService::ACCESS_TOKEN,
          issued_at: Time.current.to_i
        }
        JWT.encode(payload, Rails.application.credentials.devise_jwt_secret_key!)
      end

      it 'returns user and device' do
        result = described_class.new.decode_and_verify_token(device_id, token)

        expect(result[0]).to eq(user)
        expect(result[1]).to eq(device)
      end

      it 'updates last activity time' do
        freeze_time do
          described_class.new.decode_and_verify_token(device_id, token)
          device.reload
          expect(device.last_activity_at).to eq(Time.current)
        end
      end
    end

    context 'with expired token' do
      let(:token) do
        payload = {
          sub: user.id.to_s,
          scp: "user",
          jti: SecureRandom.uuid,
          device_id: device_id,
          exp: 1.hour.ago.to_i,
          token_type: Auth::TokenService::ACCESS_TOKEN,
          issued_at: Time.current.to_i
        }
        JWT.encode(payload, Rails.application.credentials.devise_jwt_secret_key!)
      end

      it 'raises TokenExpired error' do
        expect {
          described_class.new.decode_and_verify_token(device_id, token)
        }.to raise_error(Errors::TokenExpired)
      end
    end

    context 'with refresh token instead of access token' do
      let(:token) do
        payload = {
          sub: user.id.to_s,
          scp: "user",
          jti: SecureRandom.uuid,
          device_id: device_id,
          exp: 1.day.from_now.to_i,
          token_type: Auth::TokenService::REFRESH_TOKEN,
          issued_at: Time.current.to_i
        }
        JWT.encode(payload, Rails.application.credentials.devise_jwt_secret_key!)
      end

      it 'raises InvalidToken error' do
        expect {
          described_class.new.decode_and_verify_token(device_id, token)
        }.to raise_error(Errors::InvalidToken, 'Invalid token type')
      end
    end

    context 'with mismatched device ID' do
      let(:token) do
         payload = {
          sub: user.id.to_s,
          scp: "user",
          jti: SecureRandom.uuid,
          device_id: 'different-device',
          exp: 1.day.from_now.to_i,
          token_type: Auth::TokenService::ACCESS_TOKEN,
          issued_at: Time.current.to_i
        }
        JWT.encode(payload, Rails.application.credentials.devise_jwt_secret_key!)
      end

      it 'raises InvalidDevice error' do
        expect {
          described_class.new.decode_and_verify_token(device_id, token)
        }.to raise_error(Errors::InvalidDevice)
      end
    end

    context 'with invalid device session' do
      let(:token) do
        payload = {
          sub: user.id.to_s,
          scp: "user",
          jti: SecureRandom.uuid,
          device_id: device_id,
          exp: 1.day.from_now.to_i,
          token_type: Auth::TokenService::ACCESS_TOKEN,
          issued_at: Time.current.to_i
        }
        JWT.encode(payload, Rails.application.credentials.devise_jwt_secret_key!)
      end

      before do
        device.update!(active: false)
      end

      it 'raises InvalidSession error' do
        expect {
          described_class.new.decode_and_verify_token(device_id, token)
        }.to raise_error(Errors::InvalidSession, 'Device session invalid')
      end
    end

    context 'with non-existent user' do
      let(:non_existent_user_id) { 99999 }
      let(:token) do
        payload = {
          sub: non_existent_user_id.to_s,
          scp: "user",
          jti: SecureRandom.uuid,
          device_id: device_id,
          exp: 1.day.from_now.to_i,
          token_type: Auth::TokenService::ACCESS_TOKEN,
          issued_at: Time.current.to_i
        }
        JWT.encode(payload, Rails.application.credentials.devise_jwt_secret_key!)
      end

      before do
        # Create a device for the non-existent user
        allow(DeviceRegistration).to receive_message_chain(:valid, :find_by)
          .with(user_id: non_existent_user_id.to_s, device_id: device_id)
          .and_return(device)
      end

      it 'raises UserNotFound error' do
        expect {
          described_class.new.decode_and_verify_token(device_id, token)
        }.to raise_error(Errors::UserNotFound, 'User not found')
      end
    end
  end
  describe '#refresh_access_token' do
    context 'with valid refresh token' do
      let(:refresh_token) do
        payload = {
          sub: user.id.to_s,
          scp: "user",
          jti: SecureRandom.uuid,
          device_id: device_id,
          exp: 1.year.from_now.to_i,
          token_type: Auth::TokenService::REFRESH_TOKEN,
          issued_at: Time.current.to_i
        }
        JWT.encode(payload, Rails.application.credentials.devise_jwt_secret_key!)
      end

      before do
        device.update!(
          refresh_token: Digest::SHA256.hexdigest(refresh_token),
          refresh_token_expires_at: 1.year.from_now
        )
      end

      it 'returns new tokens' do
        result = described_class.new(device, user).refresh_access_token!(refresh_token)

        expect(result).to have_key(:access_token)
        expect(result).to have_key(:refresh_token)
      end

      it 'updates refresh token hash in database' do
        old_refresh_token = device.refresh_token

        result = described_class.new(device, user).generate_tokens(true, refresh_token)

        device.reload
        expect(device.refresh_token).not_to eq(old_refresh_token)
        expect(device.refresh_token).to eq(Digest::SHA256.hexdigest(result[:refresh_token]))
      end
    end

    context 'with invalid refresh token' do
      let(:refresh_token) { 'invalid_token' }

      it 'raises InvalidRefreshToken error' do
        expect {
          described_class.new(device, user).refresh_access_token!(refresh_token)
        }.to raise_error(Errors::InvalidRefreshToken)
      end
    end

    context 'with expired refresh token' do
      let(:refresh_token) do
        payload = {
          sub: user.id.to_s,
          scp: "user",
          jti: SecureRandom.uuid,
          device_id: device_id,
          exp: 1.hour.ago.to_i,
          token_type: Auth::TokenService::REFRESH_TOKEN,
          issued_at: Time.current.to_i
        }
        JWT.encode(payload, Rails.application.credentials.devise_jwt_secret_key!)
      end

      before do
        device.update!(refresh_token: Digest::SHA256.hexdigest(refresh_token))
      end

      it 'raises RefreshTokenExpired error and deactivates device' do
        expect {
          described_class.new(device, user).refresh_access_token!(refresh_token)
        }.to raise_error(Errors::RefreshTokenExpired)

        device.reload
        expect(device.active).to be false
      end
    end

    context 'with malformed refresh token' do
      let(:refresh_token) { 'malformed.token.here' }

      before do
        device.update!(
          refresh_token: Digest::SHA256.hexdigest(refresh_token),
          refresh_token_expires_at: 1.year.from_now
        )
      end

      it 'raises InvalidRefreshToken error' do
        expect {
          described_class.new(device, user).refresh_access_token!(refresh_token)
        }.to raise_error(Errors::InvalidRefreshToken, 'Invalid refresh token format')
      end
    end

    context 'when an unexpected error occurs' do
      let(:refresh_token) do
        payload = {
          sub: user.id.to_s,
          scp: "user",
          jti: SecureRandom.uuid,
          device_id: device_id,
          exp: 1.year.from_now.to_i,
          token_type: Auth::TokenService::REFRESH_TOKEN,
          issued_at: Time.current.to_i
        }
        JWT.encode(payload, Rails.application.credentials.devise_jwt_secret_key!)
      end

      before do
        device.update!(
          refresh_token: Digest::SHA256.hexdigest(refresh_token),
          refresh_token_expires_at: 1.year.from_now
        )

        # Mock an unexpected error during token generation
        allow_any_instance_of(Auth::TokenService).to receive(:generate_tokens)
          .and_raise(StandardError, 'Unexpected error')
      end

      it 'raises InternalError' do
        expect {
          described_class.new(device, user).refresh_access_token!(refresh_token)
        }.to raise_error(Errors::InternalError, 'Token refresh failed: Unexpected error')
      end
    end
  end

  describe '#generate_temporary_token' do
    context 'temporary token' do
      it 'creates temporary token and returns token' do
        freeze_time do
          result = described_class.new(nil, user).generate_temporary_token
          expect(result[1]).to eq(5.minutes.from_now.to_i)

          user = described_class.new.decode_token(result[0])
          expect(user).to eq(user)
        end
      end
    end

     context 'with valid token' do
      let(:token) do
        payload = {
          sub: user.id.to_s,
          scp: "user",
          jti: SecureRandom.uuid,
          device_id: nil,
          exp: 5.minutes.from_now.to_i,
          token_type: Auth::TokenService::TEMPORARY_TOKEN,
          issued_at: Time.current.to_i
        }
        JWT.encode(payload, Rails.application.credentials.devise_jwt_secret_key!)
      end

      it 'returns user' do
        user = described_class.new.decode_token(token)
        expect(user).to eq(user)
      end
    end

    context 'with expired token' do
      let(:token) do
        payload = {
          sub: user.id.to_s,
          scp: "user",
          jti: SecureRandom.uuid,
          device_id: nil,
          exp: 5.minutes.ago.to_i,
          token_type: Auth::TokenService::TEMPORARY_TOKEN,
          issued_at: Time.current.to_i
        }
        JWT.encode(payload, Rails.application.credentials.devise_jwt_secret_key!)
      end

      it 'raises TokenExpired error' do
        expect {
          described_class.new.decode_token(token)
        }.to raise_error(Errors::TokenExpired)
      end
    end

    context 'with a new token' do
      let(:token) do
        described_class.new(nil, user).generate_temporary_token.first
      end

      it 'raises TokenExpired error' do
        expect(token).not_to eq(nil)
        new_token = described_class.new(nil, user).generate_temporary_token.first
        expect(new_token).not_to eq(token)
        expect {
          described_class.new.decode_token(token)
        }.to raise_error(Errors::InvalidToken)
      end
    end

    context 'with invalid token' do
      it 'raises TokenExpired error' do
        expect {
          described_class.new.decode_token("random_string")
        }.to raise_error(Errors::InvalidToken)
      end
    end
  end
end
