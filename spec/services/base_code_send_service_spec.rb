require 'rails_helper'

RSpec.describe BaseCodeSendService do
  # Create a concrete test class to test the abstract base class
  let(:test_service_class) do
    Class.new(BaseCodeSendService) do
      attr_accessor :test_code, :test_generated_at, :test_resend_count

      def initialize(user)
        super(user)
        @test_code = nil
        @test_generated_at = nil
        @test_resend_count = 0
      end

      def clear_code!
        @test_code = nil
        @test_generated_at = nil
        @test_resend_count = 0
      end

      def record_code!(code)
        @test_code = code
        @test_generated_at = Time.current
        @test_resend_count += 1
      end

      def resend_count
        @test_resend_count
      end

      def code_generated_at
        @test_generated_at
      end
    end
  end

  let(:user) { create(:user) }
  let(:service) { test_service_class.new(user) }

  describe '#initialize' do
    it 'sets the user' do
      expect(service.user).to eq(user)
    end
  end

  describe '#generate_code' do
    it 'generates a 6-digit code' do
      code = service.send(:generate_code)
      expect(code).to match(/\A\d{6}\z/)
      expect(code.to_i).to be_between(100_000, 999_999)
    end

    it 'generates different codes on multiple calls' do
      codes = 10.times.map { service.send(:generate_code) }
      expect(codes.uniq.length).to be > 1
    end
  end

  describe '#can_send_code?' do
    context 'when no code has been sent' do
      it 'returns true' do
        expect(service.send(:can_send_code?)).to be true
      end
    end

    context 'when under rate limit' do
      before do
        service.record_code!('123456')
        service.record_code!('234567')
      end

      it 'returns true' do
        expect(service.send(:can_send_code?)).to be true
      end
    end

    context 'when at rate limit' do
      before do
        3.times { service.record_code!('123456') }
      end

      it 'returns false' do
        expect(service.send(:can_send_code?)).to be false
      end
    end

    context 'when time window has expired' do
      before do
        3.times { service.record_code!('123456') }
        service.instance_variable_set(:@test_generated_at, 2.hours.ago)
      end

      it 'returns true and clears the code' do
        expect(service.send(:can_send_code?)).to be true
        expect(service.test_code).to be_nil
        expect(service.test_resend_count).to eq(0)
      end
    end
  end

  describe '#time_window_expired?' do
    context 'when code_generated_at is nil' do
      it 'returns true and clears code' do
        expect(service.send(:time_window_expired?)).to be true
      end
    end

    context 'when code was generated more than 1 hour ago' do
      before do
        service.record_code!('123456')
        service.instance_variable_set(:@test_generated_at, 2.hours.ago)
      end

      it 'returns true and clears code' do
        expect(service.send(:time_window_expired?)).to be true
        expect(service.test_code).to be_nil
      end
    end

    context 'when code was generated within 1 hour' do
      before do
        service.record_code!('123456')
      end

      it 'returns false' do
        expect(service.send(:time_window_expired?)).to be false
      end
    end
  end

  describe '#validate_code_with_expiry' do
    let(:stored_code) { '123456' }
    let(:generated_at) { Time.current }

    context 'with valid code and within expiry' do
      it 'returns true' do
        result = service.send(:validate_code_with_expiry, stored_code, '123456', generated_at)
        expect(result).to be true
      end
    end

    context 'with invalid code' do
      it 'returns false' do
        result = service.send(:validate_code_with_expiry, stored_code, '654321', generated_at)
        expect(result).to be false
      end
    end

    context 'with expired code' do
      let(:generated_at) { 10.minutes.ago }

      it 'returns false' do
        result = service.send(:validate_code_with_expiry, stored_code, '123456', generated_at)
        expect(result).to be false
      end
    end

    context 'with nil stored code' do
      it 'returns false' do
        result = service.send(:validate_code_with_expiry, nil, '123456', generated_at)
        expect(result).to be false
      end
    end

    context 'with nil generated_at' do
      it 'returns false' do
        result = service.send(:validate_code_with_expiry, stored_code, '123456', nil)
        expect(result).to be false
      end
    end
  end

  describe '#check_rate_limit!' do
    context 'when under rate limit' do
      it 'does not raise an error' do
        expect { service.send(:check_rate_limit!) }.not_to raise_error
      end
    end

    context 'when at rate limit' do
      before do
        3.times { service.record_code!('123456') }
      end

      it 'raises TooManyRequestError' do
        expect {
          service.send(:check_rate_limit!)
        }.to raise_error(Errors::TooManyRequestError, 'Request limit exceeded. Please try again after 1 hour.')
      end
    end
  end

  describe 'abstract methods' do
    let(:base_service) { BaseCodeSendService.new(user) }

    it 'raises NotImplementedError for clear_code!' do
      expect {
        base_service.send(:clear_code!)
      }.to raise_error(NotImplementedError, 'Subclass must implement clear_code!')
    end

    it 'raises NotImplementedError for record_code!' do
      expect {
        base_service.send(:record_code!, '123456')
      }.to raise_error(NotImplementedError, 'Subclass must implement record_code!')
    end

    it 'raises NotImplementedError for resend_count' do
      expect {
        base_service.send(:resend_count)
      }.to raise_error(NotImplementedError, 'Subclass must implement resend_count')
    end

    it 'raises NotImplementedError for code_generated_at' do
      expect {
        base_service.send(:code_generated_at)
      }.to raise_error(NotImplementedError, 'Subclass must implement code_generated_at')
    end
  end
end
