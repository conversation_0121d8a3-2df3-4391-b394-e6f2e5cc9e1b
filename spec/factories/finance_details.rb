FactoryBot.define do
  factory :finance_details, class: 'Vehicle::FinanceDetails' do
    association :customer_vehicle
    is_financed { false }
    has_clear_title { false }
    current_repayment_amount { nil }
    terms_months { nil }
    interest_rate { nil }
    next_due_date { nil }
    finance_company { nil }
    payout_amount { nil }

    trait :financed do
      is_financed { true }
      has_clear_title { false }
      current_repayment_amount { Faker::Number.decimal(l_digits: 4, r_digits: 2) }
      terms_months { Faker::Number.between(from: 12, to: 84) }
      interest_rate { Faker::Number.decimal(l_digits: 2, r_digits: 2) }
      next_due_date { Faker::Date.forward(days: 30) }
      finance_company { Faker::Company.name }
    end

    trait :clear_title do
      is_financed { false }
      has_clear_title { true }
    end

    trait :not_financed do
      is_financed { false }
      has_clear_title { false }
    end

    trait :with_payment_due do
      financed
      next_due_date { Date.current - 1.day }
    end

    trait :with_payment_due_soon do
      financed
      next_due_date { Date.current + 7.days }
    end

    trait :with_high_interest do
      financed
      interest_rate { 15.5 }
    end

    trait :with_low_interest do
      financed
      interest_rate { 3.25 }
    end

    trait :with_short_term do
      financed
      terms_months { 12 }
    end

    trait :with_long_term do
      financed
      terms_months { 84 }
    end

    trait :with_high_payment do
      financed
      current_repayment_amount { 1500.00 }
    end

    trait :with_low_payment do
      financed
      current_repayment_amount { 250.00 }
    end

    trait :with_payout_amount do
      payout_amount { 12345.67 }
    end

    trait :with_zero_payout_amount do
      payout_amount { 0.0 }
    end

    trait :with_negative_payout_amount do
      payout_amount { -100.0 }
    end
  end
end
