FactoryBot.define do
  factory :drive do
    dealership
    vehicle { create(:vehicle, dealership: dealership) }
    customer
    sales_person do
      user = create(:user)
      create(:user_dealership,
             user: user,
             dealership: dealership,
             role: :sales_person)
      user
    end
    drive_type { Drive.drive_types.keys.sample }
    status { Drive.statuses.keys.excluding("deleted").sample }
    notes { Faker::Lorem.paragraph }
    start_datetime { 1.hour.ago }
    end_datetime { Time.current }

    trait :with_initial_damage_report do
      after(:create) do |drive|
        create(:damage_report, drive: drive, report_type: DamageReport::INITIAL)
      end
    end

    trait :with_final_damage_report do
      after(:create) do |drive|
        create(:damage_report, drive: drive, report_type: DamageReport::FINAL)
      end
    end

    trait :with_waypoints do
      after(:create) do |drive|
        create_list(:gps_location, 3, trackable: drive)
      end
    end

    trait :test_drive_booking do
      drive_type { Drive.drive_types[:test_drive_booking] }
    end

    trait :loan_booking do
      drive_type { Drive.drive_types[:loan_booking] }
    end

    trait :enquiry do
      drive_type { Drive.drive_types[:enquiry] }
    end

    trait :test_drive do
      drive_type { Drive.drive_types[:test_drive] }
    end

    trait :vehicle_out_type do
      drive_type { Drive::VEHICLE_OUT_DRIVE_TYPES.sample }
    end

    trait :scheduled do
      status { Drive.statuses[:scheduled] }
    end

    trait :with_customer_signature do
      after(:build) do |drive|
        drive.customer_signature.attach(
          io: Rails.root.join("spec/fixtures/files/test_logo.png").open,
          filename: "sign.png",
          content_type: "image/png"
        )
      end
    end
  end
end
