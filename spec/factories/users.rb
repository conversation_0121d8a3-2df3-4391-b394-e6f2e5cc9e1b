FactoryBot.define do
  factory :user do
    first_name { "<PERSON>" }
    last_name  { "Do<PERSON>" }
    preferred_2fa { :email }
    email { Faker::Internet.email }
    password { "Drive@2025" }
    password_confirmation { "Drive@2025" }
    phone { "+***********" }
    user_type { :dealership_user }
    status { :active }
    password_change_required { false }
    onboarding_completed { true }
    uuid { SecureRandom.uuid }

    trait :with_device_registrations do
      after(:create) do |user|
        create_list(:device_registration, 2, user: user)
      end
    end

    trait :australian_phone do
      phone { "+***********" }
    end

    trait :indian_phone do
      phone { "+************" }
    end

    trait :us_phone do
      phone { "+***********" }
    end

    trait :sms do
      preferred_2fa { "sms" }
    end

    trait :super_admin do
      user_type { :super_admin }
    end

    trait :staff do
      user_type { :staff }
    end

    trait :inactive do
      status { :inactive }
    end

    trait :with_photo do
      after(:build) do |user|
        user.photo.attach(
          io: Rails.root.join("spec/fixtures/files/test_logo.png").open,
          filename: "test-image.png",
          content_type: "image/png"
        )
      end
    end
  end
end
