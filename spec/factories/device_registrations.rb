FactoryBot.define do
  factory :device_registration do
    user
    device_id { SecureRandom.uuid }
    refresh_token { SecureRandom.uuid }
    refresh_token_expires_at { 1.year.from_now }
    fcm_token { "fLD9lkhVM0WdfsXK2j10Br:APA91bHyxw#{SecureRandom.hex(8)}" }
    device_name { [ "iPhone 15 Pro", "Samsung Galaxy S23", "Google Pixel 7", "iPad Pro" ].sample }
    device_os { DeviceRegistration.device_os.keys.sample }
    device_os_version { "#{rand(10..17)}.#{rand(0..9)}.#{rand(1..9)}" }
    app_version { "#{rand(1..5)}.#{rand(0..9)}.#{rand(0..9)}" }
    app_build_number { rand(100..999).to_s }
    last_login_ip { "192.168.#{rand(1..255)}.#{rand(1..255)}" }
    active { true }
    last_activity_at { Time.current }
  end
end
