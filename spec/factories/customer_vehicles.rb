 FactoryBot.define do
  factory :customer_vehicle do
    association :appraisal

    # Set customer and dealership to match the appraisal's customer and dealership
    customer { appraisal.customer }
    dealership { appraisal.dealership }

    brand
    make { Faker::Vehicle.make }
    model { Faker::Vehicle.model }
    build_year { rand(2010..Date.current.year) }
    exterior_color { Faker::Vehicle.color }
    interior_color { Faker::Vehicle.color }
    vin { Faker::Vehicle.vin }
    rego { Faker::Vehicle.license_plate }
    compliance_month { rand(1..12) }
    compliance_year { rand(2010..Date.current.year) }
    odometer_reading { Faker::Number.between(from: 0, to: 200_000) }
    number_of_doors { [ 2, 4, 5 ].sample }
    number_of_seats { [ 2, 4, 5, 7, 8 ].sample }
    seat_type { CustomerVehicle.seat_types.keys.sample }
    fuel_type { CustomerVehicle.fuel_types.keys.sample }
    driving_wheels { CustomerVehicle.driving_wheels.keys.sample }
    spare_wheel_type { CustomerVehicle.spare_wheel_types.keys.sample }
    transmission { CustomerVehicle.transmissions.keys.sample }
    body_type { CustomerVehicle.body_types.keys.sample }

    # New fields
    engine_kilowatts { rand(50..300) }
    wheel_size_front { rand(13..22) }
    wheel_size_rear { rand(13..22) }


    trait :with_photos do
      after(:build) do |vehicle|
        vehicle.photos.attach([
          {
            io: Rails.root.join("spec/fixtures/files/car_1.png").open,
            filename: "car_1.png",
            content_type: "image/png"
          },
          {
            io: Rails.root.join("spec/fixtures/files/car_2.jpg").open,
            filename: "car_2.jpg",
            content_type: "image/jpeg"
          }
        ])
      end
    end

    trait :electric do
      fuel_type { :electric }
    end

    trait :hybrid do
      fuel_type { :hybrid }
    end

    trait :automatic do
      transmission { :automatic }
    end

    trait :manual do
      transmission { :manual }
    end

    trait :suv do
      body_type { :suv }
    end

    trait :sedan do
      body_type { :sedan }
    end
  end
end
