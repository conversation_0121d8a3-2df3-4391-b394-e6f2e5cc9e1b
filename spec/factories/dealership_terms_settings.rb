FactoryBot.define do
  factory :dealership_terms_setting do
    association :dealership

    # Default values
    test_drive_terms_text { nil }
    car_loan_terms_text { nil }
    insurance_waiver_text { nil }

    trait :with_test_drive_terms do
      test_drive_terms_text { "Standard test drive terms and conditions apply." }
    end

    trait :with_car_loan_terms do
      car_loan_terms_text { "Standard car loan terms and conditions apply." }
    end

    trait :with_insurance_waiver_text do
      insurance_waiver_text { "Standard insurance waiver terms and conditions apply." }
    end
  end
end
