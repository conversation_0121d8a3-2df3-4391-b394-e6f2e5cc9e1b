FactoryBot.define do
  factory :dealership_alert do
    dealership
    alert_type { :info }
    threshold { Faker::Number.between(from: 1, to: 10) }
    emails { Array.new(3) { |n| Faker::Internet.email(name: "user#{n + 1}") }.join(",") }
    status { :active }

    trait :warning do
      alert_type { :warning }
    end

    trait :error do
      alert_type { :error }
    end

    trait :inactive do
      status { :inactive }
    end

    trait :with_invalid_emails do
      emails { "random_string,<EMAIL>" }
    end
  end
end
