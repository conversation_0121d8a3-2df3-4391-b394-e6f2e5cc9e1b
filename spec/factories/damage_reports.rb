# frozen_string_literal: true

FactoryBot.define do
  factory :damage_report do
    description { Faker::Lorem.paragraph }
    vehicle

    trait :with_initial_drive do
      drive
      report_type { :initial }
    end

    trait :with_final_drive do
      drive
      report_type { :final }
    end

    trait :with_media_files do
      after(:build) do |damage_report|
        damage_report.media_files.attach([
          {
            io: Rails.root.join("spec/fixtures/files/short_video.mp4").open,
            filename: "short_video.mp4",
            content_type: "video/mp4"
          },
          {
            io: Rails.root.join("spec/fixtures/files/damage1.jpg").open,
            filename: "damage1.jpg",
            content_type: "image/jpg"
          },
          {
            io: Rails.root.join("spec/fixtures/files/damage2.jpg").open,
            filename: "damage2.jpg",
            content_type: "image/jpg"
          }
        ])
      end
    end

    trait :with_invalid_media_files do
      after(:build) do |damage_report|
        damage_report.media_files.attach(
          {
            io: StringIO.new("some text content"),
            filename: "test.txt",
            content_type: "text/plain"
          }
        )
      end
    end
  end
end
