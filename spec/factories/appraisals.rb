 FactoryBot.define do
  factory :appraisal do
    association :dealership
    association :customer
    association :sales_person, factory: :user
    status { :incomplete }
    created_by { sales_person }
    updated_by { sales_person }
    completed_percentage { 0 }
    awarded_value { 10000.0 }
    price { 12000.0 }
    give_price { 11000.0 }
    awarded_notes { "Initial appraisal." }
    projected_arrival_date { 1.week.from_now.to_date }
    appraisal_status { :retail }
    uuid { SecureRandom.uuid }
    created_at { Time.current }
    updated_at { Time.current }

    trait :with_vehicle do
      after(:create) do |appraisal|
        create(:customer_vehicle, appraisal: appraisal)
      end
    end

    trait :archived do
      status { :archived }
    end

    trait :wholesale do
      appraisal_status { :wholesale }
    end

    trait :lost do
      appraisal_status { :lost }
    end
 end
end
