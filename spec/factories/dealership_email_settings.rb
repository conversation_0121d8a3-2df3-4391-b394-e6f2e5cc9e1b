FactoryBot.define do
  factory :dealership_email_setting do
    association :dealership

    # Default values
    send_email_for_bookings { :none }
    email_from_address { nil }
    email_display_name { nil }

    trait :with_send_email_for_bookings_none do
      send_email_for_bookings { :none }
    end

    trait :with_send_email_for_bookings_test_drive do
      send_email_for_bookings { :test_drive }
    end

    trait :with_send_email_for_bookings_car_loan do
      send_email_for_bookings { :car_loan }
    end

    trait :with_send_email_for_bookings_both do
      send_email_for_bookings { :both }
    end

    trait :level1_odometer_settings do
      level1_odometer_warning_km { 10000 }
      level1_odometer_warning_email { "<EMAIL>" }
    end

    trait :level2_odometer_settings do
      level2_odometer_warning_km { 20000 }
      level2_odometer_warning_email { "<EMAIL>" }
    end

    trait :with_invalid_level1_odometer_warning_email do
      level1_odometer_warning_email { "<EMAIL>,invalid-email" }
    end

    trait :with_invalid_level2_odometer_warning_email do
      level2_odometer_warning_email { "invalid-email" }
    end

    trait :with_email_from_address do
      email_from_address { "<EMAIL>" }
    end

    trait :with_email_display_name do
      email_display_name { "Test Dealership" }
    end
  end
end
