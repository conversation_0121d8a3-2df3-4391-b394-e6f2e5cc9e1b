FactoryBot.define do
  factory :vehicle_history, class: 'Vehicle::VehicleHistory' do
    association :customer_vehicle
    uuid { SecureRandom.uuid }
    number_of_owners { Faker::Number.between(from: 1, to: 5) }
    has_accident_history { [ true, false ].sample }
    accident_details { has_accident_history ? Faker::Lorem.sentence : nil }
    last_service_date { Faker::Date.backward(days: 365) }
    last_service_odometer { Faker::Number.between(from: 10000, to: 200000) }
    next_service_due { Faker::Date.forward(days: 180) }
    has_dash_warning_lights { [ true, false ].sample }
    dash_warning_details { has_dash_warning_lights ? Faker::Lorem.sentence : nil }
    notes { Faker::Lorem.sentence }
    vehicle_history_status { :no_history }
  end
end
