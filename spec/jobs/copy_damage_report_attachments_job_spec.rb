# frozen_string_literal: true

require "rails_helper"

RSpec.describe CopyDamageReportAttachmentsJob, type: :job do
  describe "#perform" do
    let(:source_damage_report) { create(:damage_report, :with_media_files) }
    let(:destination_damage_report) { create(:damage_report) }

    context "when source has no media files" do
      before do
        allow(source_damage_report).to receive(:media_files).and_return([])
        allow(destination_damage_report.media_files).to receive(:attach)
      end

      it "does not copy any files" do
        described_class.new.perform(source_damage_report.id, destination_damage_report.id)

        expect(destination_damage_report.media_files).not_to have_received(:attach)
      end
    end

    context "when source damage report does not exist" do
      it "raises ActiveRecord::RecordNotFound" do
        expect {
          described_class.new.perform(999999, destination_damage_report.id)
        }.to raise_error(ActiveRecord::RecordNotFound)
      end
    end

    context "when destination damage report does not exist" do
      it "raises ActiveRecord::RecordNotFound" do
        expect {
          described_class.new.perform(source_damage_report.id, 999999)
        }.to raise_error(ActiveRecord::RecordNotFound)
      end
    end
  end

  describe "job enqueueing" do
    it "is queued on default queue" do
      expect(described_class.new.queue_name).to eq("default")
    end
  end
end

# Drive model specs for the job enqueueing logic
RSpec.describe Drive, type: :model do
  describe "#mark_completed" do
    let(:dealership) { create(:dealership) }
    let(:sales_person) { create(:user) }
    let!(:sales_person_dealership) { create(:user_dealership, user: sales_person, dealership: dealership, role: :sales_person) }
    let(:vehicle) { create(:vehicle, dealership: dealership, last_known_odometer_km: 10000) }
    let(:drive) do
      create(:drive,
             dealership: dealership,
             vehicle: vehicle,
             sales_person: sales_person,
             drive_type: :test_drive,
             status: :in_progress,
             end_odometer_reading: 10100)
    end

    before do
      allow(CopyDamageReportAttachmentsJob).to receive(:perform_later)
    end

    describe "CopyDamageReportAttachmentsJob enqueueing" do
      context "when final damage report has media files and vehicle damage report is created" do
        let!(:final_damage_report) do
          create(:damage_report, :with_media_files, drive: drive, report_type: DamageReport::FINAL, description: "Final damage").tap do |report|
            allow(report.media_files).to receive(:present?).and_return(true)
            allow(report).to receive(:media_files)
          end
        end

        it "enqueues CopyDamageReportAttachmentsJob with correct parameters" do
          drive.mark_completed

          vehicle_damage_report = DamageReport.where(vehicle: vehicle, report_type: DamageReport::VEHICLE).last
          expect(CopyDamageReportAttachmentsJob).to have_received(:perform_later).with(
            final_damage_report.id,
            vehicle_damage_report.id
          )
        end

        it "creates vehicle damage report with correct attributes" do
          expect {
            drive.mark_completed
          }.to change { DamageReport.where(vehicle: vehicle, report_type: DamageReport::VEHICLE).count }.by(1)

          vehicle_damage_report = DamageReport.where(vehicle: vehicle, report_type: DamageReport::VEHICLE).last
          expect(vehicle_damage_report.description).to eq("Final damage")
          expect(vehicle_damage_report.drive).to be_nil
          expect(vehicle_damage_report.vehicle).to eq(vehicle)
        end
      end

      context "when final damage report exists but has no media files" do
        let!(:final_damage_report) do
          create(:damage_report, drive: drive, report_type: DamageReport::FINAL, description: "Final damage").tap do |report|
            allow(report.media_files).to receive(:present?).and_return(false)
          end
        end

        it "does not enqueue CopyDamageReportAttachmentsJob" do
          drive.mark_completed

          expect(CopyDamageReportAttachmentsJob).not_to have_received(:perform_later)
        end

        it "still creates vehicle damage report" do
          expect {
            drive.mark_completed
          }.to change { DamageReport.where(vehicle: vehicle, report_type: DamageReport::VEHICLE).count }.by(1)
        end
      end

      context "when final damage report does not exist" do
        it "does not enqueue CopyDamageReportAttachmentsJob" do
          drive.mark_completed

          expect(CopyDamageReportAttachmentsJob).not_to have_received(:perform_later)
        end

        it "does not create vehicle damage report" do
          expect {
            drive.mark_completed
          }.not_to change { DamageReport.where(vehicle: vehicle, report_type: DamageReport::VEHICLE).count }
        end
      end

      context "when final damage report has media files but transaction fails" do
        let!(:final_damage_report) do
          create(:damage_report, :with_media_files, drive: drive, report_type: DamageReport::FINAL, description: "Final damage").tap do |report|
            allow(report.media_files).to receive(:present?).and_return(true)
            allow(report).to receive(:media_files).and_return([ report.media_files ])
          end
        end

        before do
          allow(vehicle).to receive(:save!).and_raise(ActiveRecord::RecordInvalid.new(vehicle))
        end

        it "does not enqueue CopyDamageReportAttachmentsJob when transaction rolls back" do
          expect {
            drive.mark_completed
          }.to raise_error(ActiveRecord::RecordInvalid)

          expect(CopyDamageReportAttachmentsJob).not_to have_received(:perform_later)
        end
      end

      context "when vehicle already has a damage report" do
        let!(:existing_vehicle_damage_report) { create(:damage_report, :with_media_files, vehicle: vehicle, report_type: DamageReport::VEHICLE, description: "Old damage") }
        let!(:final_damage_report) do
          create(:damage_report, :with_media_files, drive: drive, report_type: DamageReport::FINAL, description: "Final damage").tap do |report|
            allow(report.media_files).to receive(:present?).and_return(true)
            allow(report).to receive(:media_files).and_return(report.media_files)
          end
        end

        before do
          vehicle.update!(last_damage_report: existing_vehicle_damage_report)
        end

        it "replaces old damage report and enqueues job with new report" do
          expect {
            drive.mark_completed
          }.to change { DamageReport.where(vehicle: vehicle, report_type: DamageReport::VEHICLE).count }.by(0)

          new_vehicle_damage_report = vehicle.reload.last_damage_report
          expect(new_vehicle_damage_report).not_to eq(existing_vehicle_damage_report)
          expect(new_vehicle_damage_report.description).to eq("Final damage")

          expect(CopyDamageReportAttachmentsJob).to have_received(:perform_later).with(
            final_damage_report.id,
            new_vehicle_damage_report.id
          )
        end
      end
    end

    describe "integration with other drive completion logic" do
      let!(:final_damage_report) do
        create(:damage_report, :with_media_files, drive: drive, report_type: DamageReport::FINAL, description: "Final damage").tap do |report|
          allow(report.media_files).to receive(:present?).and_return(true)
          allow(report).to receive(:media_files).and_return(report.media_files)
        end
      end

      it "enqueues job after all other completion logic succeeds" do
        drive.mark_completed

        expect(drive.reload.status).to eq("completed")
        expect(drive.end_datetime).to be_present
        expect(vehicle.reload.last_known_odometer_km).to eq(10100)
        expect(CopyDamageReportAttachmentsJob).to have_received(:perform_later)
      end
    end
  end
end
