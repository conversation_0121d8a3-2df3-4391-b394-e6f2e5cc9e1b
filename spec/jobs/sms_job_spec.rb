require 'rails_helper'

RSpec.describe SmsJob, type: :job do
  describe '#perform' do
    let(:phone_number) { '+61777777777' }
    let(:message) { 'Test SMS message' }

    it 'calls SmsService.deliver with correct parameters' do
      expect(SmsService).to receive(:deliver).with(to: phone_number, message: message)
      described_class.perform_now(to: phone_number, message: message)
    end

    context 'when SmsService raises an error' do
      before do
        allow(SmsService).to receive(:deliver).and_raise(Errors::SmsDeliveryError, 'SMS delivery failed')
      end

      it 'propagates the error' do
        expect {
          described_class.perform_now(to: phone_number, message: message)
        }.to raise_error(Errors::SmsDeliveryError, 'SMS delivery failed')
      end
    end

    context 'when enqueued' do
      it 'enqueues the job with correct parameters' do
        expect {
          described_class.perform_later(to: phone_number, message: message)
        }.to have_enqueued_job(described_class).with(to: phone_number, message: message)
      end

      it 'uses the default queue' do
        expect(described_class.new.queue_name).to eq('default')
      end
    end
  end
end
