require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::BrandsController", type: :request do
  include_context "users_api_shared_context"

  let!(:toyota) { create(:brand, name: "Toyota Test") }
  let!(:honda) { create(:brand, name: "Honda Test") }
  let!(:ford) { create(:brand, name: "Ford Test") }
  let!(:bmw) { create(:brand, :with_logo, name: "BMW Test") }

  describe "GET /api/v1/brands" do
    let(:url) { "/api/v1/brands" }
    context "with valid authentication" do
      it "returns all brands ordered by name" do
        get url, headers: headers
        expect(response).to have_http_status(:ok)

        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Brands retrieved successfully")

        brands = json.dig("data", "brands")
        expect(brands).to be_an(Array)
        expect(brands.size).to eq(4)

        # Check ordering by name
        brand_names = brands.map { |brand| brand["name"] }
        expect(brand_names).to eq([ "BMW Test", "Ford Test", "Honda Test", "Toyota Test" ])
      end

      it "returns correct brand attributes" do
        get url, headers: headers
        expect(response).to have_http_status(:ok)

        json = response.parsed_body
        brands = json.dig("data", "brands")

        first_brand = brands.first
        expect(first_brand).to have_key("uuid")
        expect(first_brand).to have_key("name")
        expect(first_brand).to have_key("logo_url")
        expect(first_brand["uuid"]).to be_present
        expect(first_brand["name"]).to be_present
      end

      context "with brand logos" do
        let!(:brand_with_logo) { create(:brand, :with_logo, name: "Hyundai Test") }

        it "includes logo_url when logo is attached" do
          get url, headers: headers
          expect(response).to have_http_status(:ok)

          json = response.parsed_body
          brands = json.dig("data", "brands")

          bmw_brand = brands.find { |brand| brand["name"] == "Hyundai Test" }
          expect(bmw_brand["logo_url"]).to be_present
        end
      end

      context "when no brands exist" do
        before do
          Brand.destroy_all
        end

        it "returns empty array" do
          get url, headers: headers
          expect(response).to have_http_status(:ok)

          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Brands retrieved successfully")
          expect(json.dig("data", "brands")).to eq([])
        end
      end
    end

    context "without authentication" do
      it "returns 401 for missing token" do
        get url, headers: headers.except("Authorization")
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body["status"]["message"]).to eq("Missing authorization token")
      end

      it "returns 401 for invalid token" do
        invalid_headers = headers.merge("Authorization" => "Bearer invalid_token")
        get url, headers: invalid_headers
        expect(response).to have_http_status(:unauthorized)
      end

      it "returns 401 for missing device ID" do
        get url, headers: headers.except("Device-ID")
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body["status"]["message"]).to eq("Missing device ID")
      end
    end
  end


  path "/api/v1/brands" do
    get "Get all brands" do
      tags "Brands"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device ID"

      response "200", "brands retrieved successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: "Brands retrieved successfully" }
                   },
                   required: %w[code message]
                 },
                 data: {
                   type: :object,
                   properties: {
                     brands: {
                       type: :array,
                       items: {
                         type: :object,
                         properties: {
                           uuid: { type: :string, example: "123e4567-e89b-12d3-a456-************" },
                           name: { type: :string, example: "Toyota" },
                           logo_url: { type: :string, nullable: true, example: "https://example.com/logo.png" }
                         },
                         required: %w[uuid name logo_url]
                       }
                     }
                   },
                   required: %w[brands]
                 }
               },
               required: %w[status data]

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Brands retrieved successfully")

          brands = json.dig("data", "brands")
          expect(brands).to be_an(Array)
          expect(brands.size).to eq(4)

          # Check ordering by name
          brand_names = brands.map { |brand| brand["name"] }
          expect(brand_names).to eq([ "BMW Test", "Ford Test", "Honda Test", "Toyota Test" ])

          # Check structure of first brand
          first_brand = brands.first
          expect(first_brand).to have_key("uuid")
          expect(first_brand).to have_key("name")
          expect(first_brand).to have_key("logo_url")

          # Check BMW has logo_url (created with logo)
          bmw_brand = brands.find { |brand| brand["name"] == "BMW Test" }
          expect(bmw_brand["logo_url"]).to be_present

          # Check other brands have null logo_url (created without logo)
          toyota_brand = brands.find { |brand| brand["name"] == "Toyota Test" }
          expect(toyota_brand["logo_url"]).to be_nil
        end
      end

      response "401", "unauthorized" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: "Missing authorization token" }
                   },
                   required: %w[code message]
                 }
               },
               required: %w[status]

        context "missing authorization token" do
          let(:Authorization) { nil }

          run_test! do |response|
            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("Missing authorization token")
          end
        end
      end

      response "401", "unauthorized - missing device ID" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: "Invalid device" }
                   },
                   required: %w[code message]
                 }
               },
               required: %w[status]

        context "Invalid device" do
          let(:"Device-ID") { nil }

          run_test! do |response|
            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("Invalid device")
          end
        end
      end
    end
  end
end
