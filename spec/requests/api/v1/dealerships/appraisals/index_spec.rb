require 'rails_helper'
require 'swagger_helper'

RSpec.describe 'Api::V1::Dealerships::Appraisals', type: :request do
  include_context "appraisal_api_shared_context"

  describe 'GET /api/v1/dealerships/:dealership_uuid/appraisals' do
    subject { get "/api/v1/dealerships/#{dealership_uuid}/appraisals", params: params, headers: headers }

    let(:customer1) { create(:customer, dealership: dealership, first_name: '<PERSON>', last_name: '<PERSON>') }
    let(:customer2) { create(:customer, dealership: dealership, first_name: '<PERSON>', last_name: '<PERSON>') }
    let(:brand) { create(:brand, name: "Test Brand #{SecureRandom.hex(4)}") }
    let(:brand2) { create(:brand, name: "Honda #{SecureRandom.hex(4)}") }
    let!(:appraisal1) { create(:appraisal, dealership: dealership, customer: customer1, sales_person: sales_person, status: :complete) }
    let!(:appraisal2) { create(:appraisal, dealership: dealership, customer: customer2, sales_person: user, status: :incomplete) }
    let!(:deleted_appraisal) { create(:appraisal, dealership: dealership, customer: customer1, status: :deleted, sales_person: sales_person) }
    let!(:customer_vehicle1) { create(:customer_vehicle, appraisal: appraisal1, brand: brand, rego: 'ABC123', make: 'Camry') }
    let!(:customer_vehicle2) { create(:customer_vehicle, appraisal: appraisal2, brand: brand2, rego: 'DEF456', make: 'Corolla') }

    context 'without filters' do
      let(:params) { {} }

      it 'returns active appraisals' do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig('status', 'message')).to eq('Appraisals retrieved successfully')
        appraisals = json.dig('data', 'appraisals')
        expect(appraisals.size).to eq(2)
        expect(appraisals).not_to include(hash_including('uuid' => deleted_appraisal.uuid))
        expect(json.dig("data", "appraisals").first["favourite"]).to be false
      end

      it 'sets pagination headers' do
        subject
        expect(response.headers['X-Current-Page']).to eq('1')
        expect(response.headers['X-Per-Page']).to eq('20')
        expect(response.headers['X-Total-Count']).to eq('2')
        expect(response.headers['X-Total-Pages']).to eq('1')
      end

      it 'includes required data' do
        subject
        json = response.parsed_body
        appraisal_data = json.dig('data', 'appraisals').first

        expect(appraisal_data).to have_key('uuid')
        expect(appraisal_data).to have_key('status')
        expect(appraisal_data).to have_key('customer')
        expect(appraisal_data).to have_key('created_at')
        expect(appraisal_data).to have_key('updated_at')
        expect(appraisal_data['customer']).to have_key('uuid')
        expect(appraisal_data['customer']).to have_key('first_name')
      end
    end

    context 'with status filter' do
      let(:params) { { status: 'complete' } }

      it 'filters by status' do
        subject
        expect(response).to have_http_status(:ok)
        appraisals = response.parsed_body.dig('data', 'appraisals')
        expect(appraisals.size).to eq(1)
        expect(appraisals.first['status']).to eq('complete')
      end
    end

    context 'with customer filter' do
      let(:params) { { customer_uuid: customer1.uuid } }

      it 'filters by customer' do
        subject
        expect(response).to have_http_status(:ok)
        appraisals = response.parsed_body.dig('data', 'appraisals')
        expect(appraisals.size).to eq(1)
        expect(appraisals.first['uuid']).to eq(appraisal1.uuid)
      end
    end

    context 'with salesperson filter' do
      let(:params) { { salesperson_uuid: sales_person.uuid } }

      it 'filters by salesperson' do
        subject
        expect(response).to have_http_status(:ok)
        appraisals = response.parsed_body.dig('data', 'appraisals')
        expect(appraisals.size).to eq(1)
        expect(appraisals.first['uuid']).to eq(appraisal1.uuid)
      end
    end

    context 'with brand filter' do
      let(:params) { { brand_uuid: brand.uuid } }

      it 'filters by brand' do
        subject
        expect(response).to have_http_status(:ok)
        appraisals = response.parsed_body.dig('data', 'appraisals')
        expect(appraisals.size).to eq(1)
        expect(appraisals.first['uuid']).to eq(appraisal1.uuid)
      end
    end

    context 'with registration number filter' do
      let(:params) { { registration_number: 'ABC123' } }

      it 'filters by registration number' do
        subject
        expect(response).to have_http_status(:ok)
        appraisals = response.parsed_body.dig('data', 'appraisals')
        expect(appraisals.size).to eq(1)
        expect(appraisals.first['uuid']).to eq(appraisal1.uuid)
      end
    end

    context 'with date filters' do
      let(:start_date) { 1.day.ago.strftime('%Y-%m-%d') }
      let(:end_date) { 1.day.from_now.strftime('%Y-%m-%d') }

      context 'with start date only' do
        let(:params) { { start_date: start_date } }

        it 'filters by start date' do
          subject
          expect(response).to have_http_status(:ok)
          appraisals = response.parsed_body.dig('data', 'appraisals')
          expect(appraisals.size).to eq(2)
        end
      end

      context 'with end date only' do
        let(:params) { { end_date: end_date } }

        it 'filters by end date' do
          subject
          expect(response).to have_http_status(:ok)
          appraisals = response.parsed_body.dig('data', 'appraisals')
          expect(appraisals.size).to eq(2)
        end
      end

      context 'with date range' do
        let(:params) { { start_date: start_date, end_date: end_date } }

        it 'filters by date range' do
          subject
          expect(response).to have_http_status(:ok)
          appraisals = response.parsed_body.dig('data', 'appraisals')
          expect(appraisals.size).to eq(2)
        end
      end

      context 'with invalid date format' do
        let(:params) { { start_date: 'invalid-date' } }

        it 'returns error for invalid date' do
          subject
          expect(response).to have_http_status(:unprocessable_content)
          json = response.parsed_body
          expect(json.dig('status', 'message')).to include('Invalid date format')
        end
      end
    end

    context 'with only_mine filter' do
      let(:params) { { only_mine: 'true' } }

      it 'filters to current user appraisals' do
        subject
        expect(response).to have_http_status(:ok)
        appraisals = response.parsed_body.dig('data', 'appraisals')
        expect(appraisals.size).to eq(1)
        expect(appraisals.first['uuid']).to eq(appraisal2.uuid)
      end
    end

    context 'with search query' do
      let(:params) { { query: 'Alice' } }

      it 'searches by customer name' do
        subject
        expect(response).to have_http_status(:ok)
        appraisals = response.parsed_body.dig('data', 'appraisals')
        expect(appraisals.size).to eq(1)
        expect(appraisals.first['uuid']).to eq(appraisal1.uuid)
      end
    end

    context 'with pagination' do
      let(:params) { { page: 1, per_page: 1 } }

      it 'paginates results' do
        subject
        expect(response).to have_http_status(:ok)
        appraisals = response.parsed_body.dig('data', 'appraisals')
        expect(appraisals.size).to eq(1)
        expect(response.headers['X-Current-Page']).to eq('1')
        expect(response.headers['X-Total-Count']).to eq('2')
      end
    end

    context 'error cases' do
      it 'returns not found for invalid dealership' do
        get "/api/v1/dealerships/invalid-uuid/appraisals", headers: headers
        expect(response).to have_http_status(:not_found)
      end

      it 'returns unauthorized without auth' do
        get "/api/v1/dealerships/#{dealership_uuid}/appraisals"
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  # Swagger Documentation
  path '/api/v1/dealerships/{dealership_uuid}/appraisals' do
    get 'List appraisals' do
      tags 'Appraisals'
      description 'Retrieves a list of appraisals for a dealership with optional filtering'
      operationId 'listAppraisals'
      produces 'application/json'
      security [ Bearer: [] ]

      parameter name: 'dealership_uuid', in: :path, type: :string, description: 'Dealership UUID'
      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Bearer token'
      parameter name: 'Device-ID', in: :header, type: :string, required: true, description: 'Device identifier'
      parameter name: 'status', in: :query, type: :string, required: false, description: 'Filter by status', enum: %w[incomplete complete awarded archived deleted]
      parameter name: 'customer_uuid', in: :query, type: :string, required: false, description: 'Filter by customer UUID'
      parameter name: 'salesperson_uuid', in: :query, type: :string, required: false, description: 'Filter by salesperson UUID'
      parameter name: 'brand_uuid', in: :query, type: :string, required: false, description: 'Filter by vehicle brand UUID'
      parameter name: 'registration_number', in: :query, type: :string, required: false, description: 'Filter by vehicle registration number'
      parameter name: 'start_date', in: :query, type: :string, required: false, description: 'Filter by start date (YYYY-MM-DD)'
      parameter name: 'end_date', in: :query, type: :string, required: false, description: 'Filter by end date (YYYY-MM-DD)'
      parameter name: 'only_mine', in: :query, type: :string, required: false, description: 'Filter to current user appraisals'
      parameter name: 'query', in: :query, type: :string, required: false, description: 'Search term for customer name, email, phone, vehicle details'
      parameter name: 'page', in: :query, type: :integer, required: false, description: 'Page number'
      parameter name: 'per_page', in: :query, type: :integer, required: false, description: 'Items per page'

      response '200', 'Appraisals retrieved successfully' do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Appraisals retrieved successfully' }
                   },
                   required: %w[code message]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisals: {
                       type: :array,
                       items: {
                         type: :object,
                         properties: {
                           uuid: { type: :string, format: :uuid },
                           status: { type: :string, enum: %w[incomplete complete awarded archived deleted] },
                           customer: {
                             type: :object,
                             properties: {
                               uuid: { type: :string, format: :uuid },
                               first_name: { type: :string },
                               last_name: { type: :string },
                               email: { type: :string, format: :email },
                               phone_number: { type: :string },
                               full_name: { type: :string }
                             }
                           },
                           sales_person: {
                             type: :object,
                             properties: {
                               uuid: { type: :string, format: :uuid },
                               first_name: { type: :string },
                               last_name: { type: :string },
                               email: { type: :string, format: :email },
                               full_name: { type: :string }
                             }
                           },
                           created_at: { type: :string, format: 'date-time' },
                           updated_at: { type: :string, format: 'date-time' }
                         }
                       }
                     }
                   },
                   required: %w[appraisals]
                 }
               },
               required: %w[status data]



        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['status']['code']).to eq(200)
          expect(data['status']['message']).to eq('Appraisals retrieved successfully')
          expect(data['data']['appraisals']).to be_an(Array)
        end
      end

      response '404', 'Dealership not found' do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: 'Dealership not found' }
                   },
                   required: %w[code message]
                 }
               },
               required: %w[status]

        let(:dealership_uuid) { 'invalid-uuid' }

        run_test!
      end

      response '401', 'Unauthorized' do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: 'Missing authorization token' }
                   },
                   required: %w[code message]
                 }
               },
               required: %w[status]


        let(:Authorization) { nil }
        let(:'Device-ID') { nil }

        run_test!
      end
    end
  end
end
