# frozen_string_literal: true

require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::AppraisalsController#attach_customer_signature", type: :request do
  include_context "appraisal_api_shared_context"

  let!(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: user) }

  path "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/signature" do
    put "Attach customer signature to appraisal" do
      tags "Appraisals"
      consumes "multipart/form-data"
      produces "application/json"
      security [ Bearer: [] ]
      description "Upload customer signature image file for an appraisal. The request body should contain a 'signature' field with the image file."

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :appraisal_uuid, in: :path, type: :string, required: true, description: "Appraisal UUID"
      parameter name: :signature, in: :formData, type: :file, required: true, description: "Customer signature image file (PNG/JPEG, max 5MB)"

      response "200", "Customer signature uploaded successfully" do
        let(:appraisal_uuid) { appraisal.uuid }
        let(:signature) { fixture_file_upload("profile.jpg", "image/jpeg") }

        run_test! do |response|
          expect(response.status).to eq(200)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Customer signature added successfully")
          expect(json.dig("data", "appraisal", "customer_signature_url")).to be_present
          expect(appraisal.reload.customer_signature).to be_attached
        end
      end

      response "422", "Invalid signature file" do
        let(:appraisal_uuid) { appraisal.uuid }

        context "when signature file is missing" do
          let(:signature) { nil }

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to include("Signature file is required")
          end
        end

        context "when signature file is invalid format" do
          let(:signature) { fixture_file_upload("test_document.pdf", "application/pdf") }

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to include("Customer signature has an invalid content type")
          end
        end

        context "when appraisal is archived" do
          let!(:archived_appraisal) { create(:appraisal, :archived, dealership: dealership, customer: customer, sales_person: user) }
          let(:appraisal_uuid) { archived_appraisal.uuid }
          let(:signature) { fixture_file_upload("profile.jpg", "image/jpeg") }

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to include("Cannot modify archived appraisal")
          end
        end
      end

      response "404", "Appraisal not found" do
        let(:appraisal_uuid) { "non-existent-uuid" }
        let(:signature) { fixture_file_upload("profile.jpg", "image/jpeg") }

        run_test! do |response|
          expect(response.status).to eq(404)
          expect(response.parsed_body.dig("status", "message")).to include("Appraisal not found")
        end
      end

      response "401", "Unauthorized" do
        let(:appraisal_uuid) { appraisal.uuid }
        let(:signature) { fixture_file_upload("profile.jpg", "image/jpeg") }
        let(:Authorization) { "Bearer invalid-token" }

        run_test!
      end
    end
  end

  # Regular RSpec tests
  describe "PUT /api/v1/dealerships/:dealership_uuid/appraisals/:appraisal_uuid/signature" do
    let(:signature_file) { fixture_file_upload("profile.jpg", "image/jpeg") }

    subject do
      put "/api/v1/dealerships/#{dealership_uuid}/appraisals/#{appraisal.uuid}/signature",
          params: { signature: signature_file },
          headers: headers
    end

    context "when signature file is valid" do
      it "attaches customer signature successfully" do
        subject
        expect(response).to have_http_status(:ok)

        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Customer signature added successfully")
        expect(json.dig("data", "appraisal", "customer_signature_url")).to be_present
        expect(appraisal.reload.customer_signature).to be_attached
      end

      it "replaces existing signature" do
        appraisal.attach_customer_signature(fixture_file_upload("test_logo.png", "image/png"))
        expect(appraisal.customer_signature).to be_attached

        subject
        expect(response).to have_http_status(:ok)
        expect(appraisal.reload.customer_signature).to be_attached
        expect(appraisal.customer_signature.blob.filename.to_s).to eq("profile.jpg")
      end
    end

    context "when signature file is invalid" do
      context "missing signature" do
        let(:signature_file) { nil }

        it "returns error for missing signature" do
          subject
          expect(response).to have_http_status(:unprocessable_content)
          expect(response.parsed_body.dig("status", "message")).to include("Signature file is required")
        end
      end

      context "invalid file format" do
        let(:signature_file) { fixture_file_upload("invalid.txt", "text/plain") }

        it "returns error for invalid file format" do
          subject
          expect(response).to have_http_status(:unprocessable_content)
          expect(response.parsed_body.dig("status", "message")).to include("Customer signature has an invalid content type")
        end
      end
    end

    context "when appraisal is archived" do
      let!(:archived_appraisal) { create(:appraisal, :archived, dealership: dealership, customer: customer, sales_person: sales_person) }
      let!(:archived_vehicle) { create(:customer_vehicle, customer: customer, appraisal: archived_appraisal) }

      subject do
        put "/api/v1/dealerships/#{dealership_uuid}/appraisals/#{archived_appraisal.uuid}/signature",
            params: { signature: signature_file },
            headers: headers
      end

      it "returns error" do
        subject
        expect(response).to have_http_status(:unprocessable_content)
        expect(response.parsed_body.dig("status", "message")).to include("Cannot modify archived appraisal")
      end
    end

    context "when appraisal does not exist" do
      subject do
        put "/api/v1/dealerships/#{dealership_uuid}/appraisals/non-existent-uuid/signature",
            params: { signature: signature_file },
            headers: headers
      end

      it "returns not found error" do
        subject
        expect(response).to have_http_status(:not_found)
        expect(response.parsed_body.dig("status", "message")).to include("Appraisal not found")
      end
    end
  end
end
