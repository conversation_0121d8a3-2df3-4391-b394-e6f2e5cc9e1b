# frozen_string_literal: true

require 'rails_helper'
require 'swagger_helper'

RSpec.describe "PUT /api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/reassign", type: :request do
  include_context "appraisal_api_shared_context"

  let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
  let!(:new_sales_person) { create(:user) }
  let!(:new_sales_person_dealership) { create(:user_dealership, user: new_sales_person, dealership: dealership, role: :sales_person) }

  path "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/reassign" do
    put "Reassign appraisal to another sales person" do
      tags "Appraisals"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :appraisal_uuid, in: :path, type: :string, required: true, description: "Appraisal UUID"

      parameter name: :payload, in: :body, required: true, schema: {
        type: :object,
        required: %w[sales_person_uuid],
        properties: {
          sales_person_uuid: {
            type: :string,
            format: :uuid,
            description: "UUID of the new sales person to assign to the appraisal",
            example: "123e4567-e89b-12d3-a456-************"
          }
        }
      }

      response "200", "Appraisal reassigned successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Appraisal reassigned successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete', 'complete', 'awarded', 'archived', 'deleted' ], example: 'incomplete' },
                         sales_person: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'Jane' },
                             last_name: { type: :string, example: 'Smith' },
                             email: { type: :string, example: '<EMAIL>' }
                           }
                         },
                         customer: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'John' },
                             last_name: { type: :string, example: 'Doe' },
                             email: { type: :string, example: '<EMAIL>' }
                           }
                         }
                       }
                     }
                   }
                 }
               }

        let(:appraisal_uuid) { appraisal.uuid }
        let(:payload) do
          {
            sales_person_uuid: new_sales_person.uuid
          }
        end

        run_test! do |response|
          expect(response.status).to eq(200)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Appraisal reassigned successfully")
          expect(json.dig("data", "appraisal", "sales_person", "uuid")).to eq(new_sales_person.uuid)
        end
      end

      response "422", "Invalid input" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: 'sales_person_uuid is required' }
                   },
                   required: [ 'code', 'message' ]
                 }
               }

        context "when sales_person_uuid is missing" do
          let(:appraisal_uuid) { appraisal.uuid }
          let(:payload) { {} }

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to include("sales_person_uuid is required")
          end
        end

        context "when appraisal is archived" do
          let!(:archived_appraisal) { create(:appraisal, :archived, dealership: dealership, customer: customer, sales_person: sales_person) }
          let(:appraisal_uuid) { archived_appraisal.uuid }
          let(:payload) do
            {
              sales_person_uuid: new_sales_person.uuid
            }
          end

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to include("Cannot modify archived appraisal")
          end
        end
      end

      response "404", "Not found" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: 'Appraisal not found or does not belong to this dealership' }
                   },
                   required: [ 'code', 'message' ]
                 }
               }

        context "when appraisal not found" do
          let(:appraisal_uuid) { "non-existent-uuid" }
          let(:payload) do
            {
              sales_person_uuid: new_sales_person.uuid
            }
          end

          run_test! do |response|
            expect(response.status).to eq(404)
            expect(response.parsed_body.dig("status", "message")).to include("Appraisal not found")
          end
        end

        context "when sales person not found" do
          let(:appraisal_uuid) { appraisal.uuid }
          let(:payload) do
            {
              sales_person_uuid: "non-existent-user-uuid"
            }
          end

          run_test! do |response|
            expect(response.status).to eq(404)
            expect(response.parsed_body.dig("status", "message")).to include("User not found")
          end
        end
      end

      response "401", "Unauthorized" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: 'Unauthorized' }
                   },
                   required: [ 'code', 'message' ]
                 }
               }

        let(:appraisal_uuid) { appraisal.uuid }
        let(:payload) do
          {
            sales_person_uuid: new_sales_person.uuid
          }
        end
        let(:Authorization) { "Bearer invalid-token" }

        run_test!
      end
    end
  end

  # Regular RSpec tests
  describe "PUT /api/v1/dealerships/:dealership_uuid/appraisals/:appraisal_uuid/reassign" do
    subject do
      put "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}/reassign",
          params: params,
          headers: headers
    end

    context "with valid authentication" do
      context "when reassigning to valid sales person" do
        let(:params) { { sales_person_uuid: new_sales_person.uuid } }

        it "reassigns the appraisal successfully" do
          expect { subject }.to change { appraisal.reload.sales_person }.from(sales_person).to(new_sales_person)

          expect(response).to have_http_status(:ok)
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(200)
          expect(json.dig("status", "message")).to eq("Appraisal reassigned successfully")
          expect(json.dig("data", "appraisal", "sales_person", "uuid")).to eq(new_sales_person.uuid)
          expect(json.dig("data", "appraisal", "sales_person", "first_name")).to eq(new_sales_person.first_name)
          expect(json.dig("data", "appraisal", "sales_person", "last_name")).to eq(new_sales_person.last_name)
        end

        it "updates the updated_by field" do
          subject
          expect(appraisal.reload.updated_by).to eq(user)
        end
      end

      context "when sales_person_uuid is missing" do
        let(:params) { {} }

        it "returns validation error" do
          subject
          expect(response).to have_http_status(:unprocessable_content)
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to include("sales_person_uuid is required")
        end
      end

      context "when sales person does not exist" do
        let(:params) { { sales_person_uuid: "non-existent-uuid" } }

        it "returns not found error" do
          subject
          expect(response).to have_http_status(:not_found)
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(404)
          expect(json.dig("status", "message")).to include("User not found")
        end
      end

      context "when sales person belongs to different dealership" do
        let!(:other_dealership) { create(:dealership, :without_dealership_group, :without_brand) }
        let!(:other_sales_person) { create(:user) }
        let!(:other_sales_person_dealership) { create(:user_dealership, user: other_sales_person, dealership: other_dealership, role: :sales_person) }
        let(:params) { { sales_person_uuid: other_sales_person.uuid } }

        it "returns not found error" do
          subject
          expect(response).to have_http_status(:not_found)
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(404)
          expect(json.dig("status", "message")).to include("User not found or does not belong to this dealership")
        end
      end

      context "when appraisal is archived" do
        let!(:archived_appraisal) { create(:appraisal, :archived, dealership: dealership, customer: customer, sales_person: sales_person) }
        let(:params) { { sales_person_uuid: new_sales_person.uuid } }

        subject do
          put "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{archived_appraisal.uuid}/reassign",
              params: params,
              headers: headers
        end

        it "returns validation error" do
          subject
          expect(response).to have_http_status(:unprocessable_content)
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to include("Cannot modify archived appraisal")
        end
      end

      context "when appraisal does not exist" do
        let(:params) { { sales_person_uuid: new_sales_person.uuid } }

        subject do
          put "/api/v1/dealerships/#{dealership.uuid}/appraisals/non-existent-uuid/reassign",
              params: params,
              headers: headers
        end

        it "returns not found error" do
          subject
          expect(response).to have_http_status(:not_found)
          json = response.parsed_body
          expect(json.dig("status", "code")).to eq(404)
          expect(json.dig("status", "message")).to include("Appraisal not found")
        end
      end
    end

    context "with invalid dealership" do
      let(:params) { { sales_person_uuid: new_sales_person.uuid } }

      subject do
        put "/api/v1/dealerships/invalid-uuid/appraisals/#{appraisal.uuid}/reassign",
            params: params,
            headers: headers
      end

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "without authentication" do
      let(:headers) { {} }
      let(:params) { { sales_person_uuid: new_sales_person.uuid } }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
