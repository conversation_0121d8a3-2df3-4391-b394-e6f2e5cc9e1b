require 'rails_helper'
require 'swagger_helper'

RSpec.describe 'DELETE /api/v1/dealerships/:dealership_uuid/appraisals/:appraisal_uuid', type: :request do
  include_context "appraisal_api_shared_context"

  let(:customer_vehicle) { create(:customer_vehicle, customer: customer, brand: toyota, rego: 'ABC123', make: 'Toyota', model: 'Camry') }
  let!(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, customer_vehicle: customer_vehicle, sales_person: sales_person, status: :complete) }

  # Regular RSpec tests
  describe 'DELETE /api/v1/dealerships/:dealership_uuid/appraisals/:appraisal_uuid' do
    subject { delete "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}", headers: headers }

    context 'with valid authentication' do
      it 'marks the appraisal as deleted' do
        expect { subject }.to change { appraisal.reload.status }.from('complete').to('deleted')

        expect(response).to have_http_status(:ok)
        json = response.parsed_body
        expect(json.dig('status', 'code')).to eq(200)
        expect(json.dig('status', 'message')).to eq('Appraisal deleted successfully')
      end

      it 'updates the updated_by field' do
        expect { subject }.to change { appraisal.reload.updated_by_id }.to(user.id)
      end

      it 'does not physically delete the record' do
        expect { subject }.not_to change { Appraisal.unscoped.count }
      end

      it 'marks the appraisal status as deleted' do
        subject
        appraisal.reload
        expect(appraisal.status).to eq('deleted')
      end
    end

    context 'with non-existent appraisal' do
      subject { delete "/api/v1/dealerships/#{dealership.uuid}/appraisals/non-existent-uuid", headers: headers }

      it 'returns not found' do
        subject
        expect(response).to have_http_status(:not_found)

        json = response.parsed_body
        expect(json.dig('status', 'message')).to include('Appraisal not found')
      end
    end

    context 'with already deleted appraisal' do
      before { appraisal.update!(status: :deleted) }

      it 'returns not found' do
        subject
        expect(response).to have_http_status(:not_found)

        json = response.parsed_body
        expect(json.dig('status', 'message')).to include('Appraisal not found')
      end
    end

    context 'with invalid dealership' do
      subject { delete "/api/v1/dealerships/invalid-uuid/appraisals/#{appraisal.uuid}", headers: headers }

      it 'returns not found' do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context 'without authentication' do
      let(:headers) { {} }

      it 'returns unauthorized' do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  # Swagger Documentation
  path '/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}' do
    delete 'Delete an appraisal' do
      tags 'Appraisals'
      description 'Marks an appraisal as deleted (soft delete). The appraisal will no longer be accessible via normal queries but remains in the database for audit purposes.'
      operationId 'deleteAppraisal'
      produces 'application/json'
      security [ Bearer: [] ]

      parameter name: 'dealership_uuid', in: :path, type: :string, description: 'Dealership UUID'
      parameter name: 'appraisal_uuid', in: :path, type: :string, description: 'Appraisal UUID'
      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Bearer token'
      parameter name: 'Device-ID', in: :header, type: :string, required: true, description: 'Device identifier'

      response '200', 'Appraisal deleted successfully' do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Appraisal deleted successfully' }
                   },
                   required: %w[code message]
                 }
               },
               required: %w[status]


        let(:appraisal_uuid) { appraisal.uuid }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data['status']['code']).to eq(200)
          expect(data['status']['message']).to eq('Appraisal deleted successfully')

          # Verify the appraisal is marked as deleted
          appraisal.reload
          expect(appraisal.status).to eq('deleted')
        end
      end

      response '404', 'Appraisal not found' do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: 'Appraisal not found' }
                   },
                   required: %w[code message]
                 }
               },
               required: %w[status]


        let(:appraisal_uuid) { 'non-existent-uuid' }

        run_test!
      end

      response '401', 'Unauthorized' do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: 'Missing authorization token' }
                   },
                   required: %w[code message]
                 }
               },
               required: %w[status]


        let(:appraisal_uuid) { appraisal.uuid }
        let(:Authorization) { nil }
        let(:'Device-ID') { nil }

        run_test!
      end
    end
  end
end
