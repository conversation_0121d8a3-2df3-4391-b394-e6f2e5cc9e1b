# frozen_string_literal: true

require 'rails_helper'
require 'swagger_helper'

RSpec.describe "PUT /api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/reconditioning-costs", type: :request do
  include_context "appraisal_api_shared_context"

  let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
  let!(:customer_vehicle) { create(:customer_vehicle, appraisal: appraisal, customer: customer, dealership: dealership) }
  let!(:vehicle_condition) { create(:vehicle_condition, customer_vehicle: customer_vehicle) }

  path "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/reconditioning-costs" do
    put "Bulk upsert reconditioning costs for appraisal" do
      tags "Appraisals"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :appraisal_uuid, in: :path, type: :string, required: true, description: "Appraisal UUID"

      parameter name: :payload, in: :body, required: true, schema: {
        type: :object,
        required: %w[reconditioning_costs],
        properties: {
          reconditioning_costs: {
            type: :array,
            minItems: 1,
            items: {
              type: :object,
              required: %w[cost_type amount currency],
              properties: {
                cost_type: {
                  type: :string,
                  enum: %w[paint_and_panel wheels_and_tyres windscreen mechanical registration other],
                  description: "Type of reconditioning cost",
                  example: "paint_and_panel"
                },
                amount: {
                  type: :number,
                  format: :float,
                  minimum: 0,
                  description: "Cost amount",
                  example: 2000.00
                },
                currency: {
                  type: :string,
                  description: "Currency code",
                  example: "AUD"
                }
              }
            }
          }
        }
      }

      response "200", "Reconditioning costs updated successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Reconditioning costs updated successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete', 'complete', 'archived', 'deleted' ], example: 'incomplete' },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Toyota' },
                             model: { type: :string, example: 'Camry' },
                             build_year: { type: :integer, example: 2020 },
                             condition: {
                               type: :object,
                               properties: {
                                 uuid: { type: :string, format: :uuid },
                                 reconditioning_costs: {
                                   type: :array,
                                   items: {
                                     type: :object,
                                     properties: {
                                       id: { type: :integer },
                                       cost_type: { type: :string, example: "paint_and_panel" },
                                       amount: { type: :string, example: "2000.00" },
                                       currency: { type: :string, example: "AUD" }
                                     }
                                   }
                                 }
                               }
                             }
                           }
                         }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]
        let(:appraisal_uuid) { appraisal.uuid }
        let(:payload) do
          { reconditioning_costs: [
            {
              cost_type: "paint_and_panel",
              amount: 2000.00,
              currency: "AUD"
            },
            {
              cost_type: "wheels_and_tyres",
              amount: 800.00,
              currency: "AUD"
            }
          ] }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(200)
          expect(json.dig("status", "message")).to eq("Reconditioning costs updated successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "uuid")).to eq(appraisal.uuid)
          expect(json.dig("data", "appraisal", "vehicle", "vehicle_condition", "reconditioning_costs")).to be_present

          reconditioning_costs = json.dig("data", "appraisal", "vehicle", "vehicle_condition", "reconditioning_costs")
          expect(reconditioning_costs.length).to eq(2)

          paint_panel_cost = reconditioning_costs.find { |rc| rc["cost_type"] == "paint_and_panel" }
          expect(paint_panel_cost).to be_present
          expect(paint_panel_cost["id"]).to be_present
          expect(paint_panel_cost["amount"]).to eq("2000.0")
          expect(paint_panel_cost["currency"]).to eq("AUD")

          wheels_tyres_cost = reconditioning_costs.find { |rc| rc["cost_type"] == "wheels_and_tyres" }
          expect(wheels_tyres_cost).to be_present
          expect(wheels_tyres_cost["id"]).to be_present
          expect(wheels_tyres_cost["amount"]).to eq("800.0")
          expect(wheels_tyres_cost["currency"]).to eq("AUD")
        end
      end

      response "200", "Reconditioning costs updated (with existing costs updated)" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Reconditioning costs updated successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete', 'complete', 'archived', 'deleted' ], example: 'incomplete' },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Toyota' },
                             model: { type: :string, example: 'Camry' },
                             build_year: { type: :integer, example: 2020 },
                             vehicle_condition: {
                               type: :object,
                               properties: {
                                 uuid: { type: :string, format: :uuid },
                                 reconditioning_costs: {
                                   type: :array,
                                   items: {
                                     type: :object,
                                     properties: {
                                       id: { type: :integer },
                                       cost_type: { type: :string, example: "mechanical" },
                                       amount: { type: :string, example: "3500.00" },
                                       currency: { type: :string, example: "AUD" }
                                     }
                                   }
                                 }
                               }
                             }
                           }
                         }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]


        let(:appraisal_uuid) { appraisal.uuid }
        let!(:existing_cost) { create(:reconditioning_cost, vehicle_condition: vehicle_condition, cost_type: :mechanical, amount: 1500, currency: "AUD") }
        let(:payload) do
          { reconditioning_costs: [
            {
              cost_type: "mechanical",
              amount: 3500.00,
              currency: "AUD"
            }
          ] }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(200)
          expect(json.dig("status", "message")).to eq("Reconditioning costs updated successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "uuid")).to eq(appraisal.uuid)

          reconditioning_costs = json.dig("data", "appraisal", "vehicle", "vehicle_condition", "reconditioning_costs")
          expect(reconditioning_costs.length).to eq(1)

          mechanical_cost = reconditioning_costs.find { |rc| rc["cost_type"] == "mechanical" }
          expect(mechanical_cost).to be_present
          expect(mechanical_cost["id"]).to eq(existing_cost.id)
          expect(mechanical_cost["amount"]).to eq("3500.0")
          expect(mechanical_cost["currency"]).to eq("AUD")
        end
      end

      response "422", "Validation failed" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Validation failed: Amount must be greater than or equal to 0" }
                   }
                 }
               },
               required: [ 'status' ]


        let(:appraisal_uuid) { appraisal.uuid }
        let(:payload) do
          { reconditioning_costs: [
            {
              cost_type: "paint_and_panel",
              amount: -100.00,
              currency: "AUD"
            }
          ] }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to include("Validation failed")
        end
      end

      response "422", "No vehicle condition found for this appraisal" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "No vehicle condition found for this appraisal" }
                   }
                 }
               },
               required: [ 'status' ]


        let(:appraisal_uuid) { appraisal_without_condition.uuid }
        let(:appraisal_without_condition) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
        let!(:vehicle_without_condition) { create(:customer_vehicle, appraisal: appraisal_without_condition, customer: customer, dealership: dealership) }
        let(:payload) do
          { reconditioning_costs: [
            {
              cost_type: "paint_and_panel",
              amount: 2000.00,
              currency: "AUD"
            }
          ] }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to eq("No vehicle condition found for this appraisal")
        end
      end

      response "422", "Cannot modify archived appraisal" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Cannot modify archived appraisal" }
                   }
                 }
               },
               required: [ 'status' ]


        let(:appraisal_uuid) { archived_appraisal.uuid }
        let(:archived_appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person, status: :archived) }
        let!(:archived_vehicle) { create(:customer_vehicle, appraisal: archived_appraisal, customer: customer, dealership: dealership) }
        let!(:archived_condition) { create(:vehicle_condition, customer_vehicle: archived_vehicle) }
        let(:payload) do
          { reconditioning_costs: [
            {
              cost_type: "paint_and_panel",
              amount: 2000.00,
              currency: "AUD"
            }
          ] }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to eq("Cannot modify archived appraisal")
        end
      end

      response "404", "Appraisal not found" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: "Appraisal not found or does not belong to this dealership" }
                   }
                 }
               },
               required: [ 'status' ]


        let(:appraisal_uuid) { "non-existent-uuid" }
        let(:payload) do
          { reconditioning_costs: [
            {
              cost_type: "paint_and_panel",
              amount: 2000.00,
              currency: "AUD"
            }
          ] }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(404)
          expect(json.dig("status", "message")).to eq("Appraisal not found or does not belong to this dealership")
        end
      end

      response "401", "Missing authorization token" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: "Missing authorization token" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:Authorization) { nil }

        let(:appraisal_uuid) { appraisal.uuid }
        let(:payload) do
          { reconditioning_costs: [
            {
              cost_type: "paint_and_panel",
              amount: 2000.00,
              currency: "AUD"
            }
          ] }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(401)
          expect(json.dig("status", "message")).to eq("Missing authorization token")
        end
      end
    end
  end
end
