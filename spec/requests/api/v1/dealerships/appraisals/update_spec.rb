# frozen_string_literal: true

require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::AppraisalsController#update", type: :request do
  include_context "appraisal_api_shared_context"

  let!(:test_appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }

  path "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}" do
    put "Update an existing appraisal" do
      tags "Appraisals"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]
      description "Update appraisal fields including projected arrival date and appraisal status. At least one field must be provided."

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :appraisal_uuid, in: :path, type: :string, required: true, description: "Appraisal UUID"

      parameter name: :appraisal, in: :body, schema: {
        type: :object,
        properties: {
          projected_arrival_date: {
            type: :string,
            format: :"date",
            description: "Expected arrival date for the vehicle"
          },
          appraisal_status: {
            type: :string,
            enum: [ "retail", "wholesale", "lost" ],
            description: "Appraisal status classification"
          },
          awarded_value: {
            type: :number,
            description: "Final awarded value"
          },
          price: {
            type: :number,
            description: "Appraisal price"
          },
          give_price: {
            type: :number,
            description: "Give price for the vehicle"
          },
          awarded_notes: {
            type: :string,
            maxLength: 1000,
            description: "Notes about the award"
          },
          notes: {
            type: :string,
            maxLength: 1000,
            description: "General appraisal notes"
          }
        },
        description: "At least one field must be provided. All fields are optional but at least one is required."
      }

      response "200", "Appraisal updated successfully" do
        let(:appraisal_uuid) { test_appraisal.uuid }
        let(:appraisal) do
          {
            projected_arrival_date: 2.weeks.from_now.to_date,
            appraisal_status: "wholesale",
            awarded_value: 15000.50,
            notes: "Updated appraisal notes"
          }
        end

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Appraisal updated successfully")
          expect(json.dig("data", "appraisal", "appraisal_status")).to eq("wholesale")
          expect(json.dig("data", "appraisal", "awarded_value")).to eq("15000.5")
          expect(json.dig("data", "appraisal", "notes")).to eq("Updated appraisal notes")
          expect(json.dig("data", "appraisal", "projected_arrival_date")).to be_present
        end
      end

      response "422", "Invalid input" do
        let(:appraisal_uuid) { test_appraisal.uuid }

        context "when no fields are provided" do
          let(:appraisal) { {} }

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to include("At least one non-blank field must be provided for update")
          end
        end

        context "when appraisal is archived" do
          let!(:archived_appraisal) { create(:appraisal, :archived, dealership: dealership, customer: customer, sales_person: sales_person) }
          let(:appraisal_uuid) { archived_appraisal.uuid }
          let(:appraisal) { { notes: "Updated notes" } }

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to include("Cannot modify archived appraisal")
          end
        end
      end

      response "404", "Appraisal not found" do
        let(:appraisal_uuid) { "non-existent-uuid" }
        let(:appraisal) { { notes: "Updated notes" } }

        run_test! do |response|
          expect(response.status).to eq(404)
          expect(response.parsed_body.dig("status", "message")).to include("Appraisal not found")
        end
      end

      response "401", "Unauthorized" do
        let(:appraisal_uuid) { test_appraisal.uuid }
        let(:appraisal) { { notes: "Updated notes" } }
        let(:Authorization) { "Bearer invalid-token" }

        run_test!
      end
    end
  end

  # Regular RSpec request specs
  describe "PUT /api/v1/dealerships/:dealership_uuid/appraisals/:appraisal_uuid" do
    subject do
      put "/api/v1/dealerships/#{dealership_uuid}/appraisals/#{test_appraisal.uuid}",
          params: params,
          headers: headers
    end

    context "when updating projected arrival date" do
      let(:new_date) { 5.days.from_now.to_date }
      let(:params) { { projected_arrival_date: new_date } }

      it "updates the projected arrival date successfully" do
        subject
        expect(response).to have_http_status(:ok)

        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Appraisal updated successfully")
        expect(json.dig("data", "appraisal", "projected_arrival_date")).to be_present
        expect(test_appraisal.reload.projected_arrival_date).to eq(new_date)
      end
    end

    context "when updating appraisal status" do
      let(:params) { { appraisal_status: "wholesale" } }

      it "updates the appraisal status successfully" do
        subject
        expect(response).to have_http_status(:ok)

        json = response.parsed_body
        expect(json.dig("data", "appraisal", "appraisal_status")).to eq("wholesale")
        expect(test_appraisal.reload.appraisal_status).to eq("wholesale")
      end
    end

    context "when updating multiple fields" do
      let(:params) do
        {
          appraisal_status: "lost",
          awarded_value: 12000.75,
          notes: "Final appraisal completed"
        }
      end

      it "updates all provided fields successfully" do
        subject
        expect(response).to have_http_status(:ok)

        test_appraisal.reload
        expect(test_appraisal.appraisal_status).to eq("lost")
        expect(test_appraisal.awarded_value).to eq(12000.75)
        expect(test_appraisal.notes).to eq("Final appraisal completed")
      end
    end

    context "when no fields are provided" do
      let(:params) { {} }

      it "returns validation error" do
        subject
        expect(response).to have_http_status(:unprocessable_content)
        expect(response.parsed_body.dig("status", "message")).to include("At least one non-blank field must be provided for update")
      end
    end

    context "when appraisal is archived" do
      let!(:archived_appraisal) { create(:appraisal, :archived, dealership: dealership, customer: customer, sales_person: sales_person) }
      let(:params) { { notes: "Updated notes" } }

      subject do
        put "/api/v1/dealerships/#{dealership_uuid}/appraisals/#{archived_appraisal.uuid}",
            params: params,
            headers: headers
      end

      it "returns validation error" do
        subject
        expect(response).to have_http_status(:unprocessable_content)
        expect(response.parsed_body.dig("status", "message")).to include("Cannot modify archived appraisal")
      end
    end

    context "when appraisal does not exist" do
      let(:params) { { notes: "Updated notes" } }

      subject do
        put "/api/v1/dealerships/#{dealership_uuid}/appraisals/non-existent-uuid",
            params: params,
            headers: headers
      end

      it "returns not found error" do
        subject
        expect(response).to have_http_status(:not_found)
        expect(response.parsed_body.dig("status", "message")).to include("Appraisal not found")
      end
    end
  end
end
