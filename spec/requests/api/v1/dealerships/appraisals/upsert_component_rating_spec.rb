# frozen_string_literal: true

require 'rails_helper'
require 'swagger_helper'

RSpec.describe "PUT /api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/component-rating", type: :request do
  include_context "appraisal_api_shared_context"

  let(:appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
  let!(:customer_vehicle) { create(:customer_vehicle, appraisal: appraisal, customer: customer, dealership: dealership) }
  let!(:vehicle_condition) { create(:vehicle_condition, customer_vehicle: customer_vehicle) }

  path "/api/v1/dealerships/{dealership_uuid}/appraisals/{appraisal_uuid}/component-rating" do
    put "Upsert component rating for appraisal" do
      tags "Appraisals"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :appraisal_uuid, in: :path, type: :string, required: true, description: "Appraisal UUID"

      parameter name: :payload, in: :body, required: true, schema: {
        type: :object,
        required: %w[component_rating],
        properties: {
          component_rating: {
            type: :object,
            required: %w[name rating],
            properties: {
              name: {
                type: :string,
                enum: %w[front_wheels rear_wheels panel_work paint_work interior windscreen mechanical],
                description: "Component name",
                example: "interior"
              },
              rating: {
                type: :integer,
                minimum: 1,
                maximum: 5,
                description: "Component rating from 1 to 5",
                example: 3
              },
              photos: {
                type: :array,
                items: { type: :string, format: :binary },
                description: "Photos for this component"
              }
            }
          }
        }
      }

      response "201", "Component rating created successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 201 },
                     message: { type: :string, example: 'Component rating created successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete', 'complete', 'archived', 'deleted' ], example: 'incomplete' },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Toyota' },
                             model: { type: :string, example: 'Camry' },
                             build_year: { type: :integer, example: 2020 },
                             vehicle_condition: {
                               type: :object,
                               properties: {
                                 uuid: { type: :string, format: :uuid },
                                 component_ratings: {
                                   type: :array,
                                   items: {
                                     type: :object,
                                     properties: {
                                       id: { type: :integer },
                                       name: { type: :string, example: "interior" },
                                       rating: { type: :integer, example: 3 }
                                     }
                                   }
                                 }
                               }
                             }
                           }
                         }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]

        let(:appraisal_uuid) { appraisal.uuid }
        let(:payload) do
          { component_rating: {
            name: "interior",
            rating: 3
          } }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(201)
          expect(json.dig("status", "message")).to eq("Component rating created successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "uuid")).to eq(appraisal.uuid)
          expect(json.dig("data", "appraisal", "vehicle", "vehicle_condition", "component_ratings")).to be_present

          component_ratings = json.dig("data", "appraisal", "vehicle", "vehicle_condition", "component_ratings")
          interior_rating = component_ratings.find { |cr| cr["name"] == "interior" }
          expect(interior_rating).to be_present
          expect(interior_rating["id"]).to be_present
          expect(interior_rating["rating"]).to eq(3)
        end
      end

      response "200", "Component rating updated successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Component rating updated successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     appraisal: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                         status: { type: :string, enum: [ 'incomplete', 'complete', 'archived', 'deleted' ], example: 'incomplete' },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, format: :uuid, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Toyota' },
                             model: { type: :string, example: 'Camry' },
                             build_year: { type: :integer, example: 2020 },
                             vehicle_condition: {
                               type: :object,
                               properties: {
                                 uuid: { type: :string, format: :uuid },
                                 component_ratings: {
                                   type: :array,
                                   items: {
                                     type: :object,
                                     properties: {
                                       id: { type: :integer },
                                       name: { type: :string, example: "paint_work" },
                                       rating: { type: :integer, example: 5 },
                                       photo_urls: { type: :array, items: { type: :string } }
                                     }
                                   }
                                 }
                               }
                             }
                           }
                         }
                       }
                     }
                   },
                   required: [ 'appraisal' ]
                 }
               },
               required: [ 'status', 'data' ]


        let(:appraisal_uuid) { appraisal.uuid }
        let!(:existing_component) { create(:component_rating, vehicle_condition: vehicle_condition, name: :paint_work, rating: 2) }
        let(:payload) do
          { component_rating: {
            name: "paint_work",
            rating: 5
          } }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(200)
          expect(json.dig("status", "message")).to eq("Component rating updated successfully")
          expect(json.dig("data", "appraisal")).to be_present
          expect(json.dig("data", "appraisal", "uuid")).to eq(appraisal.uuid)

          component_ratings = json.dig("data", "appraisal", "vehicle", "vehicle_condition", "component_ratings")
          paint_work_rating = component_ratings.find { |cr| cr["name"] == "paint_work" }
          expect(paint_work_rating).to be_present
          expect(paint_work_rating["id"]).to eq(existing_component.id)
          expect(paint_work_rating["rating"]).to eq(5)
          # photo_urls should not be present when no photos are attached
          expect(paint_work_rating).not_to have_key("photo_urls")
        end
      end

      response "422", "Validation failed" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Validation failed: Rating must be greater than or equal to 1" }
                   }
                 }
               },
               required: [ 'status' ]


        let(:appraisal_uuid) { appraisal.uuid }
        let(:payload) do
          { component_rating: {
            name: "interior",
            rating: 0
          } }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to include("Validation failed")
        end
      end

      response "422", "No vehicle condition found for this appraisal" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "No vehicle condition found for this appraisal" }
                   }
                 }
               },
               required: [ 'status' ]


        let(:appraisal_uuid) { appraisal_without_condition.uuid }
        let(:appraisal_without_condition) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person) }
        let!(:vehicle_without_condition) { create(:customer_vehicle, appraisal: appraisal_without_condition, customer: customer, dealership: dealership) }
        let(:payload) do
          { component_rating: {
            name: "interior",
            rating: 3
          } }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to eq("No vehicle condition found for this appraisal")
        end
      end

      response "422", "Cannot modify archived appraisal" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Cannot modify archived appraisal" }
                   }
                 }
               },
               required: [ 'status' ]


        let(:appraisal_uuid) { archived_appraisal.uuid }
        let(:archived_appraisal) { create(:appraisal, dealership: dealership, customer: customer, sales_person: sales_person, status: :archived) }
        let!(:archived_vehicle) { create(:customer_vehicle, appraisal: archived_appraisal, customer: customer, dealership: dealership) }
        let!(:archived_condition) { create(:vehicle_condition, customer_vehicle: archived_vehicle) }
        let(:payload) do
          { component_rating: {
            name: "interior",
            rating: 3
          } }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(422)
          expect(json.dig("status", "message")).to eq("Cannot modify archived appraisal")
        end
      end

      response "404", "Appraisal not found" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: "Appraisal not found or does not belong to this dealership" }
                   }
                 }
               },
               required: [ 'status' ]


        let(:appraisal_uuid) { "non-existent-uuid" }
        let(:payload) do
          { component_rating: {
            name: "interior",
            rating: 3
          } }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(404)
          expect(json.dig("status", "message")).to eq("Appraisal not found or does not belong to this dealership")
        end
      end

      response "401", "Missing authorization token" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: "Missing authorization token" }
                   }
                 }
               },
               required: [ 'status' ]

        let(:Authorization) { nil }

        let(:appraisal_uuid) { appraisal.uuid }
        let(:payload) do
          { component_rating: {
            name: "interior",
            rating: 3
          } }
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "code")).to eq(401)
          expect(json.dig("status", "message")).to eq("Missing authorization token")
        end
      end

      context "with component ratings that have photos" do
        let!(:component_with_photos) { create(:component_rating, :with_photos, vehicle_condition: vehicle_condition, name: :panel_work, rating: 4) }

        it "includes photo_urls in the response" do
          get "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}", headers: headers

          expect(response).to have_http_status(:ok)
          json = response.parsed_body

          component_ratings = json.dig("data", "appraisal", "vehicle", "vehicle_condition", "component_ratings")
          panel_work_rating = component_ratings.find { |cr| cr["name"] == "panel_work" }

          expect(panel_work_rating).to be_present
          expect(panel_work_rating["photo_urls"]).to be_an(Array)
          expect(panel_work_rating["photo_urls"].length).to eq(2)
          expect(panel_work_rating["photo_urls"].first).to be_a(String)
          expect(panel_work_rating["photo_urls"].first).to include("/rails/active_storage")
        end

        it "returns empty array when no photos are attached" do
          component_without_photos = create(:component_rating, vehicle_condition: vehicle_condition, name: :interior, rating: 3)

          get "/api/v1/dealerships/#{dealership.uuid}/appraisals/#{appraisal.uuid}", headers: headers

          expect(response).to have_http_status(:ok)
          json = response.parsed_body

          component_ratings = json.dig("data", "appraisal", "vehicle", "vehicle_condition", "component_ratings")
          interior_rating = component_ratings.find { |cr| cr["name"] == "interior" }

          expect(interior_rating).to be_present
          expect(interior_rating).not_to have_key("photo_urls")
        end
      end
    end
  end
end
