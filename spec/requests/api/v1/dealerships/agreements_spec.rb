# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'Dealerships API', type: :request do
  include_context "users_api_shared_context"
  let(:toyota) { create(:brand, name: "Toyota") }

  let(:dealership) { create(:dealership, brand: toyota) }
  let(:dealership_uuid) { dealership.uuid }
  let!(:user_dealership) { create(:user_dealership, user: user, dealership: dealership, role: :sales_person) }

  path '/api/v1/dealerships/{dealership_uuid}/agreements' do
    parameter name: 'dealership_uuid', in: :path, type: :string, description: 'Dealership UUID'

    get 'Retrieves dealership agreement texts' do
      tags 'Dealerships'
      description 'Retrieves agreement texts (test drive terms, car loan terms, insurance waiver) for a specific dealership'
      consumes 'application/json'
      produces 'application/json'
      security [ Bearer: [] ]

      parameter name: 'Authorization', in: :header, type: :string, required: true,
                description: 'Bearer token in the format: Bearer <token>'

      parameter name: 'Device-ID', in: :header, type: :string, required: true,
                description: 'Device ID for authentication'

      let(:dealership_uuid) { dealership.uuid }

      response '200', 'Agreement texts retrieved successfully' do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Agreement texts retrieved successfully' }
                   }
                 },
                 data: {
                   type: :object,
                   properties: {
                     agreements: {
                       type: :object,
                       properties: {
                         test_drive_terms_text: { type: [ :string, :null ], example: 'Standard test drive terms and conditions apply.' },
                         car_loan_terms_text: { type: [ :string, :null ], example: 'Standard car loan terms and conditions apply.' },
                         insurance_waiver_text: { type: [ :string, :null ], example: 'Standard insurance waiver terms and conditions apply.' }
                       }
                     }
                   }
                 }
               }

        context 'when dealership has terms setting with all texts' do
          let!(:terms_setting) do
            create(:dealership_terms_setting,
                   dealership: dealership,
                   test_drive_terms_text: "Test drive terms and conditions",
                   car_loan_terms_text: "Car loan terms and conditions",
                   insurance_waiver_text: "Insurance waiver terms and conditions")
          end

          run_test! do |response|
            data = JSON.parse(response.body)
            expect(data.dig("status", "message")).to eq("Agreement texts retrieved successfully")
            agreements = data.dig("data", "agreements")
            expect(agreements["test_drive_terms_text"]).to eq("Test drive terms and conditions")
            expect(agreements["car_loan_terms_text"]).to eq("Car loan terms and conditions")
            expect(agreements["insurance_waiver_text"]).to eq("Insurance waiver terms and conditions")
          end
        end

        context 'when dealership has no terms setting' do
          run_test! do |response|
            data = JSON.parse(response.body)
            expect(data.dig("status", "message")).to eq("Agreement texts retrieved successfully")
            agreements = data.dig("data", "agreements")
            expect(agreements["test_drive_terms_text"]).to be_nil
            expect(agreements["car_loan_terms_text"]).to be_nil
            expect(agreements["insurance_waiver_text"]).to be_nil
          end
        end
      end

      response '404', 'Dealership not found' do
        let(:dealership_uuid) { 'non-existent-uuid' }

        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: 'Dealership not found or you don\'t have access to it' }
                   }
                 }
               }

        run_test!
      end

      response '401', 'Unauthorized' do
        let(:Authorization) { 'Bearer invalid_token' }

        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: 'Invalid or expired token' }
                   }
                 }
               }

        run_test!
      end

      response '401', 'Missing Device-ID header' do
        let(:"Device-ID") { nil }

        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: 'Invalid device' }
                   }
                 }
               }

        run_test!
      end
    end
  end

  # Non-Swagger tests for additional coverage

  describe "GET /api/v1/dealerships/:dealership_uuid/agreements" do
    subject { get "/api/v1/dealerships/#{dealership.uuid}/agreements", headers: headers }

    context "when dealership has terms setting" do
      let!(:terms_setting) do
        create(:dealership_terms_setting,
               dealership: dealership,
               test_drive_terms_text: "Test drive terms and conditions",
               car_loan_terms_text: "Car loan terms and conditions",
               insurance_waiver_text: "Insurance waiver terms and conditions")
      end

      it "returns the agreement texts" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Agreement texts retrieved successfully")
        agreements = json.dig("data", "agreements")
        expect(agreements["test_drive_terms_text"]).to eq("Test drive terms and conditions")
        expect(agreements["car_loan_terms_text"]).to eq("Car loan terms and conditions")
        expect(agreements["insurance_waiver_text"]).to eq("Insurance waiver terms and conditions")
      end
    end

    context "when dealership has no terms setting" do
      it "returns nil values for all agreement texts" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Agreement texts retrieved successfully")
        agreements = json.dig("data", "agreements")
        expect(agreements["test_drive_terms_text"]).to be_nil
        expect(agreements["car_loan_terms_text"]).to be_nil
        expect(agreements["insurance_waiver_text"]).to be_nil
      end
    end

    context "when dealership has terms setting with partial data" do
      let!(:terms_setting) do
        create(:dealership_terms_setting,
               dealership: dealership,
               test_drive_terms_text: "Only test drive terms",
               car_loan_terms_text: nil,
               insurance_waiver_text: nil)
      end

      it "returns the available agreement texts and nil for missing ones" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Agreement texts retrieved successfully")
        agreements = json.dig("data", "agreements")
        expect(agreements["test_drive_terms_text"]).to eq("Only test drive terms")
        expect(agreements["car_loan_terms_text"]).to be_nil
        expect(agreements["insurance_waiver_text"]).to be_nil
      end
    end

    context "when dealership does not exist" do
      subject { get "/api/v1/dealerships/non-existent-uuid/agreements", headers: headers }

      it "returns 404 error" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end

    context "when user does not have access to dealership" do
      let(:other_dealership) { create(:dealership) }
      subject { get "/api/v1/dealerships/#{other_dealership.uuid}/agreements", headers: headers }

      it "returns 404 error" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
