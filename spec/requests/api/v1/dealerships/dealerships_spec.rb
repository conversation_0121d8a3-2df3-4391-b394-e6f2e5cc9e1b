# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'Dealerships API', type: :request do
  include_context "users_api_shared_context"
  let(:toyota) { create(:brand, name: "Toyota") }
  let(:honda) { create(:brand, name: "Honda") }
  let(:dealership) { create(:dealership, :with_dealership_features_setting, brand: toyota) }
  let(:dealership2) { create(:dealership, status: :suspended) }
  let(:dealership3) { create(:dealership, brand: honda) }

  before do
    create(:user_dealership, user: user, dealership: dealership, role: :dealership_admin)
    create(:user_dealership, user: user, dealership: dealership2, role: :sales_person)
    create(:user_dealership, user: user, dealership: dealership3, role: :sales_person)
  end

  path '/api/v1/dealerships' do
    get 'Retrieves dealerships' do
      tags 'Dealerships'
      description 'Retrieves a list of dealerships associated with the authenticated user'
      consumes 'application/json'
      produces 'application/json'
      security [ Bearer: [] ]

      parameter name: 'Authorization', in: :header, type: :string, required: true,
                description: 'Bearer token in the format: Bearer <token>'

      parameter name: 'Device-ID', in: :header, type: :string, required: true,
                description: 'Device ID for authentication'

      response '200', 'Dealerships retrieved successfully' do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Dealerships retrieved successfully' }
                   }
                 },
                 data: {
                   type: :object,
                   properties: {
                     dealerships: {
                       type: :array,
                       items: {
                         type: :object,
                         properties: {
                           uuid: { type: :string, format: :uuid },
                           name: { type: :string, example: 'AutoNation Toyota Melbourne' },
                           setting_date_format: { type: :string, example: 'dd/mm/yyyy' },
                           setting_time_zone: { type: :string, example: 'Melbourne' },
                           setting_distance_unit: { type: :string, example: 'kilometers' },
                           status: { type: :string, example: 'active' },
                           address_line1: { type: :string, example: '123 Collins Street' },
                           state: { type: :string, example: 'Victoria' },
                           postcode: { type: :string, example: '3000' },
                           country: { type: :string, example: 'au' },
                           phone: { type: :string, example: '+61412345678' },
                           email: { type: :string, example: '<EMAIL>' },
                           website: { type: :string, example: 'https://autonation.com.au' },
                           long_name: { type: [ :string, :null ], example: 'AutoNation Toyota Melbourne Central' },
                           address_line2: { type: [ :string, :null ], example: 'Level 5' },
                           suburb: { type: [ :string, :null ], example: 'Melbourne' },
                           external_id: { type: [ :string, :null ] },
                           abn: { type: [ :string, :null ], example: '***********' },
                           created_at: { type: :string, format: :datetime },
                           updated_at: { type: :string, format: :datetime },
                           brand: {
                             type: :object,
                             properties: {
                               uuid: { type: :string, format: :uuid },
                               name: { type: :string, example: 'Toyota' }
                             }
                           },
                           dealership_features_setting: {
                             type: :object,
                             nullable: true,
                             properties: {
                               advance_booking_enabled: { type: :boolean, example: true },
                               insurance_waiver_enabled: { type: :boolean, example: true },
                               dealer_drive_subscription: { type: :boolean, example: true },
                               appraisals_subscription: { type: :boolean, example: true },
                               fuel_level_in_test_drive: { type: :boolean, example: true },
                               fuel_level_in_loan: { type: :boolean, example: true }
                             }
                           }
                         }
                       }
                     }
                   }
                 }
               }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Dealerships retrieved successfully")
          expect(data.dig("data", "dealerships")).to be_an(Array)
          dealerships = data.dig("data", "dealerships")
          expect(dealerships.size).to eq(2)
          expect(dealerships.map { |d| d["status"] }).to eq([ "active", "active" ])
          expect(dealerships.map { |d| d["brand"]["name"] }).to eq([ "Toyota", "Honda" ])
          expect(dealerships.first["uuid"]).to eq(dealership.uuid)
          expect(dealerships.first["dealership_features_setting"]["advance_booking_enabled"]).to be true
          expect(dealerships.first["dealership_features_setting"]["dealer_drive_subscription"]).to be true
          expect(dealerships.first["dealership_features_setting"]["appraisals_subscription"]).to be true
          expect(dealerships.first["dealership_features_setting"]["fuel_level_in_test_drive"]).to be true
          expect(dealerships.first["dealership_features_setting"]["fuel_level_in_loan"]).to be true
        end
      end

      response '401', 'Unauthorized' do
        let(:Authorization) { 'Bearer invalid_token' }

        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: 'Invalid or expired token' }
                   }
                 }
               }

        run_test!
      end

      response '401', 'Missing Device-ID header' do
        let(:"Device-ID") { nil }

        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: 'Invalid device' }
                   }
                 }
               }

        run_test!
      end
    end
  end

  # Non-Swagger tests for additional coverage
  describe "GET /api/v1/dealerships" do
    subject { get "/api/v1/dealerships", headers: headers }

    context "when user has multiple dealerships" do
      it "returns all active dealerships for the user" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Dealerships retrieved successfully")
        dealerships = json.dig("data", "dealerships")
        expect(dealerships.size).to eq(2)
      end
    end

    context "when user has no dealerships" do
      let(:user_without_dealerships) { create(:user) }
      let(:device_without_dealerships) { create(:device_registration, user: user_without_dealerships) }
      let(:token_without_dealerships) { Auth::TokenService.new(device_without_dealerships).generate_tokens[:access_token] }
      let(:headers_without_dealerships) do
        {
          "Authorization" => "Bearer #{token_without_dealerships}",
          "Device-ID" => device_without_dealerships.device_id
        }
      end

      it "returns empty array" do
        get "/api/v1/dealerships", headers: headers_without_dealerships
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Dealerships retrieved successfully")
        expect(json.dig("data", "dealerships")).to eq([])
      end
    end
  end
end
