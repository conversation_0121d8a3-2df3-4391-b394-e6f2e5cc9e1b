# frozen_string_literal: true

require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::DrivesController", type: :request do
  include_context "drive_api_shared_context"

  path '/api/v1/dealerships/{dealership_uuid}/drives' do
    parameter name: 'dealership_uuid', in: :path, type: :string, description: 'Dealership UUID'

    post('Create a new drive') do
      tags 'Drives'
      description 'Creates a new drive record for a dealership. Only test_drive, loan and self_loan are allowed.'
      operationId 'createDrive'
      consumes 'application/json'
      produces 'application/json'

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Bearer token for authentication'
      parameter name: 'Device-ID', in: :header, type: :string, required: true, description: 'Device ID for the request'
      parameter name: :drive, in: :body, schema: {
        type: :object,
        required: %w[vehicle_uuid drive_type],
        properties: {
          vehicle_uuid: { type: :string, description: 'UUID of the vehicle for the drive' },
          drive_type: { type: :string, enum: %w[test_drive loan self_loan], description: 'Type of drive' },
          sales_person_uuid: { type: :string, description: 'UUID of the sales person (optional)' }
        }
      }
      let(:drive) { { vehicle_uuid: vehicle_uuid, drive_type: drive_type, sales_person_uuid: sales_person_uuid } }

      response(201, 'drive created successfully') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 201 },
                     message: { type: :string, example: 'Drive Created successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     drive: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                         drive_type: { type: :string, enum: [ 'test_drive', 'loan', 'self_loan' ], example: 'test_drive' },
                         status: { type: :string, enum: [ 'draft' ], example: 'draft' },
                         sold_status: { type: :string, enum: [ 'unsold', 'sold' ], example: 'unsold' },
                         notes: { type: :string, nullable: true },
                         expected_pickup_datetime: { type: :string, format: 'date-time', nullable: true },
                         expected_return_datetime: { type: :string, format: 'date-time', nullable: true },
                         start_datetime: { type: :string, format: 'date-time', nullable: true },
                         end_datetime: { type: :string, format: 'date-time', nullable: true },
                         start_odometer_reading: { type: :integer, nullable: true },
                         end_odometer_reading: { type: :integer, nullable: true },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Toyota' },
                             model: { type: :string, example: 'Camry' },
                             build_year: { type: :integer, example: 2023 },
                             color: { type: :string, example: 'White' },
                             rego: { type: :string, example: 'ABC123' },
                             display_name: { type: :string, example: '2023 Toyota Camry' }
                           }
                         },
                         customer: {
                           type: :object,
                           nullable: true,
                           properties: {
                             uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'John' },
                             last_name: { type: :string, example: 'Doe' },
                             email: { type: :string, example: '<EMAIL>' },
                             phone_number: { type: :string, example: '+61400000000' },
                             full_name: { type: :string, example: 'John Doe' }
                           }
                         },
                         sales_person: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'Jane' },
                             last_name: { type: :string, example: 'Smith' },
                             email: { type: :string, example: '<EMAIL>' },
                             full_name: { type: :string, example: 'Jane Smith' }
                           }
                         },
                         sales_person_accompanying: {
                           type: :object,
                           nullable: true,
                           properties: {
                             uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'Bob' },
                             last_name: { type: :string, example: 'Johnson' },
                             email: { type: :string, example: '<EMAIL>' },
                             full_name: { type: :string, example: 'Bob Johnson' }
                           }
                         },
                         trade_plate: {
                           type: :object,
                           nullable: true,
                           properties: {
                             uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                             number: { type: :string, example: 'TP001' },
                             status: { type: :string, enum: [ 'active', 'inactive' ], example: 'active' },
                             expiry: { type: :string, format: 'date', example: '2024-12-31' }
                           }
                         },
                         created_at: { type: :string, format: 'date-time', example: '2023-07-01T09:00:00Z' },
                         updated_at: { type: :string, format: 'date-time', example: '2023-07-01T12:00:00Z' }
                       }
                     }
                   },
                   required: [ 'drive' ]
                 }
               },
               required: [ 'status', 'data' ]
        let(:drive_type) { 'test_drive' }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Drive Created successfully")
          expect(json.dig("data", "drive", "drive_type")).to eq("test_drive")
          expect(json.dig("data", "drive", "status")).to eq("draft")
          expect(json.dig("data", "drive", "vehicle", "uuid")).to eq(vehicle.uuid)
          expect(json.dig("data", "drive", "sales_person", "uuid")).to eq(sales_person.uuid)
        end
      end

      response(422, 'invalid drive type') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: 'Invalid Drive Type.' }
                   },
                   required: [ 'code', 'message' ]
                 }
               },
               required: [ 'status' ]
        let(:drive_type) { 'invalid_type' }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Invalid Drive Type.")
        end
      end

      response(404, 'vehicle not found') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: 'Vehicle not found' }
                   },
                   required: [ 'code', 'message' ]
                 }
               },
               required: [ 'status' ]

        let(:vehicle_uuid) { 'non-existent-uuid' }
        let(:drive_type) { 'test_drive' }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Vehicle not found")
        end
      end

      response(404, 'sales person not found') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: 'Sales person not found' }
                   },
                   required: [ 'code', 'message' ]
                 }
               },
               required: [ 'status' ]
        let(:drive_type) { 'test_drive' }
        let(:sales_person_uuid) { 'non-existent-uuid' }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Sales person not found")
        end
      end

      response(404, 'dealership not found') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: 'Dealership not found or you don\'t have access to it' }
                   },
                   required: [ 'code', 'message' ]
                 }
               },
               required: [ 'status' ]
        let(:dealership_uuid) { 'non-existent-uuid' }
        let(:drive_type) { 'test_drive' }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Dealership not found or you don't have access to it")
        end
      end

      response(401, 'unauthorized') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: 'Missing or invalid authorization token' }
                   },
                   required: [ 'code', 'message' ]
                 }
               },
               required: [ 'status' ]
        let(:Authorization) { "Bearer invalid-token" }
        let(:'Device-ID') { 'some-device-id' }
        let(:drive_type) { 'test_drive' }

        run_test!
      end
    end
  end

  # Regular RSpec request specs for create method
  describe "POST /api/v1/dealerships/:dealership_uuid/drives" do
    let(:url) { "/api/v1/dealerships/#{dealership.uuid}/drives" }
    let(:valid_params) do
      {
        vehicle_uuid: vehicle.uuid,
        drive_type: "test_drive",
        sales_person_uuid: sales_person.uuid
      }
    end

    context "with valid parameters" do
      it "creates a new drive successfully" do
        expect {
          post url, params: valid_params, headers: headers
        }.to change(Drive, :count).by(1)

        expect(response).to have_http_status(:created)
        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Drive Created successfully")

        drive_data = json.dig("data", "drive")
        expect(drive_data["drive_type"]).to eq("test_drive")
        expect(drive_data["status"]).to eq("draft")
        expect(drive_data["vehicle"]["uuid"]).to eq(vehicle.uuid)
        expect(drive_data["sales_person"]["uuid"]).to eq(sales_person.uuid)
      end

      it "defaults to current user when sales_person_uuid is not provided" do
        params_without_sales_person = valid_params.except(:sales_person_uuid)

        post url, params: params_without_sales_person, headers: headers

        expect(response).to have_http_status(:created)
        json = response.parsed_body
        drive_data = json.dig("data", "drive")
        expect(drive_data["sales_person"]["uuid"]).to eq(user.uuid)
      end

      it "works with loan drive type" do
        params = valid_params.merge(drive_type: "loan")

        post url, params: params, headers: headers

        expect(response).to have_http_status(:created)
        json = response.parsed_body
        drive_data = json.dig("data", "drive")
        expect(drive_data["drive_type"]).to eq("loan")
      end

      it "works with self_loan drive type" do
        params = valid_params.merge(drive_type: "self_loan")

        post url, params: params, headers: headers

        expect(response).to have_http_status(:created)
        json = response.parsed_body
        drive_data = json.dig("data", "drive")
        expect(drive_data["drive_type"]).to eq("self_loan")
      end
    end

    context "with invalid drive type" do
      it "returns error for invalid drive type" do
        params = valid_params.merge(drive_type: "invalid_type")

        post url, params: params, headers: headers

        expect(response).to have_http_status(:unprocessable_content)
        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Invalid Drive Type.")
      end
    end

    context "with invalid vehicle" do
      it "returns error for non-existent vehicle" do
        params = valid_params.merge(vehicle_uuid: "non-existent-uuid")

        post url, params: params, headers: headers

        expect(response).to have_http_status(:not_found)
        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Vehicle not found")
      end

      it "returns error for vehicle from different dealership" do
        other_dealership = create(:dealership)
        other_vehicle = create(:vehicle, dealership: other_dealership)
        params = valid_params.merge(vehicle_uuid: other_vehicle.uuid)

        post url, params: params, headers: headers

        expect(response).to have_http_status(:not_found)
        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Vehicle not found")
      end
    end

    context "with invalid sales person" do
      it "returns error for non-existent sales person" do
        params = valid_params.merge(sales_person_uuid: "non-existent-uuid")

        post url, params: params, headers: headers

        expect(response).to have_http_status(:not_found)
        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Sales person not found")
      end

      it "returns error for sales person from different dealership" do
        other_user = create(:user)
        params = valid_params.merge(sales_person_uuid: other_user.uuid)

        post url, params: params, headers: headers

        expect(response).to have_http_status(:not_found)
        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Sales person not found")
      end
    end

    context "without authentication" do
      let(:headers) { {} }

      it "returns unauthorized" do
        post url, params: valid_params, headers: headers
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
