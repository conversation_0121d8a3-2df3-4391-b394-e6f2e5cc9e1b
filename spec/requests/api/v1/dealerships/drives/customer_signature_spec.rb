# frozen_string_literal: true

require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::DrivesController", type: :request do
  include_context "drive_api_shared_context"

  let!(:test_drive) do
    create(:drive, :vehicle_out_type,
           dealership: dealership,
           vehicle: vehicle,
           customer: customer,
           sales_person: sales_person,
           status: :draft,
           start_odometer_reading: 10000,
           start_datetime: nil,
           end_datetime: nil)
  end

  path "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/customer-signature" do
    put "Save customer signature for drive" do
      tags "Drives"
      consumes "multipart/form-data"
      produces "application/json"
      security [ Bearer: [] ]
      description "Upload customer signature image file. The request body should contain a 'signature' field with the image file."

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :uuid, in: :path, type: :string, required: true, description: "Drive UUID"

      parameter name: :signature, in: :formData, schema: {
        type: :object,
        properties: {
          signature: {
            type: :string,
            format: :binary,
            description: "Customer signature image file (PNG/JPEG, max 5MB)"
          }
        },
        required: [ :signature ]
      }

      response "200", "Customer signature uploaded successfully" do
        let(:uuid) { test_drive.uuid }
        let(:signature) { fixture_file_upload("test_logo.png", "image/png") }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Customer signature added successfully")
          expect(json.dig("data", "drive", "customer_signature_url")).to be_present
          expect(test_drive.reload.customer_signature).to be_attached
        end
      end

      response "422", "Invalid signature file" do
        let(:uuid) { test_drive.uuid }

        context "when signature file is missing" do
          let(:signature) { nil }

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to include("Signature file is required")
          end
        end

        context "when signature file is invalid format" do
          let(:signature) { fixture_file_upload("test_document.pdf", "application/pdf") }

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to include("content type")
          end
        end
      end

      response "404", "Drive not found" do
        let(:uuid) { "non-existent-uuid" }
        let(:signature) { fixture_file_upload("test_logo.png", "image/png") }

        run_test! do |response|
          expect(response.status).to eq(404)
          expect(response.parsed_body.dig("status", "message")).to include("Drive not found")
        end
      end

      response "401", "Unauthorized" do
        let(:uuid) { test_drive.uuid }
        let(:signature) { fixture_file_upload("test_logo.png", "image/png") }

        context "when authorization header is missing" do
          let(:Authorization) { nil }

          run_test! do |response|
            expect(response.status).to eq(401)
          end
        end
      end
    end
  end
end
