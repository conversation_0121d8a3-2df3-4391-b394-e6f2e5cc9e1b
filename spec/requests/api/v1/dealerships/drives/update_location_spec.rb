# frozen_string_literal: true

require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::DrivesController", type: :request do
  include_context "drive_api_shared_context"

  path "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/location" do
    post "Update drive GPS location" do
      tags "Drives"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :uuid, in: :path, type: :string, required: true, description: "Drive UUID"
      parameter name: :location, in: :body, required: true, schema: {
        type: :object,
        properties: {
          latitude: { type: :number, format: "double", minimum: -90, maximum: 90, example: 12.9716, description: "Latitude coordinate" },
          longitude: { type: :number, format: "double", minimum: -180, maximum: 180, example: 77.5946, description: "Longitude coordinate" },
          accuracy: { type: :number, minimum: 0, maximum: 1000, example: 5.0, description: "Accuracy of the GPS reading in meters" }
        },
        required: [ "latitude", "longitude" ]
      }
      let(:location) { { latitude: 12.9716, longitude: 77.5946, accuracy: 5.0 } }

      response "200", "Location updated successfully" do
        let(:test_drive) { create(:drive, :test_drive, status: :in_progress, dealership: dealership, vehicle: vehicle,
                                  customer: customer, sales_person: sales_person, sales_person_accompanying: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:location) { { latitude: 12.9716, longitude: 77.5946, accuracy: 5.0 } }

        run_test! do |response|
          json = response.parsed_body.as_json
          expect(json["status"]["message"]).to eq("Location updated successfully")
          expect(test_drive.waypoints.count).to eq(1)
          expect(test_drive.waypoints.first.latitude).to eq(location[:latitude])
          expect(test_drive.waypoints.first.longitude).to eq(location[:longitude])
          expect(test_drive.waypoints.first.accuracy).to eq(location[:accuracy])
          expect(test_drive.waypoints.first.created_at).to be_within(1.second).of(Time.zone.now)
        end
      end

      response "422", "GPS data can only be pushed for in-progress test drives (wrong status)" do
        let(:test_drive) { create(:drive, :test_drive, status: :scheduled, dealership: dealership, vehicle: vehicle,
                                  customer: customer, sales_person: sales_person, sales_person_accompanying: sales_person) }
        let(:uuid) { test_drive.uuid }

        run_test! do |response|
          json = response.parsed_body.as_json
          expect(response.status).to eq(422)
          expect(json["status"]["code"]).to eq(422)
          expect(json["status"]["message"]).to eq("GPS data can only be pushed for in-progress test drives with accompanying sales person")
        end
      end

      response "422", "GPS data can only be pushed with accompanying sales person (sales_person_accompanying is nil)" do
        let(:test_drive) { create(:drive, :test_drive, status: :in_progress, dealership: dealership, vehicle: vehicle,
                                  customer: customer, sales_person: sales_person, sales_person_accompanying: nil) }
        let(:uuid) { test_drive.uuid }

        run_test! do |response|
          json = response.parsed_body.as_json
          expect(response.status).to eq(422)
          expect(json["status"]["code"]).to eq(422)
          expect(json["status"]["message"]).to eq("GPS data can only be pushed for in-progress test drives with accompanying sales person")
        end
      end

      response "422", "GPS data can only be pushed for in-progress test drives with accompanying sales person " do
        let(:test_drive) { create(:drive, :loan, status: :in_progress, dealership: dealership, vehicle: vehicle,
                                  customer: customer, sales_person: sales_person, sales_person_accompanying: create(:user)) }
        let(:uuid) { test_drive.uuid }

        run_test! do |response|
          json = response.parsed_body.as_json
          expect(response.status).to eq(422)
          expect(json["status"]["code"]).to eq(422)
          expect(json["status"]["message"]).to eq("GPS data can only be pushed for in-progress test drives with accompanying sales person")
        end
      end

      response "401", "Unauthorized" do
        let(:test_drive) { create(:drive, :test_drive, status: :in_progress, dealership: dealership, vehicle: vehicle,
                                  customer: customer, sales_person: sales_person, sales_person_accompanying: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:Authorization) { "Bearer invalid_token" }

        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end

      response "404", "Drive not found" do
        let(:uuid) { "non-existent-uuid" }

        run_test! do |response|
          expect(response.status).to eq(404)
        end
      end
    end
  end
end
