# frozen_string_literal: true

require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::DrivesController", type: :request do
  include_context "drive_api_shared_context"

  let!(:vehicle1) { create(:vehicle, dealership: dealership, last_known_odometer_km: 10000, last_known_fuel_gauge_level: 50) }
  let!(:customer1) { create(:customer, dealership: dealership) }
  let!(:drive) do
    create(:drive,
           dealership: dealership,
           vehicle: vehicle1,
           customer: customer1,
           sales_person: sales_person,
           drive_type: :test_drive,
           status: :in_progress,
           start_odometer_reading: 10000,
           end_odometer_reading: 10100,
           start_fuel_gauge_level: 50,
           end_fuel_gauge_level: 40)
  end
  let(:uuid) { drive.uuid }

  describe "PUT /api/v1/dealerships/{dealership_uuid}/drives/{uuid}/complete" do
    subject { put "/api/v1/dealerships/#{dealership_uuid}/drives/#{uuid}/complete", params: params, headers: headers }

    let(:params) { {} }

    context "with valid authentication" do
      context "when drive can be completed" do
        it "completes the drive successfully" do
          subject
          expect(response).to have_http_status(:ok)

          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Drive completed successfully")
          expect(json.dig("data", "drive", "status")).to eq("completed")
          expect(json.dig("data", "drive", "end_datetime")).to be_present

          drive.reload
          expect(drive.status).to eq("completed")
          expect(drive.end_datetime).to be_present
        end

        it "updates vehicle odometer" do
          expect { subject }.to change { vehicle1.reload.last_known_odometer_km }.to(10100)
        end

        it "updates vehicle fuel gauge" do
          expect { subject }.to change { vehicle1.reload.last_known_fuel_gauge_level }.to(40)
        end

        context "with notes parameter" do
          let(:params) { { notes: "Drive completed successfully with customer satisfaction" } }

          it "saves the notes" do
            subject
            expect(response).to have_http_status(:ok)

            drive.reload
            expect(drive.notes).to eq("Drive completed successfully with customer satisfaction")
          end
        end

        context "with waypoints" do
          let!(:waypoint1) { create(:gps_location, trackable: drive, latitude: -33.8688, longitude: 151.2093, accuracy: 5.0) }
          let!(:waypoint2) { create(:gps_location, trackable: drive, latitude: -33.8700, longitude: 151.2100, accuracy: 3.0) }

          before do
            # Create an initial location for the vehicle so destroy! doesn't fail
            vehicle1.update!(last_known_location: create(:gps_location, trackable: vehicle1))
          end

          it "updates vehicle location from last waypoint" do
            subject
            expect(response).to have_http_status(:ok)

            vehicle1.reload
            expect(vehicle1.last_known_location.latitude).to eq(-33.8700)
            expect(vehicle1.last_known_location.longitude).to eq(151.2100)
          end
        end

        context "with final damage report" do
          let!(:final_damage_report) { create(:damage_report, drive: drive, report_type: DamageReport::FINAL, description: 'Minor scratch on door') }

          before do
            # Create an initial damage report for the vehicle so destroy! doesn't fail
            vehicle1.update!(last_damage_report: create(:damage_report, vehicle: vehicle1, report_type: DamageReport::VEHICLE, description: 'Major scratch'))

            # Mock media files to avoid S3 issues in tests
            allow(final_damage_report).to receive(:media_files).and_return(double('media_files', each: nil))
          end

          it "creates vehicle damage report" do
            expect { subject }.to change { DamageReport.where(vehicle: vehicle1, report_type: DamageReport::VEHICLE).count }.by(0)
            expect(response).to have_http_status(:ok)

            vehicle1.reload
            expect(vehicle1.last_damage_report).to be_present
            expect(vehicle1.last_damage_report.description).to eq('Minor scratch on door')
          end
        end
      end

      context "when drive cannot be completed" do
        context "when drive is not in progress" do
          let!(:scheduled_drive) do
            create(:drive,
                   dealership: dealership,
                   vehicle: vehicle1,
                   sales_person: sales_person,
                   drive_type: :test_drive,
                   status: :scheduled)
          end
          let(:uuid) { scheduled_drive.uuid }

          it "returns error" do
            subject
            expect(response).to have_http_status(:unprocessable_content)

            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("Only in-progress drives can be marked as completed")
          end
        end

        context "when drive type is not vehicle out type" do
          let!(:booking_drive) do
            create(:drive,
                   dealership: dealership,
                   vehicle: vehicle1,
                   sales_person: sales_person,
                   drive_type: :test_drive_booking,
                   status: :in_progress)
          end
          let(:uuid) { booking_drive.uuid }

          it "returns error" do
            subject
            expect(response).to have_http_status(:unprocessable_content)

            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("This drive cannot be marked as completed")
          end
        end

        context "when drive is already completed" do
          let!(:completed_drive) do
            create(:drive,
                   dealership: dealership,
                   vehicle: vehicle1,
                   sales_person: sales_person,
                   drive_type: :test_drive,
                   status: :completed)
          end
          let(:uuid) { completed_drive.uuid }

          it "returns error" do
            subject
            expect(response).to have_http_status(:unprocessable_content)

            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("Only in-progress drives can be marked as completed")
          end
        end

        context "when end odometer reading is not provided" do
          let!(:drive) do
            create(:drive,
                   dealership: dealership,
                   vehicle: vehicle1,
                   sales_person: sales_person,
                   drive_type: :test_drive,
                   status: :in_progress,
                   start_odometer_reading: 10000)
          end
          let(:uuid) { drive.uuid }

          it "returns error" do
            subject
            expect(response).to have_http_status(:unprocessable_content)

            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("End odometer reading is required")
          end
        end
      end

      context "when drive does not exist" do
        let(:uuid) { "non-existent-uuid" }

        it "returns not found error" do
          subject
          expect(response).to have_http_status(:not_found)

          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Drive not found")
        end
      end
    end

    context "without authentication" do
      let(:headers) { {} }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "with invalid dealership" do
      let(:dealership_uuid) { "invalid-uuid" }

      it "returns not found error" do
        subject
        expect(response).to have_http_status(:not_found)
      end
    end
  end

  # Rswag API Documentation
  path "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/complete" do
    parameter name: 'dealership_uuid', in: :path, type: :string, description: 'Dealership UUID'
    parameter name: 'uuid', in: :path, type: :string, description: 'Drive UUID'

    put "Complete a drive" do
      tags "Drives"
      description "Marks an in-progress drive as completed. Only vehicle out drive types (test_drive, loan, self_loan) can be completed. Updates vehicle odometer, location, and damage reports."
      operationId "completeDrive"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Bearer token for authentication'
      parameter name: 'Device-ID', in: :header, type: :string, required: true, description: 'Device ID for the request'

      parameter name: :payload, in: :body, required: false, schema: {
        type: :object,
        properties: {
          notes: { type: :string, description: "Optional completion notes", example: "Drive completed successfully" }
        }
      }

      response(200, 'Drive completed successfully') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Drive completed successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     drive: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890' },
                         status: { type: :string, example: 'completed' },
                         drive_type: { type: :string, example: 'test_drive' },
                         start_datetime: { type: :string, format: 'date-time', example: '2024-01-15T10:00:00.000Z' },
                         end_datetime: { type: :string, format: 'date-time', example: '2024-01-15T12:00:00.000Z' },
                         expected_pickup_datetime: { type: :string, format: 'date-time', example: '2024-01-15T10:00:00.000Z' },
                         expected_return_datetime: { type: :string, format: 'date-time', example: '2024-01-15T12:00:00.000Z' },
                         start_odometer_reading: { type: :integer, example: 10000 },
                         end_odometer_reading: { type: :integer, example: 10100 },
                         notes: { type: :string, example: 'Drive completed successfully' },
                         sold_status: { type: :string, example: 'unsold' },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string },
                             make: { type: :string },
                             model: { type: :string },
                             vin: { type: :string },
                             rego: { type: :string }
                           }
                         },
                         customer: {
                           type: :object,
                           properties: {
                             uuid: { type: :string },
                             first_name: { type: :string },
                             last_name: { type: :string },
                             email: { type: :string }
                           }
                         },
                         sales_person: {
                           type: :object,
                           properties: {
                             uuid: { type: :string },
                             first_name: { type: :string },
                             last_name: { type: :string }
                           }
                         }
                       },
                       required: [ 'uuid', 'status', 'drive_type' ]
                     }
                   },
                   required: [ 'drive' ]
                 }
               },
               required: [ 'status', 'data' ]

        let(:notes) { "Drive completed successfully" }
        let(:payload) { { notes: notes } }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig('status', 'message')).to eq('Drive completed successfully')
          expect(data.dig('data', 'drive', 'status')).to eq('completed')
          expect(data.dig('data', 'drive', 'end_datetime')).to be_present
        end
      end

      response(422, 'Drive cannot be completed') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: 'Only in-progress drives can be marked as completed' }
                   },
                   required: [ 'code', 'message' ]
                 }
               },
               required: [ 'status' ]

        let!(:drive) do
          create(:drive,
                 dealership: dealership,
                 vehicle: vehicle,
                 sales_person: sales_person,
                 drive_type: :test_drive,
                 status: :scheduled)
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig('status', 'message')).to eq('Only in-progress drives can be marked as completed')
        end
      end

      response(404, 'Drive not found') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: 'Drive not found' }
                   },
                   required: [ 'code', 'message' ]
                 }
               },
               required: [ 'status' ]

        let(:uuid) { 'non-existent-uuid' }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig('status', 'message')).to eq('Drive not found')
        end
      end

      response(401, 'Unauthorized') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: 'Missing authorization token' }
                   },
                   required: [ 'code', 'message' ]
                 }
               },
               required: [ 'status' ]

        let(:Authorization) { nil }
        let(:"Device-ID") { nil }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig('status', 'code')).to eq(401)
        end
      end
    end
  end
end
