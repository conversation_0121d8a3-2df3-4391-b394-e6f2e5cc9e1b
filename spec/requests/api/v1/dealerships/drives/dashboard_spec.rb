# frozen_string_literal: true

require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::DrivesController", type: :request do
  include_context "drive_api_shared_context"

  let!(:vehicle1) { create(:vehicle, dealership: dealership) }
  let!(:vehicle2) { create(:vehicle, dealership: dealership) }
  let!(:customer1) { create(:customer, dealership: dealership) }
  let!(:customer2) { create(:customer, dealership: dealership) }

  # Create drives for dashboard data
  let!(:scheduled_today_drive) do
    create(:drive, :test_drive_booking, :scheduled,
           dealership: dealership,
           vehicle: vehicle1,
           customer: customer1,
           sales_person: sales_person,
           expected_pickup_datetime: Date.current.beginning_of_day + 10.hours,
           expected_return_datetime: Date.current.beginning_of_day + 12.hours)
  end

  let!(:in_progress_drive) do
    create(:drive, :vehicle_out_type, :in_progress,
           dealership: dealership,
           vehicle: vehicle2,
           customer: customer2,
           sales_person: sales_person)
  end

  let!(:upcoming_drive) do
    create(:drive, :test_drive_booking, :scheduled,
           dealership: dealership,
           vehicle: vehicle1,
           customer: customer1,
           sales_person: sales_person,
           expected_pickup_datetime: 2.days.from_now,
           expected_return_datetime: 2.days.from_now + 2.hours)
  end

  let!(:overdue_drive) do
    create(:drive, :vehicle_out_type, :in_progress,
           dealership: dealership,
           vehicle: vehicle2,
           customer: customer2,
           sales_person: sales_person,
           expected_return_datetime: 1.day.ago)
  end

  path '/api/v1/dealerships/{dealership_uuid}/drives/dashboard' do
    parameter name: 'dealership_uuid', in: :path, type: :string, description: 'Dealership UUID'

    get('Get dashboard data for drives') do
      tags 'Drives'
      description 'Retrieves dashboard statistics for drives including scheduled today, open drives, upcoming activities, and overdue activities'
      operationId 'getDrivesDashboard'
      consumes 'application/json'
      produces 'application/json'

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Bearer token for authentication'
      parameter name: 'Device-ID', in: :header, type: :string, required: true, description: 'Device ID for the request'

      response(200, 'successful') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Dashboard data retrieved successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     scheduled_today: { type: :integer, example: 5, description: 'Number of drives scheduled for today' },
                     open_drives: { type: :integer, example: 3, description: 'Number of drives currently in progress' },
                     upcoming_activities: { type: :integer, example: 8, description: 'Number of drives scheduled in the next 1-3 days' },
                     overdue_activities: { type: :integer, example: 2, description: 'Number of overdue drives' }
                   },
                   required: [ 'scheduled_today', 'open_drives', 'upcoming_activities', 'overdue_activities' ]
                 }
               },
               required: [ 'status', 'data' ]

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Dashboard data retrieved successfully")
          expect(json.dig("data", "scheduled_today")).to eq(1)
          expect(json.dig("data", "open_drives")).to eq(2)
          expect(json.dig("data", "upcoming_activities")).to eq(1)
          expect(json.dig("data", "overdue_activities")).to eq(1)
        end
      end

      response(401, 'unauthorized') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: 'Missing or invalid authorization token' }
                   },
                   required: [ 'code', 'message' ]
                 }
               },
               required: [ 'status' ]
        let(:Authorization) { "Bearer invalid-token" }
        let(:'Device-ID') { 'some-device-id' }

        run_test!
      end
    end
  end

  # Regular RSpec request specs for dashboard method
  describe "GET /api/v1/dealerships/:dealership_uuid/drives/dashboard" do
    let(:url) { "/api/v1/dealerships/#{dealership.uuid}/drives/dashboard" }

    context "when authenticated" do
      it "returns dashboard data successfully" do
        get url, headers: headers

        expect(response).to have_http_status(:ok)
        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Dashboard data retrieved successfully")
        expect(json["data"]).to include(
          "scheduled_today",
          "open_drives",
          "upcoming_activities",
          "overdue_activities"
        )
      end

      it "returns correct counts for each metric" do
        get url, headers: headers

        json = response.parsed_body
        data = json["data"]

        expect(data["scheduled_today"]).to eq(1)
        expect(data["open_drives"]).to eq(2)
        expect(data["upcoming_activities"]).to eq(1)
        expect(data["overdue_activities"]).to eq(1)
      end
    end

    context "when not authenticated" do
      it "returns unauthorized error" do
        get url

        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "when dealership not found" do
      let(:url) { "/api/v1/dealerships/invalid-uuid/drives/dashboard" }

      it "returns not found error" do
        get url, headers: headers

        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
