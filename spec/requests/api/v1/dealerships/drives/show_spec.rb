# frozen_string_literal: true

require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::DrivesController", type: :request do
  include_context "drive_api_shared_context"

  let!(:vehicle1) { create(:vehicle, dealership: dealership) }
  let!(:customer1) { create(:customer, dealership: dealership) }
  let!(:trade_plate) { create(:trade_plate, dealership: dealership) }

  let!(:drive1) do
    create(:drive,
           dealership: dealership,
           vehicle: vehicle1,
           customer: customer1,
           sales_person: sales_person,
           trade_plate: trade_plate,
           drive_type: :test_drive,
           status: :completed,
           sold_status: :sold,
           start_datetime: 2.days.ago)
  end

  describe "GET /api/v1/dealerships/:dealership_uuid/drives/:uuid" do
    subject { get "/api/v1/dealerships/#{dealership.uuid}/drives/#{drive1.uuid}", headers: headers }

    context "with valid authentication" do
      it "returns the specific drive" do
        subject
        expect(response).to have_http_status(:ok)

        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Drive retrieved successfully")

        drive_data = json.dig("data", "drive")
        expect(drive_data["uuid"]).to eq(drive1.uuid)
        expect(drive_data["drive_type"]).to eq("test_drive")
        expect(drive_data["status"]).to eq("completed")
      end

      it "includes all associations" do
        subject

        json = response.parsed_body
        drive_data = json.dig("data", "drive")

        expect(drive_data["vehicle"]["uuid"]).to eq(vehicle1.uuid)
        expect(drive_data["customer"]["uuid"]).to eq(customer1.uuid)
        expect(drive_data["sales_person"]["uuid"]).to eq(sales_person.uuid)
        expect(drive_data["trade_plate"]["uuid"]).to eq(trade_plate.uuid)
      end
    end

    context "with non-existent drive" do
      subject { get "/api/v1/dealerships/#{dealership.uuid}/drives/non-existent-uuid", headers: headers }

      it "returns not found" do
        subject
        expect(response).to have_http_status(:not_found)

        json = response.parsed_body
        expect(json.dig("status", "message")).to include("Drive not found")
      end
    end

    context "without authentication" do
      let(:headers) { {} }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  path '/api/v1/dealerships/{dealership_uuid}/drives/{uuid}' do
    parameter name: 'dealership_uuid', in: :path, type: :string, description: 'Dealership UUID'
    parameter name: 'uuid', in: :path, type: :string, description: 'Drive UUID'

    get('Get drive by ID') do
      tags 'Drives'
      description 'Retrieves a specific drive by its UUID for a dealership'
      operationId 'getDriveById'
      consumes 'application/json'
      produces 'application/json'

      parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Bearer token for authentication'
      parameter name: 'Device-ID', in: :header, type: :string, required: true, description: 'Device ID for the request'

      response(200, 'successful') do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Drive retrieved successfully' }
                   },
                   required: [ 'code', 'message' ]
                 },
                 data: {
                   type: :object,
                   properties: {
                     drive: {
                       type: :object,
                       properties: {
                         uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                         drive_type: { type: :string, enum: [ 'test_drive', 'enquiry', 'loan', 'loan_booking', 'test_drive_booking', 'self_loan' ], example: 'test_drive' },
                         status: { type: :string, enum: [ 'scheduled', 'in_progress', 'completed', 'cancelled', 'draft', 'deleted' ], example: 'completed' },
                         sold_status: { type: :string, enum: [ 'unsold', 'sold' ], example: 'sold' },
                         notes: { type: :string, example: 'Customer interested in purchasing', nullable: true },
                         expected_pickup_datetime: { type: :string, format: 'date-time', example: '2023-07-01T10:00:00Z', nullable: true },
                         expected_return_datetime: { type: :string, format: 'date-time', example: '2023-07-01T12:00:00Z', nullable: true },
                         start_datetime: { type: :string, format: 'date-time', example: '2023-07-01T10:15:00Z', nullable: true },
                         end_datetime: { type: :string, format: 'date-time', example: '2023-07-01T11:45:00Z', nullable: true },
                         start_odometer_reading: { type: :integer, example: 15000, nullable: true },
                         end_odometer_reading: { type: :integer, example: 15025, nullable: true },
                         vehicle: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                             make: { type: :string, example: 'Toyota' },
                             model: { type: :string, example: 'Camry' },
                             build_year: { type: :integer, example: 2023 },
                             color: { type: :string, example: 'Blue' },
                             rego: { type: :string, example: 'ABC123' },
                             display_name: { type: :string, example: '2023 Toyota Camry' }
                           }
                         },
                         customer: {
                           type: :object,
                           nullable: true,
                           properties: {
                             uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'John' },
                             last_name: { type: :string, example: 'Doe' },
                             full_name: { type: :string, example: 'John Doe' },
                             email: { type: :string, example: '<EMAIL>' },
                             phone_number: { type: :string, example: '+61412345678' }
                           }
                         },
                         sales_person: {
                           type: :object,
                           properties: {
                             uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'Jane' },
                             last_name: { type: :string, example: 'Smith' },
                             full_name: { type: :string, example: 'Jane Smith' },
                             email: { type: :string, example: '<EMAIL>' }
                           }
                         },
                         sales_person_accompanying: {
                           type: :object,
                           nullable: true,
                           properties: {
                             uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                             first_name: { type: :string, example: 'Bob' },
                             last_name: { type: :string, example: 'Wilson' },
                             full_name: { type: :string, example: 'Bob Wilson' },
                             email: { type: :string, example: '<EMAIL>' }
                           }
                         },
                         trade_plate: {
                           type: :object,
                           nullable: true,
                           properties: {
                             uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                             number: { type: :string, example: 'TP001' },
                             expiry: { type: :string, format: :date, example: '2024-12-31' },
                             status: { type: :string, enum: [ 'active', 'inactive' ], example: 'active' }
                           }
                         },
                         driver_license: {
                           type: :object,
                           nullable: true,
                           properties: {
                             uuid: { type: :string, example: '550e8400-e29b-41d4-a716-************' },
                             licence_number: { type: :string, example: 'DL123456789' },
                             full_name: { type: :string, example: 'John Doe' },
                             expiry_date: { type: :string, format: :date, example: '2025-06-30' }
                           }
                         },
                         created_at: { type: :string, format: 'date-time', example: '2023-07-01T09:00:00Z' },
                         updated_at: { type: :string, format: 'date-time', example: '2023-07-01T12:00:00Z' }
                       }
                     }
                   },
                   required: [ 'drive' ]
                 }
               },
               required: [ 'status', 'data' ]
        let!(:drive) { create(:drive, dealership: dealership) }
        let(:uuid) { drive.uuid }
        run_test!
      end

      response(401, 'unauthorized') do
        let(:uuid) { 'some-uuid' }
        let(:Authorization) { 'Bearer invalid_token' }
        let(:'Device-ID') { 'invalid_device_id' }
        run_test!
      end

      response(404, 'drive not found') do
        let(:uuid) { 'non-existent-uuid' }
        run_test!
      end
    end
  end
end
