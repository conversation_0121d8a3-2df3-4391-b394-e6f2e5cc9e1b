require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::UsersController#update_profile", type: :request do
  include_context "users_api_shared_context"
  let(:user) { create(:user, first_name: "<PERSON>", last_name: "<PERSON><PERSON>") }

  path "/api/v1/profile" do
    patch "Update user profile" do
      tags "Users"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"

      parameter name: :profile_params, in: :body, schema: {
        type: :object,
        properties: {
          first_name: { type: :string, example: "<PERSON>" },
          last_name: { type: :string, example: "<PERSON>" },
          preferred_2fa: { type: :string, enum: [ "email", "sms" ], example: "email" },
          job_title: { type: :string, example: "Sales Manager" },
          preferred_language: { type: :string, example: "spanish" },
          time_zone: { type: :string, example: "Brisbane" },
          onboarding_completed: { type: :boolean, example: true }
        }
      }

      response "200", "Profile updated successfully" do
        let(:profile_params) do
          {
            first_name: "Jane",
            last_name: "Smith",
            job_title: "Sales Manager",
            preferred_language: "english",
            time_zone: "Adelaide",
            onboarding_completed: true
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Profile updated successfully")
          expect(data.dig("data", "user", "first_name")).to eq("Jane")
          expect(data.dig("data", "user", "last_name")).to eq("Smith")
          expect(data.dig("data", "user", "job_title")).to eq("Sales Manager")

          # Verify database was updated
          user.reload
          expect(user.first_name).to eq("Jane")
          expect(user.last_name).to eq("Smith")
          expect(user.job_title).to eq("Sales Manager")
          expect(user.preferred_language).to eq("english")
          expect(user.time_zone).to eq("Adelaide")
          expect(user.onboarding_completed).to eq(true)
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid_token" }
        let(:profile_params) { { first_name: "Jane" } }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to include("Invalid token")
        end
      end

      response "422", "Validation failed" do
        let(:profile_params) { { first_name: "" } }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(response.status).to eq(422)
          expect(data.dig("status", "message")).to include("can't be blank")
        end
      end
    end
  end

  # Regular RSpec tests for more detailed scenarios
  describe "PATCH /api/v1/profile" do
    subject { patch "/api/v1/profile", params: params, headers: headers, as: :json }

    context "when updating basic profile information" do
      let(:params) do
        {
          first_name: "Jane",
          last_name: "Smith"
        }
      end

      it "updates the user profile" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Profile updated successfully")
        expect(json.dig("data", "user", "first_name")).to eq("Jane")
        expect(json.dig("data", "user", "last_name")).to eq("Smith")

        user.reload
        expect(user.first_name).to eq("Jane")
        expect(user.last_name).to eq("Smith")
      end
    end

    context "when updating preferences" do
      let(:params) do
        {
          preferred_2fa: "sms",
          preferred_language: "english",
          time_zone: "Perth"
        }
      end

      it "updates user preferences" do
        subject
        expect(response).to have_http_status(:ok)

        user.reload
        expect(user.preferred_2fa).to eq("sms")
        expect(user.preferred_language).to eq("english")
        expect(user.time_zone).to eq("Perth")
      end
    end

    context "when completing onboarding" do
      let(:params) do
        {
          onboarding_completed: true
        }
      end

      it "marks onboarding as completed" do
        subject
        expect(response).to have_http_status(:ok)

        user.reload
        expect(user.onboarding_completed).to be true
      end
    end

    context "when missing authorization" do
      let(:headers) { {} }
      let(:params) { { first_name: "Jane" } }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "when missing device ID" do
      let(:headers) do
        {
          "Authorization" => "Bearer #{valid_token}"
        }
      end
      let(:params) { { first_name: "Jane" } }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
