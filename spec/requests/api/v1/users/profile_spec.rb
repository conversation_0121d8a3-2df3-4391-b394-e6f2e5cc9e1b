require "rails_helper"
require "swagger_helper"

RSpec.describe "GET /api/v1/profile", type: :request do
  include_context "users_api_shared_context"

  path "/api/v1/profile" do
    get("Get profile") do
      tags "Users"
      security [ Bearer: [] ]
      consumes "application/json"
      produces "application/json"

      parameter name: "Authorization", in: :header, type: :string, required: true, example: "Bearer <token>"
      parameter name: "Device-ID", in: :header, type: :string, required: true, example: "device-123"

      response(200, "Profile retrieved successfully") do
        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("Profile retrieved successfully")
          expect(json.dig("data", "user")).to be_present
          expect(json.dig("data", "current_device", "device_id")).to eq(device_registration.device_id)
        end
      end

      response(401, "Missing authorization token") do
        let(:Authorization) { nil }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("Missing authorization token")
        end
      end

      response(401, "Missing device ID") do
        let(:'Device-ID') { nil }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("Invalid device")
        end
      end

      response(401, "Token has expired") do
        before do
          allow_any_instance_of(Auth::TokenService)
            .to receive(:decode_and_verify_token)
            .and_raise(Errors::TokenExpired.new("Token has expired"))
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("Token has expired")
        end
      end

      response(401, "Invalid token") do
        before do
          allow_any_instance_of(Auth::TokenService)
            .to receive(:decode_and_verify_token)
            .and_raise(Errors::InvalidToken.new("Invalid token"))
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("Invalid token")
        end
      end
    end
  end

  describe "GET /api/v1/profile" do
    subject { get "/api/v1/profile", headers: request_headers }

    context "when authorized" do
      let(:request_headers) { headers }

      it "returns user profile with current device info" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Profile retrieved successfully")
        expect(json.dig("data", "user")).to be_present
        expect(json.dig("data", "current_device", "device_id")).to eq(device_registration.device_id)
      end

      it "includes dealerships data in profile response" do
        dealership = create(:dealership)
        create(:user_dealership, user: user, dealership: dealership)

        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        user_data = json.dig("data", "user")
        expect(user_data).to have_key("dealerships")
        expect(user_data["dealerships"]).to be_an(Array)
      end
    end

    context "when missing Authorization token" do
      let(:request_headers) { headers.except("Authorization") }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Missing authorization token")
      end
    end

    context "when missing Device-ID header" do
      let(:request_headers) { headers.except("Device-ID") }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Missing device ID")
      end
    end

    context "when token is expired" do
      let(:request_headers) { headers }

      before do
        allow_any_instance_of(Auth::TokenService)
          .to receive(:decode_and_verify_token)
          .and_raise(Errors::TokenExpired.new("Token has expired"))
      end

      it "returns token expired error" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Token has expired")
      end
    end

    context "when token is invalid" do
      let(:request_headers) { headers }

      before do
        allow_any_instance_of(Auth::TokenService)
          .to receive(:decode_and_verify_token)
          .and_raise(Errors::InvalidToken.new("Invalid token"))
      end

      it "returns invalid token error" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Invalid token")
      end
    end
  end
end
