require "rails_helper"
require "swagger_helper"

RSpec.describe "PUT /api/v1/profile/photo", type: :request do
  include_context "users_api_shared_context"

  let(:url) { "/api/v1/profile/photo" }

  path "/api/v1/profile/photo" do
    put("Update profile photo") do
      tags "Users"
      consumes "multipart/form-data"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "photo", in: :formData, type: :file, required: true, description: "Image file (JPEG/PNG)"
      parameter name: "Authorization", in: :header, type: :string, required: true, example: "Bearer <token>"
      parameter name: "Device-ID", in: :header, type: :string, required: true, example: "device-123"

      response(200, "Photo updated successfully") do
        let(:photo) { fixture_file_upload(Rails.root.join("spec/fixtures/files/profile.jpg"), "image/jpeg") }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("Profile photo updated successfully")
        end
      end

      response "422", "Photo is missing" do
        let(:photo) { nil }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Photo is required")
        end
      end

      response(422, "Unsupported file type") do
        let(:photo) { fixture_file_upload(Rails.root.join("spec/fixtures/files/invalid.txt"), "text/plain") }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to include("invalid content type")
        end
      end

      response(401, "Missing authorization token") do
        let(:Authorization) { nil }
        let("Device-ID") { device_registration.device_id }
        let(:photo) { fixture_file_upload(Rails.root.join("spec/fixtures/files/profile.jpg"), "image/jpeg") }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("Missing authorization token")
        end
      end

      response(401, "Token has expired") do
        before do
          allow_any_instance_of(Auth::TokenService)
            .to receive(:decode_and_verify_token)
            .and_raise(Errors::TokenExpired.new("Token has expired"))
        end
        let(:photo) { fixture_file_upload(Rails.root.join("spec/fixtures/files/profile.jpg"), "image/jpeg") }

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("Token has expired")
        end
      end
    end
  end

  describe "PUT /api/v1/profile/photo" do
    subject { put url, params: params, headers: request_headers }

    let(:valid_photo) do
      fixture_file_upload(Rails.root.join("spec/fixtures/files/profile.jpg"), "image/jpeg")
    end

    context "when authorized and photo is valid" do
      let(:request_headers) { headers }
      let(:params) { { photo: valid_photo } }

      it "updates the profile photo" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.dig("status", "message")).to eq("Profile photo updated successfully")
      end
    end

    context "when photo is missing" do
      let(:request_headers) { headers }
      let(:params) { {} }

      it "returns 422 with error message" do
        subject
        expect(response).to have_http_status(:unprocessable_content)
        expect(response.parsed_body.dig("status", "message")).to eq("Photo is required")
      end
    end

    context "when file type is invalid" do
      let(:request_headers) { headers }
      let(:invalid_file) do
        fixture_file_upload(Rails.root.join("spec/fixtures/files/invalid.txt"), "text/plain")
      end
      let(:params) { { photo: invalid_file } }

      it "returns unsupported media type" do
        subject
        expect(response).to have_http_status(:unprocessable_content)
        expect(response.parsed_body.dig("status", "message")).to include("invalid content type")
      end
    end

    context "when missing Authorization token" do
      let(:request_headers) { headers.except("Authorization") }
      let(:params) { { photo: valid_photo } }

      it "returns unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Missing authorization token")
      end
    end

    context "when token is expired" do
      let(:request_headers) { headers }
      let(:params) { { photo: valid_photo } }

      before do
        allow_any_instance_of(Auth::TokenService)
          .to receive(:decode_and_verify_token)
          .and_raise(Errors::TokenExpired.new("Token has expired"))
      end

      it "returns token expired error" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Token has expired")
      end
    end
  end
end
