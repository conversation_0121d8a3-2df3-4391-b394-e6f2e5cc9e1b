require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::UsersController#verify_reset_code", type: :request do
  include_context "users_api_shared_context"

  let(:valid_code) { "123456" }
  let(:temporary_token) { Auth::TokenService.new(nil, user).generate_temporary_token.first }

  path "/api/v1/auth/verify-reset-code" do
    post "Verify Reset Code" do
      tags "Authentication"
      consumes "application/json"
      produces "application/json"

      parameter name: :payload, in: :body, schema: {
        type: :object,
        required: %w[temporary_token reset_code],
        properties: {
          temporary_token: { type: :string, example: "temp.token" },
          reset_code: { type: :string, example: "123456" }
        }
      }

      response "200", "code verified" do
        let(:payload) { { temporary_token: temporary_token, reset_code: valid_code } }

        before do
          allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_return(user)
          allow_any_instance_of(PasswordService).to receive(:validate_security_code).and_return(true)
          allow_any_instance_of(PasswordService).to receive(:clear_code!)
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("Reset Code verified successfully.")
        end
      end

      response "422", "missing inputs" do
        let(:payload) { { temporary_token: "" } }

        run_test! do |response|
          expect(response).to have_http_status(:unprocessable_content)
        end
      end
    end
  end

  describe "POST /api/v1/auth/verify-reset-code" do
    subject do
      post "/api/v1/auth/verify-reset-code", params: {
        temporary_token: token,
        reset_code: code
      }
    end

    before do
      allow_any_instance_of(Auth::TokenService).to receive(:generate_temporary_token).and_return([ "reset.token", 15.minutes.from_now.to_i ])
      allow_any_instance_of(PasswordService).to receive(:clear_code!)
    end

    context "with valid token and code" do
      let(:token) { temporary_token }
      let(:code) { valid_code }

      before do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_return(user)
        allow_any_instance_of(PasswordService).to receive(:validate_security_code).and_return(true)
      end

      it "returns success with reset token" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.dig("status", "message")).to eq("Reset Code verified successfully.")
      end
    end

    context "with missing token or code" do
      context "missing token" do
        let(:token) { nil }
        let(:code) { valid_code }

        it "returns 422" do
          subject
          expect(response).to have_http_status(:unprocessable_content)
        end
      end

      context "missing code" do
        let(:token) { temporary_token }
        let(:code) { nil }

        it "returns 422" do
          subject
          expect(response).to have_http_status(:unprocessable_content)
        end
      end
    end

    context "with invalid or expired token" do
      let(:token) { "invalid.token" }
      let(:code) { valid_code }

      it "returns 401 for invalid token" do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_raise(Errors::InvalidToken)
        subject
        expect(response).to have_http_status(:unauthorized)
      end

      it "returns 401 for expired token" do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_raise(Errors::TokenExpired)
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end

    context "with invalid reset code" do
      let(:token) { temporary_token }
      let(:code) { "wrong-code" }

      before do
        allow_any_instance_of(Auth::TokenService).to receive(:decode_token).and_return(user)
        allow_any_instance_of(PasswordService).to receive(:validate_security_code).and_return(false)
      end

      it "returns 401 unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
