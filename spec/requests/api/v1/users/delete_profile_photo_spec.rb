require "rails_helper"
require "swagger_helper"

RSpec.describe "DELETE /api/v1/profile/photo", type: :request do
  include_context "users_api_shared_context"

  path "/api/v1/profile/photo" do
    delete("Delete profile photo") do
      tags "Users"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: :"Authorization", in: :header, type: :string, required: true
      parameter name: :"Device-ID", in: :header, type: :string, required: true

      response "200", "Profile photo deleted successfully" do
        before do
          user.photo.attach(
            io: Rails.root.join("spec/fixtures/files/car_1.png").open,
            filename: "test.png",
            content_type: "image/png"
          )
        end

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Profile photo deleted successfully")
        end
      end

      response "422", "No photo attached" do
        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("No photo attached")
        end
      end
    end
  end

  describe "Regular RSpec request specs" do
    subject { delete "/api/v1/profile/photo", headers: headers }

    context "when user has a photo attached" do
      before do
        user.photo.attach(io: Rails.root.join("spec/fixtures/files/car_1.png").open, filename: "test.png", content_type: "image/png")
      end

      it "removes the photo and returns success" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.dig("status", "message")).to eq("Profile photo deleted successfully")
      end
    end

    context "when user has no photo attached" do
      it "returns 422 with appropriate error message" do
        subject
        expect(response).to have_http_status(:unprocessable_content)
        expect(response.parsed_body.dig("status", "message")).to eq("No photo attached")
      end
    end
  end
end
