require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::UsersController#change_password", type: :request do
  include_context "users_api_shared_context"

  path "/api/v1/auth/change-password" do
    put "Change Password" do
      tags "Authentication"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true
      parameter name: "Device-ID", in: :header, type: :string, required: true

      parameter name: :payload, in: :body, schema: {
        type: :object,
        required: %w[current_password new_password],
        properties: {
          current_password: { type: :string },
          new_password: { type: :string }
        }
      }

      response "200", "password changed" do
        let(:payload) { { current_password: "Drive@2025", new_password: "NewPass@456" } }

        before do
          allow(user).to receive(:valid_password?).with("Drive@2025").and_return(true)
          allow(user).to receive(:update!).with(password: "NewPass@456").and_return(true)
          allow(user).to receive(:mark_password_as_changed!).and_return(true)
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("Password changed successfully")
        end
      end

      response "422", "Incorrect current password" do
        let(:payload) { { current_password: "wrong", new_password: "NewPass@456" } }

        before do
          allow(user).to receive(:valid_password?).with("wrong").and_return(false)
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("Incorrect current password")
        end
      end

      response "422", "New password is same as current" do
        let(:payload) { { current_password: "Drive@2025", new_password: "Drive@2025" } }

        before do
          allow(user).to receive(:valid_password?).with("Drive@2025").and_return(true)
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to eq("New password cannot be the same as the current password")
        end
      end

      response "422", "Weak new password" do
        let(:payload) { { current_password: "Drive@2025", new_password: "weak" } }

        before do
          allow(user).to receive(:valid_password?).with("Drive@2025").and_return(true)
          allow(user).to receive(:update!).with(password: "weak").and_raise(
            ActiveRecord::RecordInvalid.new(user.tap { |u|
              u.errors.add(:password, "Password requirement not met. Please use: 1 uppercase, 1 lowercase, 1 digit and 1 special character")
            })
          )
        end

        run_test! do |response|
          json = JSON.parse(response.body)
          expect(json.dig("status", "message")).to include("Password requirement not met")
        end
      end

      response "401", "Unauthorized" do
        let(:Authorization) { "Bearer invalid" }
        let(:payload) { { current_password: "Drive@2025", new_password: "NewPass@456" } }

        run_test!
      end
    end
  end

  describe "PUT /api/v1/auth/change-password" do
    subject { put "/api/v1/auth/change-password", params: params, headers: headers, as: :json }

    context "when password is changed successfully" do
      let(:params) { { current_password: "Drive@2025", new_password: "NewPass@456" } }

      before do
        allow(user).to receive(:valid_password?).with("Drive@2025").and_return(true)
        allow(user).to receive(:update!).with(password: "NewPass@456").and_return(true)
        allow(user).to receive(:mark_password_as_changed!).and_return(true)
      end

      it "returns success response" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.dig("status", "message")).to eq("Password changed successfully")
      end
    end

    context "when current password is incorrect" do
      let(:params) { { current_password: "wrong", new_password: "NewPass@456" } }

      before do
        allow(user).to receive(:valid_password?).with("wrong").and_return(false)
      end

      it "returns 422 error" do
        subject
        expect(response).to have_http_status(:unprocessable_content)
        expect(response.parsed_body.dig("status", "message")).to eq("Incorrect current password")
      end
    end

    context "when new password is same as current" do
      let(:params) { { current_password: "Drive@2025", new_password: "Drive@2025" } }

      before do
        allow(user).to receive(:valid_password?).with("Drive@2025").and_return(true)
      end

      it "returns 422 error" do
        subject
        expect(response).to have_http_status(:unprocessable_content)
        expect(response.parsed_body.dig("status", "message")).to eq("New password cannot be the same as the current password")
      end
    end

    context "when new password is weak" do
      let(:params) { { current_password: "Drive@2025", new_password: "weak" } }

      before do
        allow(user).to receive(:valid_password?).with("Drive@2025").and_return(true)
        allow(user).to receive(:update!).with(password: "weak").and_raise(
          ActiveRecord::RecordInvalid.new(user.tap { |u|
            u.errors.add(:password, "Password requirement not met. Please use: 1 uppercase, 1 lowercase, 1 digit and 1 special character")
          })
        )
      end

      it "returns 422 error" do
        subject
        expect(response).to have_http_status(:unprocessable_content)
        expect(response.parsed_body.dig("status", "message")).to include("Password requirement not met")
      end
    end

    context "when headers are missing" do
      let(:headers) { {} }
      let(:params) { { current_password: "Drive@2025", new_password: "NewPass@456" } }

      it "returns 401 unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
      end
    end
  end
end
