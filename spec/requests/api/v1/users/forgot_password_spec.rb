require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::UsersController#forgot_password", type: :request do
  include_context "users_api_shared_context"

  path "/api/v1/auth/forgot-password" do
    post "Forgot Password" do
      tags "Authentication"
      consumes "application/json"
      produces "application/json"

      parameter name: :payload, in: :body, schema: {
        type: :object,
        required: %w[email],
        properties: {
          email: { type: :string, example: "<EMAIL>" }
        }
      }

      response "200", "reset code sent" do
        let(:payload) { { email: user.email } }

        before do
          allow(PasswordService).to receive(:new)
            .and_return(instance_double(PasswordService, generate_and_send_reset_code: true))

          allow_any_instance_of(Auth::TokenService)
            .to receive(:generate_temporary_token)
            .and_return([ "mock-temporary-token", 1.hour.from_now.to_i ])
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Reset password code sent successfully")
          expect(data.dig("data", "temporary_token")).to eq("mock-temporary-token")
          expect(data.dig("data", "expires_at")).to be > 0
        end
      end

      response "404", "user not found" do
        let(:payload) { { email: "<EMAIL>" } }

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("User not found")
        end
      end
    end
  end

  describe "POST /api/v1/auth/forgot-password" do
    subject { post "/api/v1/auth/forgot-password", params: params }

    let(:mock_token) { "mock-temporary-token" }
    let(:mock_expiry_time) { 1.hour.from_now.to_i }

    before do
      allow(PasswordService).to receive(:new)
        .and_return(instance_double(PasswordService, generate_and_send_reset_code: true))

      allow_any_instance_of(Auth::TokenService)
        .to receive(:generate_temporary_token)
        .and_return([ mock_token, mock_expiry_time ])
    end

    context "when the email is valid" do
      let(:params) { { email: user.email } }

      it "returns temporary token and success message" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Reset password code sent successfully")
        expect(json.dig("data", "temporary_token")).to eq(mock_token)
        expect(json.dig("data", "expires_at")).to be > 0
      end
    end

    context "when the user is not found" do
      let(:params) { { email: "<EMAIL>" } }

      it "returns 404 with user not found message" do
        subject
        expect(response).to have_http_status(:not_found)
        expect(response.parsed_body.dig("status", "message")).to eq("User not found")
      end
    end

    context "when email param is missing" do
      let(:params) { {} }

      it "returns 404 not found" do
        post "/api/v1/auth/forgot-password", params: {}
        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
