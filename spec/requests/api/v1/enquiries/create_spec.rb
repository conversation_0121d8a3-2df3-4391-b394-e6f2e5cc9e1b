require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::EnquiriesController#create", type: :request do
  include_context "drive_api_shared_context"

  path "/api/v1/dealerships/{dealership_uuid}/enquiries" do
    post "Creates an enquiry" do
      tags "Enquiries"
      consumes "multipart/form-data"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device ID"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :enquiry, in: :formData, schema: {
        type: :object,
        properties: {
          vehicle_uuid: { type: :string, description: "Vehicle UUID" },
          sales_person_uuid: { type: :string, description: "Sales person UUID (optional, defaults to current user)" },
          customer_uuid: { type: :string, description: "Existing customer UUID" },
          notes: { type: :string, description: "Enquiry notes" },
          customer_info: {
            type: :object,
            description: "New customer information (alternative to customer_uuid)",
            properties: {
              first_name: { type: :string },
              last_name: { type: :string },
              age: { type: :integer },
              email: { type: :string },
              phone_number: { type: :string },
              gender: { type: :string, enum: %w[unspecified male female other] },
              address_line1: { type: :string },
              address_line2: { type: :string },
              suburb: { type: :string },
              city: { type: :string },
              state: { type: :string },
              country: { type: :string },
              postcode: { type: :string },
              company_name: { type: :string },
              driver_license: {
                type: :object,
                properties: {
                  licence_number: { type: :string },
                  expiry_date: { type: :string, format: :date },
                  issuing_state: { type: :string },
                  category: { type: :string },
                  issue_date: { type: :string, format: :date },
                  issuing_country: { type: :string },
                  full_name: { type: :string },
                  date_of_birth: { type: :string, format: :date },
                  front_image: { type: :string, format: :binary },
                  back_image: { type: :string, format: :binary }
                }
              }
            }
          }
        },
        required: [ :vehicle_uuid ]
      }

      response "201", "Enquiry created successfully" do
        let(:enquiry) do
          {
            vehicle_uuid: vehicle.uuid,
            sales_person_uuid: sales_person.uuid,
            customer_uuid: customer.uuid,
            notes: "Customer enquiry about vehicle"
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data["status"]["code"]).to eq(200)
          expect(data["status"]["message"]).to eq("Enquiry created successfully")
          expect(data["data"]["enquiry"]["drive_type"]).to eq("enquiry")
          expect(data["data"]["enquiry"]["vehicle"]["uuid"]).to eq(vehicle.uuid)
          expect(data["data"]["enquiry"]["customer"]["uuid"]).to eq(customer.uuid)
          expect(data["data"]["enquiry"]["sales_person"]["uuid"]).to eq(sales_person.uuid)
        end
      end

      response "404", "Vehicle not found" do
        let(:enquiry) do
          {
            vehicle_uuid: "non-existent-uuid",
            sales_person_uuid: sales_person.uuid,
            customer_uuid: customer.uuid
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data["status"]["code"]).to eq(404)
          expect(data["status"]["message"]).to include("Vehicle not found")
        end
      end
    end
  end

  describe "POST /api/v1/dealerships/:dealership_uuid/enquiries" do
    let(:url) { "/api/v1/dealerships/#{dealership.uuid}/enquiries" }

    let(:valid_params) do
      {
        enquiry: {
          vehicle_uuid: vehicle.uuid,
          sales_person_uuid: sales_person.uuid,
          customer_uuid: customer.uuid,
          notes: "Customer enquiry about vehicle"
        }
      }
    end

    it "creates an enquiry with existing customer" do
      post url, params: valid_params, headers: headers
      expect(response).to have_http_status(:created)
      data = response.parsed_body[:data][:enquiry]
      expect(data[:customer][:uuid]).to eq(customer.uuid)
      expect(data[:drive_type]).to eq("enquiry")
    end

    it "creates an enquiry with new customer info" do
      params_with_customer_info = {
        enquiry: {
          vehicle_uuid: vehicle.uuid,
          sales_person_uuid: sales_person.uuid,
          notes: "Customer enquiry",
          customer_info: {
            first_name: "John",
            last_name: "Doe",
            email: "<EMAIL>",
            phone_number: "1234567890"
          }
        }
      }

      post url, params: params_with_customer_info, headers: headers
      expect(response).to have_http_status(:created)
      data = response.parsed_body[:data][:enquiry]
      expect(data[:customer][:first_name]).to eq("John")
      expect(data[:drive_type]).to eq("enquiry")
    end

    it "assigns current_user as sales_person when not provided" do
      params_without_sales_person = {
        enquiry: {
          vehicle_uuid: vehicle.uuid,
          customer_uuid: customer.uuid,
          notes: "Customer enquiry"
        }
      }

      post url, params: params_without_sales_person, headers: headers
      expect(response).to have_http_status(:created)
      data = response.parsed_body[:data][:enquiry]
      expect(data[:sales_person][:uuid]).to eq(user.uuid)
    end

    it "returns error for non-existent vehicle" do
      invalid_params = valid_params.deep_dup
      invalid_params[:enquiry][:vehicle_uuid] = "non-existent-uuid"

      post url, params: invalid_params, headers: headers
      expect(response).to have_http_status(:not_found)
      data = response.parsed_body
      expect(data[:status][:message]).to include("Vehicle not found")
    end

    it "returns error when neither customer_uuid nor customer_info provided" do
      invalid_params = {
        enquiry: {
          vehicle_uuid: vehicle.uuid,
          sales_person_uuid: sales_person.uuid,
          notes: "Customer enquiry"
        }
      }

      post url, params: invalid_params, headers: headers
      expect(response).to have_http_status(:unprocessable_content)
      data = response.parsed_body
      expect(data[:status][:message]).to eq("Either customer_uuid or customer_info must be provided")
    end

    it "returns error when both customer_uuid and customer_info provided" do
      invalid_params = {
        enquiry: {
          vehicle_uuid: vehicle.uuid,
          customer_uuid: customer.uuid,
          customer_info: {
            first_name: "John",
            last_name: "Doe",
            email: "<EMAIL>",
            phone_number: "1234567890"
          },
          sales_person_uuid: sales_person.uuid,
          notes: "Customer enquiry"
        }
      }

      post url, params: invalid_params, headers: headers
      expect(response).to have_http_status(:unprocessable_content)
      data = response.parsed_body
      expect(data[:status][:message]).to eq("Cannot provide both customer_uuid and customer_info. Use one or the other")
    end

    it "creates an enquiry with complete customer info including address" do
      params_with_complete_info = {
        enquiry: {
          vehicle_uuid: vehicle.uuid,
          sales_person_uuid: sales_person.uuid,
          notes: "Customer enquiry with complete info",
          customer_info: {
            first_name: "Jane",
            last_name: "Smith",
            age: 30,
            email: "<EMAIL>",
            phone_number: "+***********",
            gender: "female",
            address_line1: "123 Main Street",
            address_line2: "Apt 4B",
            suburb: "Richmond",
            city: "Melbourne",
            state: "VIC",
            country: "Australia",
            postcode: "3121",
            company_name: "ACME Corp"
          }
        }
      }

      post url, params: params_with_complete_info, headers: headers
      expect(response).to have_http_status(:created)
      data = response.parsed_body[:data][:enquiry]
      expect(data[:customer][:first_name]).to eq("Jane")
      expect(data[:customer][:last_name]).to eq("Smith")
      expect(data[:customer][:email]).to eq("<EMAIL>")
      expect(data[:drive_type]).to eq("enquiry")
    end

    it "creates an enquiry with customer info and driver license" do
      params_with_license = {
        enquiry: {
          vehicle_uuid: vehicle.uuid,
          sales_person_uuid: sales_person.uuid,
          notes: "Customer enquiry with license",
          customer_info: {
            first_name: "Robert",
            last_name: "Johnson",
            email: "<EMAIL>",
            phone_number: "+***********",
            driver_license: {
              licence_number: "**********",
              expiry_date: "2025-12-31",
              issuing_state: "NSW",
              category: "C",
              issue_date: "2020-01-01",
              issuing_country: "Australia",
              full_name: "Robert Johnson",
              date_of_birth: "1985-05-15"
            }
          }
        }
      }

      post url, params: params_with_license, headers: headers
      expect(response).to have_http_status(:created)
      data = response.parsed_body[:data][:enquiry]
      expect(data[:customer][:first_name]).to eq("Robert")
      expect(data[:drive_type]).to eq("enquiry")
    end

    it "creates an enquiry with driver license images using multipart form data" do
      front_image = fixture_file_upload("spec/fixtures/files/front_dl.png", "image/png")
      back_image = fixture_file_upload("spec/fixtures/files/back_dl.png", "image/png")

      params_with_images = {
        enquiry: {
          vehicle_uuid: vehicle.uuid,
          sales_person_uuid: sales_person.uuid,
          notes: "Customer enquiry with license images",
          customer_info: {
            first_name: "Sarah",
            last_name: "Wilson",
            email: "<EMAIL>",
            phone_number: "+***********",
            gender: "female",
            address_line1: "456 Oak Street",
            city: "Sydney",
            state: "NSW",
            postcode: "2000",
            driver_license: {
              licence_number: "**********",
              expiry_date: "2026-06-30",
              issuing_state: "NSW",
              category: "C",
              issue_date: "2021-01-01",
              issuing_country: "Australia",
              full_name: "Sarah Wilson",
              date_of_birth: "1988-03-20",
              front_image: front_image,
              back_image: back_image
            }
          }
        }
      }

      post url, params: params_with_images, headers: headers
      expect(response).to have_http_status(:created)
      data = response.parsed_body[:data][:enquiry]
      expect(data[:customer][:first_name]).to eq("Sarah")
      expect(data[:drive_type]).to eq("enquiry")
    end

    it "creates an enquiry with only front image" do
      front_image = fixture_file_upload("spec/fixtures/files/front_dl.png", "image/png")

      params_with_front_image = {
        enquiry: {
          vehicle_uuid: vehicle.uuid,
          sales_person_uuid: sales_person.uuid,
          notes: "Customer enquiry with front image only",
          customer_info: {
            first_name: "Michael",
            last_name: "Brown",
            email: "<EMAIL>",
            phone_number: "+***********",
            driver_license: {
              licence_number: "**********",
              expiry_date: "2027-03-15",
              issuing_state: "VIC",
              category: "C",
              issue_date: "2022-04-01",
              issuing_country: "Australia",
              full_name: "Michael Brown",
              date_of_birth: "1990-08-10",
              front_image: front_image
            }
          }
        }
      }

      post url, params: params_with_front_image, headers: headers
      expect(response).to have_http_status(:created)
      data = response.parsed_body[:data][:enquiry]
      expect(data[:customer][:first_name]).to eq("Michael")
      expect(data[:drive_type]).to eq("enquiry")
    end

    it "creates an enquiry with only back image" do
      back_image = fixture_file_upload("spec/fixtures/files/back_dl.png", "image/png")

      params_with_back_image = {
        enquiry: {
          vehicle_uuid: vehicle.uuid,
          sales_person_uuid: sales_person.uuid,
          notes: "Customer enquiry with back image only",
          customer_info: {
            first_name: "Lisa",
            last_name: "Davis",
            email: "<EMAIL>",
            phone_number: "+***********",
            driver_license: {
              licence_number: "**********",
              expiry_date: "2028-01-20",
              issuing_state: "QLD",
              category: "C",
              issue_date: "2023-02-01",
              issuing_country: "Australia",
              full_name: "Lisa Davis",
              date_of_birth: "1992-11-25",
              back_image: back_image
            }
          }
        }
      }

      post url, params: params_with_back_image, headers: headers
      expect(response).to have_http_status(:created)
      data = response.parsed_body[:data][:enquiry]
      expect(data[:customer][:first_name]).to eq("Lisa")
      expect(data[:drive_type]).to eq("enquiry")
    end
  end
end
