require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::SessionsController#login", type: :request do
  include_context "users_api_shared_context"

  path "/api/v1/auth/login" do
    post "Login" do
      tags "Authentication"
      consumes "application/json"
      produces "application/json"

      parameter name: :payload, in: :body, required: true, schema: {
        type: :object,
        properties: {
          user: {
            type: :object,
            properties: {
              email: { type: :string, example: "<EMAIL>" },
              password: { type: :string, example: "Drive@2025" }
            },
            required: [ "email", "password" ]
          }
        },
        required: [ "user" ]
      }

      response "200", "Login successful, OTP sent" do
        let(:user) do
          create(:user, password: "Drive@2025", password_confirmation: "Drive@2025").tap do |u|
            u.dealerships << create(:dealership)
          end
        end

        let(:payload) do
          {
            user: {
              email: user.email,
              password: "Drive@2025"
            }
          }
        end

        before do
          allow(OtpService).to receive(:new).with(user).and_return(
            instance_double(OtpService, generate_and_send_otp: "123456")
          )
          allow(Auth::TokenService).to receive(:new).and_return(
            instance_double(Auth::TokenService, generate_temporary_token: [ "temp.token", 5.minutes.from_now.to_i ])
          )
        end

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("data", "temporary_token")).to eq("temp.token")
          expect(json.dig("status", "message")).to eq("Require 2FA challenge")
        end
      end

      response "401", "Invalid email or password" do
        let(:user) do
          create(:user, password: "Drive@2025", password_confirmation: "Drive@2025")
        end

        let(:payload) do
          {
            user: {
              email: user.email,
              password: "wrongpassword"
            }
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:unauthorized)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Email or password is incorrect.")
          expect(json.dig("status", "code")).to eq(401)
        end
      end

      response "401", "User not found" do
        let(:payload) do
          {
            user: {
              email: "<EMAIL>",
              password: "Drive@2025"
            }
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:unauthorized)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Email or password is incorrect.")
          expect(json.dig("status", "code")).to eq(401)
        end
      end

      response "401", "User not active" do
        let(:user) do
          create(:user, password: "Drive@2025", password_confirmation: "Drive@2025", status: :inactive)
        end

        let(:payload) do
          {
            user: {
              email: user.email,
              password: "Drive@2025"
            }
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:unauthorized)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("User is not active")
          expect(json.dig("status", "code")).to eq(401)
        end
      end

      response "401", "User does not belong to any dealership" do
        let(:user) do
          create(:user, password: "Drive@2025", password_confirmation: "Drive@2025")
        end

        let(:payload) do
          {
            user: {
              email: user.email,
              password: "Drive@2025"
            }
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:unauthorized)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("User does not belong to any dealership")
          expect(json.dig("status", "code")).to eq(401)
        end
      end

      response "401", "Missing credentials" do
        let(:payload) do
          {
            user: {
              email: nil,
              password: nil
            }
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:unauthorized)
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Email or password is incorrect.")
          expect(json.dig("status", "code")).to eq(401)
        end
      end
    end
  end

  describe "POST /api/v1/auth/login" do
    subject do
      post "/api/v1/auth/login",
           params: params.to_json,
           headers: { "CONTENT_TYPE" => "application/json", "ACCEPT" => "application/json" }
    end

    context "with valid credentials" do
      let(:user) do
        create(:user, password: "Drive@2025", password_confirmation: "Drive@2025").tap do |u|
          u.dealerships << create(:dealership)
        end
      end

      let(:params) { { user: { email: user.email, password: "Drive@2025" } } }

      before do
        allow(OtpService).to receive(:new).with(user).and_return(
          instance_double(OtpService, generate_and_send_otp: "123456")
        )
        allow(Auth::TokenService).to receive(:new).and_return(
          instance_double(Auth::TokenService, generate_temporary_token: [ "temp.token", 5.minutes.from_now.to_i ])
        )
      end

      it "returns temporary token and message" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.dig("data", "temporary_token")).to eq("temp.token")
        expect(response.parsed_body.dig("status", "message")).to eq("Require 2FA challenge")
      end
    end

    context "with invalid credentials" do
      let(:user) do
        create(:user, password: "Drive@2025", password_confirmation: "Drive@2025")
      end

      let(:params) { { user: { email: user.email, password: "wrongpassword" } } }

      it "returns 401 unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Email or password is incorrect.")
        expect(json.dig("status", "code")).to eq(401)
      end
    end

    context "with missing credentials" do
      let(:params) { { user: { email: nil, password: nil } } }

      it "returns 401 unauthorized" do
        subject
        expect(response).to have_http_status(:unauthorized)
        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Email or password is incorrect.")
        expect(json.dig("status", "code")).to eq(401)
      end
    end
  end
end
