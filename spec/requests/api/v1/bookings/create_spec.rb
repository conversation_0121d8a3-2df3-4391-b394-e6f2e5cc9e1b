require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::BookingsController#create", type: :request do
  include_context "drive_api_shared_context"

  let(:expected_pickup_time) { "2025-06-01T10:00:00.000Z" }
  let(:expected_return_time) { "2025-06-01T12:00:00.000Z" }

  path "/api/v1/dealerships/{dealership_uuid}/bookings" do
    post "Creates a booking" do
      tags "Bookings"
      consumes "multipart/form-data"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device ID"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :booking, in: :formData, schema: {
        type: :object,
        properties: {
          vehicle_uuid: { type: :string },
          drive_type: { type: :string, enum: %w[test_drive_booking loan_booking] },
          expected_pickup_datetime: { type: :string, format: "date-time" },
          expected_return_datetime: { type: :string, format: "date-time" },
          sales_person_uuid: { type: :string },
          customer_uuid: { type: :string },
          notes: { type: :string },
          customer_info: {
            type: :object,
            properties: {
              first_name: { type: :string },
              last_name: { type: :string },
              email: { type: :string },
              phone_number: { type: :string },
              address_line1: { type: :string },
              city: { type: :string },
              state: { type: :string },
              postcode: { type: :string },
              age: { type: :integer },
              gender: { type: :string, enum: %w[unspecified male female other] },
              company_name: { type: :string },
              suburb: { type: :string },
              address_line2: { type: :string },
              country: { type: :string },
              driver_license: {
                type: :object,
                properties: {
                  licence_number: { type: :string },
                  expiry_date: { type: :string },
                  issuing_state: { type: :string },
                  issuing_country: { type: :string },
                  category: { type: :string },
                  issue_date: { type: :string },
                  full_name: { type: :string },
                  date_of_birth: { type: :string },
                  front_image: { type: :string, format: "binary" },
                  back_image: { type: :string, format: "binary" }
                }
              }
            }
          }
        },
        required: [
          :vehicle_uuid, :drive_type,
          :expected_pickup_datetime, :expected_return_datetime,
          :sales_person_uuid
        ]
      }

      let(:booking) do
        {
          vehicle_uuid: vehicle.uuid,
          drive_type: "test_drive_booking",
          expected_pickup_datetime: expected_pickup_time,
          expected_return_datetime: expected_return_time,
          sales_person_uuid: sales_person.uuid,
          customer_uuid: customer.uuid,
          notes: "Test drive booking"
        }
      end

      before { customer.update(driver_license: driver_license) }

      response "201", "booking created with existing customer" do
        run_test! do |response|
          data = JSON.parse(response.body, symbolize_names: true)[:data][:booking]

          expect(data[:uuid]).to be_present
          expect(data[:drive_type]).to eq("test_drive_booking")
          expect(data[:expected_pickup_datetime]).to eq(expected_pickup_time)
          expect(data[:expected_return_datetime]).to eq(expected_return_time)
          expect(data[:notes]).to eq("Test drive booking")

          expect(data[:vehicle][:uuid]).to eq(vehicle.uuid)
          expect(data[:vehicle][:make]).to eq(vehicle.make)
          expect(data[:vehicle][:model]).to eq(vehicle.model)

          expect(data[:customer][:uuid]).to eq(customer.uuid)
          expect(data[:customer][:first_name]).to eq(customer.first_name)
          expect(data[:customer][:last_name]).to eq(customer.last_name)

          expect(data[:sales_person][:uuid]).to eq(sales_person.uuid)
          expect(data[:sales_person][:first_name]).to eq(sales_person.first_name)
          expect(data[:sales_person][:last_name]).to eq(sales_person.last_name)
        end
      end

      response "201", "booking created with new customer successfully w/o driver license photo" do
        let(:booking) do
          {
            vehicle_uuid: vehicle.uuid,
            drive_type: "test_drive_booking",
            expected_pickup_datetime: expected_pickup_time,
            expected_return_datetime: expected_return_time,
            sales_person_uuid: sales_person.uuid,
            notes: "Test drive booking with new customer",
            customer_info: {
              first_name: "John",
              last_name: "Doe",
              email: "<EMAIL>",
              phone_number: "+***********",
              address_line1: "123 Main St",
              city: "Sydney",
              state: "NSW",
              postcode: "2000",
              driver_license: {
                licence_number: "DL12345",
                expiry_date: "2025-12-31",
                issuing_state: "NSW",
                issuing_country: "Australia",
                category: "C",
                issue_date: "2020-01-01",
                full_name: "John Doe",
                date_of_birth: "1990-01-01"
              }
            }
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body, symbolize_names: true)[:data][:booking]

          expect(data[:uuid]).to be_present
          expect(data[:drive_type]).to eq("test_drive_booking")
          expect(data[:notes]).to eq("Test drive booking with new customer")

          customer_data = data[:customer]
          expect(customer_data[:uuid]).to be_present
          expect(customer_data[:first_name]).to eq("John")
          expect(customer_data[:last_name]).to eq("Doe")
          expect(customer_data[:email]).to eq("<EMAIL>")

          new_customer = Customer.find_by(uuid: customer_data[:uuid])
          expect(new_customer).to be_present
          expect(new_customer.email).to eq("<EMAIL>")
          expect(new_customer.dealership_id).to eq(dealership.id)

          driver_license = new_customer.driver_license
          expect(driver_license).to be_present
          expect(driver_license.licence_number).to eq("DL12345")
          expect(driver_license.expiry_date).to eq(Date.parse("2025-12-31"))
          expect(driver_license.issuing_state).to eq("NSW")
          expect(driver_license.issuing_country).to eq("Australia")
          expect(driver_license.category).to eq("C")
          expect(driver_license.issue_date).to eq(Date.parse("2020-01-01"))
          expect(driver_license.full_name).to eq("John Doe")
          expect(driver_license.date_of_birth).to eq(Date.parse("1990-01-01"))
        end
      end

      response "201", "booking created with new customer successfully with driver license photos" do
        let(:booking) do
          {
            vehicle_uuid: vehicle.uuid,
            drive_type: "test_drive_booking",
            expected_pickup_datetime: expected_pickup_time,
            expected_return_datetime: expected_return_time,
            sales_person_uuid: sales_person.uuid,
            notes: "Test drive booking with new customer",
            customer_info: {
              first_name: "John",
              last_name: "Doe",
              email: "<EMAIL>",
              phone_number: "+***********",
              address_line1: "123 Main St",
              city: "Sydney",
              state: "NSW",
              postcode: "2000",
              driver_license: {
                licence_number: "DL12345",
                expiry_date: "2025-12-31",
                issuing_state: "NSW",
                issuing_country: "Australia",
                category: "C",
                issue_date: "2020-01-01",
                full_name: "John Doe",
                date_of_birth: "1990-01-01",
                front_image: fixture_file_upload("spec/fixtures/files/front_dl.png", "image/png"),
                back_image: fixture_file_upload("spec/fixtures/files/back_dl.png", "image/png")
              }
            }
          }
        end

        run_test! do |response|
          data = JSON.parse(response.body, symbolize_names: true)[:data][:booking]

          expect(data[:uuid]).to be_present
          expect(data[:drive_type]).to eq("test_drive_booking")
          expect(data[:notes]).to eq("Test drive booking with new customer")

          customer_data = data[:customer]
          expect(customer_data[:uuid]).to be_present
          expect(customer_data[:first_name]).to eq("John")
          expect(customer_data[:last_name]).to eq("Doe")
          expect(customer_data[:email]).to eq("<EMAIL>")

          new_customer = Customer.find_by(uuid: customer_data[:uuid])
          expect(new_customer).to be_present
          expect(new_customer.email).to eq("<EMAIL>")
          expect(new_customer.dealership_id).to eq(dealership.id)

          driver_license = new_customer.driver_license
          expect(driver_license).to be_present
          expect(driver_license.licence_number).to eq("DL12345")
          expect(driver_license.expiry_date).to eq(Date.parse("2025-12-31"))
          expect(driver_license.issuing_state).to eq("NSW")
          expect(driver_license.issuing_country).to eq("Australia")
          expect(driver_license.category).to eq("C")
          expect(driver_license.issue_date).to eq(Date.parse("2020-01-01"))
          expect(driver_license.full_name).to eq("John Doe")
          expect(driver_license.date_of_birth).to eq(Date.parse("1990-01-01"))
          expect(driver_license.front_image.attached?).to be_truthy
          expect(driver_license.back_image.attached?).to be_truthy
        end
      end

      response "201", "loan booking created successfully" do
        let(:booking) { super().merge(drive_type: "loan_booking", notes: "Loan type") }

        run_test! do |response|
          data = JSON.parse(response.body, symbolize_names: true)[:data][:booking]
          expect(data[:drive_type]).to eq("loan_booking")
          expect(data[:notes]).to eq("Loan type")
        end
      end

      response "201", "test drive booking created successfully" do
        let(:booking) { super().merge(drive_type: "test_drive_booking", notes: "Test drive booking") }

        run_test! do |response|
          data = JSON.parse(response.body, symbolize_names: true)[:data][:booking]
          expect(data[:drive_type]).to eq("test_drive_booking")
          expect(data[:notes]).to eq("Test drive booking")
        end
      end

      response "422", "Invalid Booking Type" do
        let(:booking) { super().merge(drive_type: "invalid_type") }

        run_test! do |response|
          expect(response.body).to include("Invalid Booking Type")
        end
      end

      response "422", "invalid time sequence (pickup >= return)" do
        let(:booking) do
          super().merge(
            expected_pickup_datetime: "2025-06-01 13:00:00",
            expected_return_datetime: "2025-06-01 12:00:00",
            drive_type: "test_drive_booking"
          )
        end

        run_test! do |response|
          expect(response.body).to include("must be after pickup time")
        end
      end

      response "401", "unauthorized - no device id" do
        let(:"Device-ID") { nil }

        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Invalid device")
        end
      end

      response "401", "unauthorized - invalid token" do
        let(:Authorization) { "Bearer invalid_token" }

        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Invalid token")
        end
      end

      response "401", "unauthorized - expired token" do
        let(:Authorization) do
          "Bearer #{generate_expired_token(user, device_registration.device_id)}"
        end

        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Access token expired, please refresh")
        end
      end

      response "404", "customer not found" do
        let(:booking) { super().merge(customer_uuid: "non-existent") }

        run_test! do |response|
          expect(response.body).to include("Customer not found")
        end
      end

      response "404", "vehicle not found in dealership" do
        let(:other_vehicle) { create(:vehicle) }
        let(:booking) { super().merge(vehicle_uuid: other_vehicle.uuid) }

        run_test! do |response|
          expect(response.body).to include("Vehicle not found")
        end
      end

      response "404", "dealership not found" do
        let(:dealership_uuid) { "non-existent-dealership" }

        run_test! do |response|
          expect(response.body).to include("Dealership not found")
        end
      end

      response "404", "sales person not found" do
        let(:booking) { super().merge(sales_person_uuid: "invalid-uuid") }

        run_test! do |response|
          expect(response.body).to include("Sales person not found")
        end
      end
    end
  end

  # Regular RSpec request specs
  describe "POST /api/v1/bookings" do
    let(:url) { "/api/v1/dealerships/#{dealership.uuid}/bookings" }

    let(:valid_params) do
       {
        booking: {
          vehicle_uuid: vehicle.uuid,
          drive_type: "test_drive_booking",
          expected_pickup_datetime: expected_pickup_time,
          expected_return_datetime: expected_return_time,
          sales_person_uuid: sales_person.uuid,
          customer_uuid: customer.uuid,
          notes: "Test drive booking"
        }
      }
    end

    before { customer.update(driver_license: driver_license) }

    it "creates a booking with existing customer" do
      post url, params: valid_params, headers: headers
      expect(response).to have_http_status(:created)
      data = response.parsed_body[:data][:booking]
      expect(data[:customer][:uuid]).to eq(customer.uuid)
    end

    it "creates a booking with customer_info" do
      params = valid_params.deep_dup
      params[:booking].delete(:customer_uuid)
      params[:booking][:customer_info] = {
        first_name: "Jane",
        last_name: "Doe",
        email: "<EMAIL>",
        phone_number: "+***********",
        address_line1: "123 Main St",
        city: "Sydney",
        state: "NSW",
        postcode: "2000",
        driver_license: {
          licence_number: "DL54321",
          expiry_date: "2025-12-31",
          issuing_state: "NSW",
          issuing_country: "Australia",
          category: "C",
          issue_date: "2020-01-01",
          full_name: "Jane Doe",
          date_of_birth: "1990-01-01"
        }
      }

      post url, params: params, headers: headers
      expect(response).to have_http_status(:created)
      expect(response.parsed_body[:data][:booking][:customer][:email]).to eq("<EMAIL>")
    end

    it "returns 422 for Invalid Booking Type" do
      params = valid_params.deep_dup
      params[:booking][:drive_type] = "invalid_type"
      post url, params: params, headers: headers
      expect(response.status).to eq(422)
      expect(response.body).to include("Invalid Booking Type")
    end

    it "returns 422 for pickup time after return" do
      params = valid_params.deep_dup
      params[:booking][:drive_type] = "test_drive_booking"
      params[:booking][:expected_pickup_datetime] = "2025-06-01 13:00:00"
      params[:booking][:expected_return_datetime] = "2025-06-01 12:00:00"
      post url, params: params, headers: headers
      expect(response.status).to eq(422)
      expect(response.body).to include("must be after pickup time")
    end

    it "returns 401 for missing device id" do
      post url, headers: headers.except("Device-ID")
      expect(response).to have_http_status(:unauthorized)
      expect(response.parsed_body["status"]["message"]).to eq("Missing device ID")
    end

    it "returns 401 for invalid device id" do
      post url, headers: headers.merge("Device-ID" => nil)
      expect(response.status).to eq(401)
      expect(response.parsed_body["status"]["message"]).to eq("Missing device ID")
    end

    it "returns 401 for missing token" do
      post url, params: valid_params, headers: headers.except("Authorization")
      expect(response.status).to eq(401)
      expect(response.parsed_body["status"]["message"]).to eq("Missing authorization token")
    end

    it "returns 401 for invalid token" do
      post url, headers: headers.merge("Authorization" => "Bearer invalid_token")
      expect(response.status).to eq(401)
      expect(response.parsed_body["status"]["message"]).to eq("Invalid token")
    end

    it "returns 401 for expired token" do
      expired_token = generate_expired_token(user, device_registration.device_id)
      post url, params: valid_params, headers: headers.merge("Authorization" => "Bearer #{expired_token}")
      expect(response.status).to eq(401)
      expect(response.parsed_body["status"]["message"]).to eq("Access token expired, please refresh")
    end

    it "returns 404 if customer not found" do
      params = {
        booking: {
          vehicle_uuid: vehicle.uuid,
          drive_type: "test_drive_booking",
          expected_pickup_datetime: expected_pickup_time,
          expected_return_datetime: expected_return_time,
          sales_person_uuid: sales_person.uuid,
          customer_uuid: "non-existent",
          notes: "Unknown customer"
        }
      }
      post url, params: params, headers: headers
      expect(response.status).to eq(404)
      expect(response.parsed_body["status"]["message"]).to include("Customer not found")
    end

    it "returns 404 when sales_person_uuid is invalid" do
      params = valid_params.deep_dup
      params[:booking][:sales_person_uuid] = "invalid-uuid"
      post url, params: params, headers: headers
      expect(response.status).to eq(404)
      expect(response.body).to include("Sales person not found")
    end

    it "returns 422 for invalid drive type" do
      params = valid_params.deep_dup
      params[:booking][:drive_type] = "invalid_type"
      post url, params: params, headers: headers
      expect(response.status).to eq(422)
      expect(response.parsed_body["status"]["message"]).to eq("Invalid Booking Type.")
    end

    it "returns 404 for non-existent vehicle" do
      params = valid_params.deep_dup
      params[:booking][:vehicle_uuid] = "non-existent-uuid"
      post url, params: params, headers: headers
      expect(response.status).to eq(404)
      expect(response.parsed_body["status"]["message"]).to eq("Vehicle not found")
    end

    it "uses current user as sales person when sales_person_uuid is blank" do
      params = valid_params.deep_dup
      params[:booking][:sales_person_uuid] = ""
      post url, params: params, headers: headers
      expect(response).to have_http_status(:created)
      data = response.parsed_body[:data][:booking]
      expect(data[:sales_person][:uuid]).to eq(user.uuid)
    end
  end
end
