# frozen_string_literal: true

require "swagger_helper"

RSpec.describe "Api::V1::BookingsController#update", type: :request do
  include_context "drive_api_shared_context"

  let!(:drive) { create(:drive, :test_drive_booking, dealership: dealership, vehicle: vehicle, customer: customer, sales_person: sales_person) }
  let(:uuid) { drive.uuid }

  path "/api/v1/dealerships/{dealership_uuid}/bookings/{uuid}" do
    put "Update an existing booking" do
      tags "Bookings"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true
      parameter name: "Device-ID", in: :header, type: :string, required: true
      parameter name: :dealership_uuid, in: :path, type: :string, required: true
      parameter name: :uuid, in: :path, type: :string, required: true
      parameter name: :booking, in: :body, schema: {
        type: :object,
        properties: {
          vehicle_uuid: { type: :string },
          sales_person_uuid: { type: :string },
          expected_pickup_datetime: { type: :string, format: :"date-time" },
          expected_return_datetime: { type: :string, format: :"date-time" },
          notes: { type: :string }
        },
        description: "At least one of the fields must be provided. All fields are optional but at least one is required."
      }

      let(:booking) do
        {
          id: drive.uuid,
          vehicle_uuid: vehicle.uuid,
          sales_person_uuid: sales_person.uuid,
          expected_pickup_datetime: "2025-07-05T10:00:00Z",
          expected_return_datetime: "2025-07-05T12:00:00Z",
          notes: "New notes"
        }
      end

      response "200", "booking updated successfully" do
        let!(:new_vehicle) { create(:vehicle, dealership: dealership) }
        before do
          new_vehicle.update!(status: "available")
        end
        let(:booking) do
          {
            vehicle_uuid: new_vehicle.uuid,
            sales_person_uuid: sales_person.uuid,
            expected_pickup_datetime: '2025-07-02T12:00:00.000Z',
            expected_return_datetime: '2025-07-02T14:00:00.000Z',
            notes: "Updated notes"
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:ok)
          data = response.parsed_body[:data][:booking]
          expect(data[:uuid]).to eq(drive.uuid)
          expect(data[:expected_pickup_datetime]).to eq(booking[:expected_pickup_datetime])
          expect(data[:expected_return_datetime]).to eq(booking[:expected_return_datetime])
          expect(data[:vehicle][:uuid]).to eq(new_vehicle.uuid)
          expect(data[:notes]).to eq(booking[:notes])
        end
      end

      response "200", "booking updated successfully" do
        let!(:new_vehicle) { create(:vehicle, dealership: dealership) }
        before do
          new_vehicle.update!(status: "available")
        end
        let(:booking) do
          {
            vehicle_uuid: new_vehicle.uuid
          }
        end

        run_test! do |response|
          expect(response).to have_http_status(:ok)
          data = response.parsed_body[:data][:booking]
          expect(data[:uuid]).to eq(drive.uuid)
          expect(data[:vehicle][:uuid]).to eq(new_vehicle.uuid)
          expect(data[:expected_pickup_datetime]).to eq(drive.expected_pickup_datetime&.iso8601)
          expect(data[:expected_return_datetime]).to eq(drive.expected_return_datetime&.iso8601)
        end
      end

      response "422", "invalid input (pickup before return)" do
        before do
          vehicle.update!(status: "available")
        end
        let(:booking) do
          {
            vehicle_uuid: vehicle.uuid,
            sales_person_uuid: sales_person.uuid,
            expected_pickup_datetime: "2025-07-05T14:00:00Z",
            expected_return_datetime: "2025-07-05T12:00:00Z"
          }
        end

        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Validation failed: Expected return datetime must be after pickup time")
        end
      end

      response "401", "unauthorized - missing token" do
        let(:Authorization) { nil }

        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Missing authorization token")
        end
      end

      response "401", "unauthorized - invalid token" do
        let(:Authorization) { "Bearer invalid_token" }

        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Invalid token")
        end
      end

      response "401", "unauthorized - expired token" do
        let(:Authorization) do
          "Bearer #{generate_expired_token(user, device_registration.device_id)}"
        end

        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Access token expired, please refresh")
        end
      end

      response "401", "unauthorized - Invalid device" do
        let(:"Device-ID") { nil }

        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Invalid device")
        end
      end
    end
  end

  describe "PUT /api/v1/dealerships/:dealership_uuid/bookings/:uuid" do
    let(:url) { "/api/v1/dealerships/#{dealership.uuid}/bookings/#{uuid}" }
    before do
      vehicle.update!(status: "available")
    end
    let(:valid_params) do
      {
        vehicle_uuid: vehicle.uuid,
        sales_person_uuid: sales_person.uuid,
        expected_pickup_datetime: "2025-07-05T10:00:00Z",
        expected_return_datetime: "2025-07-05T12:00:00Z"
      }
    end

    it "updates the booking successfully" do
      put url, params: valid_params, headers: headers
      expect(response).to have_http_status(:ok)
      data = response.parsed_body[:data][:booking]
      expect(data[:uuid]).to eq(drive.uuid)
    end

    it "allows partial update with only vehicle_uuid" do
      new_vehicle = create(:vehicle, dealership: dealership, status: "available")
      partial_params = { vehicle_uuid: new_vehicle.uuid }

      put url, params: partial_params, headers: headers
      expect(response).to have_http_status(:ok)
      data = response.parsed_body[:data][:booking]
      expect(data[:vehicle][:uuid]).to eq(new_vehicle.uuid)
      # Other fields should remain unchanged
      expect(data[:expected_pickup_datetime]).to eq(drive.expected_pickup_datetime)
      expect(data[:expected_return_datetime]).to eq(drive.expected_return_datetime)
    end

    it "allows partial update with only expected_pickup_datetime" do
      new_pickup_time = "2025-08-01T10:00:00.000Z"
      partial_params = { expected_pickup_datetime: new_pickup_time }

      put url, params: partial_params, headers: headers
      expect(response).to have_http_status(:ok)
      data = response.parsed_body[:data][:booking]
      expect(data[:expected_pickup_datetime]).to eq(new_pickup_time)
      # Other fields should remain unchanged
      expect(data[:vehicle][:uuid]).to eq(vehicle.uuid)
    end

    it "returns 422 for pickup time after return time" do
      params = valid_params.merge(
        expected_pickup_datetime: "2025-07-05T14:00:00Z",
        expected_return_datetime: "2025-07-05T12:00:00Z"
      )
      put url, params: params, headers: headers

      expect(response.status).to eq(422)
      expect(response.parsed_body["status"]["message"]).to eq("Validation failed: Expected return datetime must be after pickup time")
    end

    it "returns 401 for missing token" do
      put url, params: valid_params, headers: headers.except("Authorization")
      expect(response.status).to eq(401)
      expect(response.parsed_body["status"]["message"]).to eq("Missing authorization token")
    end

    it "returns 401 for invalid token" do
      put url, params: valid_params, headers: headers.merge("Authorization" => "Bearer invalid_token")
      expect(response.status).to eq(401)
      expect(response.parsed_body["status"]["message"]).to eq("Invalid token")
    end

    it "returns 401 for expired token" do
      expired_token = generate_expired_token(user, device_registration.device_id)
      put url, params: valid_params, headers: headers.merge("Authorization" => "Bearer #{expired_token}")
      expect(response.status).to eq(401)
      expect(response.parsed_body["status"]["message"]).to eq("Access token expired, please refresh")
    end

    it "returns 401 for missing device id" do
      put url, params: valid_params, headers: headers.except("Device-ID")
      expect(response.status).to eq(401)
      expect(response.parsed_body["status"]["message"]).to eq("Missing device ID")
    end
  end
end
