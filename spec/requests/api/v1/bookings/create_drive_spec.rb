# frozen_string_literal: true

require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::DrivesController", type: :request do
  include_context "drive_api_shared_context"

  path "/api/v1/dealerships/{dealership_uuid}/bookings/{uuid}/create-drive" do
    post "Convert booking to drive" do
      tags "Drives"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]
      description "Convert an existing Booking to a test_drive or loan. Converts test_drive_booking to test_drive and loan_booking to loan."

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :uuid, in: :path, type: :string, required: true, description: "Booking UUID"

      response "201", "Drive created from booking successfully" do
        context "when creating test drive from test_drive_booking" do
          let!(:test_drive_booking) do
            create(:drive, :test_drive_booking, :scheduled,
                   dealership: dealership,
                   vehicle: vehicle,
                   customer: customer,
                   sales_person: sales_person,
                   notes: "Test drive booking notes",
                   expected_pickup_datetime: 1.hour.from_now,
                   expected_return_datetime: 2.hours.from_now,
                   start_odometer_reading: 10000)
          end
          let(:uuid) { test_drive_booking.uuid }

          run_test! do |response|
            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("Drive created from booking successfully")

            drive_data = json.dig("data", "drive")
            expect(drive_data["drive_type"]).to eq("test_drive")
            expect(drive_data["status"]).to eq("draft")
            expect(drive_data["notes"]).to eq("Test drive booking notes")
            expect(drive_data["expected_return_datetime"]).to be_present

            # Verify vehicle and customer data copied
            expect(drive_data["vehicle"]["uuid"]).to eq(vehicle.uuid)
            expect(drive_data["customer"]["uuid"]).to eq(customer.uuid)
            expect(drive_data["sales_person"]["uuid"]).to eq(sales_person.uuid)

            # Verify new drive was created in database
            new_drive = Drive.find_by(uuid: drive_data["uuid"])
            expect(new_drive).to be_present
            expect(new_drive.drive_type).to eq("test_drive")
            expect(new_drive.status).to eq("draft")
          end
        end

        context "when creating loan from loan_booking" do
          let!(:loan_booking) do
            create(:drive, :loan_booking, :scheduled,
                   dealership: dealership,
                   vehicle: vehicle,
                   customer: customer,
                   sales_person: sales_person,
                   notes: "Loan booking notes",
                   expected_pickup_datetime: 1.day.from_now,
                   expected_return_datetime: 3.days.from_now,
                   start_odometer_reading: 15000)
          end
          let(:uuid) { loan_booking.uuid }

          run_test! do |response|
            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("Drive created from booking successfully")

            drive_data = json.dig("data", "drive")
            expect(drive_data["drive_type"]).to eq("loan")
            expect(drive_data["status"]).to eq("draft")
            expect(drive_data["notes"]).to eq("Loan booking notes")
            expect(drive_data["expected_return_datetime"]).to be_present

            # Verify new drive was created in database
            new_drive = Drive.find_by(uuid: drive_data["uuid"])
            expect(new_drive).to be_present
            expect(new_drive.drive_type).to eq("loan")
            expect(new_drive.status).to eq("draft")
          end
        end
      end

      response "422", "Invalid booking status" do
        context "when booking is not scheduled" do
          let!(:cancelled_booking) do
            create(:drive, :test_drive_booking, :cancelled,
                   dealership: dealership,
                   vehicle: vehicle,
                   customer: customer,
                   sales_person: sales_person)
          end
          let(:uuid) { cancelled_booking.uuid }

          run_test! do |response|
            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("Booking must be scheduled")
          end
        end
      end

      response "404", "Booking not found" do
        context "when booking UUID does not exist" do
          let(:uuid) { "non-existent-uuid" }

          run_test! do |response|
            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("Booking not found")
          end
        end

        context "when drive is not a booking type" do
          let!(:regular_drive) do
            create(:drive, :test_drive,
                   dealership: dealership,
                   vehicle: vehicle,
                   customer: customer,
                   sales_person: sales_person)
          end
          let(:uuid) { regular_drive.uuid }

          run_test! do |response|
            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("Booking not found")
          end
        end
      end

      response "401", "Unauthorized" do
        context "when authorization header is missing" do
          let!(:test_drive_booking) do
            create(:drive, :test_drive_booking, :scheduled,
                   dealership: dealership,
                   vehicle: vehicle,
                   customer: customer,
                   sales_person: sales_person)
          end
          let(:uuid) { test_drive_booking.uuid }
          let(:Authorization) { nil }

          run_test! do |response|
            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("Missing authorization token")
          end
        end
      end
    end
  end

  describe "Drive creation from booking validation" do
    let!(:test_drive_booking) do
      create(:drive, :test_drive_booking, :scheduled,
             dealership: dealership,
             vehicle: vehicle,
             customer: customer,
             sales_person: sales_person,
             notes: "Original booking notes",
             expected_pickup_datetime: 1.hour.from_now,
             expected_return_datetime: 2.hours.from_now,
             start_odometer_reading: 12000)
    end
    let(:url) { "/api/v1/dealerships/#{dealership.uuid}/bookings/#{test_drive_booking.uuid}/create-drive" }

    context "when all data is copied correctly" do
      it "creates drive with all booking data" do
        post url, headers: headers

        expect(response).to have_http_status(:created)
        json = response.parsed_body

        drive_data = json.dig("data", "drive")
        new_drive = Drive.find_by(uuid: drive_data["uuid"])

        # Verify all data was copied
        expect(new_drive.vehicle).to eq(test_drive_booking.vehicle)
        expect(new_drive.customer).to eq(test_drive_booking.customer)
        expect(new_drive.sales_person).to eq(test_drive_booking.sales_person)
        expect(new_drive.notes).to eq(test_drive_booking.notes)
        expect(new_drive.expected_return_datetime).to eq(test_drive_booking.expected_return_datetime)

        # Verify drive type conversion
        expect(new_drive.drive_type).to eq("test_drive")
        expect(new_drive.status).to eq("draft")
      end
    end
  end
end
