# frozen_string_literal: true

require "swagger_helper"

RSpec.describe "Api::V1::BookingsController#cancel", type: :request do
  include_context "drive_api_shared_context"

  let!(:drive) { create(:drive, :test_drive_booking, status: Drive.statuses[:scheduled], vehicle: vehicle, customer: customer, dealership: dealership, sales_person: sales_person) }
  let(:uuid) { drive.uuid }

  path "/api/v1/dealerships/{dealership_uuid}/bookings/{uuid}/cancel" do
    patch "Cancel a booking" do
      tags "Bookings"
      description "Cancels a booking"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true
      parameter name: "Device-ID", in: :header, type: :string, required: true
      parameter name: :dealership_uuid, in: :path, type: :string, required: true
      parameter name: :uuid, in: :path, type: :string, required: true
      parameter name: :body, in: :body, schema: {
        type: :object,
        properties: {
          cancel_reason: { type: :string }
        },
        required: [ :cancel_reason ]
      }

      response "200", "booking cancelled" do
        let(:body) { { cancel_reason: "Customer changed mind" } }
        let(:raw_post) { body.to_json }
        run_test! do |response|
          data = response.parsed_body[:data][:booking]
          expect(data[:uuid]).to eq(drive.uuid)
          expect(data[:status]).to eq("cancelled")
          expect(data[:cancelled_at]).to be_present
        end
      end

      response "422", "missing cancel reason" do
        let(:body) { { dealership_uuid: dealership.uuid } }
        let(:raw_post) { body.to_json }
        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Cancel reason is required")
        end
      end

      response "404", "booking not found" do
        let(:uuid) { "non-existent-uuid" }
        let(:body) { { cancel_reason: "Customer changed mind" } }
        let(:raw_post) { body.to_json }
        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to include("Booking not found")
        end
      end

      response "401", "unauthorized - missing token" do
        let(:Authorization) { nil }
        let(:body) { { cancel_reason: "Customer changed mind" } }
        let(:raw_post) { body.to_json }
        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Missing authorization token")
        end
      end

      response "401", "unauthorized - invalid token" do
        let(:Authorization) { "Bearer invalid_token" }
        let(:body) { { cancel_reason: "Customer changed mind" } }
        let(:raw_post) { body.to_json }
        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Invalid token")
        end
      end

      response "401", "unauthorized - expired token" do
        let(:Authorization) { "Bearer #{generate_expired_token(user, device_registration.device_id)}" }
        let(:body) { { cancel_reason: "Customer changed mind" } }
        let(:raw_post) { body.to_json }
        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Access token expired, please refresh")
        end
      end

      response "401", "unauthorized - invalid device" do
        let(:"Device-ID") { nil }
        let(:body) { { cancel_reason: "Customer changed mind" } }
        let(:raw_post) { body.to_json }
        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Invalid device")
        end
      end
    end
  end

  describe "PATCH /api/v1/dealerships/:dealership_uuid/bookings/:uuid/cancel" do
    let(:url) { "/api/v1/dealerships/#{dealership.uuid}/bookings/#{uuid}/cancel" }
    let(:params) { { cancel_reason: "Customer changed mind" } }

    it "cancels the booking successfully" do
      patch url, params: params, headers: headers
      expect(response).to have_http_status(:ok)
      data = response.parsed_body[:data][:booking]
      expect(data[:uuid]).to eq(drive.uuid)
      expect(data[:status]).to eq("cancelled")
      expect(data[:cancelled_at]).to be_present
    end

    it "returns 422 for already cancelled booking" do
      drive.cancel_with_reason!("Customer changed mind")
      patch url, params: params, headers: headers
      expect(response.status).to eq(422)
      expect(response.parsed_body["status"]["message"]).to eq("Cancellation not allowed")
    end

    it "returns 422 for missing cancel reason" do
      patch url, params: {}, headers: headers
      expect(response.status).to eq(422)
      expect(response.parsed_body["status"]["message"]).to eq("Cancel reason is required")
    end

    it "returns 404 for booking not found" do
      patch "/api/v1/dealerships/#{dealership.uuid}/bookings/non-existent-uuid/cancel", params: params, headers: headers
      expect(response.status).to eq(404)
      expect(response.parsed_body["status"]["message"]).to include("Booking not found")
    end

    it "returns 401 for missing token" do
      patch url, params: params, headers: headers.except("Authorization")
      expect(response.status).to eq(401)
      expect(response.parsed_body["status"]["message"]).to eq("Missing authorization token")
    end

    it "returns 401 for invalid token" do
      patch url, params: params, headers: headers.merge("Authorization" => "Bearer invalid_token")
      expect(response.status).to eq(401)
      expect(response.parsed_body["status"]["message"]).to eq("Invalid token")
    end

    it "returns 401 for expired token" do
      expired_token = generate_expired_token(user, device_registration.device_id)
      patch url, params: params, headers: headers.merge("Authorization" => "Bearer #{expired_token}")
      expect(response.status).to eq(401)
      expect(response.parsed_body["status"]["message"]).to eq("Access token expired, please refresh")
    end

    it "returns 401 for missing device id" do
      patch url, params: params, headers: headers.except("Device-ID")
      expect(response.status).to eq(401)
      expect(response.parsed_body["status"]["message"]).to eq("Missing device ID")
    end
  end
end
