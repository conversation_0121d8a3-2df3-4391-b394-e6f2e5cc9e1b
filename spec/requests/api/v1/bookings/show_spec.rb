# frozen_string_literal: true

require "swagger_helper"

RSpec.describe "Api::V1::BookingsController#show", type: :request do
  include_context "drive_api_shared_context"

  let!(:drive) { create(:drive, :test_drive_booking, vehicle: vehicle, customer: customer, dealership: dealership, sales_person: sales_person) }
  let(:uuid) { drive.uuid }

  path "/api/v1/dealerships/{dealership_uuid}/bookings/{uuid}" do
    get "Retrieve a specific booking by UUID" do
      tags "Bookings"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true
      parameter name: "Device-ID", in: :header, type: :string, required: true
      parameter name: :uuid, in: :path, type: :string, required: true
      parameter name: :dealership_uuid, in: :path, type: :string, required: true

      response "200", "booking found" do
        let!(:drive) { create(:drive, :test_drive_booking, vehicle: vehicle, customer: customer, dealership: dealership, sales_person: sales_person) }
        run_test! do |response|
          data = response.parsed_body[:data][:booking]
          expect(data[:uuid]).to eq(drive.uuid)
          expect(data[:vehicle][:uuid]).to eq(vehicle.uuid)
          expect(data[:customer][:uuid]).to eq(customer.uuid)
          expect(data[:sales_person][:uuid]).to eq(sales_person.uuid)
        end
      end

      response "404", "booking not found" do
        let(:uuid) { "non-existent-uuid" }
        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to include("Booking not found")
        end
      end

      response "401", "unauthorized - missing token" do
        let(:Authorization) { nil }
        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Missing authorization token")
        end
      end

      response "401", "unauthorized - invalid token" do
        let(:Authorization) { "Bearer invalid_token" }
        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Invalid token")
        end
      end

      response "401", "unauthorized - expired token" do
        let(:Authorization) { "Bearer #{generate_expired_token(user, device_registration.device_id)}" }
        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Access token expired, please refresh")
        end
      end

      response "401", "unauthorized - Invalid device" do
        let(:"Device-ID") { nil }
        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Invalid device")
        end
      end
    end
  end

  describe "GET /api/v1/dealerships/:dealership_uuid/bookings/:uuid" do
    let(:url) { "/api/v1/dealerships/#{dealership.uuid}/bookings/#{uuid}" }

    context "when booking exists" do
      let!(:drive) { create(:drive, :test_drive_booking, vehicle: vehicle, customer: customer, dealership: dealership, sales_person: sales_person) }
      it "returns the booking details" do
        get url, headers: headers
        expect(response).to have_http_status(:ok)
        data = response.parsed_body[:data][:booking]
        expect(data[:uuid]).to eq(drive.uuid)
        expect(data[:vehicle][:uuid]).to eq(vehicle.uuid)
        expect(data[:customer][:uuid]).to eq(customer.uuid)
        expect(data[:sales_person][:uuid]).to eq(sales_person.uuid)
      end
    end

    context "when booking does not exist" do
      let(:uuid) { "non-existent" }
      it "returns 404" do
        get url, headers: headers
        expect(response.status).to eq(404)
        expect(response.parsed_body["status"]["message"]).to include("Booking not found")
      end
    end

    it "returns 401 for missing token" do
      get url, headers: headers.except("Authorization")
      expect(response).to have_http_status(:unauthorized)
      expect(response.parsed_body["status"]["message"]).to eq("Missing authorization token")
    end

    it "returns 401 for invalid token" do
      get url, headers: headers.merge("Authorization" => "Bearer invalid_token")
      expect(response).to have_http_status(:unauthorized)
      expect(response.parsed_body["status"]["message"]).to eq("Invalid token")
    end

    it "returns 401 for expired token" do
      expired_token = generate_expired_token(user, device_registration.device_id)
      get url, headers: headers.merge("Authorization" => "Bearer #{expired_token}")
      expect(response).to have_http_status(:unauthorized)
      expect(response.parsed_body["status"]["message"]).to eq("Access token expired, please refresh")
    end

    it "returns 401 for missing device id" do
      get url, headers: headers.except("Device-ID")
      expect(response).to have_http_status(:unauthorized)
      expect(response.parsed_body["status"]["message"]).to eq("Missing device ID")
    end
  end
end
