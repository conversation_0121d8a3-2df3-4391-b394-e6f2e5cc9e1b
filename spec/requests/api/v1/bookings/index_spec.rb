# frozen_string_literal: true

require "swagger_helper"

RSpec.describe "Api::V1::BookingsController#index", type: :request do
  include_context "drive_api_shared_context"

  # Create test data for filtering
  let!(:vehicle2) { create(:vehicle, dealership: dealership) }
  let(:sales_person2) { create(:user) }
  let!(:sales_person_dealership2) { create(:user_dealership, user: sales_person2, dealership: dealership, role: :sales_person) }

  # Create bookings with different attributes for testing filters
  let!(:test_drive_booking) { create(:drive, :test_drive_booking, :scheduled, dealership: dealership, vehicle: vehicle, customer: customer, sales_person: sales_person, expected_pickup_datetime: 1.day.from_now, expected_return_datetime: 1.day.from_now + 2.hours) }
  let!(:loan_booking) { create(:drive, :loan_booking, :scheduled, dealership: dealership, vehicle: vehicle, customer: customer, sales_person: sales_person, expected_pickup_datetime: 2.days.from_now, expected_return_datetime: 2.days.from_now + 3.hours) }
  let!(:completed_booking) { create(:drive, :test_drive_booking, :completed, dealership: dealership, vehicle: vehicle, customer: customer, sales_person: sales_person, expected_pickup_datetime: 1.day.ago, expected_return_datetime: 1.day.ago + 2.hours) }
  let!(:cancelled_booking) { create(:drive, :test_drive_booking, :cancelled, dealership: dealership, vehicle: vehicle, customer: customer, sales_person: sales_person, expected_pickup_datetime: 2.days.ago, expected_return_datetime: 2.days.ago + 2.hours) }
  let!(:different_vehicle_booking) { create(:drive, :test_drive_booking, :scheduled, dealership: dealership, vehicle: vehicle2, customer: customer, sales_person: sales_person, expected_pickup_datetime: 3.days.from_now, expected_return_datetime: 3.days.from_now + 2.hours) }
  let!(:different_salesperson_booking) { create(:drive, :test_drive_booking, :scheduled, dealership: dealership, vehicle: vehicle, customer: customer, sales_person: sales_person2, expected_pickup_datetime: 4.days.from_now, expected_return_datetime: 4.days.from_now + 2.hours) }

  path "/api/v1/dealerships/{dealership_uuid}/bookings" do
    get "Get list of bookings" do
      tags "Bookings"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true
      parameter name: "Device-ID", in: :header, type: :string, required: true
      parameter name: :dealership_uuid, in: :path, type: :string, required: true
      parameter name: :status, in: :query, type: :string, required: false, enum: [ 'scheduled', 'completed', 'cancelled' ], description: "Filter bookings by status"
      parameter name: :drive_type, in: :query, type: :string, required: false, enum: [ 'test_drive_booking', 'loan_booking' ], description: "Filter bookings by drive type: test_drive_booking, loan_booking"
      parameter name: :vehicle_uuid, in: :query, type: :string, required: false, description: "Filter bookings by vehicle UUID"
      parameter name: :sales_person_uuid, in: :query, type: :string, required: false, description: "Filter bookings by sales person UUID"
      parameter name: :start_date, in: :query, type: :string, required: false, format: "date", description: "Filter bookings from this date"
      parameter name: :end_date, in: :query, type: :string, required: false, format: "date", description: "Filter bookings until this date"
      parameter name: :page, in: :query, type: :integer, required: false, description: "Page number for pagination (default: 1)"
      parameter name: :per_page, in: :query, type: :integer, required: false, description: "Number of items per page (default: 20, max: 100)"

      response "200", "bookings retrieved" do
        let(:vehicle_uuid) { nil }
        let(:sales_person_uuid) { nil }
        run_test! do |response|
          data = response.parsed_body[:data][:bookings]
          expect(data).to be_an(Array)
          expect(data.size).to be >= 6 # At least our test bookings
        end
      end

      response "200", "bookings filtered by status" do
        let(:vehicle_uuid) { nil }
        let(:sales_person_uuid) { nil }
        let(:status) { 'scheduled' }

        run_test! do |response|
          data = response.parsed_body[:data][:bookings]
          expect(data).to be_an(Array)
          expect(data.size).to be >= 4 # All scheduled bookings
          expect(data.map { |b| b[:status] }.uniq).to eq([ 'scheduled' ])
        end
      end

      response "200", "bookings filtered by drive_type" do
        let(:vehicle_uuid) { nil }
        let(:sales_person_uuid) { nil }
        let(:drive_type) { 'loan_booking' }

        run_test! do |response|
          data = response.parsed_body[:data][:bookings]
          expect(data).to be_an(Array)
          expect(data.size).to be >= 1 # Only loan bookings
          expect(data.map { |b| b[:drive_type] }.uniq).to eq([ 'loan_booking' ])
        end
      end

      response "200", "bookings filtered by vehicle_uuid" do
        let(:sales_person_uuid) { nil }
        let(:vehicle_uuid) { vehicle2.uuid }

        run_test! do |response|
          data = response.parsed_body[:data][:bookings]
          expect(data).to be_an(Array)
          expect(data.size).to be >= 1 # Only bookings for vehicle2
          expect(data.map { |b| b[:vehicle][:uuid] }.uniq).to eq([ vehicle2.uuid ])
        end
      end

      response "200", "bookings filtered by sales_person_uuid" do
        let(:vehicle_uuid) { nil }
        let(:sales_person_uuid) { sales_person2.uuid }

        run_test! do |response|
          data = response.parsed_body[:data][:bookings]
          expect(data).to be_an(Array)
          expect(data.size).to be >= 1 # Only bookings for sales_person2
          expect(data.map { |b| b[:sales_person][:uuid] }.uniq).to eq([ sales_person2.uuid ])
        end
      end

      response "200", "bookings filtered by date range" do
        let(:vehicle_uuid) { nil }
        let(:sales_person_uuid) { nil }
        let(:start_date) { Date.tomorrow.to_s }
        let(:end_date) { 5.days.from_now.to_s }

        run_test! do |response|
          data = response.parsed_body[:data][:bookings]
          expect(data).to be_an(Array)
          expect(data.size).to be >= 4 # All future scheduled bookings

          # All bookings should have pickup dates within the specified range
          data.each do |booking|
            pickup_date = DateTime.parse(booking[:expected_pickup_datetime])
            expect(pickup_date).to be >= Date.tomorrow.beginning_of_day
            expect(pickup_date).to be <= 5.days.from_now.end_of_day
          end
        end
      end

      response "200", "bookings with pagination" do
        let(:vehicle_uuid) { nil }
        let(:sales_person_uuid) { nil }
        let(:page) { 1 }
        let(:per_page) { 2 }

        run_test! do |response|
          data = response.parsed_body[:data][:bookings]
          expect(data).to be_an(Array)
          expect(data.size).to eq(2) # Only 2 per page

          # Check pagination headers
          expect(response.headers['X-Current-Page']).to eq('1')
          expect(response.headers['X-Per-Page']).to eq('2')
          expect(response.headers['X-Total-Count']).to be_present
          expect(response.headers['X-Total-Pages']).to be_present
          expect(response.headers['X-Next-Page']).to be_present
        end
      end

      response "401", "unauthorized - missing token" do
        let(:Authorization) { nil }
        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Missing authorization token")
        end
      end

      response "401", "unauthorized - invalid token" do
        let(:Authorization) { "Bearer invalid_token" }
        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Invalid token")
        end
      end

      response "401", "unauthorized - expired token" do
        let(:Authorization) { "Bearer #{generate_expired_token(user, device_registration.device_id)}" }
        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Access token expired, please refresh")
        end
      end

      response "401", "unauthorized - Invalid device" do
        let(:"Device-ID") { nil }
        run_test! do |response|
          expect(response.parsed_body["status"]["message"]).to eq("Invalid device")
        end
      end
    end
  end

  describe "GET /api/v1/dealerships/:dealership_uuid/bookings" do
    let(:url) { "/api/v1/dealerships/#{dealership.uuid}/bookings" }

    it "returns a list of all bookings" do
      get url, headers: headers
      expect(response).to have_http_status(:ok)
      data = response.parsed_body[:data][:bookings]
      expect(data).to be_an(Array)
      expect(data.size).to be >= 6
    end

    it "filters bookings by status" do
      get url, params: { status: 'completed' }, headers: headers
      expect(response).to have_http_status(:ok)
      data = response.parsed_body[:data][:bookings]
      expect(data.map { |b| b[:status] }.uniq).to eq([ 'completed' ])
    end

    it "filters bookings by drive_type" do
      get url, params: { drive_type: 'loan_booking' }, headers: headers
      expect(response).to have_http_status(:ok)
      data = response.parsed_body[:data][:bookings]
      expect(data.map { |b| b[:drive_type] }.uniq).to eq([ 'loan_booking' ])
    end

    it "filters bookings by vehicle_uuid" do
      get url, params: { vehicle_uuid: vehicle2.uuid }, headers: headers
      expect(response).to have_http_status(:ok)
      data = response.parsed_body[:data][:bookings]
      expect(data.map { |b| b[:vehicle][:uuid] }.uniq).to eq([ vehicle2.uuid ])
    end

    it "filters bookings by sales_person_uuid" do
      get url, params: { sales_person_uuid: sales_person2.uuid }, headers: headers
      expect(response).to have_http_status(:ok)
      data = response.parsed_body[:data][:bookings]
      expect(data.map { |b| b[:sales_person][:uuid] }.uniq).to eq([ sales_person2.uuid ])
    end

    it "filters bookings by date range" do
      get url, params: { start_date: Date.tomorrow.to_s, end_date: 5.days.from_now.to_s }, headers: headers
      expect(response).to have_http_status(:ok)
      data = response.parsed_body[:data][:bookings]

      # All bookings should have pickup dates within the specified range
      data.each do |booking|
        pickup_date = DateTime.parse(booking[:expected_pickup_datetime])
        expect(pickup_date).to be >= Date.tomorrow.beginning_of_day
        expect(pickup_date).to be <= 5.days.from_now.end_of_day
      end
    end

    it "paginates results correctly" do
      get url, params: { page: 1, per_page: 2 }, headers: headers
      expect(response).to have_http_status(:ok)
      data = response.parsed_body[:data][:bookings]
      expect(data.size).to eq(2)

      # Check pagination headers
      expect(response.headers['X-Current-Page']).to eq('1')
      expect(response.headers['X-Per-Page']).to eq('2')
      expect(response.headers['X-Total-Count']).to be_present
      expect(response.headers['X-Total-Pages']).to be_present
      expect(response.headers['X-Next-Page']).to be_present
    end

    it "returns 401 for missing token" do
      get url, headers: headers.except("Authorization")
      expect(response.status).to eq(401)
      expect(response.parsed_body["status"]["message"]).to eq("Missing authorization token")
    end

    it "returns 401 for invalid token" do
      get url, headers: headers.merge("Authorization" => "Bearer invalid_token")
      expect(response.status).to eq(401)
      expect(response.parsed_body["status"]["message"]).to eq("Invalid token")
    end

    it "returns 401 for expired token" do
      expired_token = generate_expired_token(user, device_registration.device_id)
      get url, headers: headers.merge("Authorization" => "Bearer #{expired_token}")
      expect(response.status).to eq(401)
      expect(response.parsed_body["status"]["message"]).to eq("Access token expired, please refresh")
    end

    it "returns 401 for missing device id" do
      get url, headers: headers.except("Device-ID")
      expect(response.status).to eq(401)
      expect(response.parsed_body["status"]["message"]).to eq("Missing device ID")
    end
  end
end
