require "rails_helper"
require "swagger_helper"

RSpec.describe "DELETE /api/v1/auth/logout", type: :request do
  include_context "users_api_shared_context"

  let(:device_id) { device_registration.device_id }

  path "/api/v1/auth/logout" do
    delete("Logout current device") do
      tags "Authentication"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: :"Authorization", in: :header, type: :string, required: true
      parameter name: :"Device-ID", in: :header, type: :string, required: true

      let(:Authorization) { "Bearer #{valid_token}" }
      let(:"Device-ID") { device_id }

      response "200", "Device logged out successfully" do
        before do
          allow_any_instance_of(User).to receive(:logout_device).with(device_id)

          mock_relation = double("ActiveDevicesRelation")
          allow(mock_relation).to receive(:find_by).with(device_id: device_id).and_return(device_registration)
          allow_any_instance_of(User).to receive(:active_devices).and_return(mock_relation)
        end

        run_test! do |response|
          expect(response).to have_http_status(:ok)
          expect(response.parsed_body.dig("status", "message")).to eq("Device logged out successfully")
        end
      end

      response "401", "Invalid device" do
        let(:device_id) { "non-existent-id" }
        let(:"Device-ID") { "non-existent-id" }

        run_test! do |response|
          expect(response).to have_http_status(:unauthorized)
          expect(response.parsed_body.dig("status", "message")).to eq("Invalid device")
        end
      end
    end
  end

  describe "DELETE /api/v1/auth/logout" do
    subject { delete "/api/v1/auth/logout", headers: headers }

    context "when logging out a valid device" do
      before do
        allow_any_instance_of(User).to receive(:logout_device).with(device_id)

        mock_relation = double("ActiveDevicesRelation")
        allow(mock_relation).to receive(:find_by).with(device_id: device_id).and_return(device_registration)
        allow_any_instance_of(User).to receive(:active_devices).and_return(mock_relation)
      end

      it "logs out the device and returns success" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.dig("status", "message")).to eq("Device logged out successfully")
      end
    end

    context "when device is not found" do
      let(:headers) do
        {
          "Authorization" => "Bearer #{valid_token}",
          "Device-ID" => "non-existent-id",
          "Accept" => "application/json"
        }
      end

      it "returns 401 invalid device" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Invalid device")
      end
    end
  end
end
