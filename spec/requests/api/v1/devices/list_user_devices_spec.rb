require "rails_helper"
require "swagger_helper"

RSpec.describe "GET /api/v1/devices", type: :request do
  include_context "users_api_shared_context"

  path "/api/v1/devices" do
    get("List user devices") do
      tags "User Devices"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: :"Authorization", in: :header, type: :string, required: true
      parameter name: :"Device-ID", in: :header, type: :string, required: true

      response "200", "Devices retrieved successfully" do
        before do
          create_list(:device_registration, 2, user: user)
        end

        run_test! do |response|
          json = response.parsed_body

          expect(json.dig("status", "message")).to eq("Active devices retrieved successfully")
          expect(json.dig("data", "devices")).to be_an(Array)
          expect(json["data"]["devices"].any? { |d| d["is_current"] }).to be true
        end
      end

      response "401", "Missing authorization token" do
        let(:Authorization) { nil }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Missing authorization token")
        end
      end

      response "401", "Missing device ID" do
        let(:"Device-ID") { nil }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Invalid device")
        end
      end

      response "401", "Invalid token" do
        before do
          allow_any_instance_of(Auth::TokenService)
            .to receive(:decode_and_verify_token)
            .and_raise(Errors::InvalidToken.new("Invalid token"))
        end

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Invalid token")
        end
      end

      response "401", "Token expired" do
        before do
          allow_any_instance_of(Auth::TokenService)
            .to receive(:decode_and_verify_token)
            .and_raise(Errors::TokenExpired.new("Token has expired"))
        end

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Token has expired")
        end
      end
    end
  end

  describe "Regular RSpec request specs" do
    subject { get "/api/v1/devices", headers: request_headers }

    context "when authorized" do
      let(:request_headers) { headers }

      before do
        create_list(:device_registration, 2, user: user)
      end

      it "returns a list of active devices with current device marked" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Active devices retrieved successfully")
        expect(json.dig("data", "devices")).to be_an(Array)
        expect(json["data"]["devices"].any? { |d| d["is_current"] }).to be true
      end
    end

    context "when missing Authorization token" do
      let(:request_headers) { headers.except("Authorization") }

      it "returns 401 with missing token message" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Missing authorization token")
      end
    end

    context "when missing Device-ID header" do
      let(:request_headers) { headers.except("Device-ID") }

      it "returns 401 with missing device ID message" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Missing device ID")
      end
    end

    context "when token is expired" do
      let(:request_headers) { headers }

      before do
        allow_any_instance_of(Auth::TokenService)
          .to receive(:decode_and_verify_token)
          .and_raise(Errors::TokenExpired.new("Token has expired"))
      end

      it "returns 401 with token expired message" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Token has expired")
      end
    end

    context "when token is invalid" do
      let(:request_headers) { headers }

      before do
        allow_any_instance_of(Auth::TokenService)
          .to receive(:decode_and_verify_token)
          .and_raise(Errors::InvalidToken.new("Invalid token"))
      end

      it "returns 401 with invalid token message" do
        subject
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Invalid token")
      end
    end
  end
end
