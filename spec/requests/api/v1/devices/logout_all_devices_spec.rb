require "rails_helper"
require "swagger_helper"

RSpec.describe "DELETE /api/v1/devices", type: :request do
  include_context "users_api_shared_context"

  path "/api/v1/devices" do
    delete("Logout all devices") do
      tags "User Devices"
      produces "application/json"
      consumes "application/json"
      security [ Bearer: [] ]

      parameter name: :"Authorization", in: :header, type: :string, required: true
      parameter name: :"Device-ID", in: :header, type: :string, required: true

      response "200", "All devices logged out successfully" do
        before do
          allow_any_instance_of(User).to receive(:logout_all_devices)
        end

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("All devices logged out successfully")
        end
      end

      response "401", "Missing authorization token" do
        let(:Authorization) { nil }

        run_test! do |response|
          expect(response.status).to eq(401)
          expect(response.parsed_body.dig("status", "message")).to eq("Missing authorization token")
        end
      end

      response "401", "Missing device ID" do
        let(:"Device-ID") { nil }

        run_test! do |response|
          expect(response.status).to eq(401)
          expect(response.parsed_body.dig("status", "message")).to eq("Invalid device")
        end
      end
    end
  end

  describe "Regular RSpec request specs" do
    subject { delete "/api/v1/devices", headers: headers }

    context "when authorized" do
      before do
        allow_any_instance_of(User).to receive(:logout_all_devices)
      end

      it "logs out all devices and returns success" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.dig("status", "message")).to eq("All devices logged out successfully")
      end
    end

    context "when Authorization header is missing" do
      it "returns 401 unauthorized" do
        delete "/api/v1/devices", headers: headers.except("Authorization")
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Missing authorization token")
      end
    end

    context "when Device-ID header is missing" do
      it "returns 401 unauthorized" do
        delete "/api/v1/devices", headers: headers.except("Device-ID")
        expect(response).to have_http_status(:unauthorized)
        expect(response.parsed_body.dig("status", "message")).to eq("Missing device ID")
      end
    end
  end
end
