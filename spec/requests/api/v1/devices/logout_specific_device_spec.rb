require "rails_helper"
require "swagger_helper"

RSpec.describe "DELETE /api/v1/devices/:device_id", type: :request do
  include_context "users_api_shared_context"

  let!(:other_device) { create(:device_registration, user: user, active: true, refresh_token_expires_at: 1.day.from_now) }
  let(:target_device_id) { other_device.device_id.to_s }
  let(:valid_token) { Auth::TokenService.new(other_device).generate_tokens[:access_token] }

  let(:headers) do
    {
      "Authorization" => "Bearer #{valid_token}",
      "Device-ID" => target_device_id
    }
  end

  path "/api/v1/devices/{device_id}" do
    delete("Logout a specific device") do
      tags "User Devices"
      produces "application/json"
      consumes "application/json"
      security [ Bearer: [] ]

      parameter name: :device_id, in: :path, type: :string, required: true
      parameter name: :"Authorization", in: :header, type: :string, required: true
      parameter name: :"Device-ID", in: :header, type: :string, required: true

      response "200", "Device logged out successfully" do
        let(:device_id) { target_device_id }
        let(:Authorization) { "Bearer #{valid_token}" }
        let(:"Device-ID") { target_device_id }

        before do
          allow_any_instance_of(User).to receive(:logout_device)
          mock_relation = double("ActiveDevicesRelation", find_by: other_device)
          allow(mock_relation).to receive(:find_by).with(device_id: target_device_id).and_return(other_device)
          allow_any_instance_of(User).to receive(:active_devices).and_return(mock_relation)
        end

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Device logged out successfully")
        end
      end

      response "404", "Device not found" do
        let(:device_id) { "non-existent-id" }
        let(:Authorization) { "Bearer #{valid_token}" }
        let(:"Device-ID") { target_device_id }

        before do
          mock_relation = double("ActiveDevicesRelation", find_by: nil)
          allow(mock_relation).to receive(:find_by).with(device_id: "non-existent-id").and_return(nil)
          allow_any_instance_of(User).to receive(:active_devices).and_return(mock_relation)
        end

        run_test! do |response|
          expect(response.status).to eq(404)
          expect(response.parsed_body.dig("status", "message")).to eq("Device not found")
        end
      end
    end
  end

  describe "Regular RSpec request specs" do
    subject { delete "/api/v1/devices/#{target_device_id}", headers: headers }

    context "when logging out a valid device" do
      before do
        allow_any_instance_of(User).to receive(:logout_device)
        mock_relation = double("ActiveDevicesRelation", find_by: other_device)
        allow(mock_relation).to receive(:find_by).with(device_id: target_device_id).and_return(other_device)
        allow_any_instance_of(User).to receive(:active_devices).and_return(mock_relation)
      end

      it "logs out the device and returns success" do
        expect_any_instance_of(User).to receive(:logout_device).with(target_device_id)
        subject
        expect(response).to have_http_status(:ok)
        expect(response.parsed_body.dig("status", "message")).to eq("Device logged out successfully")
      end
    end

    context "when device not found" do
      let!(:auth_device) { create(:device_registration, user: user, active: true, refresh_token_expires_at: 1.day.from_now) }
      let(:target_device_id) { "non-existent-id" }
      let(:valid_token) { Auth::TokenService.new(auth_device).generate_tokens[:access_token] }

      let(:headers) do
        {
          "Authorization" => "Bearer #{valid_token}",
          "Device-ID" => auth_device.device_id
        }
      end

      before do
        mock_relation = double("ActiveDevicesRelation", find_by: nil)
        allow(mock_relation).to receive(:find_by).with(device_id: "non-existent-id").and_return(nil)
        allow_any_instance_of(User).to receive(:active_devices).and_return(mock_relation)
      end

      it "returns 404 device not found" do
        subject
        expect(response).to have_http_status(:not_found)
        expect(response.parsed_body.dig("status", "message")).to eq("Device not found")
      end
    end
  end
end
