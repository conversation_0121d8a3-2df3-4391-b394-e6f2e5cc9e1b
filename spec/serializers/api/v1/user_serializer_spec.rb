require 'rails_helper'

RSpec.describe Api::V1::UserSerializer, type: :serializer do
  let(:user) { create(:user) }
  let(:dealership) { create(:dealership) }

  before do
    create(:user_dealership, user: user, dealership: dealership)
  end

  describe 'dealerships association' do
    it 'includes dealerships data by default' do
      result = described_class.render_as_json(user)

      expect(result).to have_key('dealerships')
      expect(result['dealerships'].first['uuid']).to eq(dealership.uuid)
    end

    it 'excludes dealerships data when using without_dealerships view' do
      result = described_class.render_as_json(user, view: :without_dealerships)

      expect(result).not_to have_key('dealerships')
    end
  end

  describe 'user fields' do
    it 'includes expected fields' do
      result = described_class.render_as_json(user)

      expect(result).to have_key('uuid')
      expect(result).to have_key('email')
      expect(result).to have_key('two_factor_methods')
    end

    it 'includes dealership_role when dealership provided' do
      result = described_class.render_as_json(user, dealership: dealership)

      expect(result['dealership_role']).to be_present
    end
  end
end
