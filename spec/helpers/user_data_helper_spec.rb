require 'rails_helper'

RSpec.describe UserDataHelper, type: :helper do
  include UserDataHelper

  describe '#masked_email' do
    it 'masks single-letter local part' do
      expect(masked_email('<EMAIL>')).to eq("*@example.com")
    end

    it 'masks two-letter local part' do
      expect(masked_email('<EMAIL>')).to eq("a*@example.com")
    end

    it 'masks longer local part with first and last letter' do
      expect(masked_email('<EMAIL>')).to eq("a****<EMAIL>")
    end

    it 'masks complex local part' do
      expect(masked_email('<EMAIL>')).to eq("u**********<EMAIL>")
    end

    it 'returns masked email of same length' do
      expect(masked_email('<EMAIL>').length).to eq(14)
    end
  end

  describe '#masked_phone' do
    it 'masks all but last 4 digits' do
      expect(masked_phone('+61111111111')).to eq("********1111")
    end

    it 'returns masked phone of same length' do
      expect(masked_phone('+61111111111').length).to eq(12)
    end
  end
end
