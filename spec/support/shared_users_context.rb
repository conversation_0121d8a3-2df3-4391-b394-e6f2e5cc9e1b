RSpec.shared_context "users_api_shared_context", shared_context: :metadata do
  let(:user) { create(:user) }
  let(:device_registration) { create(:device_registration, user: user) }
  let(:valid_token) { Auth::TokenService.new(device_registration).generate_tokens[:access_token] }

  let(:headers) do
    {
      "Authorization" => "Bearer #{valid_token}",
      "Device-ID" => device_registration.device_id
    }
  end
  let(:Authorization) { "Bearer #{valid_token}" }
  let(:"Device-ID") { device_registration.device_id }
end
