RSpec.shared_context "drive_api_shared_context", shared_context: :metadata do
  include_context "dealership_api_shared_context"

  let(:vehicle) { create(:vehicle, dealership: dealership) }
  let(:vehicle_uuid) { vehicle.uuid }
  let(:customer) { create(:customer, dealership: dealership) }
  let(:driver_license) { create(:driver_license, holder: customer) }
  let(:sales_person) { create(:user) }
  let(:sales_person_uuid) { sales_person.uuid }
  let!(:sales_person_dealership) { create(:user_dealership, user: sales_person, dealership: dealership, role: :sales_person) }
end
