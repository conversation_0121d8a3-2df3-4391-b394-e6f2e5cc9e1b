RSpec.shared_context "appraisal_api_shared_context", shared_context: :metadata do
  include_context "dealership_api_shared_context"

  let(:customer) { create(:customer, dealership: dealership) }
  let(:sales_person) { create(:user) }
  let(:sales_person_uuid) { sales_person.uuid }
  let!(:sales_person_dealership) { create(:user_dealership, user: sales_person, dealership: dealership, role: :sales_person) }
end
