require "rails_helper"

RSpec.describe EmailCsvListValidator do
  let(:validator) { described_class.new(attributes: [ :emails ]) }
  let(:model) { double("model") }
  let(:attribute) { :emails }
  let(:errors) { double("errors") }

  before do
    allow(errors).to receive(:add)
    allow(model).to receive(:errors).and_return(errors)
  end

  describe "#validate_each" do
    context "when value is blank" do
      it "does not add any errors" do
        validator.validate_each(model, attribute, nil)
        expect(errors).not_to have_received(:add)
      end

      it "handles empty string" do
        validator.validate_each(model, attribute, "")
        expect(errors).not_to have_received(:add)
      end

      it "handles whitespace" do
        validator.validate_each(model, attribute, "   ")
        expect(errors).not_to have_received(:add)
      end
    end

    context "when value contains no valid emails" do
      it "adds error for empty list" do
        validator.validate_each(model, attribute, ",")
        expect(errors).to have_received(:add).with(attribute, "must contain at least one valid email address")
      end

      it "adds error for list with only whitespace" do
        validator.validate_each(model, attribute, " , , ")
        expect(errors).to have_received(:add).with(attribute, "must contain at least one valid email address")
      end

      it "adds error for list with only invalid emails" do
        validator.validate_each(model, attribute, "invalid-email,another-invalid")
        expect(errors).to have_received(:add).with(attribute, "invalid-email is not a valid email")
        expect(errors).to have_received(:add).with(attribute, "another-invalid is not a valid email")
      end
    end

    context "when value contains valid and invalid emails" do
      it "adds error only for invalid emails" do
        validator.validate_each(model, attribute, "<EMAIL>,invalid-email,<EMAIL>")
        expect(errors).to have_received(:add).with(attribute, "invalid-email is not a valid email")
        expect(errors).not_to have_received(:add).with(attribute, "<EMAIL> is not a valid email")
        expect(errors).not_to have_received(:add).with(attribute, "<EMAIL> is not a valid email")
      end
    end

    context "when value contains only valid emails" do
      it "does not add any errors" do
        validator.validate_each(model, attribute, "<EMAIL>,<EMAIL>")
        expect(errors).not_to have_received(:add)
      end

      it "handles whitespace around emails" do
        validator.validate_each(model, attribute, "  <EMAIL>  ,  <EMAIL>  ")
        expect(errors).not_to have_received(:add)
      end
    end

    context "when value contains emails exceeding length limit" do
      it "adds error for emails longer than 255 characters" do
        long_email = "a" * 256 + "@example.com"
        validator.validate_each(model, attribute, "<EMAIL>,#{long_email}")
        expect(errors).to have_received(:add).with(attribute, "#{long_email} must be under 255 characters")
      end

      it "allows emails of exactly 255 characters" do
        email = "a" * 243 + "@example.com"
        expect(email.length).to eq(255)
        validator.validate_each(model, attribute, "<EMAIL>,#{email}")
        expect(errors).not_to have_received(:add)
      end
    end

    context "when value contains emails with consecutive dots" do
      it "adds error for emails with consecutive dots" do
        validator.validate_each(model, attribute, "<EMAIL>,<EMAIL>")
        expect(errors).to have_received(:add).with(attribute, "<EMAIL> is not a valid email")
      end
    end
  end
end
