require "rails_helper"

RSpec.describe EmailFormatValidator do
  let(:validator) { described_class.new(attributes: [ :email ]) }
  let(:model) { double("model") }
  let(:attribute) { :email }
  let(:errors) { double("errors") }

  before do
    allow(errors).to receive(:add)
    allow(model).to receive(:errors).and_return(errors)
  end

  describe "#validate_each" do
    context "when value is blank" do
      it "does not add any errors" do
        validator.validate_each(model, attribute, nil)
        expect(errors).not_to have_received(:add)
      end

      it "handles empty string" do
        validator.validate_each(model, attribute, "")
        expect(errors).not_to have_received(:add)
      end

      it "handles whitespace" do
        validator.validate_each(model, attribute, "   ")
        expect(errors).not_to have_received(:add)
      end
    end

    context "when email format is invalid" do
      invalid_emails = [
        # Missing @ symbol
        "plainaddress",
        "user.name",
        "user+tag",

        # Invalid @ placement
        "@domain.com",
        "user@",
        "user@domain",
        "user@.com",
        "user@domain.",

        # Invalid characters
        "user@domain.com_",
        "<EMAIL>-",
        "<EMAIL>,",
        "<EMAIL> ",
        "<EMAIL>\n",
        "<EMAIL>\t",

        # Invalid domain format
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>.",
        "<EMAIL>-",
        "user@domain.com_",

        # Multiple @ symbols
        "<EMAIL>@domain.com",
        "user@@domain.com",

        # Spaces
        "user @domain.com",
        "user@ domain.com",
        "<EMAIL> <EMAIL>",

        # Invalid TLD
        "user@domain.c",
        "user@domain.1",
        "user@domain.c1",
        "user@domain.c-",
        "user@domain.c_",

        # Special characters in TLD
        "<EMAIL>.au_",
        "<EMAIL>-",

        # Invalid local part
        ".<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "user <EMAIL>",
        "user\<EMAIL>",
        "user\<EMAIL>",

        # Invalid length TLD
        "user@domain.a", # Too short
        "user@domain." + "a" * 64 # Too long
      ]

      invalid_emails.each do |email|
        it "adds error for invalid email: #{email}" do
          validator.validate_each(model, attribute, email)
          expect(errors).to have_received(:add).with(attribute, "#{email} is not a valid email")
        end
      end
    end

    context "when email format is valid" do
      valid_emails = [
        # Basic formats
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",

        # Subdomains
        "<EMAIL>",
        "<EMAIL>",

        # Country TLDs
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",

        # Hyphenated domains
        "<EMAIL>",
        "<EMAIL>",

        # Numbers in domain
        "<EMAIL>",
        "<EMAIL>",

        # Special characters in local part
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",

        # Long TLDs
        "<EMAIL>",
        "<EMAIL>",

        # Short TLDs
        "<EMAIL>",
        "<EMAIL>"
      ]

      valid_emails.each do |email|
        it "does not add error for valid email: #{email}" do
          validator.validate_each(model, attribute, email)
          expect(errors).not_to have_received(:add)
        end
      end
    end

    context "when email length exceeds 255 characters" do
      it "adds error for email longer than 255 characters" do
        long_email = "a" * 256 + "@example.com"
        validator.validate_each(model, attribute, long_email)
        expect(errors).to have_received(:add).with(attribute, "#{long_email} must be under 255 characters")
      end

      it "allows email of exactly 255 characters" do
        # Calculate exact length needed for 255 characters
        # "@example.com" is 12 characters
        # So we need 255 - 12 = 243 'a' characters
        email = "a" * 243 + "@example.com"
        expect(email.length).to eq(255) # Verify the length is exactly 255

        validator.validate_each(model, attribute, email)
        expect(errors).not_to have_received(:add)
      end
    end
  end
end
