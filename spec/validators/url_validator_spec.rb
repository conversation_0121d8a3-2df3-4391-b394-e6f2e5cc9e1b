require 'rails_helper'

RSpec.describe UrlValidator do
  # Create a test model to use the validator
  let(:test_class) do
    Class.new do
      include ActiveModel::Validations
      attr_accessor :website_url
      validates :website_url, url: true
    end
  end

  let(:test_instance) { test_class.new }

  describe '#validate_each' do
    context 'with valid URLs' do
      it 'accepts valid HTTP URLs' do
        test_instance.website_url = 'http://example.com'
        expect(test_instance).to be_valid
      end

      it 'accepts valid HTTPS URLs' do
        test_instance.website_url = 'https://example.com'
        expect(test_instance).to be_valid
      end

      it 'accepts URLs with paths' do
        test_instance.website_url = 'https://example.com/path/to/page'
        expect(test_instance).to be_valid
      end

      it 'accepts URLs with query parameters' do
        test_instance.website_url = 'https://example.com/search?q=test&page=1'
        expect(test_instance).to be_valid
      end

      it 'accepts URLs with ports' do
        test_instance.website_url = 'https://example.com:8080'
        expect(test_instance).to be_valid
      end

      it 'accepts URLs with subdomains' do
        test_instance.website_url = 'https://subdomain.example.com'
        expect(test_instance).to be_valid
      end
    end

    context 'with blank values' do
      it 'accepts nil values' do
        test_instance.website_url = nil
        expect(test_instance).to be_valid
      end

      it 'accepts empty string values' do
        test_instance.website_url = ''
        expect(test_instance).to be_valid
      end

      it 'accepts whitespace-only values' do
        test_instance.website_url = '   '
        expect(test_instance).to be_valid
      end
    end

    context 'with invalid URLs' do
      it 'rejects non-HTTP/HTTPS schemes' do
        test_instance.website_url = 'ftp://example.com'
        expect(test_instance).not_to be_valid
        expect(test_instance.errors[:website_url]).to include('is not a valid URL')
      end

      it 'rejects file URLs' do
        test_instance.website_url = 'file:///path/to/file'
        expect(test_instance).not_to be_valid
        expect(test_instance.errors[:website_url]).to include('is not a valid URL')
      end

      it 'rejects mailto URLs' do
        test_instance.website_url = 'mailto:<EMAIL>'
        expect(test_instance).not_to be_valid
        expect(test_instance.errors[:website_url]).to include('is not a valid URL')
      end

      it 'rejects malformed URLs' do
        test_instance.website_url = 'not-a-url'
        expect(test_instance).not_to be_valid
        expect(test_instance.errors[:website_url]).to include('is not a valid URL')
      end

      it 'rejects URLs with invalid characters' do
        test_instance.website_url = 'https://example.com/path with spaces'
        expect(test_instance).not_to be_valid
        expect(test_instance.errors[:website_url]).to include('is not a valid URL')
      end

      it 'rejects URLs that raise URI::InvalidURIError' do
        test_instance.website_url = 'https://[invalid'
        expect(test_instance).not_to be_valid
        expect(test_instance.errors[:website_url]).to include('is not a valid URL')
      end
    end
  end
end
