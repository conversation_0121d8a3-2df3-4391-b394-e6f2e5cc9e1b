namespace :users do
  desc "Invite a new user via command line"
  task :invite, [ :email, :first_name, :last_name, :phone, :user_type, :dealership_uuid, :role ] => :environment do |t, args|
    begin
      # Validate required arguments
      required_args = [ :email, :first_name, :last_name, :phone, :user_type ]
      missing_args = required_args.select { |arg| args[arg].blank? }

      if missing_args.any?
        puts "Error: Missing required arguments: #{missing_args.join(', ')}"
        puts "Usage: rake users:invite[email,first_name,last_name,phone,user_type,dealership_uuid,role]"
        puts "Example: rake users:invite[<EMAIL>,John,Doe,555-1234,dealership_user,abc-123,sales_person]"
        exit 1
      end

      # Find the first super admin to use as the inviting user
      inviting_user = User.where(user_type: :super_admin).first

      unless inviting_user
        puts "Error: No super admin user found in the system"
        exit 1
      end

      # Prepare parameters for the service
      invite_params = {
        email: args[:email],
        first_name: args[:first_name],
        last_name: args[:last_name],
        phone: args[:phone],
        user_type: args[:user_type]
      }

      # Add optional parameters if provided
      invite_params[:dealership_uuid] = args[:dealership_uuid] if args[:dealership_uuid].present?
      invite_params[:role] = args[:role] if args[:role].present?

      # Call the invitation service
      service = UserInvitationService.new(inviting_user)
      user = service.invite_user(**invite_params)

      puts "Success! User invited successfully:"
      puts "  Email: #{user.email}"
      puts "  Name: #{user.first_name} #{user.last_name}"
      puts "  User Type: #{user.user_type}"
      puts "  Temporary Password: #{service.temp_password}"

      if service.dealership
        puts "  Dealership: #{service.dealership.name} (#{service.dealership.uuid})"
        user_dealership = user.user_dealerships.first
        puts "  Role: #{user_dealership.role}" if user_dealership
      end

    rescue => e
      puts "Error: #{e.message}"
      exit 1
    end
  end
end
