class CreateDealershipFeaturesSettings < ActiveRecord::Migration[8.0]
  def change
    create_table :dealership_features_settings do |t|
      t.references :dealership, null: false, foreign_key: true, index: true
      t.boolean :advance_booking_enabled, default: false, null: false
      t.boolean :insurance_waiver_enabled, default: false, null: false
      t.boolean :dealer_drive_subscription, default: false, null: false
      t.boolean :appraisals_subscription, default: false, null: false
      t.integer :setting_recent_customer_age

      t.timestamps
    end
  end
end
