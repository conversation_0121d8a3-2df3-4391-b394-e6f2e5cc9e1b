# rubocop:disable Rails/BulkChangeTable
class AddFieldsToModels < ActiveRecord::Migration[8.0]
  def change
    # Customer model updates
    add_column :customers, :company_name, :string
    rename_column :customers, :telephone, :phone_number
    add_column :customers, :external_id, :string

    # Drive model updates
    add_column :drives, :start_datetime, :datetime
    add_column :drives, :end_datetime, :datetime
    add_column :drives, :start_odometer_reading, :integer
    add_column :drives, :end_odometer_reading, :integer
    # DamageReport belongs to Drive and it has a report_type.
    add_reference :damage_reports, :drive, foreign_key: true
    add_column :damage_reports, :report_type, :integer
    add_reference :damage_reports, :vehicle, foreign_key: true
    add_reference :drives, :sales_person_accompanying, foreign_key: { to_table: :users }
    add_column :drives, :sold_status, :integer, limit: 1

    # DriverLicense model updates
    add_column :driver_licenses, :expiry_date, :date
    add_column :driver_licenses, :issuing_country, :string, limit: 100, default: 'au'
    add_column :driver_licenses, :issuing_state, :string, limit: 100
    add_column :driver_licenses, :category, :string, limit: 20
    add_column :driver_licenses, :full_name, :string
    add_column :driver_licenses, :date_of_birth, :date
    add_column :driver_licenses, :issue_date, :date

    # Vehicle model updates
    add_column :vehicles, :registration_expiry, :date
    add_column :vehicles, :brand, :string
    add_column :vehicles, :available_for_drive, :boolean, default: false, null: false
    add_column :vehicles, :vehicle_type, :integer
  end
end

# rubocop:enable Rails/BulkChangeTable
