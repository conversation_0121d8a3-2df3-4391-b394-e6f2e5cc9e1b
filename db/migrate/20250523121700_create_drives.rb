class CreateDrives < ActiveRecord::Migration[7.0]
  def change
    create_table :drives do |t|
      t.references :dealership, null: false, foreign_key: true
      t.references :vehicle, null: false, foreign_key: true
      t.references :customer, null: false, foreign_key: true
      t.references :sales_person, null: false, foreign_key: { to_table: :users }
      t.references :driver_license, foreign_key: true

      t.integer :drive_type, null: false, limit: 1, unsigned: true
      t.integer :status, null: false, default: 0, limit: 1, unsigned: true

      t.datetime :expected_pickup_datetime
      t.datetime :expected_return_datetime

      t.text :notes

      t.timestamps
    end

    add_index :drives, [ :dealership_id, :status, :id ], name: 'idx_dealership_status_id'
  end
end
