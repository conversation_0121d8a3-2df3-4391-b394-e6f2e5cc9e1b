class CreateVehicleConditions < ActiveRecord::Migration[8.0]
  def change
    create_table :vehicle_conditions do |t|
      t.string :uuid, limit: 36, null: false
      t.references :customer_vehicle, null: false, foreign_key: true
      t.boolean :is_clean, default: true, null: false
      t.boolean :is_wet, default: false, null: false
      t.boolean :is_road_tested, default: false, null: false
      t.boolean :has_signs_of_repair, default: false, null: false
      t.string :repair_details
      t.string :additional_notes
      t.timestamps
      t.index :uuid, unique: true
    end

    create_table :component_ratings do |t|
      t.integer :name, null: false
      t.integer :rating, null: false
      t.references :vehicle_condition, null: false, foreign_key: true
      t.timestamps
    end

    create_table :body_part_conditions do |t|
      t.references :vehicle_condition, null: false, foreign_key: true
      t.integer :part_name, null: false
      t.string :condition, null: false
      t.string :description
      t.timestamps
    end

    create_table :reconditioning_costs do |t|
      t.references :vehicle_condition, null: false, foreign_key: true
      t.integer :cost_type, null: false
      t.integer :amount_cents, null: false
      t.string :currency, default: "AUD", null: false
      t.timestamps
    end
  end
end
