class CreateCustomers < ActiveRecord::Migration[7.0]
  def change
    create_table :customers do |t|
      t.references :dealership, null: false, foreign_key: true
      t.string :first_name, null: false
      t.string :last_name, null: false
      t.integer :age, limit: 2
      t.string :telephone, limit: 20
      t.string :email
      t.integer :gender, limit: 1
      t.string :postcode, limit: 10
      t.string :suburb, limit: 100
      t.string :address_line1
      t.string :address_line2
      t.string :city, limit: 100
      t.string :state, limit: 100
      t.string :country, null: false, limit: 100, default: 'au'

      t.timestamps
    end
  end
end
