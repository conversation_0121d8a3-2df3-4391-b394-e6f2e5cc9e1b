class CreateDealershipEmailSettings < ActiveRecord::Migration[8.0]
  def change
    create_table :dealership_email_settings do |t|
      t.references :dealership, null: false, foreign_key: true, index: true
      t.boolean :send_test_drive_review_email, default: false, null: false
      t.boolean :send_test_drive_terms_email, default: false, null: false
      t.boolean :send_loan_terms_email, default: false, null: false
      t.integer :level1_odometer_warning_km
      t.string :level1_odometer_warning_email
      t.integer :level2_odometer_warning_km
      t.string :level2_odometer_warning_email
      t.string :email_from_address
      t.string :email_display_name
      t.boolean :loan_review_email_enabled, default: false, null: false
      t.integer :send_email_for_bookings, default: 0, null: false

      t.timestamps
    end
  end
end
