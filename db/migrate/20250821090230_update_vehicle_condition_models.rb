class UpdateVehicleConditionModels < ActiveRecord::Migration[8.0]
  def change
    # Change amount_cents to amount with decimal type in reconditioning_costs table
    reversible do |dir|
      dir.up do
        change_column :reconditioning_costs, :amount_cents, :decimal, precision: 15, scale: 2, null: false
        rename_column :reconditioning_costs, :amount_cents, :amount
      end
      dir.down do
        rename_column :reconditioning_costs, :amount, :amount_cents
        change_column :reconditioning_costs, :amount_cents, :integer, null: false
      end
    end

    # Add unique constraints to vehicle condition models
    add_index :body_part_conditions, [ :vehicle_condition_id, :part_name ], unique: true, name: 'index_body_part_conditions_on_vehicle_condition_and_part_name'
    add_index :component_ratings, [ :vehicle_condition_id, :name ], unique: true, name: 'index_component_ratings_on_vehicle_condition_and_name'
    add_index :reconditioning_costs, [ :vehicle_condition_id, :cost_type ], unique: true, name: 'index_reconditioning_costs_on_vehicle_condition_and_cost_type'

    # Change condition column from string to integer in body_part_conditions using reversible block
    reversible do |dir|
      dir.up   { change_column :body_part_conditions, :condition, :integer, null: false }
      dir.down { change_column :body_part_conditions, :condition, :string, null: false }
    end
  end
end
