class ChangeAppraisalCustomerVehicleRelationship < ActiveRecord::Migration[8.0]
  def change
    # Add appraisal_id to customer_vehicles table
    add_reference :customer_vehicles, :appraisal, foreign_key: true, type: :bigint

    # Remove customer_vehicle_id from appraisals table
    remove_foreign_key :appraisals, :customer_vehicles
    remove_reference :appraisals, :customer_vehicle, type: :bigint
  end
end
