class CreateVehicles < ActiveRecord::Migration[7.0]
  def change
    create_table :vehicles do |t|
      t.references :dealership, null: false, foreign_key: true
      t.string :vin, limit: 50
      t.string :stock_number, limit: 50
      t.string :rego, limit: 50
      t.string :make, null: false, limit: 255
      t.string :model, null: false, limit: 255
      t.string :color, limit: 45
      t.integer :year, limit: 5, unsigned: true
      t.date :rego_expiry
      t.boolean :is_trade_plate_used, default: false, null: false
      t.integer :last_known_odometer_km
      t.integer :last_known_fuel_gauge_level, default: -1
      t.datetime :last_system_inspection_timestamp
      t.integer :status, null: false, limit: 3, unsigned: true

      t.timestamps

      t.index [ :dealership_id, :stock_number ], name: 'idx_dealership_stock_number'
      t.index :rego, name: 'idx_vehicles_on_rego'
    end
  end
end
