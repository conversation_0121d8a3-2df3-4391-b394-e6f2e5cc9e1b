# rubocop:disable Rails/BulkChangeTable

class AddsUuidToVehicleRelatedModels < ActiveRecord::Migration[8.0]
  def change
    add_column :customers, :uuid, :string, limit: 36
    add_index :customers, :uuid, unique: true

   add_column :damage_reports, :uuid, :string, limit: 36
   add_index :damage_reports, :uuid, unique: true

   add_column :drives, :uuid, :string, limit: 36
   add_index :drives, :uuid, unique: true

   add_column :driver_licenses, :uuid, :string, limit: 36
   add_index :driver_licenses, :uuid, unique: true

   add_column :vehicles, :uuid, :string, limit: 36
   add_index :vehicles, :uuid, unique: true
  end
end

# rubocop:enable Rails/BulkChangeTable
