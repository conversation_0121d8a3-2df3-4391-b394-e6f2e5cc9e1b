class CreateDeviceRegistrations < ActiveRecord::Migration[8.0]
  def change
    create_table :device_registrations do |t|
      t.references :user, null: false, foreign_key: true
      t.string :device_id, null: false
      t.string :fcm_token
      t.string :device_name
      t.integer :device_os, null: false
      t.string :device_os_version
      t.string :app_version, null: false
      t.string :app_build_number, null: false
      t.string :last_login_ip
      t.datetime :last_login_timestamp
      t.string :refresh_token, null: false
      t.datetime :refresh_token_expires_at, null: false
      t.boolean :active, default: true, null: false
      t.datetime :logged_out_at
      t.datetime :last_activity_at

      t.timestamps
    end

    add_index :device_registrations, [ :user_id, :device_id ], unique: true # rubocop:disable Rails/BulkChangeTable
    add_index :device_registrations, :refresh_token, unique: true
  end
end
