class AddFieldsToOptionsFitted < ActiveRecord::Migration[8.0]
  def change
    change_table :options_fitted, bulk: true do |t|
      t.integer :sunroof_type
      t.integer :number_of_keys
      t.boolean :heated_seats, default: false, null: false
      t.boolean :cargo_blind, default: false, null: false
      t.boolean :tonneau_cover, default: false, null: false
      t.integer :tonneau_type
      t.integer :on_written_off_register
      t.date :last_ppsr_date
    end
  end
end
