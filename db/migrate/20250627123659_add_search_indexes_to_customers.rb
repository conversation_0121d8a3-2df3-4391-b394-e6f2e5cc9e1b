class AddSearchIndexesToCustomers < ActiveRecord::Migration[8.0]
  def change
    change_table :customers, bulk: true do |t|
      t.index :first_name
      t.index :last_name
      t.index :email
      t.index :phone_number
      t.index [ :first_name, :last_name ], name: 'index_customers_on_full_name'
      t.index [ :dealership_id, :first_name ], name: 'index_customers_on_dealership_first_name'
      t.index [ :dealership_id, :last_name ], name: 'index_customers_on_dealership_last_name'
      t.index [ :dealership_id, :email ], name: 'index_customers_on_dealership_email'
      t.index [ :dealership_id, :phone_number ], name: 'index_customers_on_dealership_phone_number'
    end
  end
end
