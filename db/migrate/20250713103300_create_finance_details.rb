class CreateFinanceDetails < ActiveRecord::Migration[8.0]
  def change
    create_table :finance_details do |t|
      t.string :uuid, limit: 36, null: false
      t.references :customer_vehicle, null: false, foreign_key: true, index: true
      t.boolean :is_financed, null: false, default: false
      t.boolean :has_clear_title, null: false, default: false
      t.decimal :current_repayment_amount, precision: 10, scale: 2
      t.integer :terms_months
      t.decimal :interest_rate, precision: 5, scale: 2
      t.date :next_due_date
      t.string :finance_company, limit: 50

      t.timestamps
    end

    add_index :finance_details, :uuid, unique: true
  end
end
