class CreateVehicleHistories < ActiveRecord::Migration[8.0]
  def change
    create_table :vehicle_histories do |t|
      t.string :uuid, limit: 36, null: false
      t.references :customer_vehicle, null: false, foreign_key: true
      t.integer :number_of_owners
      t.boolean :has_accident_history, default: false, null: false
      t.text :accident_details
      t.date :last_service_date
      t.integer :last_service_odometer
      t.date :next_service_due
      t.boolean :has_dash_warning_lights, default: false, null: false
      t.text :dash_warning_details
      t.text :notes
      t.integer :vehicle_history_status, null: false, default: 0

      t.timestamps
    end
    add_index :vehicle_histories, :uuid, unique: true
  end
end
