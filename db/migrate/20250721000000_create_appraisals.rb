class CreateAppraisals < ActiveRecord::Migration[7.0]
  def change
    create_table :appraisals do |t|
      t.string :uuid, null: false, limit: 36
      t.references :dealership, null: false, foreign_key: true, type: :bigint
      t.references :customer, null: false, foreign_key: true, type: :bigint
      t.references :customer_vehicle, null: true, foreign_key: true, type: :bigint
      t.references :sales_person, foreign_key: { to_table: :users }, type: :bigint
      t.references :created_by, foreign_key: { to_table: :users }, type: :bigint
      t.references :updated_by, foreign_key: { to_table: :users }, type: :bigint

      t.integer :status, null: false, default: 0
      t.integer :completed_percentage
      t.decimal :awarded_value, precision: 15, scale: 2
      t.decimal :price, precision: 15, scale: 2
      t.decimal :give_price, precision: 15, scale: 2
      t.string :awarded_notes, limit: 1000
      t.string :notes, limit: 1000

      t.timestamps
    end
    add_index :appraisals, :uuid, unique: true
  end
end
