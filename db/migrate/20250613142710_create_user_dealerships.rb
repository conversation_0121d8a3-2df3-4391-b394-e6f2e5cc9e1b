class CreateUserDealerships < ActiveRecord::Migration[8.0]
  def change
    create_table :user_dealerships do |t|
      t.references :user, null: false, foreign_key: true
      t.references :dealership, null: false, foreign_key: true
      t.integer :role, null: false, default: 1, unsigned: true, limit: 1 # 0: admin, 1: sales_person

      t.timestamps
    end

    add_index :user_dealerships, [ :user_id, :dealership_id ], unique: true
  end
end
