puts "Seeding drives..."

unless Dealership.any?
  puts "Dealerships missing. Please run dealership seeds first."
  return
end

unless Customer.any?
  puts "Customers missing. Please run customer seeds first."
  return
end

unless DealershipVehicle.any?
  puts "Vehicles missing. Please run vehicle seeds first."
  return
end

unless User.sales_people.any?
  puts "Sales People missing. Please run user seeds first."
  return
end

dealership1 = Dealership.first
dealership2 = Dealership.second

[
  {
    dealership: dealership1,
    drive_type: :test_drive_booking,
    status: :scheduled,
    expected_pickup_datetime: 1.day.from_now.change(hour: 10),
    expected_return_datetime: 1.day.from_now.change(hour: 12),
    notes: "Test Drive Booking 1"
  },
  {
    dealership: dealership1,
    drive_type: :loan_booking,
    status: :scheduled,
    expected_pickup_datetime: 1.day.from_now.change(hour: 10),
    expected_return_datetime: 2.days.from_now.change(hour: 12),
    notes: "Loan Booking 1"
  },
  {
    dealership: dealership1,
    drive_type: :enquiry,
    notes: "Enquiry 1"
  },
  {
    dealership: dealership1,
    drive_type: :test_drive_booking,
    status: :cancelled,
    cancel_reason: "Customer changed mind",
    cancelled_at: 1.day.ago,
    notes: "Cancelled Test Drive Booking 1"
  },
  {
    dealership: dealership1,
    drive_type: :test_drive,
    status: :in_progress,
    start_odometer_reading: 1000,
    sales_person_accompanying_id: dealership1.sales_people.first.id,
    notes: "Test Drive 1",
    start_datetime: 1.day.ago.change(hour: 10)
  },
  {
    dealership: dealership1,
    drive_type: :test_drive,
    status: :completed,
    start_odometer_reading: 10000,
    end_odometer_reading: 10100,
    sold_status: :sold,
    notes: "Completed Test Drive 1 Sold",
    start_datetime: 2.days.ago.change(hour: 10),
    end_datetime: 2.days.ago.change(hour: 12)
  },
  {
    dealership: dealership1,
    drive_type: :loan,
    status: :in_progress,
    with_trade_plate: true,
    start_odometer_reading: 20000,
    notes: "Loan 1",
    start_datetime: 1.day.ago.change(hour: 10)
  },
  {
    dealership: dealership1,
    drive_type: :loan,
    status: :completed,
    start_odometer_reading: 30000,
    end_odometer_reading: 30500,
    notes: "Completed Loan 1",
    start_datetime: 2.days.ago.change(hour: 10),
    end_datetime: 1.day.ago.change(hour: 12)
  },
  {
    dealership: dealership1,
    drive_type: :self_loan,
    status: :completed,
    start_odometer_reading: 40000,
    end_odometer_reading: 40500,
    notes: "Completed Self Loan 1",
    start_datetime: 2.days.ago.change(hour: 10),
    end_datetime: 1.day.ago.change(hour: 12)
  },
  {
    dealership: dealership2,
    drive_type: :test_drive_booking,
    status: :scheduled,
    start_odometer_reading: nil
  },
  {
    dealership: dealership2,
    drive_type: :test_drive_booking,
    status: :scheduled,
    start_odometer_reading: nil
  }
].each_with_index do |drive_data, i|
  dealership = drive_data[:dealership]
  vehicle = dealership.vehicles.order("RAND()").first
  vehicle = dealership.vehicles.where(rego: nil).order("RAND()").first if drive_data[:with_trade_plate]
  customer = dealership.customers.order("RAND()").first
  sales_person = dealership.sales_people.order("RAND()").first
  trade_plate = dealership.trade_plates.active.order("RAND()").first if drive_data[:with_trade_plate]

  if vehicle && customer && sales_person
    drive = Drive.create!(
      dealership: dealership,
      vehicle: vehicle,
      customer: customer,
      trade_plate: trade_plate,
      sales_person: sales_person,
      **drive_data.except(:dealership, :with_trade_plate)
    )
    drive.initial_damage_report = DamageReport.create!(
      drive: drive,
      vehicle: vehicle,
      report_type: DamageReport::INITIAL,
      description: "Minor scratches on the front bumper"
    ) if ( drive.in_progress? || drive.completed? ) && Drive::VEHICLE_OUT_DRIVE_TYPES.include?(drive.drive_type)
    drive.initial_damage_report.media_files.attach([
      {
        io: File.open(Rails.root.join("spec/fixtures/files/damage1.jpg").to_s),
        filename: "damage1.jpg",
        content_type: "image/jpg"
      },
      {
        io: File.open(Rails.root.join("spec/fixtures/files/damage2.jpg").to_s),
        filename: "damage2.jpg",
        content_type: "image/jpg"
      }
    ]) if drive.initial_damage_report

    drive.final_damage_report = DamageReport.create!(
      drive: drive,
      vehicle: vehicle,
      report_type: DamageReport::FINAL,
      description: "Damage observed during drive"
    ) if drive.completed? && Drive::VEHICLE_OUT_DRIVE_TYPES.include?(drive.drive_type)
    drive.final_damage_report.media_files.attach([
      {
        io: File.open(Rails.root.join("spec/fixtures/files/damage3.jpg").to_s),
        filename: "damage3.jpg",
        content_type: "image/jpg"
      },
      {
        io: File.open(Rails.root.join("spec/fixtures/files/damage4.jpg").to_s),
        filename: "damage4.jpg",
        content_type: "image/jpg"
      }
      ]) if drive.final_damage_report

    puts "Created drive for #{customer.full_name} in #{dealership.name} and Trade plate: #{trade_plate&.number}"
  else
    puts "Skipping: Missing data for drive #{i + 1}"
  end
end

# Add 50-60 more drives for dealership1
[
  # Test Drive Bookings
  {
    dealership: dealership1,
    drive_type: :test_drive_booking,
    status: :scheduled,
    expected_pickup_datetime: 2.days.from_now.change(hour: 9),
    expected_return_datetime: 2.days.from_now.change(hour: 11),
    notes: "Customer wants to test the hybrid model"
  },
  {
    dealership: dealership1,
    drive_type: :test_drive_booking,
    status: :scheduled,
    expected_pickup_datetime: 3.days.from_now.change(hour: 14),
    expected_return_datetime: 3.days.from_now.change(hour: 16),
    notes: "Family test drive - multiple passengers"
  },
  {
    dealership: dealership1,
    drive_type: :test_drive_booking,
    status: :cancelled,
    cancel_reason: "Weather conditions",
    cancelled_at: 2.hours.ago,
    notes: "Cancelled due to heavy rain"
  },

  # Test Drives (completed and in progress)
  {
    dealership: dealership1,
    drive_type: :test_drive,
    status: :completed,
    start_odometer_reading: 15000,
    end_odometer_reading: 15025,
    sold_status: :sold,
    start_datetime: 3.days.ago.change(hour: 10),
    end_datetime: 3.days.ago.change(hour: 11),
    notes: "Excellent test drive - customer purchased immediately",
    with_waypoints: true,
    with_damage_reports: true
  },
  {
    dealership: dealership1,
    drive_type: :test_drive,
    status: :completed,
    start_odometer_reading: 25000,
    end_odometer_reading: 25040,
    sold_status: :unsold,
    start_datetime: 4.days.ago.change(hour: 15),
    end_datetime: 4.days.ago.change(hour: 16),
    notes: "Customer needs more time to decide",
    with_waypoints: true,
    with_damage_reports: true
  },
  {
    dealership: dealership1,
    drive_type: :test_drive,
    status: :in_progress,
    start_odometer_reading: 35000,
    start_datetime: 30.minutes.ago,
    notes: "Currently on highway test",
    with_waypoints: true,
    with_initial_damage: true
  },

  # Loan Bookings
  {
    dealership: dealership1,
    drive_type: :loan_booking,
    status: :scheduled,
    expected_pickup_datetime: 1.day.from_now.change(hour: 8),
    expected_return_datetime: 3.days.from_now.change(hour: 17),
    notes: "Weekend loan for family trip"
  },
  {
    dealership: dealership1,
    drive_type: :loan_booking,
    status: :scheduled,
    expected_pickup_datetime: 4.days.from_now.change(hour: 12),
    expected_return_datetime: 1.week.from_now.change(hour: 12),
    notes: "Extended loan for business evaluation"
  },
  {
    dealership: dealership1,
    drive_type: :loan_booking,
    status: :cancelled,
    cancel_reason: "Customer found alternative",
    cancelled_at: 1.day.ago,
    notes: "Customer purchased from competitor"
  },

  # Loans (active and completed)
  {
    dealership: dealership1,
    drive_type: :loan,
    status: :completed,
    start_odometer_reading: 45000,
    end_odometer_reading: 45200,
    start_datetime: 5.days.ago.change(hour: 9),
    end_datetime: 3.days.ago.change(hour: 18),
    notes: "Successful 2-day loan period",
    with_waypoints: true,
    with_damage_reports: true
  },
  {
    dealership: dealership1,
    drive_type: :loan,
    status: :in_progress,
    with_trade_plate: true,
    start_odometer_reading: 55000,
    start_datetime: 1.day.ago.change(hour: 11),
    notes: "Business loan - delivery vehicle evaluation",
    with_waypoints: true,
    with_initial_damage: true
  },
  {
    dealership: dealership1,
    drive_type: :loan,
    status: :completed,
    start_odometer_reading: 65000,
    end_odometer_reading: 65150,
    start_datetime: 1.week.ago.change(hour: 10),
    end_datetime: 4.days.ago.change(hour: 16),
    notes: "Extended loan - customer very satisfied",
    with_damage_reports: true
  },

  # Self Loans
  {
    dealership: dealership1,
    drive_type: :self_loan,
    status: :completed,
    start_odometer_reading: 75000,
    end_odometer_reading: 75080,
    start_datetime: 6.days.ago.change(hour: 8),
    end_datetime: 5.days.ago.change(hour: 19),
    notes: "Sales person personal use",
    with_waypoints: true
  },
  {
    dealership: dealership1,
    drive_type: :self_loan,
    status: :in_progress,
    start_odometer_reading: 85000,
    start_datetime: 2.hours.ago,
    notes: "Manager taking vehicle for service appointment",
    with_initial_damage: true
  },

  # Enquiries
  {
    dealership: dealership1,
    drive_type: :enquiry,
    status: :scheduled,
    notes: "Customer interested in financing options"
  },
  {
    dealership: dealership1,
    drive_type: :enquiry,
    status: :completed,
    notes: "Provided detailed vehicle specifications and pricing"
  },
  {
    dealership: dealership1,
    drive_type: :enquiry,
    status: :cancelled,
    cancel_reason: "Customer not responding",
    cancelled_at: 3.days.ago,
    notes: "Multiple follow-up attempts failed"
  },

  # More varied test drives
  {
    dealership: dealership1,
    drive_type: :test_drive,
    status: :completed,
    start_odometer_reading: 95000,
    end_odometer_reading: 95035,
    sold_status: :sold,
    start_datetime: 2.weeks.ago.change(hour: 14),
    end_datetime: 2.weeks.ago.change(hour: 15),
    notes: "Quick decision - loved the performance",
    with_damage_reports: true
  },
  {
    dealership: dealership1,
    drive_type: :test_drive,
    status: :completed,
    start_odometer_reading: 105000,
    end_odometer_reading: 105060,
    sold_status: :unsold,
    start_datetime: 10.days.ago.change(hour: 11),
    end_datetime: 10.days.ago.change(hour: 12, min: 30),
    notes: "Extended test drive - customer comparing with other brands",
    with_waypoints: true,
    with_damage_reports: true
  },

  # Additional loan bookings with various statuses
  {
    dealership: dealership1,
    drive_type: :loan_booking,
    status: :scheduled,
    expected_pickup_datetime: 5.days.from_now.change(hour: 10),
    expected_return_datetime: 1.week.from_now.change(hour: 15),
    notes: "Corporate fleet evaluation"
  },
  {
    dealership: dealership1,
    drive_type: :loan_booking,
    status: :cancelled,
    cancel_reason: "Budget constraints",
    cancelled_at: 4.hours.ago,
    notes: "Customer postponed purchase decision"
  },

  # More completed loans with different patterns
  {
    dealership: dealership1,
    drive_type: :loan,
    status: :completed,
    start_odometer_reading: 115000,
    end_odometer_reading: 115300,
    start_datetime: 2.weeks.ago.change(hour: 9),
    end_datetime: 1.week.ago.change(hour: 17),
    notes: "Week-long evaluation - high mileage usage",
    with_waypoints: true,
    with_damage_reports: true
  },
  {
    dealership: dealership1,
    drive_type: :loan,
    status: :completed,
    with_trade_plate: true,
    start_odometer_reading: 125000,
    end_odometer_reading: 125120,
    start_datetime: 9.days.ago.change(hour: 12),
    end_datetime: 7.days.ago.change(hour: 14),
    notes: "Trade plate loan - unregistered demo vehicle",
    with_damage_reports: true
  },

  # Mix of recent and older drives
  {
    dealership: dealership1,
    drive_type: :test_drive_booking,
    status: :scheduled,
    expected_pickup_datetime: 6.hours.from_now,
    expected_return_datetime: 8.hours.from_now,
    notes: "Same-day booking - urgent customer request"
  },
  {
    dealership: dealership1,
    drive_type: :test_drive,
    status: :completed,
    start_odometer_reading: 135000,
    end_odometer_reading: 135020,
    sold_status: :sold,
    start_datetime: 3.weeks.ago.change(hour: 16),
    end_datetime: 3.weeks.ago.change(hour: 17),
    notes: "Evening test drive - customer works during day",
    with_waypoints: true
  },

  # Self loans for different scenarios
  {
    dealership: dealership1,
    drive_type: :self_loan,
    status: :completed,
    start_odometer_reading: 145000,
    end_odometer_reading: 145050,
    start_datetime: 4.days.ago.change(hour: 7),
    end_datetime: 4.days.ago.change(hour: 20),
    notes: "Sales person demo for customer presentation",
    with_waypoints: true
  },

  # Additional enquiries
  {
    dealership: dealership1,
    drive_type: :enquiry,
    status: :completed,
    notes: "Phone enquiry - provided detailed information packet"
  },
  {
    dealership: dealership1,
    drive_type: :enquiry,
    status: :scheduled,
    notes: "Follow-up appointment scheduled for detailed discussion"
  },

  # More in-progress drives
  {
    dealership: dealership1,
    drive_type: :loan,
    status: :in_progress,
    start_odometer_reading: 155000,
    start_datetime: 4.hours.ago,
    notes: "Customer testing for weekend family use",
    with_waypoints: true,
    with_initial_damage: true
  },
  {
    dealership: dealership1,
    drive_type: :test_drive,
    status: :in_progress,
    start_odometer_reading: 165000,
    start_datetime: 1.hour.ago,
    notes: "Performance test on highway",
    with_waypoints: true,
    with_initial_damage: true
  },

  # Historical completed drives
  {
    dealership: dealership1,
    drive_type: :test_drive,
    status: :completed,
    start_odometer_reading: 175000,
    end_odometer_reading: 175045,
    sold_status: :unsold,
    start_datetime: 1.month.ago.change(hour: 13),
    end_datetime: 1.month.ago.change(hour: 14),
    notes: "Customer decided to wait for new model year",
    with_damage_reports: true
  },
  {
    dealership: dealership1,
    drive_type: :loan,
    status: :completed,
    start_odometer_reading: 185000,
    end_odometer_reading: 185180,
    start_datetime: 3.weeks.ago.change(hour: 8),
    end_datetime: 2.weeks.ago.change(hour: 18),
    notes: "Extended evaluation period - business fleet consideration",
    with_waypoints: true,
    with_damage_reports: true
  },

  # Recent bookings
  {
    dealership: dealership1,
    drive_type: :test_drive_booking,
    status: :scheduled,
    expected_pickup_datetime: 1.week.from_now.change(hour: 11),
    expected_return_datetime: 1.week.from_now.change(hour: 13),
    notes: "Repeat customer - previous buyer returning"
  },
  {
    dealership: dealership1,
    drive_type: :loan_booking,
    status: :scheduled,
    expected_pickup_datetime: 10.days.from_now.change(hour: 9),
    expected_return_datetime: 2.weeks.from_now.change(hour: 17),
    notes: "Extended loan for thorough evaluation"
  },

  # Cancelled drives with various reasons
  {
    dealership: dealership1,
    drive_type: :test_drive_booking,
    status: :cancelled,
    cancel_reason: "Vehicle unavailable",
    cancelled_at: 6.hours.ago,
    notes: "Vehicle in service - rescheduling required"
  },
  {
    dealership: dealership1,
    drive_type: :loan_booking,
    status: :cancelled,
    cancel_reason: "Customer illness",
    cancelled_at: 2.days.ago,
    notes: "Customer requested postponement due to health"
  },

  # More self loans
  {
    dealership: dealership1,
    drive_type: :self_loan,
    status: :completed,
    start_odometer_reading: 195000,
    end_odometer_reading: 195025,
    start_datetime: 1.week.ago.change(hour: 18),
    end_datetime: 1.week.ago.change(hour: 22),
    notes: "Evening demo preparation for next day presentation"
  },

  # Final batch of varied drives
  {
    dealership: dealership1,
    drive_type: :test_drive,
    status: :completed,
    start_odometer_reading: 205000,
    end_odometer_reading: 205055,
    sold_status: :sold,
    start_datetime: 5.days.ago.change(hour: 10),
    end_datetime: 5.days.ago.change(hour: 11, min: 30),
    notes: "Successful sale after comprehensive test",
    with_waypoints: true,
    with_damage_reports: true
  },
  {
    dealership: dealership1,
    drive_type: :loan,
    status: :completed,
    with_trade_plate: true,
    start_odometer_reading: 215000,
    end_odometer_reading: 215090,
    start_datetime: 8.days.ago.change(hour: 14),
    end_datetime: 6.days.ago.change(hour: 16),
    notes: "Demo vehicle loan - customer education program",
    with_damage_reports: true
  },
  {
    dealership: dealership1,
    drive_type: :enquiry,
    status: :completed,
    notes: "Detailed consultation - provided comprehensive vehicle comparison"
  },
  {
    dealership: dealership1,
    drive_type: :test_drive_booking,
    status: :scheduled,
    expected_pickup_datetime: 2.weeks.from_now.change(hour: 14),
    expected_return_datetime: 2.weeks.from_now.change(hour: 16),
    notes: "Future booking - customer planning ahead"
  }
].each_with_index do |drive_data, i|
  dealership = drive_data[:dealership]
  with_waypoints = drive_data.delete(:with_waypoints)
  with_damage_reports = drive_data.delete(:with_damage_reports)
  with_initial_damage = drive_data.delete(:with_initial_damage)

  vehicle = dealership.vehicles.order("RAND()").first
  vehicle = dealership.vehicles.where(rego: nil).order("RAND()").first if drive_data[:with_trade_plate]
  customer = dealership.customers.order("RAND()").first
  sales_person = dealership.sales_people.order("RAND()").first
  trade_plate = dealership.trade_plates.active.order("RAND()").first if drive_data[:with_trade_plate]

  if vehicle && customer && sales_person
    drive = Drive.create!(
      dealership: dealership,
      vehicle: vehicle,
      customer: customer,
      trade_plate: trade_plate,
      sales_person: sales_person,
      **drive_data.except(:dealership, :with_trade_plate)
    )

    # Add initial damage report for in-progress and completed drives
    if (drive.in_progress? || drive.completed? || with_initial_damage) && Drive::VEHICLE_OUT_DRIVE_TYPES.include?(drive.drive_type)
      drive.initial_damage_report = DamageReport.create!(
        drive: drive,
        vehicle: vehicle,
        report_type: DamageReport::INITIAL,
        description: "Pre-drive inspection: #{['Minor scratches on bumper', 'Small dent on door', 'Tire wear noted', 'Clean condition', 'Minor paint chips'].sample}"
      )
      drive.initial_damage_report.media_files.attach([
        {
          io: File.open(Rails.root.join("spec/fixtures/files/damage1.jpg").to_s),
          filename: "damage1.jpg",
          content_type: "image/jpg"
        }
      ])
    end

    # Add final damage report for completed drives
    if (drive.completed? || with_damage_reports) && Drive::VEHICLE_OUT_DRIVE_TYPES.include?(drive.drive_type)
      drive.final_damage_report = DamageReport.create!(
        drive: drive,
        vehicle: vehicle,
        report_type: DamageReport::FINAL,
        description: "Post-drive inspection: #{['No new damage', 'Minor additional wear', 'Small scratch added', 'Excellent condition maintained', 'Normal usage wear'].sample}"
      )
      drive.final_damage_report.media_files.attach([
        {
          io: File.open(Rails.root.join("spec/fixtures/files/damage3.jpg").to_s),
          filename: "damage3.jpg",
          content_type: "image/jpg"
        }
      ])
    end

    # Add waypoints for drives with location tracking
    if with_waypoints || (drive.in_progress? && Drive::VEHICLE_OUT_DRIVE_TYPES.include?(drive.drive_type))
      waypoint_count = rand(3..8)
      waypoint_count.times do |j|
        GpsLocation.create!(
          trackable: drive,
          latitude: -37.8136 + (rand(-50..50) * 0.001), # Melbourne area coordinates
          longitude: 144.9631 + (rand(-50..50) * 0.001),
          accuracy: rand(5..15),
          created_at: drive.start_datetime + (j * 10).minutes,
          updated_at: drive.start_datetime + (j * 10).minutes
        )
      end
    end

    puts "Created additional drive #{i + 1} for #{customer.full_name} - #{drive.drive_type} (#{drive.status})"
  else
    puts "Skipping additional drive #{i + 1}: Missing data"
  end
end

puts "Adding 10 draft drives..."

# Add 10 more draft drives for dealership1
10.times do |i|
  drive_types = [:test_drive, :loan, :self_loan]
  vehicle = DealershipVehicle.where(dealership: dealership1).sample

  Drive.create!(
    dealership: dealership1,
    vehicle: vehicle,
    trade_plate: vehicle.rego.blank? ? TradePlate.where(dealership: dealership1).sample : nil,
    customer: Customer.where(dealership: dealership1).with_driving_license.sample,
    sales_person: dealership1.sales_people.sample,
    drive_type: drive_types.sample,
    status: :draft,
    start_datetime: nil,
    end_datetime: nil,
    expected_return_datetime: (1..5).to_a.sample.hours.from_now,
    start_odometer_reading: rand(5000..50000)
  )
end

puts "Drives seeded successfully!"
