# Create Users with different types and dealership associations

# First, ensure we have some dealerships to work with
dealerships = Dealership.all
if dealerships.empty?
  puts "No dealerships found. Please run dealership seeds first."
  return
end

# Create Super Admin users (no dealership association needed)
super_admins = [
  {
    email: "<EMAIL>",
    password: "SuperAdmin123!",
    password_confirmation: "SuperAdmin123!",
    first_name: "<PERSON>",
    last_name: "Ad<PERSON>",
    phone: "+61777777777",
    user_type: :super_admin,
    status: :active
  },
  {
    email: "<EMAIL>",
    password: "SystemAdmin123!",
    password_confirmation: "SystemAdmin123!",
    first_name: "System",
    last_name: "Administrator",
    phone: "+61717777777",
    user_type: :super_admin,
    status: :active
  }
]

super_admins.each do |admin_attrs|
  user = User.find_or_create_by!(email: admin_attrs[:email]) do |u|
    u.assign_attributes(admin_attrs)
  end
  puts "Created/Found Super Admin: #{user.email}"
end

# Create Staff users (internal company staff)
staff_users = [
  {
    email: "<EMAIL>",
    password: "Support123!",
    password_confirmation: "Support123!",
    first_name: "Support",
    last_name: "Team",
    phone: "+61727777777",
    user_type: :staff,
    status: :active
  },
  {
    email: "<EMAIL>",
    password: "Manager123!",
    password_confirmation: "Manager123!",
    first_name: "Operations",
    last_name: "Manager",
    phone: "+61737777777",
    user_type: :staff,
    status: :active
  }
]

staff_users.each do |staff_attrs|
  user = User.find_or_create_by!(email: staff_attrs[:email]) do |u|
    u.assign_attributes(staff_attrs)
  end
  puts "Created/Found Staff User: #{user.email}"
end

# Create Dealership Users with various roles
dealership_users_data = [
  # Dealership Admins
  {
    email: "<EMAIL>",
    password: "DealerAdmin123!",
    first_name: "John",
    last_name: "Manager",
    phone: "+61747777777",
    user_type: :dealership_user,
    status: :active,
    dealership_roles: [ { dealership_index: 0, role: :dealership_admin } ]
  },
  {
    email: "<EMAIL>",
    password: "DealerAdmin123!",
    first_name: "Sarah",
    last_name: "Director",
    phone: "+61757777777",
    user_type: :dealership_user,
    status: :active,
    dealership_roles: [ { dealership_index: 1, role: :dealership_admin } ]
  },

  # Sales People
  {
    email: "<EMAIL>",
    password: "SalesPerson123!",
    first_name: "Vipul",
    last_name: "Bhardwaj",
    phone: "+918239635900",
    user_type: :dealership_user,
    status: :active,
    dealership_roles: [
      { dealership_index: 0, role: :dealership_admin },
      { dealership_index: 1, role: :sales_person }
    ]
  },
  {
    email: "<EMAIL>",
    password: "SalesPerson123!",
    first_name: "lavish",
    last_name: "Swarnkar",
    phone: "+918949199863",
    user_type: :dealership_user,
    status: :active,
    dealership_roles: [
      { dealership_index: 0, role: :dealership_admin },
      { dealership_index: 1, role: :sales_person }
    ]
  },
  {
    email: "<EMAIL>",
    password: "SalesPerson123!",
    first_name: "Ravi",
    last_name: "Talajiya",
    phone: "+919662128608",
    user_type: :dealership_user,
    status: :active,
    dealership_roles: [ { dealership_index: 1, role: :sales_person } ]
  },
  # Multi-dealership users
  {
    email: "<EMAIL>",
    password: "Regional123!",
    first_name: "Pankaj",
    last_name: "Batra",
    phone: "+919873241274",
    user_type: :dealership_user,
    status: :active,
    dealership_roles: [
      { dealership_index: 0, role: :dealership_admin },
      { dealership_index: 1, role: :sales_person }
    ]
  },
  {
    email: "<EMAIL>",
    password: "DealerUser123!",
    first_name: "Anmol",
    last_name: "Dhingra",
    phone: "+911234567890",
    user_type: :dealership_user,
    status: :active,
    dealership_roles: [
      { dealership_index: 0, role: :dealership_admin },
      { dealership_index: 1, role: :sales_person }
    ]
  },
  {
    email: "<EMAIL>",
    password: "FloatingSales123!",
    first_name: "Mark",
    last_name: "Hewitt-Park",
    phone: "+61438295154",
    user_type: :dealership_user,
    status: :active,
    dealership_roles: [
      { dealership_index: 0, role: :sales_person },
      { dealership_index: 1, role: :dealership_admin }
    ]
  },
  {
    email: "<EMAIL>",
    password: "Password#1",
    first_name: "Automated",
    last_name: "User",
    phone: "+918949199863",
    user_type: :dealership_user,
    status: :active,
    preferred_2fa: :totp,
    totp_secret: "F2Y4QCFHIUZBVVSGWYIKN4XBXWL37OI5",
    dealership_roles: [
      { dealership_index: 0, role: :sales_person },
      { dealership_index: 1, role: :dealership_admin }
    ]
  },

  # Test users with different statuses
  {
    email: "<EMAIL>",
    password: "InactiveUser123!",
    first_name: "Inactive",
    last_name: "User",
    phone: "+919923361333",
    user_type: :dealership_user,
    status: :inactive,
    dealership_roles: [ { dealership_index: 0, role: :sales_person } ]
  },
  {
    email: "<EMAIL>",
    password: "DisabledUser123!",
    first_name: "Disabled",
    last_name: "User",
    phone: "+919923352333",
    user_type: :dealership_user,
    status: :disabled,
    dealership_roles: [ { dealership_index: 1, role: :sales_person } ]
  }
]

dealership_users_data.each do |user_data|
  dealership_roles = user_data.delete(:dealership_roles)

  user = User.find_or_create_by!(email: user_data[:email]) do |u|
    u.assign_attributes(user_data)
  end

  user.photo.attach(
    io: File.open(Rails.root.join("spec/fixtures/files/profile.jpg").to_s),
    filename: "profile.jpg",
    content_type: "image/jpeg"
  ) if user_data[:email].include?("gmail.com") && !user.photo.attached?

  # Create UserDealership associations
  dealership_roles.each do |role_data|
    dealership = dealerships[role_data[:dealership_index]]
    next unless dealership

    user_dealership = UserDealership.find_or_create_by!(
      user: user,
      dealership: dealership
    ) do |ud|
      ud.role = role_data[:role]
    end

  puts "Created/Found User: #{user.email} - #{role_data[:role]} at #{dealership.name}"
  end
end

# Print summary
puts "\n=== User Seeds Summary ==="
puts "Super Admins: #{User.super_admin.count}"
puts "Staff Users: #{User.staff.count}"
puts "Dealership Users: #{User.dealership_user.count}"
puts "Total Users: #{User.count}"
puts "Total UserDealership associations: #{UserDealership.count}"
puts "Dealership Admins: #{UserDealership.dealership_admin.count}"
puts "Sales People: #{UserDealership.sales_person.count}"
