

# Create Brands
puts "Creating Brands..."
brands = [
  { name: "Toyota" },
  { name: "Honda" },
  { name: "Ford" },
  { name: "Hyundai" },
  { name: "<PERSON><PERSON>" },
  { name: "Mazda" },
  { name: "Nissan" },
  { name: "Subaru" },
  { name: "Volkswagen" },
  { name: "BMW" },
  { name: "Mercedes-Benz" },
  { name: "Audi" },
  { name: "Chevrolet" },
  { name: "Jeep" },
  { name: "Chrysler" },
  { name: "Dodge" },
  { name: "Fiat" },
  { name: "GMC" }
]

brands.each do |brand_data|
  brand = Brand.find_or_create_by!(name: brand_data[:name])
  puts "Created/Found Brand: #{brand.name}"
end
