require "twilio-ruby"

Twilio.configure do |config|
  config.account_sid = Rails.application.credentials[Rails.env.to_sym].dig(:twilio, :account_sid)
  config.auth_token = Rails.application.credentials[Rails.env.to_sym].dig(:twilio, :auth_token)
end

# Use from_number as there are issues with MessageServiceId for Australian numbers
TWILIO_SENDER = Rails.application.credentials[Rails.env.to_sym].dig(:twilio, :from_number)
