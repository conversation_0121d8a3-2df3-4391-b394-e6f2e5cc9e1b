class EmailFormatValidator < ActiveModel::EachValidator
  # This regex is more strict than URI::MailTo::EMAIL_REGEXP
  # It requires:
  # - Local part can contain letters, numbers, and these special chars: ._%+-@
  # - Domain must have at least one dot (TLD)
  # - TLD must be 2-63 chars and contain only letters
  # - No consecutive dots
  # - No leading/trailing dots
  # - No leading/trailing hyphens in domain parts
  EMAIL_REGEX = %r{
    \A
    # Local part
    [a-zA-Z0-9] # Must start with alphanumeric
    (?:[a-zA-Z0-9._%+-]*[a-zA-Z0-9])? # Optional middle part that can't end with dot
    (?:\.(?!\.)[a-zA-Z0-9._%+-]*[a-zA-Z0-9])* # Optional parts separated by single dots
    @
    # Domain part
    (?:
      [a-zA-Z0-9] # Must start with alphanumeric
      [a-zA-Z0-9-]* # Can contain hyphens
      [a-zA-Z0-9] # Must end with alphanumeric
      \. # Must have a dot
    )+
    # TLD
    [a-zA-Z]{2,63} # 2-63 letters
    \z
  }x

  def validate_each(record, attribute, value)
    return if value.blank?

    # Check length before format
    if value.length > 255
      record.errors.add(attribute, "#{value} must be under 255 characters")
      return
    end

    # Check for consecutive dots first (more reliable than regex)
    if value.include?("..")
      record.errors.add(attribute, "#{value} is not a valid email")
      return
    end

    unless value.match?(EMAIL_REGEX)
      record.errors.add(attribute, "#{value} is not a valid email")
    end
  end
end
