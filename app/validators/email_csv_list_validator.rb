class EmailCsvListValidator < ActiveModel::EachValidator
  def validate_each(record, attribute, value)
    return if value.blank?

    emails = value.split(",").map(&:strip).reject(&:empty?)

    if emails.empty?
      record.errors.add(attribute, "must contain at least one valid email address")
      return
    end

    emails.each do |email|
      EmailFormatValidator.new(attributes: [ attribute ]).validate_each(record, attribute, email)
    end
  end
end
