class Api::V1::DriverLicenseController < Api::V1::BaseController
  before_action :find_or_initialize, only: [ :create_or_update ]
  before_action :set_model, except: [ :create_or_update ]

  def show
    render_success_response("Driving license retrieved successfully", {
      driving_license: Api::V1::DriverLicenseSerializer.render_as_json(@driver_license)
    })
  end

  def create_or_update
    @driver_license.assign_attributes(driving_license_params)

    if @driver_license.save!
      action = @driver_license.previously_new_record? ? "created" : "updated"
      render_success_response("Driving license #{action} successfully", {
      driving_license: Api::V1::DriverLicenseSerializer.render_as_json(@driver_license)
    })
    end
  end

  def destroy
    @driver_license.destroy
    render_success_response("Driving license deleted successfully")
  end

  def create_image
    image_type = permitted_image_params[:image_type]&.downcase
    @driver_license.attach_image(image_type, permitted_image_params[:image])

    if @driver_license.valid?
      render_success_response("#{image_type.capitalize} image uploaded successfully", {
        driving_license: Api::V1::DriverLicenseSerializer.render_as_json(@driver_license)
      })
    else
      raise Errors::InvalidInput, @driver_license.errors.full_messages.join(", ")
    end
  end

  def destroy_image
     image_type = permitted_image_delete_params[:image_type]&.downcase
     @driver_license.remove_image(image_type)

    render_success_response("#{image_type.capitalize} image deleted successfully", {
      driving_license: Api::V1::DriverLicenseSerializer.render_as_json(@driver_license)
    })
  end

  private

  def find_or_initialize
    if permitted_params[:customer_uuid].present?
      @driver_license = customer.driver_license || customer.build_driver_license
    else
      @driver_license = current_user.driver_license || current_user.build_driver_license
    end
  end

  def set_model
    if permitted_params[:customer_uuid].present?
      @driver_license = customer.driver_license
    else
      @driver_license = current_user.driver_license
    end
    unless @driver_license
      raise Errors::RecordNotFound, "Driving license not found"
    end
  end

  def driving_license_params
    params.permit(:licence_number, :expiry_date, :issue_date, :category,
                  :issuing_country, :issuing_state, :full_name, :date_of_birth, :front_image, :back_image)
  end

  def customer
    @customer ||= dealership.customers.find_by!(uuid: permitted_params[:customer_uuid])
  end

  def permitted_params
    params.permit(:customer_uuid, :dealership_uuid)
  end

  def permitted_image_params
    params.permit(:image_type, :image)
  end

  def permitted_image_delete_params
    params.permit(:image_type)
  end
end
