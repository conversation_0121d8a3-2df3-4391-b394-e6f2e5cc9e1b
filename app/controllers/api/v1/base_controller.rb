# frozen_string_literal: true

class Api::V1::BaseController < ApplicationController
  include JwtAuthenticatable
  include ResponseHandler
  include DateTimeFormatHelper
  include ActiveStorage::SetCurrent
  include Pagy::Backend

  # Pagination constants
  DEFAULT_PER_PAGE = 20
  MAX_PER_PAGE = 100

  private

  def set_pagination_headers(pagy)
    response.headers["X-Current-Page"] = pagy.page.to_s
    response.headers["X-Per-Page"] = pagy.vars[:limit].to_s
    response.headers["X-Total-Count"] = pagy.count.to_s
    response.headers["X-Total-Pages"] = pagy.pages.to_s
    response.headers["X-Next-Page"] = pagy.next.to_s if pagy.next
    response.headers["X-Prev-Page"] = pagy.prev.to_s if pagy.prev
  end


  def pagination_validated_per_page
    per_page = params[:per_page].to_i

    # Return default if per_page is 0 or negative
    return DEFAULT_PER_PAGE if per_page <= 0

    # Enforce max_items limit
    [ per_page, MAX_PER_PAGE ].min
  end

  private

  def dealership
    @dealership ||= current_user.dealerships.active.find_by!(uuid: (params[:dealership_uuid] || params[:uuid]))
  rescue ActiveRecord::RecordNotFound
    raise Errors::RecordNotFound, "Dealership not found or you don't have access to it"
  end

  def sales_person
    @sales_person ||= find_sales_person(params[:sales_person_uuid])
  end

  def vehicle
    @vehicle ||= find_vehicle(params[:vehicle_uuid])
  end

  def customer
    @customer ||= find_customer(params[:customer_uuid])
  end

  def find_vehicle(vehicle_uuid)
    dealership.vehicles.find_by!(uuid: vehicle_uuid)
  rescue ActiveRecord::RecordNotFound
    raise Errors::RecordNotFound, "Vehicle not found"
  end

  def find_sales_person(sales_person_uuid)
    return current_user if sales_person_uuid.blank?

    dealership.sales_people.find_by!(uuid: sales_person_uuid)
  rescue ActiveRecord::RecordNotFound
    raise Errors::RecordNotFound, "Sales person not found"
  end

  def find_customer(customer_uuid)
    dealership.customers.find_by!(uuid: customer_uuid)
  rescue ActiveRecord::RecordNotFound
    raise Errors::RecordNotFound, "Customer not found or does not belong to this dealership"
  end

  def find_or_create_customer(customer_uuid, customer_info)
    validate_customer_params(customer_uuid, customer_info)
    if customer_uuid.present?
      find_customer(customer_uuid)
    else
      customer_attributes = customer_info.to_h
      if customer_attributes[:driver_license].present?
        customer_attributes[:driver_license_attributes] = customer_attributes.delete(:driver_license)
      end
      dealership.customers.create!(customer_attributes)
    end
  end

  def validate_customer_params(customer_uuid, customer_info)
    # Check if either customer_uuid or customer_info is provided
    unless customer_uuid.present? || customer_info.present?
      raise Errors::InvalidInput, "Either customer_uuid or customer_info must be provided"
    end

    # Check if both are provided (not allowed)
    if customer_uuid.present? && customer_info.present?
      raise Errors::InvalidInput, "Cannot provide both customer_uuid and customer_info. Use one or the other"
    end
  end

  def find_brand(uuid)
    Brand.find_by!(uuid: uuid)
  rescue ActiveRecord::RecordNotFound
    raise Errors::RecordNotFound, "Brand not found"
  end
end
