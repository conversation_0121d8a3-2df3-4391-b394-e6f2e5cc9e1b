class Api::V1::Dealerships::DrivesController < Api::V1::BaseController
  include DateTimeFormatHelper
  before_action :set_drive, only: [ :show, :start, :update_times, :update_odometer, :reassign, :assign_trade_plate,
                                    :create_damage_report, :update_customer, :complete, :update_sold_status,
                                    :attach_customer_signature, :update_location ]
  before_action :set_trade_plate, only: [ :assign_trade_plate ]

  def index
    drives = filtered_drives

    @pagy, @drives = pagy(
      drives,
      limit: pagination_validated_per_page,
      page: params[:page] || 1
    )
    set_pagination_headers(@pagy)

    render :index
  end

  def show
    render :show
  end

  def dashboard
    date_today = Date.current.to_s
    dashboard_data = {
      scheduled_today: dealership.drives.bookings.where(status: :scheduled)
              .pickup_between_dates(date_today, date_today).count,
      open_drives: dealership.drives.vehicle_out_type_drives.where(status: :in_progress).count,
      upcoming_activities: dealership.drives.bookings.where(status: :scheduled)
              .pickup_between_dates(1.day.from_now.to_s, 3.days.from_now.to_s).count,
      overdue_activities: dealership.drives.overdue.count
    }

    render_success_response("Dashboard data retrieved successfully", dashboard_data)
  end

  def create
    raise Errors::InvalidInput, "Invalid Drive Type." unless Drive.drive_types.key?(create_params[:drive_type])

    @drive = dealership.drives.new(status: :draft,
      sales_person:,
      vehicle:,
      drive_type: create_params[:drive_type]
    )
    @drive.save!
    @status_message = "Drive Created successfully"
    render :show, status: :created
  end

  def start
    @drive.start_drive!(ActiveModel::Type::Boolean.new.cast(start_params[:sales_person_accompanying]), current_user)
    @status_message = "Drive started successfully"
    render :show
  end

  def complete
    @drive.mark_completed(drive_complete_params[:notes])
    @status_message = "Drive completed successfully"
    render :show
  end

  def update_times
    expected_pickup_datetime = drive_times_params[:expected_pickup_datetime]
    expected_return_datetime = drive_times_params[:expected_return_datetime]

    if expected_return_datetime.blank?
      raise Errors::InvalidInput, "Expected return datetime is required"
    end

    if Drive::VEHICLE_OUT_DRIVE_TYPES.include?(@drive.drive_type) && expected_pickup_datetime
      raise Errors::InvalidInput, "Pickup time cannot be updated for this drive type"
    end

    if Drive::BOOKING_TYPES.include?(@drive.drive_type) && expected_pickup_datetime.blank?
      raise Errors::InvalidInput, "Pickup time is required for this drive type"
    end

    update_attrs = {}
    update_attrs[:expected_return_datetime] = parse_date_time(expected_return_datetime) if expected_return_datetime.present?
    update_attrs[:expected_pickup_datetime] = parse_date_time(expected_pickup_datetime) if expected_pickup_datetime.present?
    update_attrs[:status] = :scheduled if Drive::BOOKING_TYPES.include?(@drive.drive_type)

    @drive.update!(update_attrs)
    @status_message = "Pickup/Return Time updated successfully"
    render :show
  end

  def update_odometer
    validate_odometer_update

    update_attrs = {}
    update_attrs[:start_odometer_reading] = drive_odometer_params[:start_odometer_reading] if drive_odometer_params[:start_odometer_reading].present?
    update_attrs[:end_odometer_reading] = drive_odometer_params[:end_odometer_reading] if drive_odometer_params[:end_odometer_reading].present?
    update_attrs[:start_fuel_gauge_level] = drive_odometer_params[:start_fuel_gauge_level] if drive_odometer_params[:start_fuel_gauge_level].present?
    update_attrs[:end_fuel_gauge_level] = drive_odometer_params[:end_fuel_gauge_level] if drive_odometer_params[:end_fuel_gauge_level].present?

    @drive.update!(update_attrs)
    @status_message = "Odometer/Fuel Gauge reading updated successfully"
    render :show
  end

  def reassign
    validate_reassign_params

    update_params = {}
    if drive_reassign_params[:sales_person_uuid].present?
      sales_person = find_and_validate_user(drive_reassign_params[:sales_person_uuid])
      update_params[:sales_person] = sales_person
    end

    if drive_reassign_params[:sales_person_accompanying_uuid].present?
      sales_person_accompanying = find_and_validate_user(drive_reassign_params[:sales_person_accompanying_uuid])
      update_params[:sales_person_accompanying] = sales_person_accompanying
    end

    @drive.update!(update_params)
    @status_message = "Drive reassigned successfully"
    render :show
  end

  def update_customer
    customer = find_or_create_customer(drive_customer_params[:customer_uuid], drive_customer_params[:customer_info])

    @drive.update!(customer: customer)
    @status_message = "Customer updated successfully"
    render :show
  end

  def attach_customer_signature
    @drive.attach_customer_signature(customer_signature_params[:signature])

    if @drive.valid?
      @status_message = "Customer signature added successfully"
      render :show
    else
      raise Errors::InvalidInput, @drive.errors.full_messages.join(", ")
    end
  end

  def update_sold_status
    @drive.update_sold_status!(drive_sold_status_params[:sold_status])
    @status_message = "Sold status updated successfully"
    render :show
  end

  def create_damage_report
    validate_damage_report_params

    # Check if damage report already exists
    existing_report = find_existing_damage_report(damage_report_params[:damage_type])

    @damage_report = DamageReport.new(
      drive: @drive,
      vehicle: @drive.vehicle,
      report_type: damage_report_params[:damage_type],
      description: damage_report_params[:damage_notes]
    )

    # Attach media files if provided
    if damage_report_params[:media_files].present?
      @damage_report.media_files.attach(damage_report_params[:media_files])
    end

    @damage_report.save!
    existing_report&.destroy!

    @status_message = "#{damage_report_params[:damage_type].capitalize} damage report created successfully"
    render :show, status: :created
  end

  def assign_trade_plate
    @drive.update!(trade_plate: @trade_plate)
    @status_message = "Trade plate assigned successfully"
    render :show
  end

  def update_location
    # Ensure drive is a `test_drive`, is in a in-progress state, and has a sales person accompanying
    unless @drive.test_drive? && @drive.in_progress? && @drive.sales_person_accompanying
      raise Errors::InvalidInput, "GPS data can only be pushed for in-progress test drives with accompanying sales person"
    end

    permitted_params = params.permit(:latitude, :longitude, :accuracy)
    @drive.waypoints.create!(permitted_params)
    render_success_response("Location updated successfully", {})
  end

  private

  def set_drive
    @drive = dealership.drives
                      .includes(:vehicle, { customer: :driver_license }, :sales_person,
                               :sales_person_accompanying, :trade_plate, :driver_license)
                      .find_by!(uuid: params[:uuid])
  rescue ActiveRecord::RecordNotFound
    raise Errors::RecordNotFound, "Drive not found"
  end

  def create_params
    params.permit(:vehicle_uuid, :drive_type, :sales_person_uuid)
  end

  def drive_times_params
    params.expect(drive: [ :expected_return_datetime, :expected_pickup_datetime ])
  end

  def start_params
    params.permit(:sales_person_accompanying)
  end

  def drive_complete_params
    params.permit(:notes)
  end

  def drive_odometer_params
    params.expect(drive: [ :start_odometer_reading, :end_odometer_reading, :start_fuel_gauge_level, :end_fuel_gauge_level ])
  end

  def drive_reassign_params
    params.permit(:sales_person_uuid, :sales_person_accompanying_uuid)
  end

  def drive_customer_params
    params.permit(:customer_uuid, customer_info: [
      :first_name, :last_name, :age, :email, :phone_number, :gender,
      :address_line1, :address_line2, :suburb, :city, :state, :country,
      :postcode, :company_name,
      driver_license: [ :licence_number, :expiry_date, :issuing_state,
                      :issuing_country, :full_name, :date_of_birth, :category, :issue_date, :front_image, :back_image ]
    ])
  end

  def damage_report_params
    params.permit(:damage_type, :damage_notes, media_files: [])
  end

  def trade_plate_params
    params.permit(:trade_plate_uuid)
  end

  def customer_signature_params
    params.permit(:signature)
  end

  def drive_sold_status_params
    params.permit(:sold_status)
  end

  def validate_odometer_update
    # Check if exactly one reading is provided
    if drive_odometer_params[:start_odometer_reading].present? && drive_odometer_params[:end_odometer_reading].present?
      raise Errors::InvalidInput, "Only one of start_odometer_reading or end_odometer_reading can be provided at a time"
    end

    if drive_odometer_params[:start_fuel_gauge_level].present? && drive_odometer_params[:end_fuel_gauge_level].present?
      raise Errors::InvalidInput, "Only one of start_fuel_gauge_level or end_fuel_gauge_level can be provided at a time"
    end

    # Check if at least one reading is provided
    unless drive_odometer_params[:start_odometer_reading].present? || drive_odometer_params[:end_odometer_reading].present?
      raise Errors::InvalidInput, "At least one of start_odometer_reading or end_odometer_reading must be provided"
    end
  end

  def validate_reassign_params
    # Check if at least one user is provided
    unless drive_reassign_params[:sales_person_uuid].present? || drive_reassign_params[:sales_person_accompanying_uuid].present?
      raise Errors::InvalidInput, "At least one of sales_person_uuid or sales_person_accompanying_uuid must be provided"
    end
  end

  def find_and_validate_user(user_uuid)
    dealership.users.find_by!(uuid: user_uuid)
  rescue ActiveRecord::RecordNotFound
    raise Errors::RecordNotFound, "User not found or does not belong to this dealership"
  end

  def set_trade_plate
    raise Errors::InvalidInput, "Trade plate UUID is required" if trade_plate_params[:trade_plate_uuid].blank?

    @trade_plate = dealership.trade_plates.active.find_by!(uuid: trade_plate_params[:trade_plate_uuid])
  rescue ActiveRecord::RecordNotFound
    raise Errors::RecordNotFound, "Trade plate not found or does not belong to this dealership"
  end

  def filtered_drives
    drives = dealership.drives
                      .includes(:vehicle, :customer, :sales_person,
                               :sales_person_accompanying, :trade_plate, :driver_license)

    drives = drives.start_datetime_between(index_params[:start_date_from],
                                          index_params[:start_date_to]) if date_filters_present?
    drives = drives.by_vehicle(index_params[:vehicle_uuid]) if index_params[:vehicle_uuid].present?
    drives = drives.by_customer(index_params[:customer_uuid]) if index_params[:customer_uuid].present?
    drives = drives.by_salesperson(index_params[:sales_person_uuid]) if index_params[:sales_person_uuid].present?
    drives = drives.by_trade_plate(index_params[:trade_plate_uuid]) if index_params[:trade_plate_uuid].present?
    drives = drives.filter_by_status(index_params[:status]) if index_params[:status].present?
    drives = drives.filter_by_drive_type(index_params[:drive_type]) if index_params[:drive_type].present?
    drives = drives.filter_by_sold_status(index_params[:sold_status]) if index_params[:sold_status].present?
    drives = drives.overdue if index_params[:overdue].present?
    drives = drives.eligible_for_return if index_params[:eligible_for_return].present?
    drives = drives.updated_between_dates(index_params[:updated_from],
                                         index_params[:updated_to]) if update_date_filters_present?
    drives = drives.search_by_term(index_params[:query]) if index_params[:query].present?
    drives.order(updated_at: :desc)
  end

  def index_params
    params.permit(:vehicle_uuid, :customer_uuid, :drive_type, :sales_person_uuid, :eligible_for_return,
                  :status, :sold_status, :trade_plate_uuid, :start_date_from, :start_date_to, :overdue,
                  :updated_from, :updated_to, :query)
  end

  def date_filters_present?
    index_params[:start_date_from].present? || index_params[:start_date_to].present?
  end

  def validate_damage_report_params
    # Drive type can only be VEHICLE_OUT_DRIVE_TYPES
    raise Errors::InvalidInput, "Invalid Drive Type." unless Drive::VEHICLE_OUT_DRIVE_TYPES.include?(@drive.drive_type)

    if damage_report_params[:damage_type].blank?
      raise Errors::InvalidInput, "Damage type is required"
    end

    unless DamageReport.report_types.keys.include?(damage_report_params[:damage_type])
      valid_types = DamageReport.report_types.keys.join(", ")
      raise Errors::InvalidInput, "Invalid damage type. Must be one of: #{valid_types}"
    end

    if damage_report_params[:damage_notes].blank?
      raise Errors::InvalidInput, "Damage notes are required"
    end
  end

  def find_existing_damage_report(damage_type)
    DamageReport.where(drive: @drive, report_type: damage_type).first
  end

  def update_date_filters_present?
    index_params[:updated_from].present? || index_params[:updated_to].present?
  end
end
