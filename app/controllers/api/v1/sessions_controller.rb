module Api
  module V1
    class SessionsController < Devise::SessionsController
      include ResponseHandler

      private

      def respond_with(current_user, _opts = {})
        raise Errors::AuthenticationError, "Email or password is incorrect." unless current_user&.id
        raise(Errors::AuthenticationError, "User is not active") unless current_user.active?
        raise(Errors::AuthenticationError, "User does not belong to any dealership") if current_user.dealerships.active.empty?

        OtpService.new(current_user).generate_and_send_otp(current_user.preferred_2fa)
        temporary_token, expiry_time = Auth::TokenService.new(nil, current_user).generate_temporary_token
        render_success_response("Require 2FA challenge", {
          temporary_token: temporary_token,
          expires_at: expiry_time,
          token_type: "Bearer",
          challenge_type: current_user.preferred_2fa,
          masked_destination: current_user.masked_destination,
          two_factor_methods: current_user.available_2fa_methods
        })
      end
    end
  end
end
