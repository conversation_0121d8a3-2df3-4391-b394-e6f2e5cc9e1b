class Api::V1::EnquiriesController < Api::V1::BaseController
  def create
    @enquiry = dealership.drives.create!(
      vehicle: find_vehicle(create_params[:vehicle_uuid]),
      drive_type: :enquiry,
      customer: find_or_create_customer(create_params[:customer_uuid], create_params[:customer_info]),
      sales_person: find_sales_person(create_params[:sales_person_uuid]),
      notes: create_params[:notes]
    )

    render :show, status: :created
  end

  private

  def create_params
    params.expect(enquiry: [ :vehicle_uuid, :sales_person_uuid, :customer_uuid, :notes,
      customer_info: [ :first_name, :last_name, :age, :email, :phone_number, :gender, :address_line1,
        :address_line2, :suburb, :city, :state, :country, :postcode, :company_name,
        driver_license: [ :licence_number, :expiry_date, :issuing_state, :issuing_country, :full_name,
          :date_of_birth, :category, :issue_date, :front_image, :back_image ] ] ]
    )
  end
end
