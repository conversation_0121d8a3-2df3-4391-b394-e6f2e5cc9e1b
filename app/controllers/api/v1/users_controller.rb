class Api::V1::UsersController < Api::V1::BaseController
  skip_before_action :authenticate_jwt_user!, only: [ :forgot_password, :verify_reset_code, :reset_password,
    :resend_otp, :verify_2fa, :refresh, :request_2fa_method ]

  def change_password
    error_message = validate_change_password_request
    raise Errors::InvalidInput, error_message if error_message

    unless current_user&.valid_password?(change_password_params[:current_password])
      raise Errors::InvalidInput, "Incorrect current password"
    end

    current_user.update!(password: change_password_params[:new_password])
    current_user.mark_password_as_changed!
    render_success_response("Password changed successfully")
  end

  def forgot_password
    user = User.find_by(email: forgot_password_params[:email])

    unless user
      raise Errors::UserNotFound, "User not found"
    end

    PasswordService.new(user).generate_and_send_reset_code

    render_temporary_token_response(user, "Reset password code sent successfully")
  end

  def verify_reset_code
    user, error_response = validate_token_and_reset_code(
      verify_reset_code_params[:temporary_token],
      verify_reset_code_params[:reset_code]
    )
    return error_response if error_response

    PasswordService.new(user).clear_code!
    render_temporary_token_response(user, "Reset Code verified successfully.")
  end

  def reset_password
    new_password = reset_password_params[:new_password]
    user, error_response = validate_token_and_password(
      reset_password_params[:temporary_token],
      new_password
    )
    return error_response if error_response

    if user.valid_password?(new_password)
      raise Errors::InvalidInput, "New password cannot be the same as the current password"
    end

    user.update!(password: new_password)
    user.mark_password_as_changed!
    render_success_response("Password reset successful.")
  end

  def profile
    render_success_response(
      "Profile retrieved successfully",
      user: Api::V1::UserSerializer.render_as_json(current_user),
      current_device: Api::V1::DeviceSerializer.render_as_json(current_device, current_device: current_device)
    )
  end

  def update_profile
    current_user.update!(profile_params)
    render_success_response(
      "Profile updated successfully",
      user: Api::V1::UserSerializer.render_as_json(current_user.reload)
    )
  end

  def update_photo
    if photo_params[:photo].blank?
      raise Errors::InvalidInput, "Photo is required"
    end

    current_user.add_photo(photo_params[:photo])
    if current_user.valid?
      render_success_response(
        "Profile photo updated successfully",
        photo_url: current_user.profile_photo_url
      )
    else
      raise Errors::InvalidInput, current_user.errors.full_messages.join(", ")
    end
  end

  def destroy_photo
    unless current_user.photo.attached?
      raise Errors::InvalidInput, "No photo attached"
    end

    current_user.photo.purge
    render_success_response("Profile photo deleted successfully")
  end

  def resend_otp
    user, error_response = validate_token_and_extract_user(resend_otp_params[:temporary_token])
    return error_response if error_response

    channel = resend_otp_params[:method]
    raise Errors::InvalidInput, "Method is required" if channel.blank?

    OtpService.new(user).generate_and_send_otp(channel)

    render_success_response(
      "OTP sent successfully.",
      two_factor_method: channel
    )
  end

  def verify_2fa
    user, error_response = validate_token_and_extract_user(user_login_params[:temporary_token])
    return error_response if error_response

    input_otp = user_login_params[:otp]
    channel = user_login_params[:method] || user.preferred_2fa
    device_id = request.headers["Device-ID"] || user_login_params[:device_id]
    device_os = request.headers["Device-OS"] || user_login_params[:device_os]
    app_version = request.headers["App-Version"] || user_login_params[:app_version]
    app_build_number = request.headers["App-Build-Number"] || user_login_params[:app_build_number]

    if device_id.blank? || app_version.blank? || app_build_number.blank? || device_os.blank?
      raise Errors::InvalidInput, "Device ID, app version, device OS and app build number are required for API authentication."
    end

    raise Errors::AuthenticationError, "OTP is required" if input_otp.blank?

    otp_service = OtpService.new(user)
    unless otp_service.validate_otp(input_otp, channel)
      raise Errors::AuthenticationError, "Invalid OTP"
    end

    begin
      Thread.current[:request_ip] = request.remote_ip
      login_result = Auth::DeviceLoginService.call(user, device_id, app_version, app_build_number, device_os)
      otp_service.clear_code!

      render_success_response("Logged in successfully.", {
        user: Api::V1::UserSerializer.render_as_json(user),
        access_token: login_result[:access_token],
        refresh_token: login_result[:refresh_token],
        expires_at: login_result[:expires_at],
        token_type: "Bearer"
      })
    ensure
      # Clear thread variable
      Thread.current[:request_ip] = nil
    end
  end

  def refresh
    device_id = request.headers["Device-ID"] || refresh_token_params[:device_id]
    refresh_token = refresh_token_params[:refresh_token]

    if device_id.blank? || refresh_token.blank?
      raise Errors::InvalidInput, "Device ID and refresh token are required"
    end

    user = User.joins(:device_registrations)
            .where(device_registrations: { device_id: device_id, active: true })
            .first

    raise Errors::AuthenticationError, "Invalid device or user not found" unless user

    device = user.device_registrations.valid.find_by(device_id: device_id)
    raise Errors::AuthenticationError, "Invalid device" unless device

    tokens = Auth::TokenService.new(device, user).refresh_access_token!(refresh_token)

    render_success_response("Token refreshed successfully", {
      access_token: tokens[:access_token],
      refresh_token: tokens[:refresh_token],
      expires_at: tokens[:expires_at],
      token_type: "Bearer"
    })
  end

  def request_2fa_method
    temporary_token = request_2fa_params[:temporary_token]
    method = request_2fa_params[:method]

    validate_2fa_request!(temporary_token, method)

    user = Auth::TokenService.new.decode_token(temporary_token)

    # Generate and send OTP via the requested alternative method
    OtpService.new(user).generate_and_send_otp(method)

    # Generate a new temporary token
    new_temporary_token, expiry_time = Auth::TokenService.new(nil, user).generate_temporary_token

    render_success_response("2FA challenge sent successfully.", {
      temporary_token: new_temporary_token,
      expires_at: expiry_time,
      token_type: "Bearer",
      challenge_type: method,
      masked_destination: user.masked_destination(method),
      two_factor_methods: user.available_2fa_methods
    })
  end

  def setup_2fa
    setup_result = OtpService.new(current_user).setup_totp

    render_success_response("2FA setup initiated successfully", {
      secret_key: setup_result[:secret_key],
      qr_code: setup_result[:qr_code],
      qr_code_url: setup_result[:qr_code_url]
    })
  end

  def verify_2fa_setup
    otp_code = verify_2fa_params[:otp]
    if otp_code.blank?
      raise Errors::InvalidInput, "OTP code is required"
    end

    OtpService.new(current_user).verify_totp_setup(otp_code)

    render_success_response("2FA successfully enabled")
  end

  private

  def profile_params
    params.permit(
      :first_name, :last_name, :preferred_2fa, :job_title,
      :preferred_language, :time_zone, :onboarding_completed
    )
  end

  def photo_params
    params.permit(:photo)
  end

  def change_password_params
    params.permit(:current_password, :new_password)
  end

  def forgot_password_params
    params.permit(:email)
  end

  def verify_reset_code_params
    params.permit(:temporary_token, :reset_code)
  end

  def reset_password_params
    params.permit(:temporary_token, :new_password)
  end

  def validate_change_password_request
    return "Current Password is required" if change_password_params[:current_password].blank?
    return "New Password is required" if change_password_params[:new_password].blank?
    return "New password cannot be the same as the current password" if change_password_params[:new_password] == change_password_params[:current_password]
    nil
  end

  def request_2fa_params
    params.permit(:temporary_token, :method)
  end

  def verify_2fa_params
    params.permit(:otp)
  end

  # Helper method for generating and rendering temporary token responses
  def render_temporary_token_response(user, message)
    temporary_token, expiry_time = Auth::TokenService.new(nil, user).generate_temporary_token
    render_success_response(message, {
      temporary_token: temporary_token,
      expires_at: expiry_time
    })
  end

  # Helper method for validating token and extracting user
  def validate_token_and_extract_user(token)
    raise Errors::InvalidInput, "Token is required" if token.blank?

    user = Auth::TokenService.new.decode_token(token)
    raise Errors::AuthenticationError, "Invalid token" unless user&.id

    [ user, nil ]
  end

  # Helper method for validating token and reset code
  def validate_token_and_reset_code(token, reset_code)
    raise Errors::InvalidInput, "Token or Reset Code is missing" if token.blank? || reset_code.blank?

    user, error_response = validate_token_and_extract_user(token)
    return [ nil, error_response ] if error_response

    password_service = PasswordService.new(user)
    unless password_service.validate_security_code(reset_code)
      raise Errors::AuthenticationError, "Invalid Reset Code"
    end

    [ user, nil ]
  end

  # Helper method for validating token and password
  def validate_token_and_password(token, password)
    raise Errors::InvalidInput, "Temporary Token or New Password is missing" if token.blank? || password.blank?

    validate_token_and_extract_user(token)
  end

  def resend_otp_params
    params.permit(:temporary_token, :method)
  end

  def refresh_token_params
    params.permit(:refresh_token, :device_id)
  end

  def user_login_params
    params.permit(:email, :password, :device_id, :app_version, :app_build_number, :device_os, :temporary_token, :otp, :method)
  end

  def validate_2fa_request!(temporary_token, method)
    raise Errors::InvalidInput, "Temporary Token is required" if temporary_token.blank?
    raise Errors::InvalidInput, "2FA Method is required" if method.blank?
    raise Errors::InvalidInput, "Invalid 2FA Method. Must be 'sms' or 'email'" unless [ "sms", "email" ].include?(method)
  end
end
