class Api::V1::DealershipsController < Api::V1::BaseController
  def index
    render_success_response("Dealerships retrieved successfully", {
      dealerships: Api::V1::DealershipSerializer.render_as_json(current_user.dealerships.active)
    })
  end

  def agreements
    terms_setting = dealership.dealership_terms_setting
    render_success_response("Agreement texts retrieved successfully", {
      agreements: {
        test_drive_terms_text: terms_setting&.test_drive_terms_text,
        car_loan_terms_text: terms_setting&.car_loan_terms_text,
        insurance_waiver_text: terms_setting&.insurance_waiver_text
      }
    })
  end
end
