class Api::V1::DevicesController < Api::V1::BaseController
  def active_devices
    render_success_response(
      "Active devices retrieved successfully",
      devices: Api::V1::DeviceSerializer.render_as_json(current_user.active_devices, current_device: current_device)
    )
  end

  def logout_device
    device_id = request.headers["Device-ID"] || params[:device_id]
    device = current_user.active_devices.find_by(device_id: device_id)

    if device
      current_user.logout_device(device_id)
      render_success_response("Device logged out successfully")
    else
      raise Errors::RecordNotFound, "Device not found"
    end
  end

  def logout_all_devices
    current_user.logout_all_devices
    render_success_response("All devices logged out successfully")
  end

  def update_device
    current_device.assign_attributes(
      device_os: device_params[:device_os],
      device_os_version: device_params[:device_os_version],
      app_version: device_params[:app_version],
      app_build_number: device_params[:app_build_number],
      fcm_token: device_params[:fcm_token],
      device_name: device_params[:device_name]
    )

    current_device.save!
    render_success_response("Device details updated successfully", {
      device: Api::V1::DeviceSerializer.render_as_json(current_device, current_device: current_device)
    })
  end

  private

  def device_params
    params.permit(
      :device_os, :device_os_version, :app_version, :app_build_number, :fcm_token, :device_name
    )
  end
end
