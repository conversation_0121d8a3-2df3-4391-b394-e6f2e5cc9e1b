module JwtAuthenticatable
  extend ActiveSupport::Concern

  included do
    before_action :authenticate_jwt_user!
  end

  private

  def authenticate_jwt_user!
    token = extract_token_from_header
    device_id = request.headers["Device-ID"]

    raise Errors::InvalidToken, "Missing authorization token" unless token
    raise Errors::MissingDeviceId, "Missing device ID" unless device_id

    @current_user, @current_device = Auth::TokenService.new.decode_and_verify_token(device_id, token)
  end

  def extract_token_from_header
    auth_header = request.headers["Authorization"]
    return nil unless auth_header&.start_with?("Bearer ")
    auth_header.split(" ").last
  end

  def current_user
    @current_user
  end

  def current_device
    @current_device
  end
end
