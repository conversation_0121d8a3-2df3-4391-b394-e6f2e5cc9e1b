module ApplicationHelper
  def use_svg(css_class, svg_path)
    content_tag :svg, class: css_class do
      content_tag :use, "xlink:href" => "#svg-#{svg_path}" do
      end
    end
  end
  def strip_tags_and_truncate(text:, length: 30, separator: " ", escape: false)
    # strip_tags_and_truncate(text: 'This &amp; That <sup> d&reg; d®</sup> onwards!')
    # => "This &amp; That  d® d®..."
    truncate(strip_tags(text), length: length, separator: separator, escape: escape)
  end
end
