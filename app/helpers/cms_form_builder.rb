class CmsFormBuilder < ActionView::Helpers::<PERSON><PERSON><PERSON><PERSON>
  def image_uploader(attribute, options = {})
    mandatory = options.key?(:mandatory) ? options.delete(:mandatory) : false
    label_append = "<sup class='imf-mandatory'>*</sup>" if mandatory
    class_append = "sub-single" if mandatory
    outer_class_append = []
    outer_class_append << "field_with_errors" if @object.errors[attribute].any?
    outer_class_append << "is_mandatory" if mandatory
    # image can be loaded from a local path (after session check), a data URI (from the form POST), a remote URL (DB attribute). It can also not exist yet if we're creating a new item.
    image_url =
      if @template.load_image_from_session?(@object.send(attribute))
        @template.session[:temporary_image_url]
      elsif @object.send("#{attribute}_base64").present?
        @object.send("#{attribute}_base64")
      elsif @object.asset_present?(attribute)
        @object.image_location(@object.send("#{attribute}_url"))
      else
        ""
      end
    image_value = @object.send(attribute).presence || 0
    (@template.content_tag :div, class: "form-asset-module-header" do
      "Image must be at least #{@object.send("#{attribute}_maximum_width")}px wide and #{@object.send("#{attribute}_maximum_height")}px high (Max file size is 5MB)"
    end) +
      (@template.content_tag :div, id: "#{attribute}_status", class: "form-asset-module-footer status-#{image_url.present? ? 'existing' : 'new'} #{outer_class_append.join(' ')}" do
        (@template.content_tag :div, class: "fam-wrapper-first asset-existing" do
          (@template.content_tag :div, class: "fam-wrapper-sub #{class_append}" do
            (@template.content_tag :a, id: "#{attribute}_link", href: image_url, target: "_blank", class: "fam-image-link" do
              @template.image_tag image_url, id: "#{attribute}_image", class: "fam-img-thumb", alt: @object.send("#{attribute}_alternate_text")
            end) +
            (@template.content_tag :a, href: "#action_replace_#{attribute}", class: "fam-action-wrapper", id: "action_replace_#{attribute}" do
              (@template.content_tag :span, class: "au-icon-wrapper" do
                @template.use_svg("icon-asset-module", "arrow-circle-anti-dot")
              end) +
              (@template.content_tag :span, class: "fam-action-text" do
                "Replace Image"
              end)
            end)
          end) +
          (
            unless mandatory
              @template.content_tag :div, class: "fam-wrapper-sub" do
                (@template.content_tag :a, href: "#action_clear_#{attribute}", class: "fam-action-wrapper", id: "action_clear_#{attribute}" do
                  (@template.content_tag :span, class: "au-icon-wrapper" do
                    @template.use_svg("icon-asset-module", "clear")
                  end) +
                  (@template.content_tag :span, class: "fam-action-text" do
                    "Clear Image"
                  end)
                end)
              end
            end
          )
        end) +
        (@template.content_tag :div, class: "fam-wrapper-first asset-new" do
          @template.content_tag :div, class: "fam-wrapper-sub sub-single" do
            @template.use_svg("icon-no-asset fam-icon-no-asset", "no-asset") +
            (@template.content_tag :a, href: "#action_add_#{attribute}", class: "fam-action-wrapper", id: "action_add_#{attribute}" do
              (@template.content_tag :span, class: "au-icon-wrapper" do
                @template.use_svg("icon-asset-module", "add")
              end) +
              (@template.content_tag :span, class: "fam-action-text" do
                "Add Image"
              end)
            end)
          end
        end) +
        (@template.content_tag :div, class: "fam-wrapper-last" do
          @template.content_tag :div, class: "fam-input-wrapper" do
            (@template.label @object_name, "#{attribute}_alternate_text".to_sym, "Image Alt Text#{label_append}", class: "fam-label") +
            (@template.text_field @object_name, "#{attribute}_alternate_text".to_sym, objectify_options(options.merge(class: "fam-input")))
          end
        end)
      end) +
      @template.hidden_field(@object_name, attribute, objectify_options(options.merge(id: attribute))) +
      @template.hidden_field(@object_name, "#{attribute}_base64".to_sym, objectify_options(options.merge(id: "#{attribute}_base64"))) +
      @template.file_field(@object_name, "#{attribute}_file".to_sym, objectify_options(options.merge(class: "hidden", id: "#{attribute}-file"))) +
      (@template.javascript_tag do
        <<~JAVASCRIPT
          var #{attribute}_asset = new ImageUploader(#{@object.send("#{attribute}_maximum_width")}, #{@object.send("#{attribute}_maximum_height")});
          var #{attribute}_upload = new AssetUploader('#{attribute}', #{image_value}, #{attribute}_asset);
          #{attribute}_upload.base64 = '#{@object.send("#{attribute}_base64")}';
          #{attribute}_upload.attach_events();
        JAVASCRIPT
      end)
  end

  def file_uploader(attribute, options = {})
    mandatory = options.key?(:mandatory) ? options.delete(:mandatory) : false
    class_append = "sub-single" if mandatory
    outer_class_append = []
    outer_class_append << "field_with_errors" if @object.errors[attribute].any?
    outer_class_append << "is_mandatory" if mandatory
    # file can be loaded from a local path (after session check), a data URI (from the form POST), a remote URL (DB attribute). It can also not exist yet if we're creating a new item.
    file_url =
      if @template.load_file_from_session?(@object.send(attribute))
        @template.session[:temporary_file_url]
      elsif @object.send("#{attribute}_base64").present?
        @object.send("#{attribute}_base64")
      elsif @object.asset_present?(attribute)
        @object.file_location(@object.send("#{attribute}_url"))
      else
        ""
      end
    file_value = @object.send(attribute).presence || 0
    (@template.content_tag :div, class: "form-asset-module-header" do
      "Maximum file size is 5MB"
    end) +
      (@template.content_tag :div, id: "#{attribute}_status", class: "form-asset-module-footer status-#{file_url.present? ? 'existing' : 'new'} #{outer_class_append.join(' ')}" do
        (@template.content_tag :div, class: "fam-wrapper asset-existing" do
          (@template.content_tag :div, class: "fam-wrapper-sub #{class_append}" do
            (@template.content_tag :a, id: "#{attribute}_link", href: file_url, target: "_blank", class: "fam-image-link" do
              @template.use_svg("au-icon", "file")
            end) +
            (@template.content_tag :a, href: "#action_replace_#{attribute}", class: "fam-action-wrapper", id: "action_replace_#{attribute}" do
              (@template.content_tag :span, class: "au-icon-wrapper" do
                @template.use_svg("icon-asset-module", "arrow-circle-anti-dot")
              end) +
              (@template.content_tag :span, class: "fam-action-text" do
                "Replace File"
              end)
            end)
          end
          ) + (
          unless mandatory
            @template.content_tag :div, class: "fam-wrapper-sub" do
              (@template.content_tag :a, href: "#action_clear_#{attribute}", class: "fam-action-wrapper", id: "action_clear_#{attribute}" do
                (@template.content_tag :span, class: "au-icon-wrapper" do
                  @template.use_svg("icon-asset-module", "clear")
                end) +
                (@template.content_tag :span, class: "fam-action-text" do
                  "Clear File"
                end)
              end)
            end
          end)
        end) +
        (@template.content_tag :div, class: "fam-wrapper asset-new" do
          (@template.content_tag :div, class: "fam-wrapper-sub sub-single" do
            @template.use_svg("icon-no-asset fam-icon-no-asset", "no-asset") +
            (@template.content_tag :a, href: "#action_add_#{attribute}", class: "fam-action-wrapper", id: "action_add_#{attribute}" do
              (@template.content_tag :span, class: "au-icon-wrapper" do
                @template.use_svg("icon-asset-module", "add")
              end) +
              (@template.content_tag :span, class: "fam-action-text" do
                "Add File"
              end)
            end)
          end)
        end)
      end) +
      @template.hidden_field(@object_name, attribute, objectify_options(options.merge(id: attribute))) +
      @template.hidden_field(@object_name, "#{attribute}_base64".to_sym, objectify_options(options.merge(id: "#{attribute}_base64"))) +
      @template.file_field(@object_name, "#{attribute}_file".to_sym, objectify_options(options.merge(class: "hidden", id: "#{attribute}-file"))) +
      (@template.javascript_tag do
        <<~JAVASCRIPT
          var #{attribute}_asset = new FileUploader();
          var #{attribute}_upload = new AssetUploader('#{attribute}', #{file_value}, #{attribute}_asset);
          #{attribute}_upload.base64 = '#{@object.send("#{attribute}_base64")}';
          #{attribute}_upload.attach_events();
        JAVASCRIPT
      end)
  end

  def text_field_counter(attribute, options = {})
    max_characters = false
    identifier = "js-counter_#{@object.object_id}_#{rand(1000)}"
    if options.key?(:max)
      max_characters = options[:max]
      options.delete(:max)
      options[:class] = if options[:class]
                          options[:class] + " " + identifier
      else
                          identifier
      end
    end
    initial_length = @object.send(attribute).nil? ? 0 : @object.send(attribute).length

    (@template.text_field @object_name, attribute, objectify_options(options)) +
      (
        if max_characters
          (@template.content_tag :span, nil, class: "character-counter", data: { field: ".#{identifier}", max_allowed: max_characters, initial_length: initial_length })
        end
      )
  end

  def label(attribute, text = nil, options = {}, &block)
    # usage examples:
    # f.label :first_name => <label for="blah_first_name">First name</label>
    # f.label :first_name, 'First Name' => <label for="blah_first_name">First Name</label> (capitalised Name)
    # f.label :first_name, 'First Name', mandatory: true => <label for="blah_first_name">First Name<sup class='imf-mandatory'>*</sup></label> (capitalised Name)
    # f.label :first_name, nil, mandatory: true => <label for="blah_first_name">First name<sup class='imf-mandatory'>*</sup></label>
    # f.label :first_name, nil, class: 'field', data-item: true => <label class="field" data-item="true" for="blah_first_name">First name</label>
    mandatory = options.key?(:mandatory) ? options.delete(:mandatory) : false
    if mandatory
      text = attribute.to_s.humanize if text.nil?
      # text = (text + "<sup class='imf-mandatory'>*</sup>")
    end
    super(attribute, text, options, &block)
  end
end
