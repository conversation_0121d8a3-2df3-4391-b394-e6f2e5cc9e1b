# frozen_string_literal: true

module DateTimeFormatHelper
  def parse_date_time(date_time)
    return nil if date_time.blank?

    DateTime.parse(date_time)
  rescue ArgumentError
    raise Errors::InvalidInput, "Invalid datetime format: #{date_time}"
  end

  # the method always returns the datetime in UTC even if the input datetime has a different offset.
  def format_iso8601_with_offset(datetime)
    datetime.utc.strftime("%Y-%m-%dT%H:%M:%S.%3NZ") if datetime.present?
  end
end
