class UserInvitationMailer < ApplicationMailer
  def invitation_email(user, temporary_password)
    @user = user
    @temporary_password = temporary_password

    @login_url = Rails.application.routes.url_helpers.new_user_session_url(host: Rails.application.credentials[Rails.env.to_sym].dig(:domain) || "localhost:3000")

    # Get app store URLs from environment variables
    @ios_app_url = Rails.application.credentials[Rails.env.to_sym].dig(:app_store, :ios_url)
    @android_app_url = Rails.application.credentials[Rails.env.to_sym].dig(:app_store, :android_url)

    mail(
      to: @user.email,
      subject: "Welcome to Dealer Drive - Your Account Invitation"
    )
  end
end
