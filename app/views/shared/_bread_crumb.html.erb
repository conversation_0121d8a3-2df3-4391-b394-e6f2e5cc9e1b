<div class="bread-crumb-wrapper">
  <ul class="bc-list">
    <% for crumb in crumbs %>
      <li class="bc-item">
        <% if crumb[:link].present? %>
          <%= link_to(crumb[:link], class: 'bc-link') do %>
            <span class="bc-text"><%= strip_tags_and_truncate(text: crumb[:step], length: 35) %></span>
          <% end %>
        <% else %>
          <span class="bc-text"><%= strip_tags_and_truncate(text: crumb[:step], length: 35) %></span>
        <% end %>
        <span class="triangle-right"></span>
      </li>
    <% end %>
  </ul>
</div>
