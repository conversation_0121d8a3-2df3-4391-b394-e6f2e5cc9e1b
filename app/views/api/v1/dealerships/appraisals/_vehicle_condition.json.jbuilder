# Vehicle condition data
if vehicle_condition.present?
  json.extract! vehicle_condition,
    :uuid, :is_clean, :is_wet, :is_road_tested,
    :has_signs_of_repair, :repair_details, :additional_notes

  # Condition photos
  json.photo_urls vehicle_condition.photos.map { |photo| photo.url } if vehicle_condition.photos.attached?

  # Body part conditions
  if vehicle_condition.body_part_conditions.present?
    json.body_part_conditions do
      json.array! vehicle_condition.body_part_conditions do |body_part|
        json.extract! body_part, :id, :part_name, :condition, :description
        # Add damage images if they exist
        json.photo_urls body_part.photos.map { |photo| photo.url } if body_part.photos.attached?
      end
    end
  end

  # Component ratings
  if vehicle_condition.component_ratings.present?
    json.component_ratings do
      json.array! vehicle_condition.component_ratings do |rating|
        json.extract! rating, :id, :name, :rating
        # Add photos if they exist
        json.photo_urls rating.photos.map { |photo| photo.url } if rating.photos.attached?
      end
    end
  end

  # Reconditioning costs
  if vehicle_condition.reconditioning_costs.present?
    json.reconditioning_costs do
      json.array! vehicle_condition.reconditioning_costs do |cost|
        json.extract! cost, :id, :amount, :currency, :cost_type
      end
    end
  end
end
