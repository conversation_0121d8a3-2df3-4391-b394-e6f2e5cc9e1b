json.status do
  json.code 201
  json.message @status_message || "Damage report created successfully"
end

json.data do
  json.damage_report do
    json.uuid @damage_report.uuid
    json.report_type @damage_report.report_type
    json.description @damage_report.description
    json.created_at format_iso8601_with_offset(@damage_report.created_at)
    json.updated_at format_iso8601_with_offset(@damage_report.updated_at)

    json.media_files do
      json.array! @damage_report.media_files do |file|
        json.filename file.filename.to_s
        json.content_type file.content_type
        json.byte_size file.byte_size
        json.url file&.url
      end
    end

    json.media_files_count @damage_report.media_files.count
  end

  json.drive do
    json.partial! "api/v1/drive", drive: @drive
  end
end
