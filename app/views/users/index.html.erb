<% content_for(:body_class, 'users') %>
<% content_for(:title, "Users") %>
<% content_for(:section_heading, pluralize(@users.size, 'User')) %>
<main class="main">
  <div class="box-trbl">
    <div class="col-wrapper-2">
      <div class="action-edit-wrapper lr-col-lg">
        <%= link_to new_user_path, class: 'ae-link' do %>
          <span class="ae-icon-wrapper"><%= use_svg('ae-icon icon-circle-plus', 'circle-plus') %></span>
          <span class="ae-text">Invite User</span>
        <% end %>
      </div>
    </div>
    <div class="sticky-wrapper">
      <ul class="lr-header-row">
        <li class="lr-item lr-item-heading">
          <div class="lr-col lr-col-lg"><span class="lr-text-heading">User</span></div>
          <div class="lr-col lr-col-300 col-hide"><span class="lr-text-heading">Last Logged In</span></div>
          <div class="lr-col lr-col-300 col-hide"><span class="lr-text-heading">Last Active</span></div>
          <div class="lr-col lr-col-md col-hide"><span class="lr-text-heading">Activated?</span></div>
          <div class="lr-col lr-col-sm-md"><span class="lr-text-heading">Access</span></div>
          <div class="lr-col lr-col-sm-md"><span class="lr-text-heading">Edit</span></div>
          <div class="lr-col lr-col-sm-md"><span class="lr-text-heading">Delete</span></div>
        </li>
      </ul>
    </div>
    <div id="users" data-filter="filter-items" data-filter_attributes='name, id, level, email'>
      <ul class='list-recurring js-filtering-list-container' >
      <% @users.each do |user| %>
        <li class="lr-item" data-name="<%= user.name.strip %>" data-id="<%= user.id %>" data-level="<%= user.user_type %>" data-email="<%= user.email %>">
          <div class="lr-col lr-col-lg">
            <div class="lr-text"><%= user.name %></div>
          </div>
          <div class="lr-col lr-col-300 col-hide">
            <div class="lr-text lr-text-center">
              <% if user.last_login_at %>
                <%= "#{time_ago_in_words(user.last_login_at)} ago." %>
              <% else %>
                &mdash;
              <% end %>
            </div>
          </div>
          <div class="lr-col lr-col-300 col-hide">
            <div class="lr-text lr-text-center">
              <% if user.last_active_on %>
                <%= "#{time_ago_in_words(user.last_active_on)} ago." %>
              <% else %>
                &mdash;
              <% end %>
            </div>
          </div>
          <div class="lr-col lr-col-md col-hide lr-col-center">
            <% if user.inactive? %>
              <%= use_svg('lr-icon lr-static-icon icon-email', 'email') %>
            <% elsif user.active? %>
              <%= use_svg('lr-icon lr-action-icon icon-tick', 'tick') %>
            <% else %>
              &mdash;
            <% end %>
          </div>
          <div class="lr-col lr-col-sm-md">
            <div class="lr-text lr-text-center">
              <%= user.user_type.humanize %>
            </div>
          </div>
          <div class="lr-col lr-col-sm-md">
              <%= link_to(user_path(user), class: 'lr-link') do %>
                <%= use_svg('lg-icon icon-pencil-edit-line', 'pencil-edit-line') %>
              <% end %>
            </div>
          <div class="lr-col lr-col-sm-md">
            <%= link_to(user_path(user),
              data: { turbo_method: :delete, 
              turbo_confirm: "Are you sure you want to permanently delete #{user.name}?"  },
              class: 'lr-link') do %>
              <%= use_svg('lg-icon icon-trash icon-alert', 'trash') %>
            <% end %>
          </div>
        </li>
      <% end %>
      </ul>
    </div>
  </div>
</main>
