<header class="header box-nav1">
  <%= link_to(root_url, class: 'header-logo-link') do %><%= use_svg('logo-imotor', 'logo-imotor') %><% end %>
  <% if current_user %>
  <span class="header-message">Welcome <%= current_user.name %></span>
  <ul class="n1-list">
    <li class="n1-item">
      <%= link_to(root_url, class: 'n1-link n1-link-1') do %><%= use_svg('n1-icon icon-home-sm', 'home') %><span class="n1-text">Home</span><% end %>
    </li>
    <li class="n1-item">
      <%= link_to(users_path, class: 'n1-link n1-link-2') do %><%= use_svg('n1-icon icon-user-add-sm', 'user-add') %><span class="n1-text">Users</span><% end %>
    </li>
    <li class="n1-item">
      <%= link_to(destroy_user_session_path, data: { turbo_method: :delete }, class: 'n1-link n1-link-5') do %><%= use_svg('n1-icon icon-locked-sm', 'lock') %><span class="n1-text">Logout</span><% end %>
    </li>
  </ul>
  <div id="js_content_wiki_side_bar" class="wiki-side-bar">
    <a href="javascript:void(0)" class="closebtn" id="js_content_wiki_side_bar_close">×</a>
    <div id="js_content_wiki_contents" class="wiki-content-wrapper"></div>
  </div>
  <% end %>
</header>
<% if content_for?(:section_heading) %>
<div class="section-heading-wrapper box-rl">
  <h1 class="section-heading"><%= content_for(:section_heading) %></h1>
  <div class="flash-message-wrapper">
    <%= render "layouts/flashes" %>
  </div>
</div>
<% end %>
