<% content_for(:body_class, 'login') %>
<% content_for(:title, "Sign In") %>
<main class="main admin-bg">
  <div class="flash-message-wrapper-fw">
    <%= render "layouts/flashes" %>
  </div>
  <div class="floating-form-wrapper">
    <div class="ff-heading-wrapper">
      <h1 class="ff-heading">Please Log In</h1>
    </div>
    <div class="ff-body-wrapper">
      <%= form_for(resource, as: resource_name, url: session_path(resource_name)) do |f| %>
        <fieldset>
          <ol>
            <li class="imf-input-wrapper">
              <%= f.label :email, class: 'imf-label' %>
              <%= f.email_field :email, autofocus: true, required: true, autocomplete: "email" %>
            </li>
            <li class="imf-input-wrapper">
              <%= f.label :password, class: 'imf-label' %>
              <%= f.password_field :password, required: true, autocomplete: "current-password" %>
            </li>
          </ol>
          <% if devise_mapping.rememberable? %>
            <%= f.label :remember_me, 'Remember me for 30 days' do %>
              <%= f.check_box :remember_me %>
              Remember me for 30 days
            <% end %>
          <% end %>
          <div class="imf-btn-wrapper">
            <%= f.submit 'Log In', class: 'imf-btn' %>
            <%- if devise_mapping.recoverable? && controller_name != 'passwords' && controller_name != 'registrations' %>
              <%= link_to "Forgot your password?", new_password_path(resource_name) %><br />
            <% end %>
          </div>
        </fieldset>
      <% end %>
    </div>
  </div>
</main>
