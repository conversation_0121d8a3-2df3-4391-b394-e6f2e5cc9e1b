<% content_for(:body_class, 'login') %>
<% content_for(:title, "Reset Your Password") %>
<main class="main admin-bg">
  <div class="flash-message-wrapper-fw">
    <%= render "layouts/flashes" %>
  </div>
  <div class="floating-form-wrapper">
    <div class="ff-heading-wrapper">
      <h1 class="ff-heading">Reset Your Password</h1>
    </div>
    <div class="ff-body-wrapper">
      <%= form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :post }) do |f| %>
        <%= render "shared/errors", object: @user %>
        <fieldset>
          <ol>
            <li class="imf-input-wrapper">
              <%= f.label :email, class: 'imf-label' %>
              <%= f.email_field :email, autofocus: true, required: true, autocomplete: "email" %>
            </li>
          </ol>
          <div class="imf-btn-wrapper">
            <%= f.submit 'Send me instructions', class: 'imf-btn' %>
            <%= link_to "back to the log in form", new_session_path(resource_name) %>
          </div>
        </fieldset>
      <% end %>
    </div>
  </div>
</main>
