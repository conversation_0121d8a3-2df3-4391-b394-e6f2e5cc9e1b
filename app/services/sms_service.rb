class SmsService
  class << self
    def deliver(to:, message:)
      client = Twilio::REST::Client.new

      client.api.v2010.account.messages.create(
        from: TWILIO_SENDER,
        to: to,
        body: message
      )
    rescue Twilio::REST::RestError => e
      Rails.logger.error "Failed to send SMS: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise Errors::SmsDeliveryError, "Failed to send SMS: #{e.message}"
    end
  end
end
