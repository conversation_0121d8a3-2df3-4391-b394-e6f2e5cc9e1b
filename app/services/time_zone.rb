module TimeZone
  # ZONE_NAMES is the time zone belt (zone.tzinfo.name) a city (zone.name) belongs to.
  #   ActiveSupport::TimeZone.all.each { |z| puts "#{z.name}: #{z.tzinfo.name} #{z.formatted_offset}" } returns the 'city' name along with it's time zone name.
  #
  #   ActiveSupport::TimeZone["Wellington"].tzinfo.name ==> "Pacific/Auckland"
  #   ActiveSupport::TimeZone["Auckland"].tzinfo.name ==> "Pacific/Auckland"
  #   ActiveSupport::TimeZone["Chennai"].tzinfo.name ==> "Asia/Kolkata"
  #   ActiveSupport::TimeZone["New Delhi"].tzinfo.name ==> "Asia/Kolkata"
  #

  ZONE_NAMES = [
    "Etc/GMT+12", "Pacific/Pago_Pago", "Pacific/Midway", "Pacific/Honolulu", "America/", "Etc/UTC", "Asia/Kolkata", "Australia", "Pacific/Auckland"
  ].freeze

  def self.names
    names = ActiveSupport::TimeZone.all.map do |zone|
      format_zone(zone) if zone_in_zone_names?(zone)
    end
    names.flatten.compact.uniq
  end

  def self.format_zone(zone)
    { name: "(GMT#{offset(zone.name)}) #{zone.name}", value: zone.name }
  end

  def self.zone_in_zone_names?(zone)
    ZONE_NAMES.any? { |word| zone.tzinfo.name.downcase.starts_with?(word.downcase) }
  end

  def self.offset(time_zone)
    return unless time_zone

    ActiveSupport::TimeZone[time_zone]&.formatted_offset
  end
end
