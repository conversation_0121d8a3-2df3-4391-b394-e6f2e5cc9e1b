class PasswordService < BaseCodeSendService
  def generate_and_send_reset_code(action = :forgot_password)
    check_rate_limit!

    @reset_code = generate_code
    record_code!(@reset_code)
    send_reset_code_via_email(action)

    @reset_code
  end

  def validate_security_code(input_code)
    validate_code_with_expiry(user.password_reset_code, input_code, user.reset_code_generated_at)
  end

  def clear_code!
    user.update!(password_reset_code: nil, reset_code_generated_at: nil, reset_code_resend_count: 0)
  end

  def record_code!(code)
    user.update!(
      password_reset_code: code,
      reset_code_generated_at: Time.current,
      reset_code_resend_count: user.reset_code_resend_count + 1
    )
  end

  def send_reset_code_via_email(action)
    UserMailer.send_otp(user, @reset_code, action).deliver_later
  end

  protected

  def resend_count
    user.reset_code_resend_count
  end

  def code_generated_at
    user.reset_code_generated_at
  end
end
