class BaseCodeSendService
  attr_accessor :user

  CODE_EXPIRY = 5.minutes
  RATE_LIMIT_COUNT = 3
  RATE_LIMIT_TIME_WINDOW = 1.hour

  def initialize(user)
    @user = user
  end

  protected

  def generate_code
    rand(100_000..999_999).to_s
  end

  def can_send_code?
    time_window_expired? || resend_count < RATE_LIMIT_COUNT
  end

  def time_window_expired?
    if code_generated_at.nil? || code_generated_at.before?(RATE_LIMIT_TIME_WINDOW.ago)
      clear_code!
      return true
    end
    false
  end

  def validate_code_with_expiry(stored_code, input_code, generated_at)
    return false if stored_code.nil? || generated_at.nil?

    stored_code.to_s == input_code && Time.current <= (generated_at + self.class::CODE_EXPIRY)
  end

  def check_rate_limit!
    unless can_send_code?
      raise Errors::TooManyRequestError, "Request limit exceeded. Please try again after 1 hour."
    end
  end

  # Abstract methods to be implemented by subclasses
  def clear_code!
    raise NotImplementedError, "Subclass must implement clear_code!"
  end

  def record_code!(code)
    raise NotImplementedError, "Subclass must implement record_code!"
  end

  def resend_count
    raise NotImplementedError, "Subclass must implement resend_count"
  end

  def code_generated_at
    raise NotImplementedError, "Subclass must implement code_generated_at"
  end
end
