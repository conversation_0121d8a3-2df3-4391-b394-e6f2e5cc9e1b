class Auth::DeviceLoginService < ApplicationService
  def initialize(user, device_id, app_version, app_build_number, device_os)
    @user = user
    @device_id = device_id
    @app_version = app_version
    @app_build_number = app_build_number
    @device_os = device_os
  end

  def call
    ActiveRecord::Base.transaction do
      deactivate_existing_device_session
      create_new_device_session
      generate_authentication_tokens
    end
  end

  private

  attr_reader :user, :device_id, :app_version, :app_build_number, :device_os

  def deactivate_existing_device_session
    existing_device = user.device_registrations.find_by(device_id: device_id)
    existing_device&.invalidate!
  end

  def create_new_device_session
    # Create new user device record
    @device = user.device_registrations.find_or_initialize_by(device_id: device_id)
    # Prepare attributes for the update
    attributes = {
      app_version: @app_version,
      app_build_number: @app_build_number,
      device_os: @device_os,
      active: true,
      last_activity_at: Time.current,
      refresh_token: "temp", # Will be updated in generate_tokens
      refresh_token_expires_at: Time.current
    }

    # Update the device registration
    @device.update!(attributes)
  end

  def generate_authentication_tokens
    # Generate tokens
    tokens = Auth::TokenService.new(@device).generate_tokens(true, nil)

    {
      device_registration: @device,
      **tokens
    }
  end
end
