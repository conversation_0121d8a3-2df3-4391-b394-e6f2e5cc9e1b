class UserInvitationService
  def initialize(inviting_user)
    @inviting_user = inviting_user
    @temp_password = nil
    @dealership = nil
  end

  attr_reader :temp_password, :dealership

  def invite_user(email:, first_name:, last_name:, phone:, user_type:, dealership_uuid: nil, role: nil)
    user_type_sym = user_type.to_sym

    validate_invitation_permissions(user_type_sym, dealership_uuid, role)

    ActiveRecord::Base.transaction do
      # Create user with temporary password
      @temp_password = generate_temporary_password
      user = User.new(
        email: email,
        first_name: first_name,
        last_name: last_name,
        phone: phone,
        user_type: user_type,
        password: @temp_password,
        password_confirmation: @temp_password,
        password_change_required: true,
        status: :active
      )

      # Assign dealership if applicable
      if dealership.present?
        user.user_dealerships.build(
          dealership: dealership,
          role: role
        )
      end

      user.save!

      # Send invitation email with temporary password
      send_invitation_email(user)

      user
    end
  end

  private

  def validate_invitation_permissions(user_type_sym, dealership_uuid, role)
    validate_user_type(user_type_sym)
    fetch_and_validate_dealership(user_type_sym, dealership_uuid)
    validate_inviting_user_permissions(user_type_sym)
    validate_role_assignment(user_type_sym, role)
  end

  def fetch_and_validate_dealership(user_type_sym, dealership_uuid)
    if [ :super_admin, :staff ].include?(user_type_sym)
      if dealership_uuid.present?
        raise Errors::InvalidInput, "Dealership UUID should not be provided for super_admin or staff users"
      end
      return nil
    end

    # For dealership_user, dealership_uuid must be present and valid
    if user_type_sym == :dealership_user
      if dealership_uuid.blank?
        raise Errors::InvalidInput, "Dealership UUID is required for dealership user invitations"
      end
      @dealership = Dealership.find_by(uuid: dealership_uuid)
      raise ActiveRecord::RecordNotFound, "Dealership not found" unless dealership
    end
  end

  def validate_user_type(user_type_sym)
    unless User.user_types.key?(user_type_sym.to_s)
      raise Errors::InvalidInput, "Invalid user type. Valid types are: #{User.user_types.keys.join(', ')}"
    end
  end

  def validate_inviting_user_permissions(user_type_sym)
    return if @inviting_user.super_admin?

    if @inviting_user.staff?
      unless user_type_sym == :dealership_user
        raise Errors::ForbiddenError, "Staff members can only invite dealership users"
      end
    else
      unless user_type_sym == :dealership_user
        raise Errors::ForbiddenError, "Dealership users can only invite other dealership users"
      end
      unless dealership && @inviting_user.user_dealerships.exists?(dealership: dealership, role: :dealership_admin)
        raise Errors::ForbiddenError, "Users can only invite users to dealerships where they are an admin"
      end
    end
  end

  def validate_role_assignment(user_type_sym, role)
    return unless dealership

    if role.blank?
      raise Errors::InvalidInput, "Role is required when inviting a dealership user"
    end

    role_sym = role.to_sym

    # Check forbidden admin assignment first
    if role_sym == :dealership_admin && !can_assign_admin_role?
      raise Errors::ForbiddenError, "Only super admins and staff can assign dealership admin role"
    end

    valid_roles = available_roles_for_current_user

    unless valid_roles.include?(role_sym)
      raise Errors::InvalidInput, "Invalid role. Valid roles for your permission level are: #{valid_roles.join(', ')}"
    end
  end

  def available_roles_for_current_user
    roles = [ :sales_person, :staff ]
    roles << :dealership_admin if can_assign_admin_role?
    roles
  end

  def can_assign_admin_role?
    @inviting_user.super_admin? || @inviting_user.staff?
  end

  def generate_temporary_password
    # Ensure we have one of each required character type
    uppercase = ("A".."Z").to_a.sample(3).join
    lowercase = ("a".."z").to_a.sample(3).join
    digit = (0..9).to_a.sample(3).join
    special = [ "#", "!", "@", "$", "%", "^", "&", "*", "-" ].sample(3).join

    # Combine all characters and shuffle
    (uppercase + lowercase + digit + special).chars.shuffle.join
  end

  def send_invitation_email(user)
    # Pass the temporary password that was used to create the user
    UserInvitationMailer.invitation_email(user, temp_password).deliver_later
  end
end
