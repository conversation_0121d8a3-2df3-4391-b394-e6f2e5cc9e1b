module VehicleBase
  extend ActiveSupport::Concern
  include HasUuid

  included do
    # Validations
    validates :make, :model, :build_year, presence: true
    validates :rego, length: { maximum: 20 }
    validates :vin, length: { maximum: 17 } # VIN is 17 characters long in Australia
    validates :build_year, numericality: {
      greater_than: 1900,
      less_than_or_equal_to: -> { Date.current.year + 1 }
    }
    has_many_attached :photos
    validates :photos, content_type: [ "image/png", "image/jpeg" ],
                      size: { less_than: 5.megabytes },
                      limit: { max: 5 }

    scope :ordered_by_name, -> { order(:make, :model, :build_year) }

    # Associations
    belongs_to :dealership
    belongs_to :brand, optional: true
  end

  def display_name
    [ build_year, make, model ].compact.join(" ")
  end

  def to_param
    uuid
  end
end
