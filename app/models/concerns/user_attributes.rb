module UserAttributes
  extend ActiveSupport::Concern
  include UserDataHelper

  def name
    [ first_name, last_name ].compact.join(" ")
  end

  def full_name
    name
  end

  def masked_destination(method = preferred_2fa)
    case method
    when "sms"
      masked_phone(phone)
    when "email"
      masked_email(email)
    when "totp"
      # TODO: No need to mask authenticator name, instead show the name as is,
      "totp"
    end
  end
end
