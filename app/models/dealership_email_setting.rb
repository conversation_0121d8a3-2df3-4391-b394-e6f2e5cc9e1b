class DealershipEmailSetting < ApplicationRecord
  belongs_to :dealership

  enum :send_email_for_bookings, {
    none: 0,
    test_drive: 1,
    car_loan: 2,
    both: 3
  }, default: :none, prefix: true

  validates :email_from_address, email_format: true, allow_nil: true
  validates :level1_odometer_warning_email, email_format: true, allow_nil: true
  validates :level2_odometer_warning_email, email_format: true, allow_nil: true
  validates :level1_odometer_warning_km, numericality: { only_integer: true, greater_than: 0 }, allow_nil: true
  validates :level2_odometer_warning_km, numericality: { only_integer: true, greater_than: 0 }, allow_nil: true
end
