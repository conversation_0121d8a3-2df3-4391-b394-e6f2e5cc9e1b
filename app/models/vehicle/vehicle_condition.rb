module Vehicle
  class VehicleCondition < ApplicationRecord
    include HasUuid

    # Associations
    belongs_to :customer_vehicle
    has_many_attached :photos
    has_many :body_part_conditions, dependent: :destroy
    has_many :component_ratings, dependent: :destroy
    has_many :reconditioning_costs, dependent: :destroy

    # Validations
    validates(
      :photos,
      content_type: [ "image/png", "image/jpeg" ],
      size: { less_than: 5.megabytes },
      limit: { max: 5 }
    )
  end
end
