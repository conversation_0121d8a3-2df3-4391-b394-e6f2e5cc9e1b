module Vehicle
  class VehicleHistory < ApplicationRecord
    include HasUuid
    belongs_to :customer_vehicle

    has_many_attached :service_book_images
    validates :service_book_images, content_type: [ "image/png", "image/jpeg" ],
    size: { less_than: 5.megabytes },
    limit: { max: 5 }
    validates :number_of_owners, numericality: { only_integer: true, allow_nil: true }
    validates :last_service_odometer, numericality: { only_integer: true, allow_nil: true }
    enum :vehicle_history_status, {
      no_history: 0,
      partial_history: 1,
      full_mixed_history: 2,
      full_oem_history: 3
    }
  end
end
