module Vehicle
  class OptionsFitted < ApplicationRecord
    include HasUuid

    # Associations
    belongs_to :customer_vehicle

    # Enums
    enum :sunroof_type, { standard_metal: 0, standard_glass: 1, panoramic: 2 }, prefix: true
    enum :tonneau_type, { hard: 0, soft: 1 }, prefix: true
    enum :on_written_off_register, { yes: 0, no: 1, unknown: 2 }, prefix: true

    # Validations
    validates :number_of_keys, inclusion: { in: [ 1, 2, 3 ] }, allow_nil: true
    validates :extended_warranty_expiry, presence: true, if: :has_extended_warranty?

    # Scopes
    scope :with_extended_warranty, -> { where(has_extended_warranty: true) }
    scope :with_ppsr, -> { where(ppsr: true) }

    # Attachments
    has_many_attached :options_images
  end
end
