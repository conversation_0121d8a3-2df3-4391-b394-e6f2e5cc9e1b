module Vehicle
  class ComponentRating < ApplicationRecord
    enum :name, {
      front_wheels: 0,
      rear_wheels: 1,
      panel_work: 2,
      paint_work: 3,
      interior: 4,
      windscreen: 5,
      mechanical: 6
    }

    ALLOWED_NAMES = names.keys.freeze

    belongs_to :vehicle_condition, class_name: "Vehicle::VehicleCondition"
    has_many_attached :photos

    validates :name, presence: true, uniqueness: { scope: :vehicle_condition_id }
    validates :rating, presence: true, numericality: { only_integer: true, greater_than_or_equal_to: 1, less_than_or_equal_to: 5 }
  end
end
