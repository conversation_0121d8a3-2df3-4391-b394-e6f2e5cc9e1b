class Brand < ApplicationRecord
  include HasUuid

  has_one_attached :logo
  has_many :dealerships, dependent: :restrict_with_error
  has_many :customer_vehicles, dependent: :nullify
  has_many :dealership_vehicles, dependent: :nullify

  validates :name, presence: true, uniqueness: { case_sensitive: false }
  validates :logo, content_type: [ "image/png", "image/jpeg" ], size: { less_than: 5.megabytes }

  def logo_url
    return nil unless logo.attached?
    logo.url
  end
end
