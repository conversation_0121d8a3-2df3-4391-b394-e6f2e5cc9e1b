class DriverLicense < ApplicationRecord
  include HasUuid

  belongs_to :holder, polymorphic: true

  has_one_attached :front_image
  has_one_attached :back_image

  validates :licence_number, presence: true, length: { minimum: 1, maximum: 20 }
  validates :expiry_date, presence: true

  validates :front_image, content_type: [ "image/png", "image/jpeg" ], size: { less_than: 5.megabytes }
  validates :back_image, content_type: [ "image/png", "image/jpeg" ], size: { less_than: 5.megabytes }

  enum :verification_status, {
    pending: 0,
    verified: 1,
    rejected: 2
  }, default: :pending

  IMAGE_TYPES = %w[front back].freeze

  def valid_image_type?(type)
    IMAGE_TYPES.include?(type)
  end

  def attach_image(image_type, image_file)
    raise Errors::InvalidInput, "Invalid image type" unless valid_image_type?(image_type)
    raise Errors::InvalidInput, "Image file is required" if image_file.blank?

    send("#{image_type}_image").attach(image_file)
  end

  def remove_image(image_type)
    raise Errors::InvalidInput, "Invalid image type" unless valid_image_type?(image_type)
    raise Errors::InvalidInput, "No #{image_type} image attached" unless send("#{image_type}_image").attached?

    send("#{image_type}_image").purge
  end

  def front_image_url
    return nil unless front_image.attached?
    front_image.url
  end

  def back_image_url
    return nil unless back_image.attached?
    back_image.url
  end
end
