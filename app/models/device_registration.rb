class DeviceRegistration < ApplicationRecord
  include HasUuid

  belongs_to :user

  enum :device_os, {
    ios: 0,
    android: 1,
    ipad: 2,
    other: 3
  }, prefix: true

  validates :device_id, :app_version, :device_os, :app_build_number, :refresh_token_expires_at, presence: true
  validates :device_id, :device_name, :device_os_version, length: { maximum: 255 }
  validates :last_login_ip, length: { maximum: 45 }
  validates :app_version, :app_build_number, length: { maximum: 30 }
  validates :fcm_token, length: { maximum: 512 }
  validates :user_id, uniqueness: { scope: %i[device_id] }
  validates :refresh_token, presence: true, uniqueness: { case_sensitive: true }

  scope :active, -> { where(active: true) }
  scope :valid, -> { where(active: true).where("refresh_token_expires_at > ?", Time.current) }

  before_save :capture_last_login_ip

  def invalidate!
    update!(active: false, refresh_token_expires_at: Time.current, logged_out_at: Time.current)
  end

  private

  def capture_last_login_ip
    if Thread.current[:request_ip].present?
      self.last_login_ip = Thread.current[:request_ip]
      self.last_login_timestamp = Time.current
    end
  end
end
