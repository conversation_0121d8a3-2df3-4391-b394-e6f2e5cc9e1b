class CustomDeviseFailure < Devise::FailureApp
  def respond
    if request.format == :json
      json_error_response
    else
      super
    end
  end

  def json_error_response
    Rails.logger.warn("Email or password is incorrect.")
    self.status = :unauthorized
    self.content_type = "application/json"
    self.response_body = {
      status: {
        code: 401,
        message: "Email or password is incorrect."
      }
    }.to_json
  end
end
