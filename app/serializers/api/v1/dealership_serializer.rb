class Api::V1::DealershipSerializer < Api::V1::BaseSerializer
  fields :uuid, :name, :long_name, :setting_date_format, :setting_time_zone, :setting_distance_unit, :created_at, :updated_at,
             :status, :address_line1, :address_line2, :suburb, :state, :postcode, :country, :phone, :email, :website, :external_id,
             :abn

  association :brand, blueprint: Api::V1::BrandSerializer
  association :dealership_features_setting, blueprint: Api::V1::DealershipFeaturesSettingSerializer

  field :logo_url do |dealership|
    dealership.logo.attached? ? dealership.logo.url : nil
  end
end
