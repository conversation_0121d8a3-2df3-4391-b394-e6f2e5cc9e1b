.single-nav-wrapper {
  display: flex;
  background-color: var(--imColBlack80);

  .nav3-wrapper {
    flex: 0 0 100%;
  }
}

.search-wrapper {
  display: flex;
  align-items: center;
  width: 24rem;
  margin-bottom: 20px;

  @include mediaQueryMax($bp-md) {
    padding-right: 1rem;
  }
}

// Helpers
.justify {
  &-start {
    justify-content: flex-start;
  }

  &-end {
    justify-content: flex-end;
  }

  &-center {
    justify-content: center;
  }

  &-around {
    justify-content: space-around;
  }

  &-between {
    justify-content: space-between;
  }

  &-evenly {
    justify-content: space-evenly;
  }
}

.align-items {
  &-start {
    align-items: flex-start;
  }

  &-end {
    align-items: flex-end;
  }

  &-center {
    align-items: center;
  }

  &-stretch {
    align-items: stretch;
  }
}

.flex {
  &-grow-1 {
    flex-grow: 1;
  }
}

.margin-bottom-relax {
  margin-bottom: 40px;
}

.margin-bottom-compact {
  margin-bottom: var(--imGapLarge);
}

.margin-top-relax {
  margin-top: 40px;
}

.margin-top-compact {
  margin-top: 20px;
}
