// @import rules first

// https://s3-ap-southeast-2.amazonaws.com/assets.i-motor.com.au/webfonts/RobotoCondensed-Bold.woff2

@font-face {
  font-family: 'Lexend Light';
  font-style: normal;
  src: font-url('Lexend-Light.eot');
  src:
    font-url('Lexend-Light.eot#iefix') format('embedded-opentype'),
    font-url('Lexend-Light.woff2') format('woff2'),
    font-url('Lexend-Light.woff') format('woff'),
    font-url('Lexend-Light.ttf') format('opentype'),;
}

@font-face {
  font-family: 'Lexend Regular';
  font-style: normal;
  src: font-url('Lexend-Regular.eot');
  src:
    font-url('Lexend-Regular.eot#iefix') format('embedded-opentype'),
    font-url('Lexend-Regular.woff2') format('woff2'),
    font-url('Lexend-Regular.woff') format('woff'),
    font-url('Lexend-Regular.ttf') format('opentype'),;
}

@font-face {
  font-family: 'Lexend SemiBold';
  font-style: normal;
  src: font-url('Lexend-SemiBold.eot');
  src:
    font-url('Lexend-SemiBold.eot#iefix') format('embedded-opentype'),
    font-url('Lexend-SemiBold.woff2') format('woff2'),
    font-url('Lexend-SemiBold.woff') format('woff'),
    font-url('Lexend-SemiBold.ttf') format('opentype'),;
}

@font-face {
  font-family: 'Lexend Bold';
  font-style: normal;
  src: font-url('Lexend-Bold.eot');
  src:
    font-url('Lexend-Bold.eot#iefix') format('embedded-opentype'),
    font-url('Lexend-Bold.woff2') format('woff2'),
    font-url('Lexend-Bold.woff') format('woff'),
    font-url('Lexend-Bold.ttf') format('opentype'),;
}

@font-face {
  font-family: 'Lexend Extra Bold';
  font-style: normal;
  src: font-url('Lexend-ExtraBold.eot');
  src:
    font-url('Lexend-ExtraBold.eot#iefix') format('embedded-opentype'),
    font-url('Lexend-ExtraBold.woff2') format('woff2'),
    font-url('Lexend-ExtraBold.woff') format('woff'),
    font-url('Lexend-ExtraBold.ttf') format('opentype'),;
}

@font-face {
  font-family: 'Roboto Light';
  font-style: normal;
  src: font-url('Roboto-Light.eot');
  src:
    font-url('Roboto-Light.eot#iefix') format('embedded-opentype'),
    font-url('Roboto-Light.woff2') format('woff2'),
    font-url('Roboto-Light.woff') format('woff'),
    font-url('Roboto-Light.ttf') format('opentype'),;
}

@font-face {
  font-family: 'Roboto Regular';
  font-style: normal;
  src: font-url('Roboto-Regular.eot');
  src:
    font-url('Roboto-Regular.eot#iefix') format('embedded-opentype'),
    font-url('Roboto-Regular.woff2') format('woff2'),
    font-url('Roboto-Regular.woff') format('woff'),
    font-url('Roboto-Regular.ttf') format('opentype'),;
}

@font-face {
  font-family: 'Roboto SemiBold';
  font-style: normal;
  src: font-url('Roboto-SemiBold.eot');
  src:
    font-url('Roboto-SemiBold.eot#iefix') format('embedded-opentype'),
    font-url('Roboto-SemiBold.woff2') format('woff2'),
    font-url('Roboto-SemiBold.woff') format('woff'),
    font-url('Roboto-SemiBold.ttf') format('opentype'),;
}

@font-face {
  font-family: 'Roboto Bold';
  font-style: normal;
  src: font-url('Roboto-Bold.eot');
  src:
    font-url('Roboto-Bold.eot#iefix') format('embedded-opentype'),
    font-url('Roboto-Bold.woff2') format('woff2'),
    font-url('Roboto-Bold.woff') format('woff'),
    font-url('Roboto-Bold.ttf') format('opentype'),;
}

@font-face {
  font-family: 'Roboto Condensed';
  font-style: normal;
  src: font-url('RobotoCondensed-Regular.eot');
  src:
    font-url('RobotoCondensed-Regular.eot#iefix') format('embedded-opentype'),
    font-url('RobotoCondensed-Regular.woff2') format('woff2'),
    font-url('RobotoCondensed-Regular.woff') format('woff'),
    font-url('RobotoCondensed-Regular.ttf') format('opentype'),;
}

@font-face {
  font-family: 'Roboto Condensed Bold';
  font-style: normal;
  src: font-url('RobotoCondensed-Bold.eot');
  src:
    font-url('RobotoCondensed-Bold.eot#iefix') format('embedded-opentype'),
    font-url('RobotoCondensed-Bold.woff2') format('woff2'),
    font-url('RobotoCondensed-Bold.woff') format('woff'),
    font-url('RobotoCondensed-Bold.ttf') format('opentype'),;
}
