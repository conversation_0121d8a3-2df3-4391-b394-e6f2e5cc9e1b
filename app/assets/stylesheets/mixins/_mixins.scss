// calculates the % width of a column (minus margin) based on number of columns and removes margin of last column
// example: @include rowCol(4, 2%)
@mixin rowCol($numCol, $margin) {
  width: calc((100% - ((#{$numCol} - 1) * #{$margin})) / #{$numCol});
  margin-right: $margin;

  &:nth-child(n) {
    margin-right: $margin;
  }

  &:nth-child(#{$numCol}n) {
    margin-right: 0;
  }
}

@mixin rowColNthType($numCol, $margin) {
  &:nth-of-type(n) {
    width: calc((100% - ((#{$numCol} - 1) * #{$margin})) / #{$numCol});
    margin-right: $margin;

    &:nth-of-type(#{$numCol}n) {
      margin-right: 0;
    }
  }
}

// same as rowCol but offsets margin count + one (takes into account a hidden first child that can't be removed)
@mixin rowColFirstHidden($numCol, $margin) {
  width: (100% - ($numCol - 1) * $margin) / $numCol;
  margin-right: $margin;

  &:nth-child(n) {
    margin-right: $margin;
  }

  &:nth-child(#{$numCol}n + 1) {
    margin-right: 0;
  }
}

@mixin rowColFirstHiddenMaxWidth($numCol, $margin) {
  max-width: (100% - ($numCol - 1) * $margin) / $numCol;
  width: auto;
  margin-right: $margin;

  &:nth-child(n) {
    margin-right: $margin;
  }

  &:nth-child(#{$numCol}n + 1) {
    margin-right: 0;
  }
}

// $marginRight should be %, $marginBottom should be px -- example: @include gridMachine(4, 2%, 10px)
@mixin gridMachine($numCol, $marginRight, $marginBottom) {
  width: (100% - ($numCol - 1) * $marginRight) / $numCol;
  margin-right: $marginRight;
  margin-bottom: $marginBottom;

  &:nth-child(n) {
    margin-right: $marginRight;
  }

  &:nth-child(#{$numCol}n) {
    margin-right: 0;
  }
}

// $borderWidth should be %, gridMachine(4, 2%, 10px)
@mixin gridMachineBorder($numCol, $borderWidth) {
  width: 100% / $numCol;
  border-style: solid;
  border-width: 0;
  border-right-width: $borderWidth;
  border-top-width: $borderWidth;

  &:nth-child(n) {
    border-right-width: $borderWidth;
  }

  &:nth-child(#{$numCol}n) {
    border-right-width: 0;
  }
}

@mixin Transition($property, $milliSeconds, $timingFunction: ease-in) {
  transition: $property ($milliSeconds + ms) $timingFunction;
}

@mixin propItemsContainerSizeManipulator {
  width: 100%;
  height: 50%;

  @include mediaQueryMin($bp-lg) {
    width: 49%;
    height: 100%;
  }
}

@mixin listReset {
  padding-left: 20px;
  margin-bottom: 10px;

  li {
    list-style: revert;
  }
}

@mixin imfMessage {
  @include fontSmoothing;
  color: var(--imMessageSuccessCol);
  background: var(--imMessageWrapperBgCol);
  border: 1px solid var(--imMessageWrapperBorderCol);
}

@mixin imfGroupGridChild {
  padding: 10px;
  border-radius: 5px;
  background-color: var(--imGroupLabelAssetWrapperBgCol);
  border: 1px solid var(--imGroupGridWrapperBorderCol);
  transition: background-color 250ms ease-in-out, border-color 250ms ease-in-out;

  &:hover {
    background-color: var(--imGroupLabelAssetWrapperHoverBgCol);
    border: 1px solid var(--imGroupLabelAssetWrapperHoverBorderCol);
  }

  // Firefox doesn't support :has - must be separate from hover pseudo class or Firefox won't recognise hover styles
  &:has(:checked) {
    background-color: var(--imGroupLabelAssetWrapperActiveBgCol);
    border: 1px solid var(--imGroupLabelAssetWrapperActiveBorderCol);
  }
}

@mixin rectangleOut {
  transform: translateZ(0);
  backface-visibility: hidden;
  transition-property: color;
  transition-duration: 0.2s;

  &::before {
    content: '';
    position: absolute;
    z-index: $crust; // -1
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--nav1HoverBgCol);
    transform: scale(0);
    transition-property: transform;
    transition-duration: 0.2s;
    transition-timing-function: ease-out;
  }

  &:hover::before {
    transform: scale(1);
  }
}
