@mixin nav3Nav4CollapsedTooltip {
  position: absolute;
  opacity: 0;
  left: var(--navCollapsedTooltipLeft);
  width: var(--navCollapsedTooltipWidth);
  min-width: var(--navCollapsedTooltipMinWidth);
  max-width: var(--navCollapsedTooltipMaxWidth);
  padding: var(--navCollapsedTooltipPadding);
  border-radius: var(--navCollapsedTooltipBorderRadius);
  background-color: var(--navCollapsedTooltipBgCol);
  z-index: -1; // initially hidden

  // Tooltip triangle arrow
  &::before {
    content: '';
    position: absolute;
    left: -5px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 7px solid transparent;
    border-bottom: 7px solid transparent;
    border-right: 5px solid var(--navCollapsedTooltipBgCol);
  }
}

@mixin nav3Nav4CollapsedTooltipHover {
  font-family: var(--navCollapsedTooltipFontStack);
  font-size: var(--navCollapsedTooltipTextSize);
  color: var(--navCollapsedTooltipTextCol); // only change text color on hover to avoid flicker of white when toggling navs
  opacity: 1;
  left: 110%;
  z-index: $space; // 100 to clear other layers
  transition: opacity 300ms ease-in, left 300ms ease-in-out;
  transition-delay: 300ms; // Delay to avoid flicker when toggling navs
}

@mixin navSelectedBorderLeft {
  &::before {
    content: '';
    position: absolute;
    left: 0;
    border-left-width: var(--nav3ActiveBorderLeftWidth);
    border-left-style: solid;
    border-left-color: var(--nav3ActiveBorderLeftCol);
    height: var(--nav3ActiveBorderLeftHeight);
  }
}

// Required for repeating styles
@mixin nav3ActiveStyles {
  background-color: var(--nav3ActiveBgCol);
  @include navSelectedBorderLeft;

  .n3-icon {
    fill: var(--nav3ActiveIconFill);
  }

  .n3-text {
    color: var(--nav3ActiveTextCol);
  }
}

// Required for repeating styles
@mixin nav4ActiveStyles {
  background-color: var(--nav4ActiveBgCol);
  @include navSelectedBorderLeft;

  .n4-icon {
    fill: var(--nav4ActiveIconFill);
  }

  .n4-text {
    color: var(--nav4ActiveTextCol);
  }
}
