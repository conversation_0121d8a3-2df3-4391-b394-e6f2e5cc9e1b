@mixin mediaQueryMin($minWidth) {
  @media screen and (min-width: $minWidth) {
    @content;
  }
}

@mixin mediaQueryMax($maxWidth) {
  $maxWidth: calc(#{$maxWidth} - 1px); // maxWidth should be 1px less than min-breakpoint
  @media screen and (max-width: $maxWidth) {
    @content;
  }
}

@mixin mediaQueryBetween($minWidth, $maxWidth) {
  $maxWidth: calc(#{$maxWidth} - 1px); // make sure there is no crossover between max-width and the next media query min-width
  @media screen and (min-width: $minWidth) and (max-width: $maxWidth) {
    @content;
  }
}
