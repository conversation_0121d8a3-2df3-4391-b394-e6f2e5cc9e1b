@mixin btnReset {
  vertical-align: baseline;
  -webkit-appearance: none;
  -moz-appearance: none;
  -webkit-user-drag: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border: 0;
  text-decoration: none;
}

@mixin btnBase {
  display: inline-block;
  white-space: nowrap;
  cursor: pointer;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
  will-change: box-shadow, transform;
  transition:
    box-shadow 200ms cubic-bezier(0.4, 0, 1, 1),
    background-color 200ms cubic-bezier(0.4, 0, 0.2, 1),
    color 200ms cubic-bezier(0.4, 0, 0.2, 1),
    transform 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

@mixin mixinBtnPrimary {
  @include btnReset;
  @include btnBase;
  @include fontSmoothing;
  font-family: var(--btnPrimaryFontStack);
  font-size: var(--btnPrimaryTextSize);
  line-height: normal;
  text-align: center;
  text-transform: var(--btnPrimaryTextTransform);
  letter-spacing: var(--btnPrimaryLetterSpacing);
  color: var(--btnPrimaryTextCol);
  border-radius: var(--btnPrimaryRadius);
  background-color: var(--btnPrimaryBgCol);
  padding: var(--btnPrimaryPadding);
  margin-bottom: var(--imfFormItemMargin);
  margin-right: var(--imfFormItemMargin);
  min-width: var(--btnPrimaryMinWidth);
  position: relative;

  &:hover,
  &:focus,
  &:focus:not(:active) {
    color: var(--btnPrimaryHoverTextCol);
    background-color: var(--btnPrimaryHoverBgCol);
    transform: var(--btnPrimaryHoverTransform);
    outline: none;
  }

  &:focus {
    outline: 0;
  }

  &:active {
    box-shadow: 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    background-color: var(--btnPrimaryActiveBgCol);
    top: var(--btnPrimaryActiveTop);
  }

  &[disabled],
  &.imf-btn-disabled {
    cursor: not-allowed;
    opacity: 40%;
    box-shadow: none;
    // Keep the background color the same on hover/focus
    &:hover,
    &:focus,
    &:active {
      color: var(--btnPrimaryTextCol);
      background-color: var(--btnPrimaryBgCol);
      transform: none;
      top: 0;
    }
  }

  svg {
    fill: var(--btnPrimaryIconFill);
  }
}
