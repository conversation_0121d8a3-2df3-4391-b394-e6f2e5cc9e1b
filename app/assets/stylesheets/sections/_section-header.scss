.header {
  background: var(--nav1WrapperBg);
  background-size: var(--nav1WrapperBgSize);
  animation: var(--nav1WrapperBgAnimation);
  flex: none;

  @include mediaQueryMin($bp-lg) {
    display: flex;
    align-items: center;
    flex: none;
  }
}

.header-logo-link {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 50px;

  @include mediaQueryMin($bp-lg) {
    flex: 1;
    text-align: left;
    justify-content: flex-start;
    min-height: 62px;
  }
}

.header-message {
  font-family: var(--nav1WelcomeMessageFontStack);
  font-size: 12px;
  color: var(--nav1WelcomeMessageCol);
  text-align: center;
  display: block;
  margin-bottom: 8px;

  @include mediaQueryMin($bp-lg) {
    margin-right: 20px;
    margin-bottom: 0;
  }
}
