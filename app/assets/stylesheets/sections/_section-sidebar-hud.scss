$sh-marginBottom: 1rem;
$sh-modPadding: 1rem;

.sidebar-hud {
  background-color: var(--imColBlack5);
  order: 4;

  @include mediaQueryMin($bp-md) {
    width: 14rem;
    order: 4;
  }
}

.sh-controls-wrapper {
  background-color: var(--imColWhite);
  padding: $sh-modPadding;
  margin-bottom: $sh-marginBottom;
}

.sh-module-wrapper {
  background-color: var(--imColBlack5);
  display: flex;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover .icon-toggle-switch,
  &:hover .icon-eye {
    fill: var(--imColSecondary);
  }
}

.sh-module-text,
.sh-module-link {
  flex: 6;
  border-right: 1px solid var(--imColBlack15);
  padding: 10px;
}

.sh-module-text {
  color: var(--imColBlack50);
}

.sh-module-link {
  color: var(--imColSecondary);
}

.sh-module-icon-wrapper {
  display: flex;
  flex: 3;
  justify-content: center;
  align-items: center;
}

.sh-overview-wrapper {
  background-color: var(--imfFormBgCol);
  padding: $sh-modPadding;
  margin-bottom: $sh-marginBottom;
}

.sh-text-group {
  margin-bottom: 15px;

  &:last-child {
    margin-bottom: 0;
  }
}

.sh-heading {
  @include primaryFontStack;
  font-size: 13px;
  color: var(--imColBlack);
  margin: 0;
}

.sh-text {
  @include primaryFontStack;
  font-size: 13px;
  color: var(--imColBlack50);
  margin: 0;
}

.sh-notes-wrapper {
  padding: $sh-modPadding;
  background-color: var(--imColWhite);
}

.sh-textarea {
  height: 100px;
}
