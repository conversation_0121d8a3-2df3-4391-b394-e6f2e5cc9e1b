// Garage Big Icon Links

.garage-nav-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--imGapMedium);
  margin-block: var(--imGapSmall) var(--imGapMedium);
  margin-block: var(--imGapSmall) var(--imGapMedium);
}

.gn-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: var(--imGarageLinkBgCol);
  padding: 1rem 0;
  border: 1px solid var(--imGarageLinkBorderCol);
  border-radius: var(--imGarageLinkRadius);
  @include Transition(border, 150);

  @include mediaQueryMin($bp-md) {
    padding: 2rem 0;
  }

  &:hover {
    border: 1px solid var(--imGarageLinkHoverBorderCol);

    .gn-icon {
      fill: var(--imGarageLinkHoverIconFill);
    }

    .gn-text {
      color: var(--imGarageLinkHoverTextCol);
    }
  }
}

.gn-icon {
  fill: var(--imGarageLinkIconFill);
  margin-bottom: 9px;
  width: calc(60px * 1.7);
  height: calc(30px * 1.7);

  @include mediaQueryMin($bp-lg) {
    width: 138px;
    height: 85px;
    margin-bottom: 30px;
  }
}

.gn-text {
  font-family: var(--imGarageLinkTextFontStack);
  @include fontSmoothing;
  font-size: var(--imGarageLinkTextSize);
  text-transform: var(--imGarageLinkTextTransform);
  color: var(--imGarageLinkTextCol);

  @include Transition(color, 150);

  @include mediaQueryMin($bp-lg) {
    font-size: 22px;
  }
}
