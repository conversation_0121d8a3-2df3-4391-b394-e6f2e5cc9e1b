.section-search-wrapper {
  background-color: var(--imHomeSearchWrapperBgCol);
  flex: none;

  .imf-input-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 0;
    position: relative;

    @include mediaQueryMin($bp-md) {
      flex-direction: row;
    }
  }

  .imf-input {
    flex-grow: 0;
    width: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 50 40" preserveAspectRatio="xMidYMid meet"><path d="M12.6,32.3c1,1,2.7,0.9,3.9-0.2l5.7-5.7c0.1-0.1,0.2-0.3,0.3-0.4c1.6,1,3.4,1.5,5.4,1.5c5.7,0,10.3-4.6,10.3-10.3S33.5,7,27.9,7s-10.3,4.6-10.3,10.3c0,1.9,0.5,3.6,1.4,5.1c-0.2,0.1-0.3,0.2-0.5,0.4l-5.7,5.7C11.7,29.6,11.6,31.3,12.6,32.3z M27.9,24.2c-3.8,0-6.9-3.1-6.9-6.9s3.1-6.9,6.9-6.9s6.9,3.1,6.9,6.9S31.7,24.2,27.9,24.2z" fill="%23b2b2b2"/></svg>');
    background-repeat: no-repeat;
    background-position: right center;
    margin: 10px 0;

    @include mediaQueryMin($bp-sm) {
      margin: 15px 0;
    }
  }
}

.ss-search-toggle-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  @include mediaQueryMin($bp-sm-2) {
    flex-direction: row;
    justify-content: space-between;
  }
}

.ss-label {
  @include primaryFontStackLight;
  font-size: 22px;
  color: var(--imColBlack60);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100%;
  display: none;

  @include mediaQueryMin($bp-md) {
    font-size: 24px;
    width: 90px;
    display: block;
  }
}

.ss-search-toggle-link {
  display: flex;

  .icon-toggle {
    margin-left: 5px;
  }
}
