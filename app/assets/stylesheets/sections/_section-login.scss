.login {
  .header-logo-wrapper {
    text-align: left;
    padding-left: 1.5rem;
    width: 100%;

    @include mediaQueryMin($bp-lg) {
      padding-left: 0;
    }
  }

  .logo-imotor {
    top: 0;
    margin: 10px 0;
  }
}

.admin-bg {
  background-color: var(--imfFormBgCol);
}

.floating-form-wrapper {
  width: 100%;
  background: var(--imLoginFormWrapperBgCol);

  @include mediaQueryMin($bp-sm) {
    width: $bp-sm;
    margin-top: 10vw;
    border-radius: var(--imThemeBorderRadiusMd);
    border: 1px solid var(--imLoginFormWrapperBorderCol);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
}

.ff-heading-wrapper {
  background-color: var(--imLoginFormWrapperHeadingBgCol);
}

.ff-heading {
  @include fontSmoothing;
  font-family: var(--imLoginFormWrapperHeadingFontStack);
  font-size: var(--imLoginFormWrapperHeadingSizeMobile);
  color: var(--imLoginFormWrapperHeadingCol);
  padding: 0.7rem 1.5rem;

  @include mediaQueryMin($bp-sm) {
    font-size: var(--imLoginFormWrapperHeadingSizeDesktop);
    padding: 1rem 1.5rem;
  }
}

.ff-body-wrapper {
  padding: 1.5rem;
}

.ff-scrollable-terms {
  height: 20vh;
  padding: 0.5rem;
  overflow: scroll;
  border: 1px solid var(--imColBlack10);
}
