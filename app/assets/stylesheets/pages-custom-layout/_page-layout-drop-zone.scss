// Add components
.drop-zone {
  display: block;
  width: 100%;
  min-height: 120px;
  overflow: auto;
  padding: 15px;
  transition: background-color 200ms ease-in-out;
  cursor: pointer;
  background-color: var(--imfCompDropZoneWrapperBgCol);
  border: 3px solid var(--imfCompDropZoneWrapperBorderCol);

  @include mediaQueryMax($bp-lg) {
    margin-bottom: 20px;
  }

  @include mediaQueryMin($bp-lg) {
    height: 100%;
  }
}

.drop-zone.disabled {
  background-color: var(--imfCompDropZoneWrapperDisabledBgCol);

  &::after {
    font-size: 16px;
    content: 'Click section to add components.';
    color: var(--imColWhite);
  }

  .lr-item {
    box-shadow: none;
  }
}

// Edit components
.drop-zone-components-configure {
  border: 3px solid var(--imfCompDropZoneWrapperBorderCol);
}

// Edit components
.configurator-page-components {
  .drop-zone {
    background-color: var(--imfCompDropZoneWrapperDisabledBgCol);
  }

  .focused-column {
    background-color: var(--imfCompDropZoneWrapperBgCol);
  }
}

.drop-zone .lr-item {
  background-color: var(--imfCompDropZoneLrItemBgCol);
  border: 1px solid var(--imfCompDropZoneLrItemBorderCol);
  box-shadow: 2px 2px 1px 0 var(--imfCompDropZoneLrItemShadowCol);
  margin-bottom: 12px;

  &:nth-child(2n) {
    background-color: var(--imfCompDropZoneLrItemBgCol);
  }

  .lr-text {
    color: var(--imfCompDropZoneLrItemTextCol);
  }
}
