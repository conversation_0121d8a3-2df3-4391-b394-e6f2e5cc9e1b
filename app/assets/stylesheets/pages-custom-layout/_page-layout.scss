// lw = layouts-wrapper (content/pages/edit > select page layout)
.lw-check-wrapper-hor {
  display: flex;
  flex-wrap: wrap;
  padding: 10px 0 20px 0;
}

.lw-radio-wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-right: 60px;

  &:nth-of-type(1) .lw-radio-label::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='80' height='56' viewBox='0 0 80 56'%3E%3Crect fill='%23ffffff' x='1' y='1' width='78' height='54'/%3E%3Cpath fill='%23BCBDC0' d='M78,2v52H2V2H78 M80,0H0v56h80V0L80,0z'/%3E%3C/svg%3E");
  }

  &:nth-of-type(2) .lw-radio-label::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='80' height='56' viewBox='0 0 80 56'%3E%3Crect fill='%23ffffff' x='1' y='1' width='78' height='53.67'/%3E%3Cpath fill='%23BCBDC0' d='M78,2v51.67H2V2H78 M80,0H0v55.67h80V0L80,0z'/%3E%3Crect fill='%23ffffff' x='1' y='1' width='38' height='53.67'/%3E%3Cpath fill='%23BCBDC0' d='M38,2v51.67H2V2H38 M40,0H0v55.67h40V0L40,0z'/%3E%3C/svg%3E");
  }

  &:nth-of-type(3) .lw-radio-label::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='80' height='56' viewBox='0 0 80 56'%3E%3Crect fill='%23ffffff' x='1' y='1' width='78' height='54' /%3E%3Cpath fill='%23BCBDC0' d='M78,2v52H2V2H78 M80,0H0v56h80V0L80,0z' /%3E%3Crect fill='%23ffffff' x='58.6' y='1' width='20.4' height='54' /%3E%3Cpath fill='%23BCBDC0' d='M78,2v52H59.6V2H78 M80,0H57.6v56H80V0L80,0z' /%3E%3Crect fill='%23ffffff' x='1' y='1' width='20.4' height='54' /%3E%3Cpath fill='%23BCBDC0' d='M20.4,2v52H2V2H20.4 M22.4,0H0v56h22.4V0L22.4,0z' /%3E%3C/svg%3E");
  }
}

.lw-radio-label {
  margin: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  width: 100px;

  &:has(:checked)::before {
    opacity: 0.8;
  }
}

.lw-radio-label::before {
  content: '';
  width: 100%;
  height: 73px;
  position: relative;
  display: block;
  margin-bottom: 8px;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 100%;
  filter: var(--imfColumnSelectorImgFilter);
  filter: invert(1) sepia(0.6) saturate(5) hue-rotate(190deg);
  opacity: 0.3;
}
