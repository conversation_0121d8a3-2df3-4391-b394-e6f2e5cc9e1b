
.dropped-list .sortable-handle:hover {
  .icon-grabber {
    fill: var(--imfCompDragItemIconFill);
  }
}

.dropped-list {
  .lr-item-inert:hover {
    cursor: default;
  }
}

.components-draggable-wrapper {
  margin-top: 30px;

  .imf-sub-heading-1 {
    margin-bottom: 0;
  }
}

.draggable-items-wrapper {
  display: flex;
  flex-flow: row wrap;
  margin-bottom: 50px;
  padding: 20px;
  border: 1px solid var(--imfCompDragItemsWrapperBorderCol);
}

.draggable-item {
  height: 40px;
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 5px 20px 5px 5px;
  background-color: var(--imfCompDragItemBgCol);
  cursor: pointer;
  border: 1px solid var(--imfCompDragItemBorderCol);
  transition: box-shadow 150ms ease-in-out;

  &:hover {
    box-shadow: 2px 2px 1px 0 var(--imfCompDragItemHoverShadowCol);

    .icon-grabber {
      fill: var(--imfCompDragItemHoverIconFill);
    }
  }
}

.disabled {
  color: var(--imfCompDragItemDisabledTextCol);
}

.single-prop-wrapper {
  width: 100%;
  display: flex;
  flex-flow: column;

  @include mediaQueryMin($bp-lg) {
    flex-flow: row nowrap;
    align-items: flex-start;
    justify-content: space-between;
  }
}

.prop-items-draggable-wrapper {
  @include propItemsContainerSizeManipulator;
  display: flex;
  flex-flow: column;
  align-items: flex-start;
  border: 1px solid var(--imColBlack20);
  width: 100%;
  min-height: 150px;
  overflow: auto;

  .draggable-item {
    width: 100%;
  }

  .imf-sub-heading-1 {
    margin-top: 0;
  }
}

.prop-items-drop-zone {
  @include propItemsContainerSizeManipulator;
  border: 1px dotted var(--imColBlack20);

  .imf-sub-heading-1 {
    margin-top: 0;
  }
}
