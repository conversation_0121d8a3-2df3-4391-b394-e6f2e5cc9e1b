.component-configure-form {
  .imf-checkbox {
    margin-right: 5px;
  }
}

.list-component-props {
  .lr-bordered {
    background-color: var(--imfCompEditModalPropsBgCol);

    // add border bottom for small viewports to delineate items visually
    // can't do zebra striping here because of white inputs on grey bg pattern
    @include mediaQueryMax($bp-xl-2) {
      border-bottom: 6px solid var(--imfCompEditModalPropsBorderCol);

      &:last-of-type {
        border-bottom: 0;
      }
    }
  }

  select {
    margin: 0 0 0 5px; // align with inputs
  }

  .lr-link-remove {
    width: 100%;
    height: 100%;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    transition: background-color 150ms ease-in-out;

    &:hover {
      background-color: white;
    }
  }
}

.component-edit-link {
  width: 100%;
  transition: background-color 150ms ease-in-out;

  &:hover {
    background-color: var(--imActionToggleHoverBgCol);
  }
}

.component-instance-configuration-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: $heavensGate; // 8999 (decreased to appear below tinyMCE editor .tox-tinymce-aux)
  display: none;

  &.active {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}

.component-instance-conguration-wrapper {
  background-color: var(--imfCompEditModalWrapperBgCol);
  padding: 20px;
  box-shadow: var(--********************************);
  width: 90%;
  overflow-y: auto;

  .imf-btn {
    margin-bottom: 0;
  }
}

.props-value-wrapper {
  display: flex;
  flex-flow: column;
}
