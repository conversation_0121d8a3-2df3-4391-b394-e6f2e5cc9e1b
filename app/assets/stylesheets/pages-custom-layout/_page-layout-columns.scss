.layout-1-column,
.layout-2-column,
.layout-3-column {
  display: grid;
}

.layout-1-column {
  grid-template-columns: 1fr;
}

.layout-2-column {
  grid-template-columns: repeat(2, 1fr);
}

.layout-3-column {
  grid-template-columns: repeat(3, 1fr);
}

.layout-columns-wrapper .drop-zone-components-configure .lr-item {
  border: 2px solid var(--imfCompDropZoneLrItemBorderCol);
  box-shadow: none;
}

.layout-columns-wrapper .drop-zone-components-configure .editing-item {
  border: 2px solid var(--imColSecondary);
  box-shadow: 2px 2px 1px 0 var(--imfCompDropZoneLrItemShadowCol);
}
