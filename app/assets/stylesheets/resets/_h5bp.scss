// Copyright 2015 Google Inc. All Rights Reserved.

// What follows is the result of much research on cross-browser styling.
// Credit left inline and big thanks to <PERSON>, <PERSON>,
// <PERSON><PERSON><PERSON>, and the H5BP dev community and team.

// Remove the gap between images, videos, audio and canvas and the bottom of their containers: h5bp.com/i/440
audio,
canvas,
img,
svg,
video {
  vertical-align: middle;
}

// Allow only vertical resizing of textareas.
textarea {
  resize: vertical;
}

// Hide visually and from screen readers: h5bp.com/u
.hidden {
  display: none !important;
  visibility: hidden;
}

// Hide only visually, but have it available for screen readers: h5bp.com/v
.visuallyhidden {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

// Extends the .visuallyhidden class to allow the element to be focusable when navigated to via the keyboard: h5bp.com/p
.visuallyhidden.focusable:active,
.visuallyhidden.focusable:focus {
  clip: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  position: static;
  width: auto;
}

// Hide visually and from screen readers, but maintain layout
.invisible {
  visibility: hidden;
}

.clearfix::before,
.clearfix::after {
  content: ' ';
  display: table;
}

.clearfix::after {
  clear: both;
}
