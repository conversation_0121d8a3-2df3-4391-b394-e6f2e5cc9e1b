// Copyright 2015 Google Inc. All Rights Reserved.
// Make html take up the entire screen
// Then set touch-action to avoid touch delay on mobile IE
html {
  width: 100%;
  height: 100%;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
}

// Main display reset for IE support.
// Source: http://weblog.west-wind.com/posts/2015/Jan/12/main-HTML5-Tag-not-working-in-Internet-Explorer-91011
main {
  display: block;
}

// Apply no display to elements with the hidden attribute.
// IE 9 and 10 support.

*[hidden] {
  display: none !important;
}
