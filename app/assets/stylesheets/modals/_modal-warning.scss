.modal-bg-wrapper {
  position: fixed;
  inset: 0;
  background-color: var(--imModalAlertBgCol);
  animation: fadeIn 250ms ease 0s 1 normal forwards;
  z-index: $heaven;
  height: 100vh;
  display: grid;
  place-items: center;
}

.modal-content-wrapper {
  background-color: var(--imModalAlertMessageBgCol);
  border: 1px solid var(--imModalAlertMessageBorderCol);
  box-shadow: rgba(0, 0, 0, 0.2) 0 5px 30px;
  padding: 30px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  animation: blowUp 250ms cubic-bezier(0.165, 0.84, 0.44, 1) forwards;

  .imf-btn.imf-btn {
    margin-block-end: 0;
  }

  > * + * {
    margin-block-start: 24px;
  }
}

.modal-content-heading,
.modal-content-sub-heading {
  font-size: 18px;
  margin-block-end: 0;
}

.modal-content-heading {
  @include primaryFontStack;
  margin-left: -5px;
}

.modal-content-sub-heading {
  @include primaryFontStackBold;
}

.icon-modal-alert {
  fill: var(--imColRed);
}

.icon-size-35 {
  width: 35px;
  height: 35px;
}

.icon-size-25 {
  width: 25px;
  height: 25px;
}
