
.dashlinks-modal-backdrop {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: $modalBackdrop;
}

.dashlinks-modal-content-wrapper {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: var(--imColWhite);
  box-shadow: rgba(0, 0, 0, 0.3) 0 0 10px 3px;
  z-index: $modalContent;
  transition: opacity 200ms ease-in-out;

  @include mediaQueryMin($bp-sm) {
    margin: 30px;
    width: calc(100% - 60px);
    height: calc(100% - 60px);
  }

  @include mediaQueryMin($bp-lg) {
    margin: 50px;
    width: calc(100% - 100px);
    height: calc(100% - 100px);
  }
}

.dashlinks-close-modal-trigger {
  cursor: pointer;
  position: absolute;
  top: 0;
  right: 0;
  width: 40px;
  height: 40px;
  border: 0;
  background-color: var(--imColSecondary);
  z-index: $modalClose;
  transition: background-color 300ms ease-in-out;

  &:hover {
    background-color: var(--imThemeColAlert);
  }

  .icon-clear {
    fill: var(--imColWhite);
  }
}

.dashlinks-modal-iframe {
  width: -webkit-fill-available;
  height: -webkit-fill-available;
  border: 0;
}
