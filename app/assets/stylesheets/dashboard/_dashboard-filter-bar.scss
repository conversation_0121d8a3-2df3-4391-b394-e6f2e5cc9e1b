.dash-filter-bar-wrapper {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  flex-direction: column;
  margin: 20px 0;

  @include mediaQueryMin($bp-lg-2) {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 10px;
    margin: 20px 0;
  }

  @include mediaQueryMin($bp-xl) {
    gap: 30px;
  }

  .search-bar-wrapper {
    margin: 0;
    width: 100%;

    @include mediaQueryMin($bp-lg-2) {
      width: 30%;
    }

    .search-bar-input {
      max-width: none;
    }
  }
}

.dash-sort-wrapper {
  display: flex;
  align-items: center;
  width: 100%;

  @include mediaQueryMin($bp-lg-2) {
    width: 30%;
  }
}

.dash-sort-label {
  @include primaryFontStackBold;
  white-space: nowrap;
  margin: 0 10px 0 0;
}

.dash-checkboxes-wrapper {

  @include mediaQueryMin($bp-sm) {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  @include mediaQueryMin($bp-lg) {
    flex: 3;
  }
}

.dash-check-item-wrapper {
  display: flex;
  padding-top: 5px;
}
