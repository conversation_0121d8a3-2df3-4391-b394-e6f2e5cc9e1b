.dash-dd-large-wrapper {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;

  // @supports changed to 'grid-area: auto' to stop <PERSON> from using old spec of grid
  @supports (grid-area: auto) {
    display: grid;
    grid-gap: var(--imfDlMargin);

    @include mediaQueryMin($bp-lg) {
      grid-template-columns: 1fr 1fr;
    }
  }
}

.dash-dd-large-item {
  text-align: center;
  flex-basis: 100%;
  background-color: var(--imfDlWrapperBgCol);
  border-radius: var(--imfDlWrapperBorderRadius);
  border: 1px solid var(--imfDlWrapperBorderCol);
  margin-bottom: var(--imfDlMargin);
  transition: border 150ms ease-in-out;

  @include mediaQueryMin($bp-lg) {
    max-width: calc(50% - (var(--imfDlMargin) / 2));

    &:nth-child(1),
    &:nth-child(3) {
      margin-right: var(--imfDlMargin);
      @supports (grid-area: auto) {
        margin: 0;
      }
    }
  }

  @supports (grid-area: auto) {
    margin: 0;

    @include mediaQueryMin($bp-lg) {
      max-width: initial;
    }
  }
}

.dash-dd-large-link {
  display: block;
  padding: 2rem 0;
}

.dash-dd-large-data-heading {
  font-family: var(--imfDlHeadingFontStack);
  font-size: var(--imfDlHeadingSize);
  line-height: var(--imfDlHeadingLineHeight);
  color: var(--imfDlHeadingCol);
  margin: 0;
}

.dash-dd-large-text {
  font-size: 18px;
  color: var(--imColLinkPrimary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
