.dashlinks-section {
  margin-bottom: 20px;
}

.dashlinks-wrapper {
  display: flex;
  flex-wrap: wrap;
  border-radius: 5px;
  border: 1px solid var(--imfDlServiceLinkWrapperBorderCol);
  background-color: var(--imfDlServiceLinkWrapperBgCol);
  padding: 10px 10px 0 10px;
}

.dashlinks-item {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 130px;
  border: 1px solid var(--imfDlServiceLinkItemBorderCol);
  border-radius: var(--imfDlServiceLinkItemRadius);
  background-color: var(--imfDlServiceLinkItemBgCol);
  margin-bottom: 10px;
  padding: 10px;
  transition: border-color 150ms ease-in-out;

  @include mediaQueryMin($bp-xs) {
    @include rowCol(2, 10px);
  }

  @include mediaQueryMin($bp-md) {
    @include rowCol(3, 10px);
  }

  @include mediaQueryMin($bp-lg) {
    @include rowCol(4, 10px);
  }

  @include mediaQueryMin($bp-lg-2) {
    @include rowCol(5, 10px);
  }

  @include mediaQueryMin($bp-xl) {
    @include rowCol(6, 10px);
  }

  @include mediaQueryMin($bp-xl-2) {
    @include rowCol(7, 10px);
  }

  @include mediaQueryMin($bp-xxl) {
    @include rowCol(8, 10px);
  }

  &:hover {
    border-color: var(--imfDlServiceLinkItemHoverBorderCol);
  }
}

.dashlinks-item-logo-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  width: 100%;
  background-color: var(--imfDlServiceLinkLogoWrapperBgCol);
  border-radius: 3px;
  padding: 5px;
}

.dashlinks-item-logo-img,
.dashlinks-item-logo-svg {
  max-height: 55px;
}

.dashlinks-item-text {
  @include primaryFontStack;
  font-size: 12px;
  text-align: center;
  color: var(--btnPrimaryTextCol);
  display: block;
  border-radius: 20px;
  background-color: var(--btnPrimaryBgCol);
  margin-top: 10px;
  padding: 2px;
  transition: background-color 150ms ease-in-out;

  .dashlinks-item:hover & {
    color: var(--btnPrimaryHoverTextCol);
    background-color: var(--btnPrimaryHoverBgCol);
  }
}
