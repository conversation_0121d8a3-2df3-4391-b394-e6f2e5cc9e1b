.dash-action-module-wrapper {
  display: flex;
  flex-wrap: wrap;

  @include mediaQueryMin($bp-xl) {
    flex-wrap: nowrap;
  }

  input[type='checkbox'],
  input[type='radio'] {
    margin: 0;
    flex-basis: initial;
  }
}

.dash-am-item {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  background-color: var(--imfDashActionItemBgCol);
  border: 1px solid var(--imfDashActionItemBorderCol);
  border-radius: var(--imThemeBorderRadiusSm);
  width: 100%;

  @include mediaQueryMin($bp-xl) {
    justify-content: flex-start;
    flex: 1;
  }
}

.dash-am-item-1 {
  margin-bottom: 10px;

  @include mediaQueryMin($bp-xl) {
    margin-right: 20px;
    margin-bottom: 0;
    flex: 0.7;
  }
}

.dash-am-item-2 {

  @include mediaQueryMin($bp-xl) {
    flex: 1;
  }

  .dash-am-item-child {

    @include mediaQueryMin($bp-xl) {
      margin-right: 10px;

      &:last-child {
        flex: 2;
      }
    }
  }
}

.dash-am-item-child {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;

  @include mediaQueryMin($bp-xl) {
    flex: 1;
  }

  &:last-child {
    padding: 10px;
  }
}

.dash-am-heading {
  background-color: var(--imfDashActionItemHeadingBgCol);
  align-items: center;
  display: flex;
  padding: 15px;
  width: 100%;

  @include mediaQueryMin($bp-xl) {
    height: 100%;
    width: auto;
  }

  &:first-child {
    min-width: 170px;
  }

  &:last-child {
    min-width: 170px;
  }
}

.dash-am-date-range {
  width: 100%;

  @include mediaQueryMin($bp-xl) {
    width: 50%;
  }

  &:first-child {
    margin-right: 10px;
  }

  .imf-input-wrapper {
    margin: 0;
  }
}

.dash-am-input {
  margin: 0;
}

.dash-am-icon {
  margin: 0 3px;
}

.dash-am-label {
  margin-right: 8px;
}

.dash-am-radio-wrapper {
  display: flex;
  width: auto;
  border: 1px solid var(--imfDashActionRadioWrapperBorderCol);
}

.dash-am-radio-label {
  text-align: center;
  cursor: pointer;
  flex-basis: 25px;
  padding: 10px;
  margin-bottom: 0;
  line-height: 15px;
  background-color: var(--imfDashActionRadioLabelBgCol);
  border-right: 1px solid var(--imfDashActionRadioLabelBorderRightCol);
  transition: background-color 150ms ease-in-out;

  &:hover {
    background-color: var(--imfDashActionRadioLabelHoverBgCol);
  }

  &:last-of-type {
    border-right: 0;
  }
}

.dash-am-radio {
  display: none;
  padding: 0;

  &:checked + .dash-am-radio-label {
    color: var(--imfDashActionRadioCheckeCol);
    background-color: var(--imfDashActionRadioCheckeBgCol);
  }
}
