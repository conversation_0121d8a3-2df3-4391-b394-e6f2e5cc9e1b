.vehicle-engagement-report {
  tr.odd > td {
    background-color: var(--imColWhite);
  }

  tr.even > td {
    background-color: var(--imColBlack3);
  }

  tr.grand_total_row > td {
    background-color: var(--imColBlack10);
    color: var(--imColBlack);
    font-weight: bold;
  }

  tr > td {
    vertical-align: middle;
  }
}

.dashboard-report-export-excel {
  cursor: pointer;
}

.reports {
  .report-view-wrapper {
    max-width: 100%;
    overflow: scroll;
    margin-bottom: var(--imfFormItemMargin);

    table tr.region-name,
    table tr.region-subtotal {
      background-color: var(--imColBlack15);
    }

    table .region-name td,
    table .region-subtotal td {
      color: var(--imColBlack80);
    }

    table .grand-total {
      background-color: var(imBlack45);
    }

    table .grand-total td {
      color: var(--imColWhite);
    }
  }
}

.dash-report-heading-section {
  margin: 30px 0;
}

.dash-report-heading {
  @include primaryFontStackLight;
  font-size: 40px;
  line-height: 50px;
  color: var(--imColLinkPrimary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.dash-report-subheading {
  @include primaryFontStackLight;
  font-size: 30px;
  color: var(--imColLinkPrimary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0 0 1rem;
  line-height: 1.7rem;
}

.print-report {
  .section-forms,
  .section-users {
    background: var(--imColWhite);
  }

  .box-nav1 {
    display: none;
  }

  .dash-report-heading-section {
    margin: 250px 0 180px 0;
  }

  .key-metrics-text {
    color: var(--imColBlack50);
  }

  .dash-dd-large-wrapper {
    grid-template-columns: 1fr 1fr;
    margin-top: 50px;

    &.key-metrics {
      margin-top: 0;
    }
  }

  .dash-dd-large-item {
    background: var(--imColBlack3);
  }

  &.overview .dash-line-chart-wrapper {
    page-break-before: always;
  }

  canvas,
  .dash-donut-inner,
  .lr-item,
  .dash-report-referrers-wrapper {
    page-break-inside: avoid;
  }

  .dash-flex-wrapper {
    margin-top: 30px;
  }
}
