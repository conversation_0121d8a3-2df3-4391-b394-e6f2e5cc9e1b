$linkArrowSize: 15px;

.dash-flex-wrapper {

  @include mediaQueryMin($bp-xl) {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
  }
}

.dash-flex-child {
  width: 100%;

  @include mediaQueryMin($bp-xl) {
    @include rowCol(2, var(--imfDlMargin));
  }

  +.dash-flex-child {
    margin-top: 20px;

    @include mediaQueryMin($bp-xl) {
      margin-top: 0;
    }
  }
}

.dash-tables-heading {
  font-family: var(--imfDlTableHeadingFontStack);
  font-size: var(--imfDlTableHeadingSize);
  color: var(--imfDlTableHeadingCol);
  margin: 0;
  padding: 20px;
  border-bottom: 1px solid var(--imfDlWrapperBorderCol);
}

.dash-tables-list {
  .lr-no-link {
    align-items: center;
  }

  .lr-link-basic {
    font-size: $linkArrowSize;
  }

  .lr-text-heading-left {
    padding-left: 20px;
  }

  .dash-tables-total {
    color: var(--imfDlTotalCol);
    padding: 5px 8px;
    background-color: var(--imfDlTotalBgCol);
    border-radius: 20px;
  }
}

.dash-recent-leads-table {
  background-color: var(--imfDlWrapperBgCol);
  border-radius: var(--imfDlWrapperBorderRadius);
  border: 1px solid var(--imfDlWrapperBorderCol);
  display: flex;
  flex-direction: column;

  .lr-col {
    border-left: 0;
    padding: 0 20px;
  }

  .lr-link {
    width: 100%;
    justify-content: space-between;
  }
}

.dash-leads-locations-table {
  background-color: var(--imColWhite);
  border: 1px solid var(--imfDlWrapperBorderCol);
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;

  .lr-col {
    padding: 0 20px;
  }
}

.dash-recent-leads-enquiry-link-wrapper {
  border-top: 1px solid var(--imfDlWrapperBorderCol);
  display: flex;
  justify-content: flex-end;
  padding: 20px;

  .imf-btn-sm {
    margin: 0;
    padding: 10px 16px;
    box-shadow: none;
  }

  .lr-link-basic {
    font-size: $linkArrowSize;
    margin-left: 5px;
  }
}
