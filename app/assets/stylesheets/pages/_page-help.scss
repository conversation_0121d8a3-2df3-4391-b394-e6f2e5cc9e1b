.help .col-wrapper {
  display: flex;
  flex-wrap: wrap;

  @include mediaQueryMin($bp-lg) {
    width: 80%;
  }

  @include mediaQueryMin($bp-xl) {
    width: 70%;
  }

  @include mediaQueryMin($bp-xxl) {
    width: 50%;
  }

  // @supports changed to 'grid-area: auto' to stop <PERSON> from using old spec of grid
  @supports (grid-area: auto) {
    display: grid;
    grid-gap: var(--imfFormItemMargin);

    @include mediaQueryMin($bp-md) {
      grid-template-columns: 1fr 1.5fr;
    }
  }
}

.text-well {
  flex-basis: 100%;
  margin-bottom: var(--imfFormItemMargin);

  // @supports changed to 'grid-area: auto' to stop <PERSON> from using old spec of grid
  @supports (grid-area: auto) {
    margin: 0;
  }

  @include mediaQueryMin($bp-lg) {
    flex-basis: calc(50% - (var(--imfFormItemMargin) / 2));

    &:nth-child(1) {
      margin-right: var(--imfFormItemMargin);

      // @supports changed to 'grid-area: auto' to stop <PERSON> from using old spec of grid
      @supports (grid-area: auto) {
        margin: 0;
      }
    }
  }
}
