.lr-text {
  &.success,
  &.failed,
  &.ongoing,
  &.timeout {
    position: relative;
    text-indent: 25px;

    &::before {
      content: '';
      position: absolute;
      background-size: 24px 24px;
      width: 24px;
      height: 24px;
      top: -4px;
      left: 0;
    }
  }

  &.success {
    color: var(--imThemeColSuccess);

    &::before {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='100%25' height='100%25' viewBox='0 0 30 30'%3E %3Cpath d='M11.3,19.5L23.5,7.4L25,9L11.3,22.6L5,16.3l1.6-1.6L11.3,19.5z' fill='%23388e3c' /%3E%3C/svg%3E");
    }
  }

  &.failed {
    color: var(--imThemeColAlert);

    &::before {
      background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='100%25' height='100%25' viewBox='0 0 30 30'%3E %3Cpolygon points='24.2,7 22.4,5.3 15,13.2 7.6,5.3 5.8,7 13.3,15 5.8,23 7.6,24.7 15,16.8 22.4,24.7 24.2,23 16.7,15' fill='%23de3226' /%3E%3C/svg%3E");
    }
  }

  &.ongoing {
    color: var(--imColOrange);
  }

  &::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='100%25' height='100%25' viewBox='0 0 30 30'%3E %3Cpath d='M20.458,19.8116a6.6947,6.6947,0,0,0,1.1436-2.7548h2.235a8.7378,8.7378,0,0,1-1.8193,4.3141Zm-4.3144,2.7029a6.621,6.621,0,0,0,2.7029-1.1436l1.6115,1.6115A8.9084,8.9084,0,0,1,16.1436,24.75Zm7.693-7.6411h-2.235a6.6947,6.6947,0,0,0-1.1436-2.7548l1.5593-1.5593a8.7378,8.7378,0,0,1,1.8193,4.3141Zm-9.928,7.6411V24.75a8.6364,8.6364,0,0,1-5.51-2.911,8.5053,8.5053,0,0,1-2.235-5.8737,8.5053,8.5053,0,0,1,2.235-5.8737,8.6364,8.6364,0,0,1,5.51-2.911V3.75l5.042,5.0421L13.9086,13.73V9.4157a6.4988,6.4988,0,0,0-3.9248,2.2611,6.5923,6.5923,0,0,0,0,8.5766A6.4988,6.4988,0,0,0,13.9086,22.5145Z' fill='%23ffa500' /%3E%3C/svg%3E");
  }

  &.timeout {
    color: var(--imColOrange);
  }
}

.lr-text.publish {
  color: var(--imColWhite);
  background: var(--imColBlack50);
  padding: 1px 6px;
  border-radius: 11px;
}
