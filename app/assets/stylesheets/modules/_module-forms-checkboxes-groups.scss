.imf-check-group-wrapper {
  margin-bottom: 15px;

  .imf-check-wrapper {
    margin: 0;
  }
}

.imf-group-heading-wrapper {
  & + .imf-check-group-wrapper {
    margin-top: 15px;
  }
}

.imf-gg-parent-container {
  container: imfgroupparentcontainer / inline-size;
}

.imf-group-wrapper {
  padding: 10px;
  background-color: var(--imGroupGridWrapperBgCol);
  border: 1px solid var(--imGroupGridWrapperBorderCol);
}

// The grid will create as many columns as will fit in the container (auto-fit)
// Each column will be at least 150px wide e.g. minmax(150px, 1fr)
// Columns will expand to fill the row up to 1fr each to share the available space equally
.imf-grid-wrapper {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;

  .imf-input-wrapper {
    margin: 0;
  }
}

.imf-gg-child {
  width: 100%; // Each child will try to fill the width of its grid cell
  max-width: 200px; // Limit the maximum width of each child
  min-width: 150px; // if the container is too small, children can shrink to 150px
  display: flex;
  flex-direction: column-reverse;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  margin: 0; // reset label margin
  @include imfGroupGridChild;
}

.imf-gg-child-text-wrapper {
  font-size: 12px;
  line-height: 1.2;
  display: flex;
  align-items: center;
}

.imf-asset-png {
  filter: var(--imGroupGridChildImgFilter);
}

.components-check-group-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 20px 30px;
  padding: 10px;
  min-height: 70px;
  align-items: center;
}

.fallback-message {
  margin: 0;
  background: var(--imColBlack3);
  border: 1px solid var(--imColBlack10);
  padding: 10px;
  padding-left: 10px;
  border-left: 5px solid var(--imColSecondary);
}
