.wiki-side-bar {
  visibility: hidden;
  opacity: 0;
  width: 0;
  height: 100%; // 100% Full-height
  position: fixed;
  right: 0;
  z-index: $edgeOfSpace; // 99 - Below navs
  top: 0;
  overflow-x: hidden; // Disable horizontal scroll
  background-color: var(--imHelpWikiBgCol);
  border-left: 1px solid var(--imHelpWikiBorderLeft);
  box-shadow: -1px 0 4px 0 rgba(0, 0, 0, 0.2);
  padding: 60px 10px 10px 10px;
  transition: width var(--imHelpWikiTransitionDuration) ease-in-out;
  @media screen and (max-height: 450px) {
    padding-top: 15px;
  }

  &.is-open {
    width: var(--imHelpWikiOpenWidthMobile);
    visibility: visible;
    opacity: 1;

    @include mediaQueryMin($bp-lg) {
      width: var(--imHelpWikiOpenWidthDesktop);
    }
  }
}

.wiki-side-bar .closebtn {
  font-family: var(--imSecondaryFontStackLight);
  font-size: 50px;
  line-height: 1;
  color: var(--imHelpWikiCloseBtnCol);
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: var(--imHelpWikiCloseBtnBgCol);
  padding: 5px;
  transform: scale(1);
  transition: transform 150ms ease-in-out, background-color 150ms ease-in-out, color 150ms ease-in-out;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: calc(1em + 5px);   // 1em for font-size, 10px for 2*padding
  height: calc(1em + 5px);  // same as above
  box-sizing: content-box;

  &:hover {
    color: var(--imHelpWikiCloseBtnHoverCol);
    background-color: var(--imHelpWikiCloseBtnHoverBgCol);
    transform: scale(1.2);
  }
}

// override custom iMotor styles with user agent styles
.wiki-content-wrapper {
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  a,
  sup,
  sub,
  blockquote,
  dt,
  dd,
  ul,
  ol,
  table {
    font-family: var(--imHelpWikiFontStack);
    color: var(--imHelpWikiTextCol);
    font-weight: revert;
    margin: 0.8rem 0 0 0;

    &:first-child {
      margin: 0;
    }
  }

  p,
  a,
  strong,
  sup,
  sub,
  blockquote,
  dt,
  dd,
  ul,
  ol,
  table {
    font-size: var(--imHelpWikiTextSize);
  }

  strong {
    font-weight: normal;
    @include fontSmoothing;
    color: var(--imColTextStrong);
  }

  li {
    font-family: var(--imHelpWikiFontStack);
    font-weight: revert;
    list-style: revert;
    margin: 0.3em 0 0 2em;
  }

  blockquote { // override froala blockquote with BaseCamp editor blockquote styles
    font-style: italic;
    color: revert;
    border-left: 3px solid var(--imHelpWikiAccent);
    margin: 0;
    padding: 0 0 0 1em;
  }
}
