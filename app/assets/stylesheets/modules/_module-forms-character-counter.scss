// meta title field has a span that counts down the number of recommended characters
// Related:
// /app/assets/javascripts/text-field-counter.js
// /app/helpers/cms_form_builder.rb

.character-counter {
  @include secondaryFontStack;
  font-size: var(--imGlobalFontSize);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  cursor: pointer;
  margin-top: 5px;

  &.counter-ok {
    color: var(--imMessageSuccessCol);
  }

  &.counter-warning {
    color: var(--imMessageWarningCol);
  }

  &.counter-error {
    color: var(--imMessageAlertCol);
  }
}
