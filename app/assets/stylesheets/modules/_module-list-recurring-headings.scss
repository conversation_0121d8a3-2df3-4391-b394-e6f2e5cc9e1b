// lr = list-recurring
.lr-header-row {
  border: 1px solid var(--imfListRowHeadingBorderCol);
  border-bottom: 0;

  .dealership-stock & {
    @include mediaQueryMin($bp-lg) {
      position: sticky;
      top: 0;
      z-index: $surface;
    }
  }
}

.lr-item-heading {
  color: var(--imfListRowHeadingCol);

  // extra class needed for specificity
  &.lr-item {
    background-color: var(--imfListRowHeadingBgCol);
  }

  .lr-col {
    padding: 7px 3px; // don't let the text touch the inline border
    border-left: 1px solid var(--imfListRowHeadingColumnBorderLeftCol);

    &:first-child {
      border-left: 0;
    }
  }
}

.lr-text-heading {
  font-family: var(--imfListRowHeadingFontStack);
  font-size: var(--imfListRowHeadingSize);
  line-height: var(--imfListRowHeadingLineHeight);
  text-align: var(--imfListRowHeadingTextAlign);
  color: var(--imfListRowHeadingCol);

  .lr-col-lg &,
  .lr-col-fit-custom & {
    text-align: left;
    margin-left: 5px;
  }
}

.lr-text-heading-right {
  text-align: right;
  line-height: 16px;
  margin-right: 5px;
}

.lr-text-heading-left {
  text-align: left;
  line-height: 16px;
  margin-left: 5px;
}

.lr-reset-password {
  .lr-item {
    min-height: 40px;
    align-items: center;
  }

  .lr-item-heading {
    min-height: auto;
    padding: 7px 10px;
  }
}

.lr-col-add {
  padding: 10px;
}
