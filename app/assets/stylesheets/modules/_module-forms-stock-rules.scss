.rule-group-wrapper {
  .rule-group-rules {
    &.rule-group-rules-inactive {
      opacity: 0.5;
    }
  }
}

.lr-item-rules-wrapper {
  border-top: 1px solid var(--imfListRowBorderCol);
}

.stock-rule-input-column {
  @include mediaQueryMin($bp-lg) {
    flex: 1;
  }

  &:first-child {
    display: flex;
    align-items: flex-end;
    flex-wrap: wrap;
  }
}

.stock-rule-input-dashed[type='text'] {
  border: 2px var(--imColBlack20) dashed;
}

.stock-rule-equals-wrapper {

  @include mediaQueryMin($bp-lg) {
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
  }
}

.stock-rule-row-item-wrapper {
  + .stock-rule-add-item-wrapper {
    margin-top: -1px; // overlap row above 1px to 'hide' top border
  }
}

.stock-rule-row-item {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
  border-bottom: 1px solid var(--imColBlack15);
  border-width: 1px;
  border-style: solid;
  border-color: var(--imColBlack15);
  border-top-width: 0;
  min-height: 50px;

  &:first-of-type {
    border-top-width: 1px;
  }

  &:nth-child(odd) {
    background-color: var(--imColBlack3);
  }

  label {
    margin: 0;
  }
}

.stock-rule-add-item-wrapper {
  padding: 10px;
  border: 1px solid var(--imColBlack15);
}

.stock-rule-add-item-input-row {
  display: flex;
  align-items: center;
}

.stock-rule-add-btn {
  &.imf-btn {
    margin: 0;
    padding: 10px 30px;
    margin-left: 10px;
    min-width: auto;
  }
}

.stock-rule-check-radio-label {
  display: flex;
  align-items: center;
  width: 100%;
  margin: 0;
}

.stock-rule-wrapper {

  @include mediaQueryMin($bp-lg) {
    display: flex;
    align-items: flex-end;
    flex-wrap: wrap;
  }
}

.stock-rule-form-control {

  @include mediaQueryMin($bp-lg) {
    flex: 1;
  }

  input:invalid { // red border appears if non integer is added to input type="number"
    border: 1px solid var(--imColRed);
  }
}

.stock-rule-text-wrapper {
  margin-bottom: var(--imfFormItemMargin);

  @include mediaQueryMin($bp-lg) {
    height: var(--imfInputHeight);
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}

.stock-rule-text {
  @include primaryFontStackBold;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--imColWhite);
  padding: 4px 15px;
  border-radius: 15px;
  background-color: var(--imColBlack70);

  @include mediaQueryMin($bp-lg) {
    margin: 0 15px;
  }
}

.stock-rule-text-inline {
  @include primaryFontStackBold;
  -webkit-font-smoothing: antialiased;
  color: #fff;
  padding: 1px 6px;
  background-color: var(--imColBlack70);
  display: inline-block;
  border-radius: 9px;
}

.lr-item-rule-add-wrapper {
  display: flex;
  align-items: center;
  padding: 8px;

  .imf-btn {
    margin: 0;
    padding: 11px 22px;
  }
}

.rule-add-btn-wrapper {
  margin: 0 0 0 5px;
}

.rule-data-format {
  color: var(--imColWhite);
  display: inline-block;
  background-color: var(--imColBlack40);
  padding: 2px 5px;
  border-radius: 10px;
  margin: 3px 3px 0 3px;
}

.rule-summary-value {
  color: var(--imColSecondary);
  display: inline-block;
  background-color: var(--imColBlack8);
  padding: 2px 5px;
  border-radius: 10px;
  border: 1px solid var(--imColBlack20);
  margin: 3px;
}
