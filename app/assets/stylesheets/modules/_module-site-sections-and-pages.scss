.pagetype-standard-show {
  display: block;
}

.pagetype-non-standard-show,
.pagetype-template-with-banner-show {
  display: none;
}

.pagetype-non-standard {
  .pagetype-standard-show {
    display: none;
  }

  .pagetype-non-standard-show {
    display: block;
  }
}

.pagetype-template-with-banner {
  .pagetype-standard-show {
    display: none;
  }

  .pagetype-template-with-banner-show {
    display: block;
  }
}

.page-banner-display,
.page-banner-thumbnail,
.page-banner-remove,
.page-banner-file-name,
.page-banner-alt-text {
  display: none;

  &.active {
    display: flex;
  }
}

.banner-picker-configuration-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: $heaven; // 9000
  display: none;

  &.active {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
}

.banner-picker-configuration-wrapper {
  background-color: var(--imColWhite);
  padding: 20px;
  box-shadow: 0 0 10px 4px rgba(0, 0, 0, 0.2);
  width: 90%;

  .imf-btn {
    margin-bottom: 0;
  }
}

.code-highlight,
.block-highlight {
  font-size: var(--imCodeHighlightTextSize, 12px);
  line-height: var(--imCodeHighlightLineHeight, 1.5);
  color: var(--imCodeHighlightTextCol);
  display: inline-block;
  border-radius: 12px;
  border: 1px solid var(--imCodeHighlightBorderCol);
  background-color: var(--imCodeHighlightBgCol);
  padding: 0 12px;
}

.block-highlight-wrapper {
  display: flex;

  .entity-start {
    margin-inline-end: 5px;
  }
}

.block-highlight {
  margin: 5px 0 0 0;

  p {
    margin: 0;
  }
}

.section-pages-wrapper {
  .highlighted-item-blue[data-title='New Vehicles'] {
    .sortable-handle {
      cursor: not-allowed;
    }

    .icon-grabber {
      opacity: 0.6;
    }
  }
}

.highlight-pill-sm {
  font-size: 11px;
  color: var(--imColWhite);
  display: inline-block;
  border-radius: 10px;
  background-color: var(--imColBlack);
  padding: 0 8px;
}
