.tabs-wrapper {
  display: flex;
  flex-flow: row wrap;
  background-color: var(--imfTabsBgCol);
  margin-bottom: var(--imGapLarge);
  border-radius: var(--imfTabsBorderRadius);
  overflow: hidden;
  box-shadow: var(--imfTabsBoxShadow);
}

.tab-count-default .tab-item,
.tab-count-1 .tab-item,
.tab-count-2 .tab-item,
.tab-count-3 .tab-item,
.tab-count-4 .tab-item {
  border-color: var(--imfTabsBorderRightCol);
  @include gridMachineBorder(2, 2px);

  &:nth-child(1),
  &:nth-child(2) {
    border-top: 0;
  }

  @include mediaQueryMin($bp-lg) {
    @include gridMachineBorder(3, 2px);

    &:nth-child(1),
    &:nth-child(2),
    &:nth-child(3) {
      border-top: 0;
    }
  }

  @include mediaQueryMin($bp-xl) {
    @include gridMachineBorder(4, 2px);

    &:nth-child(n) {
      border-top: 0;
    }
  }
}

.tab-count-5 .tab-item {
  border-color: var(--imfTabsBorderRightCol);
  @include gridMachineBorder(2, 2px);

  &:nth-child(1),
  &:nth-child(2) {
    border-top: 0;
  }

  @include mediaQueryMin($bp-lg) {
    @include gridMachineBorder(3, 2px);

    &:nth-child(1),
    &:nth-child(2),
    &:nth-child(3) {
      border-top: 0;
    }
  }

  @include mediaQueryMin($bp-xl) {
    @include gridMachineBorder(5, 2px);

    &:nth-child(n) {
      border-top: 0;
    }
  }
}

.tab-count-6 .tab-item {
  border-color: var(--imfTabsBorderRightCol);
  @include gridMachineBorder(2, 2px);

  &:nth-child(1),
  &:nth-child(2) {
    border-top: 0;
  }

  @include mediaQueryMin($bp-lg) {
    @include gridMachineBorder(3, 2px);

    &:nth-child(1),
    &:nth-child(2),
    &:nth-child(3) {
      border-top: 0;
    }
  }

  @include mediaQueryMin($bp-xl) {
    @include gridMachineBorder(6, 2px);

    &:nth-child(n) {
      border-top: 0;
    }
  }
}

.tab-count-7 .tab-item {
  border-color: var(--imfTabsBorderRightCol);
  @include gridMachineBorder(2, 2px);

  &:nth-child(1),
  &:nth-child(2) {
    border-top: 0;
  }

  @include mediaQueryMin($bp-sm) {
    @include gridMachineBorder(3, 2px);

    &:nth-child(1),
    &:nth-child(2),
    &:nth-child(3) {
      border-top: 0;
    }
  }

  @include mediaQueryMin($bp-md) {
    &:nth-child(n) { // specificity needed to override &:nth-child above
      border-color: var(--imfTabsBorderRightCol);
      @include gridMachineBorder(2, 2px);
    }

    &:nth-child(1),
    &:nth-child(2) {
      border-top: 0;
    }
  }

  @include mediaQueryMin($bp-lg) {
    &:nth-child(n) { // specificity needed to override &:nth-child above
      border-color: var(--imfTabsBorderRightCol);
      @include gridMachineBorder(4, 2px);
    }

    &:nth-child(1),
    &:nth-child(2),
    &:nth-child(3),
    &:nth-child(4) {
      border-top: 0;
    }
  }

  @include mediaQueryMin($bp-xl) {
    &:nth-child(n) { // specificity needed to override &:nth-child above
      @include gridMachineBorder(7, 2px);
      border-top: 0;
    }
  }
}

.tab-count-8 .tab-item,
.tab-count-9 .tab-item {
  border-color: var(--imfTabsBorderRightCol);
  @include gridMachineBorder(2, 2px);

  &:nth-child(1),
  &:nth-child(2) {
    border-top: 0;
  }

  @include mediaQueryMin($bp-sm) {
    @include gridMachineBorder(3, 2px);

    &:nth-child(1),
    &:nth-child(2),
    &:nth-child(3) {
      border-top: 0;
    }
  }

  @include mediaQueryMin($bp-lg) {
    @include gridMachineBorder(4, 2px);

    &:nth-child(1),
    &:nth-child(2),
    &:nth-child(3),
    &:nth-child(4) {
      border-top: 0;
    }
  }

  @include mediaQueryMin($bp-xxl) {
    @include gridMachineBorder(8, 2px);

    &:nth-child(n) {
      border-top: 0;
    }
  }
}

.tab-link {
  font-family: var(--imfTabTextFontStack);
  @include fontSmoothing;
  font-size: var(--imfTabTextSize);
  line-height: 16px;
  text-align: center;
  color: var(--imfTabTextCol);
  background-color: var(--imfTabBgCol);
  width: 100%;
  padding: 12px 5px;
  margin: 0;
  margin-right: 10px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color, 200ms ease-in-out;

  &.tab-trigger {
    cursor: pointer;
  }

  &:hover {
    color: var(--imfTabHoverTextCol);
    background-color: var(--imfTabHoverBgCol);
  }

  &.tab-active {
    color: var(--imfTabActiveTextCol);
    background-color: var(--imfTabActiveBgCol);
  }

  &.tab-alert {
    &.tab-active,
    &:hover {
      background-color: var(--imfTabAlertBgCol);
    }
  }

  &.tab-disabled,
  &.tab-disabled:hover {
    background-color: var(--imfTabDisabledBgCol);
    cursor: default;
  }
}
