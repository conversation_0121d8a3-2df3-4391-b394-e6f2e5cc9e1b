.preview-publish-button-set-wrapper {
  display: flex;
  justify-content: center;
  margin: 0.5em 0.5em;

  @include mediaQueryMin($bp-md) {
    justify-content: flex-end;
    flex-basis: 30%;
    margin: 0;
  }
}

.preview-publish-button {
  cursor: pointer;
  display: flex;
  align-items: center;
  border: 0;
  text-decoration: none;
  background-color: var(--imColSecondary);
  padding: var(--imPublishBtnPadding) 16px;
  margin: 0 0 0 8px;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
  transition: background-color 150ms ease-in-out;

  &:hover,
  &:focus,
  &:focus:not(:active) {
    background-color: var(--imColSecondary105);
  }

  &:focus {
    outline: 0;
  }
}

.button-dark {
  background-color: var(--imColBlack95);

  &:hover,
  &:focus,
  &:focus:not(:active) {
    background-color: var(--imColBlack90);
  }
}

.preview-publish-button-icon {
  fill: var(--imColWhite);
  width: 30px;
  height: 30px;
  margin-left: 5px;

  .button-icon-only & {
    margin: 0;
  }
}

.preview-publish-button-text {
  @include primaryFontStack;
  font-size: var(--imGlobalFontSize);
  color: var(--imColWhite);
}
