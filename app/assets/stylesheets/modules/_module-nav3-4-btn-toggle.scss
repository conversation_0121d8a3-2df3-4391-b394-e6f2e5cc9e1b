// // Nav3 and Nav4 collapse button
.navBtnToggle {
  display: none;

  // only show buttons on large viewports
  @include mediaQueryMin($bp-lg) {
    display: flex;
    flex-direction: row;
    text-align: center;
    justify-content: flex-end;
    border: 0;
    padding-block: var(--navCollapseBtnPaddingBlock);
    cursor: pointer;
    transition: background-color 150ms ease-in-out, color 150ms ease-in-out;
    width: 100%;

    &:hover {
      .icon-toggle-arrows {
        fill: var(--navCollapseBtnHoverIconFill);
      }
    }

    &.nav4Collapsed,
    &.nav3Collapsed {
      justify-content: center;
    }

    &.nav3 {
      background-color: var(--nav3CollapseBtnBgCol);
    }

    &.nav4 {
      background-color: var(--nav4CollapseBtnBgCol);
    }

    &.nav3,
    &.nav4 {
      &:hover {
        background-color: var(--navCollapseBtnHoverBgCol);
      }
    }
  }
}

// Nav3 and Nav4 collapse button icon
.icon-toggle-arrows {
  width: var(--navCollapseBtnIconSize);
  height: var(--navCollapseBtnIconSize);

  .nav3 & {
    fill: var(--nav3CollapseBtnIconFill);
  }

  .nav4 & {
    fill: var(--nav4CollapseBtnIconFill);
  }
}
