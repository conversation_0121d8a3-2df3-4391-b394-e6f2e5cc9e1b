.filter-inputs-wrapper {
  display: flex;
  align-items: center;
  gap: 13px;
  padding: 4px 5px 4px 13px;
  border-radius: 50px;
  border: 1px solid var(--imHomeSiteFilterWrapperBorderCol);
  background-color: var(--imHomeSiteFilterWrapperBgCol);
}

.label-flex {
  font-family: var(--imHomeSiteFilterLabelFontStack);
  @include fontSmoothing;
  font-size: 13px;
  color: var(--imHomeSiteFilterLabelCol);
  display: flex;
  gap: 5px;
  margin: 0;

  &:hover {
    color: var(--imHomeSiteFilterLabelHoverCol);
  }

  &:has(:checked) {
    color: var(--imHomeSiteFilterLabelActiveCol);
  }
}
