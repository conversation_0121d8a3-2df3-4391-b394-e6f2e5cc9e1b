.list-recurring-stock-export {
  .lr-item:not(.lr-item-heading) {
    background-color: transparent;
    border-bottom: 1px solid var(--imfListRowBgStockBorderCol);

    .lr-col:not(:first-of-type) {
      border-left: 1px solid var(--imfListRowBgStockBorderCol);
    }
  }
}

.lr-item {
  .feeds-list-export &,
  .filters-list-export &,
  .facebook-addresses-list-export & {
    border: 1px solid var(--imfListRowBgStockBorderCol);
  }

  .facebook-addresses-list-export & {
    &:last-child {
      border: 0;
      background-color: transparent;
    }
  }

  .feeds-list-export &,
  .filters-list-export & {
    border-top: 0;
  }

  .lr-content-block-wrapper {
    display: flex;
    align-items: flex-start;
    width: 100%;
    padding: 10px 10px 10px 0;
  }

  .lr-content-block-item {
    flex: 1;
  }

  .lr-content-block-link {
    text-align: center;
    display: block;
    cursor: pointer;
    flex: 0 0 var(--imfListRowHeight);
  }
}
