.bc-list {
  display: flex;
  flex: none;
  background-color: var(--imfBreadCrumbWrapperBgCol);
  border-bottom: 1px solid var(--imfBreadCrumbWrapperBorderBottom);
}

.bc-item {
  position: relative;
  line-height: 50px;
}

.bc-text {
  font-family: var(--imfBreadCrumbFontStack);
  color: var(--imfBreadCrumbTextCol);
  margin-left: 20px;
  margin-right: 15px;

  .bc-link & {
    font-family: var(--imfBreadCrumbLinkFontStack);
    color: var(--imfBreadCrumbLinkCol);
  }

  .bc-link:hover & {
    color: var(--imfBreadCrumbLinkHoverCol);
  }
}

.triangle-right {
  position: absolute;
  left: 100%;
  top: 0;
  border-top: 25px solid rgba(0, 0, 0, 0);
  border-bottom: 25px solid rgba(0, 0, 0, 0);
  border-left: 10px solid var(--imfBreadCrumbTriangleRightCol);
  z-index: $surface;

  &::after {
    content: '';
    width: 0;
    height: 0;
    border-top: 24px solid rgba(0, 0, 0, 0);
    border-bottom: 24px solid rgba(0, 0, 0, 0);
    border-left: 9px solid var(--imfBreadCrumbTriangleRightBgCol);
    position: absolute;
    top: -24px;
    left: -10px;
  }
}
