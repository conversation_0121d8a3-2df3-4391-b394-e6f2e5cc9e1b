$froalaEditorMinHeight: 500px;

.fr-toolbar.fr-top {
  box-shadow: none;
}

.fr-toolbar {
  box-shadow: none;
  background: var(--imColBlack3);
  border: 1px solid var(--imColBlack15);
  z-index: $reset; // auto
}

.fr-box.fr-basic.fr-top .fr-wrapper { // specificity needed to override default froala styles
  box-shadow: none;
  border: 1px solid var(--imColBlack15);
  border-top: 0;

  .wiki & {
    max-height: none !important; // !important needed to override Froala javascript 👿
    min-height: $froalaEditorMinHeight;
  }
}

.fr-element {
  border: 1px solid rgba(0, 0, 0, 0);
  @include Transition(border, 150);

  &:focus {
    border: 1px solid var(--imColBlack);
  }
}

.fr-box.fr-basic .fr-element { // specificity needed to override default froala styles

  .wiki & {
    min-height: $froalaEditorMinHeight;
  }
}

.fr-code-view .fr-code {
  max-height: none !important;
  min-height: $froalaEditorMinHeight;
}

.fr-toolbar .fr-command.fr-btn,
.fr-popup .fr-command.fr-btn {
  z-index: $surface;
}

.fr-wrapper { // override custom iMotor styles with user agent styles
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  a,
  strong,
  sup,
  sub,
  blockquote,
  dt,
  dd,
  ul,
  ol,
  table {
    @include secondaryFontStack;
    font-weight: revert;
    margin: 0.8rem 0 0 0;

    &:first-child {
      margin: 0;
    }
  }

  li {
    @include secondaryFontStack;
    font-weight: revert;
    list-style: revert;
    margin: 0.3em 0 0 2em;
  }

  blockquote { // override froala blockquote with BaseCamp editor blockquote styles
    font-style: italic;
    color: revert;
    border-left: 3px solid var(--imColBlack);
    margin: 0;
    padding: 0 0 0 1em;
  }
}
