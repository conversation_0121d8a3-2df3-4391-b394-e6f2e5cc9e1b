.list-grid {
  @include mediaQueryMin($bp-sm-2) {
    display: flex;
    flex-wrap: wrap;
  }

  @supports (grid-area: auto) {
    @include mediaQueryMin($bp-sm-2) {
      display: grid;
      grid-gap: 1rem;
      grid-template-columns: repeat(2, 1fr);
    }

    @include mediaQueryMin($bp-md) {
      grid-template-columns: 1fr;
    }

    @include mediaQueryMin($bp-lg) {
      grid-template-columns: repeat(2, 1fr);
    }

    @include mediaQueryMin($bp-xl) {
      grid-template-columns: repeat(3, 1fr);
    }

    @include mediaQueryMin($bp-xxl) {
      grid-template-columns: repeat(4, 1fr);
    }
  }
}

.lg-item {
  padding-right: 1rem;
  padding-bottom: 1rem;
  overflow: hidden;

  @include mediaQueryMin($bp-sm-2) {
    flex: 0 1 50%;
    max-width: 50%;

    @supports (grid-area: auto) {
      max-width: initial;
      padding: 0;
    }
  }

  @include mediaQueryMin($bp-md) {
    flex: 0 1 100%;
    max-width: 100%;

    @supports (grid-area: auto) {
      max-width: initial;
      padding: 0;
    }
  }

  @include mediaQueryMin($bp-lg) {
    flex: 0 1 50%;
    max-width: 50%;

    @supports (grid-area: auto) {
      max-width: initial;
      padding: 0;
    }
  }

  @include mediaQueryMin($bp-xl) {
    flex: 0 1 33.333%;
    max-width: 33.333%;

    @supports (grid-area: auto) {
      max-width: initial;
      padding: 0;
    }
  }

  @include mediaQueryMin($bp-xxl) {
    flex: 0 1 25%;
    max-width: 25%;

    @supports (grid-area: auto) {
      max-width: initial;
      padding: 0;
    }
  }
}

.lg-item-wrapper {
  background: var(--imColBlack3);
  border: 1px solid var(--imColBlack10);
  display: flex;
  border-radius: 3px;
  overflow: hidden;
  @include Transition(border, 150);
  position: relative;
  height: 100%;

  &:hover {
    .lg-image {
      opacity: 1;
    }
  }

  input[type='checkbox'] {
    display: none;
  }

  input[type='checkbox']:checked + .lg-icon {
    visibility: visible;
    opacity: 1;
  }
}

.lg-image-clip {
  width: 45%;
  border-right: 1px solid var(--imColBlack15);
  background: var(--imColBlack5);
}

@supports (object-fit: cover) {
  .lg-image-link {
    display: flex;
    flex-direction: column;
    height: 9rem;
  }

  .lg-image {
    object-fit: cover;
    object-position: 50% 50%;
    height: 100%;
  }
}

.lg-image {
  width: 100%;
}

.lg-col-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.lg-img-wrapper {
  flex: 1.8;
}

.lg-text-wrapper {
  padding: 10px;
  margin-bottom: auto;

  .dealership-stock & {
    flex: 1;
  }
}

.lg-text-heading {
  @include primaryFontStackBold;
}

.lg-text {
  @include primaryFontStack;
}

.lg-text-heading,
.lg-text {
  @include primaryFontStack;
  color: var(--imColBlack60);
  font-size: 13px;
  line-height: 16px;
  margin: 0;
}

.lg-specials-heading,
.lg-specials-text {
  @include primaryFontStack;
  color: var(--imColBlack60);
  font-size: 13px;
  line-height: 16px;
  margin: 0;
}

.lg-link-wrapper {
  display: flex;
  border-top: 1px solid var(--imColBlack15);
}

.lg-link {
  flex: 1;
  padding-left: 10px;
  background-color: var(--imColBlack5);
  transition: background-color 150ms ease-in-out;

  &:hover {
    background-color: var(--imColBlack8);
  }
}

.lg-link-edit {
  border-right: 1px solid var(--imColBlack15);
}

.lg-link-delete {
  color: var(--imColSecondary);
  @include Transition(color, 150);

  &:hover {
    @include Transition(color, 150);
    color: var(--imMessageAlertCol);

    .icon-trash {
      fill: var(--imIndexIconActionHoverAlertFill);
    }
  }
}

.lg-icon {
  width: 30px;
  height: 30px;

  &.icon-pencil-edit-line,
  &.icon-trash,
  &.icon-eye,
  .icon-circle-x {
    fill: var(--imIconTriggersFill);

    .lr-col a:hover & {
      transform: var(--imIndexIconActionHoverTransform);
    }
  }
}

.active-false {
  color: var(--imMessageAlertCol);
}
