.section-config-custom {
  padding: 10px 10px 0 10px;
  border: 1px solid var(--imfCmsConfigSectionBorderCol);
  margin: 0 0 var(--imfFormItemMargin);

  .imf-check-wrapper {
    margin: 0;
  }
}

.brand-dealers {
  .imf-check-wrapper {
    margin: 0;
  }

  .imf-check-wrapper + .imf-sub-heading-1 {
    margin-top: 0;
  }
}

.lr-check-wrapper,
.lr-text.lr-check-wrapper {
  display: flex;
  align-items: center;
  margin: 0 0 0 10px;

  label,
  .lr-check-label {
    font-size: 13px;
    line-height: 16px;
    margin: 0;
  }
}

.imf-check-wrapper,
.radio-wrapper-hor,
.check-wrapper-hor,
.check-wrapper-nested-hor {
  display: flex;
  flex-wrap: wrap;

  .sub-heading-config {
    width: 100%;
  }
}

.imf-check-wrapper,
.radio-wrapper-hor,
.check-wrapper-hor {
  flex-direction: column;

  @include mediaQueryMin($bp-md-2) {
    flex-direction: row;
    align-items: center;
  }
}

.check-wrapper-hor-wrap {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  align-items: center;
}

.check-wrapper-nested-hor {
  flex-direction: row;
  align-items: center;
}

.radio-wrapper-hor,
.check-wrapper-hor {
  flex-grow: 1;
  align-items: baseline;

  label {
    margin-right: 2.5rem;
  }

  input[type='radio'],
  input[type='checkbox'] {
    flex-basis: initial;
  }
}

.check-radio-col-4 {
  .imf-check-radio-nested-wrapper {
    margin-bottom: 20px;
    @include rowColFirstHidden(2, 2%);

    @include mediaQueryMin($bp-sm) {
      @include rowColFirstHidden(4, 2%);
    }

    @include mediaQueryMin($bp-md) {
      @include rowColFirstHidden(2, 2%);
    }

    @include mediaQueryMin($bp-lg) {
      @include rowColFirstHidden(4, 2%);
    }

    @include mediaQueryMin($bp-lg-2) {
      @include rowColFirstHidden(5, 2%);
    }

    @include mediaQueryMin($bp-xl) {
      @include rowColFirstHidden(6, 2%);
    }

    @include mediaQueryMin($bp-xxl) {
      @include rowColFirstHidden(9, 2%);
    }
  }
}

.checks-wrapper-columns-block {
  margin-bottom: var(--imfFormItemMargin);
}

.radio-wrapper-4,
.check-wrapper-4 {
  width: 100%;

  .imf-input-wrapper {
    width: 100%;

    @include mediaQueryMin($bp-md-2) {
      @include rowCol(2, 2%);
    }

    @include mediaQueryMin($bp-lg) {
      @include rowCol(3, 2%);
    }

    @include mediaQueryMin($bp-xl) {
      @include rowCol(4, 2%);
    }
  }

  &.child-1-hidden .imf-input-wrapper {
    @include mediaQueryMin($bp-md-2) {
      @include rowColFirstHidden(2, 2%);
    }

    @include mediaQueryMin($bp-lg) {
      @include rowColFirstHidden(3, 2%);
    }

    @include mediaQueryMin($bp-xl) {
      @include rowColFirstHidden(4, 2%);
    }
  }
}

.imf-check-wrapper-tight {
  display: flex;
  flex-wrap: wrap;
  width: 100%;

  &.child-1-hidden .imf-input-wrapper {

    @include mediaQueryMin($bp-xs) {
      @include rowColFirstHidden(2, 2%);
    }

    @include mediaQueryMin($bp-md) {
      @include rowColFirstHidden(3, 2%);
    }

    @include mediaQueryMin($bp-lg) {
      @include rowColFirstHidden(4, 2%);
    }

    @include mediaQueryMin($bp-xl) {
      @include rowColFirstHidden(5, 2%);
    }

    @include mediaQueryMin($bp-xxl) {
      @include rowColFirstHidden(6, 2%);
    }
  }
}

.imf-check-wrapper-tight-7 {
  display: flex;
  flex-wrap: wrap;
  width: 100%;

  &.child-1-hidden .imf-input-wrapper {

    @include mediaQueryMin($bp-xs) {
      @include rowColFirstHidden(2, 2%);
    }

    @include mediaQueryMin($bp-md) {
      @include rowColFirstHidden(3, 2%);
    }

    @include mediaQueryMin($bp-lg) {
      @include rowColFirstHidden(4, 2%);
    }

    @include mediaQueryMin($bp-xl) {
      @include rowColFirstHidden(5, 2%);
    }

    @include mediaQueryMin($bp-xxl) {
      @include rowColFirstHidden(7, 2%);
    }
  }
}

.imf-check-wrapper-tight-8 {
  width: 100%;
  display: grid;
  gap: 10px;

  @include mediaQueryMin($bp-xs) {
    grid-template-columns: repeat(2, 1fr);
  }

  @include mediaQueryMin($bp-md) {
    grid-template-columns: repeat(3, 1fr);
  }

  @include mediaQueryMin($bp-lg-2) {
    grid-template-columns: repeat(4, 1fr);
  }

  @include mediaQueryMin($bp-xl) {
    grid-template-columns: repeat(6, 1fr);
  }

  @include mediaQueryMin($bp-xxl) {
    grid-template-columns: repeat(8, 1fr);
  }

  .imf-input-wrapper {
    margin: 0;
  }
}

.imf-input-image-wrapper {
  .imf-check-radio-nested-wrapper + .imf-check-radio-nested-wrapper {
    margin-top: 5px;
  }

  &.item-separator {
    margin-right: 15px;
    border-right: 1px solid var(--imColBlack10);
    min-width: 100px;
  }
}

.radio-wrapper-5,
.check-wrapper-5 {
  .imf-input-wrapper {
    width: 100%;

    @include mediaQueryMin($bp-md-2) {
      @include rowCol(2, 2%);
    }

    @include mediaQueryMin($bp-lg) {
      @include rowCol(3, 2%);
    }

    @include mediaQueryMin($bp-lg-2) {
      @include rowCol(4, 2%);
    }

    @include mediaQueryMin($bp-xl) {
      @include rowCol(5, 2%);
    }
  }

  &.child-1-hidden .imf-input-wrapper {
    @include mediaQueryMin($bp-md-2) {
      @include rowColFirstHidden(2, 2%);
    }

    @include mediaQueryMin($bp-lg) {
      @include rowColFirstHidden(3, 2%);
    }

    @include mediaQueryMin($bp-lg-2) {
      @include rowColFirstHidden(4, 2%);
    }

    @include mediaQueryMin($bp-xl) {
      @include rowColFirstHidden(5, 2%);
    }
  }
}

.checks-wrapper-columns-4 {
  display: block;

  @include mediaQueryMin($bp-sm) {
    columns: 2;
    column-gap: var(--imGapMedium);

    .imf-control-flex-wrapper {
      display: inline-flex;
      width: 100%;
    }
  }

  @include mediaQueryMin($bp-md) {
    columns: 3;
  }

  @include mediaQueryMin($bp-lg-2) {
    columns: 4;
  }
}

.checkbox-toggle-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: left;
  align-items: center;

  .checkbox-toggle {
    display: none;
  }

  .checkbox-toggle-icon {
    width: 30px;
    height: 20px;
    margin-right: 5px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='100%25' height='100%25' viewBox='0 0 35 22'%3E %3Cpath d='M23.9,21.7c5.9,0,10.6-4.7,10.6-10.6S29.7,0.5,23.9,0.5H11.1C5.2,0.5,0.5,5.2,0.5,11.1s4.7,10.6,10.6,10.6H23.9z' fill='%23e64a19' stroke='%23e5e5e5'/%3E %3Ccircle cx='11.1' cy='11.1' r='10.6' fill='%23ffffff' stroke='%23e5e5e5'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
  }

  .checkbox-toggle:checked + .checkbox-toggle-icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='100%25' height='100%25' viewBox='0 0 35 22'%3E %3Cpath d='M11.1,21.7C5.2,21.7,0.5,17,0.5,11.1S5.2,0.5,11.1,0.5h12.8c5.9,0,10.6,4.7,10.6,10.6s-4.7,10.6-10.6,10.6H11.1z' fill='%23428fbd' stroke='%23e5e5e5'/%3E %3Ccircle cx='23.9' cy='11.1' r='10.6' fill='%23ffffff' stroke='%23e5e5e5'/%3E%3C/svg%3E");
  }
}

.align-bottom .check-label-bottom {
  margin-bottom: 8px;
}

.imf-check-wrapper-grid-8 {
  display: grid;
  gap: 10px;

  @include mediaQueryMin($bp-xs) {
    grid-template-columns: repeat(2, 1fr);
  }

  @include mediaQueryMin($bp-md) {
    grid-template-columns: repeat(3, 1fr);
  }

  @include mediaQueryMin($bp-lg-2) {
    grid-template-columns: repeat(4, 1fr);
  }

  @include mediaQueryMin($bp-xl) {
    grid-template-columns: repeat(6, 1fr);
  }

  @include mediaQueryMin($bp-xxl) {
    grid-template-columns: repeat(8, 1fr);
  }

  .imf-input-wrapper {
    margin: 0;
  }
}

// use with gap class found in gaps.scss (e.g. gap-15)
.check-wrapper-flex {
  display: flex;
  flex-wrap: wrap;
  align-items: center;

  .label,
  .imf-label {
    margin: 0;
  }
}

.label-nested-danger {
  border: 1px solid var(--imfDashAlertRadioLabelBorderCol);
  padding: 3px 10px;
  background-color: var(--imfDashAlertRadioLabelBgCol);
}

.check-wrapper-hor.align-center {
  margin: 10px 0;
}
