.action-module-wrapper {
  background-color: var(--imColWhite);
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  border: 1px solid var(--imColBlack8);

  @include mediaQueryMin($bp-lg) {
    flex-wrap: nowrap;
  }

  input[type='checkbox'],
  input[type='radio'] {
    margin: 0;
    flex-basis: initial;
  }
}

.am-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 9px 0;
  flex: 1;
}

.am-item-1 {
  background: var(--imColBlack8);
  width: 100%;
  flex: initial;

  @include mediaQueryMin($bp-lg) {
    flex: 0.7;
  }
}

.am-title {
  margin: 0;
}

.am-label {
  margin-right: 8px;
}

.am-input {
  margin: 0;
}

.am-icon {
  margin: 0 3px;
}

.am-btn-wrapper {
  display: flex;
  width: auto;
  border: 1px solid var(--imColBlack20);
}

.am-radio-btn {
  display: none;
  padding: 0;

  &:checked + .am-btn {
    background-color: var(--imColRed);
    color: var(--imColWhite);
  }
}

.am-btn {
  flex-basis: 25px;
  padding: 5px;
  margin-bottom: 0;
  line-height: 15px;
  text-align: center;
  cursor: pointer;

  &:nth-child(2) {
    border-right: 1px solid var(--imColBlack20);
  }
}

.date-range {
  .am-item {
    padding: 7px;
  }

  .am-item-2 {
    flex: 0.8;
  }

  .am-item-3 {
    flex: 1.5;
  }

  .am-item-4 {
    flex: 0.8;
  }
}

.am-select {
  height: 28px;
}

.check-trigger {
  display: inline-block;
  padding: 0 2px;
  text-decoration: underline;

  &:hover,
  &:focus {
    text-decoration: underline;
  }
}
