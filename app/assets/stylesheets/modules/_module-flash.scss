.flash-message-wrapper-fw {
  background: var(--imFwFlashMessageWrapperBgCol);
  width: 100%;
  opacity: 0;
  animation: flashFade 3s 0.25s ease-out forwards;
  white-space: initial;

  .fm-text {
    color: var(--imFwFlashMessageTextCol);
    padding: 10px 1.5rem;
  }
}

.flash-message-wrapper {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  opacity: 1;
  display: flex;
  align-items: center;
  white-space: nowrap;
  cursor: pointer;
  z-index: $mist; // 3

  .fm-text-wrapper {
    background: var(--imFlashMessageWrapperBgCol);
    padding: 0 20px;
    height: 100%;
    display: flex;
    align-items: center;
    position: relative;
    transition: transform 1s ease;
    animation: flashSlide 3s 0.25s ease-out;
    transform: translateX(100%) translateX(-38px);

    &::before {
      content: ' ';
      position: relative;
      left: -15px;
      padding: 15px;
      // icon colour hack
      background-color: var(--imFlashMessageIconFill);
      mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3E%3Cpath d='M23.09,21.65H6.91v-0.94l1.92-1.92v-5.22c0-1.46,0.45-2.74,1.34-3.86c0.89-1.11,2.02-1.84,3.39-2.16V6.93c0-0.41,0.14-0.76,0.42-1.03C14.25,5.63,14.6,5.5,15,5.5c0.8,0,1.41,0.59,1.41,1.43v0.62c1.39,0.32,2.54,1.05,3.43,2.16c0.89,1.12,1.34,2.4,1.34,3.86v5.22l1.92,1.92V21.65z M13.1,22.63h3.79c0,0.5-0.19,0.95-0.56,1.32c-0.37,0.37-0.81,0.56-1.34,0.56s-0.97-0.19-1.34-0.56C13.29,23.57,13.1,23.13,13.1,22.63z'/%3E%3C/svg%3E");
      mask-repeat: no-repeat;
      mask-position: center;
      mask-size: contain;
      -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='30' height='30' viewBox='0 0 30 30'%3E%3Cpath d='M23.09,21.65H6.91v-0.94l1.92-1.92v-5.22c0-1.46,0.45-2.74,1.34-3.86c0.89-1.11,2.02-1.84,3.39-2.16V6.93c0-0.41,0.14-0.76,0.42-1.03C14.25,5.63,14.6,5.5,15,5.5c0.8,0,1.41,0.59,1.41,1.43v0.62c1.39,0.32,2.54,1.05,3.43,2.16c0.89,1.12,1.34,2.4,1.34,3.86v5.22l1.92,1.92V21.65z M13.1,22.63h3.79c0,0.5-0.19,0.95-0.56,1.32c-0.37,0.37-0.81,0.56-1.34,0.56s-0.97-0.19-1.34-0.56C13.29,23.57,13.1,23.13,13.1,22.63z'/%3E%3C/svg%3E");
      -webkit-mask-repeat: no-repeat;
      -webkit-mask-position: center;
      -webkit-mask-size: contain;
    }

    &:hover {
      transform: translateX(0) translateX(0);

      .fm-text {
        color: var(--imFlashMessageTextCol);
      }
    }

    .fm-text {
      font-size: 12px;
      color: var(--imFlashMessageTextCol);
      text-indent: -11px;
      transition: color 0.5s ease;
    }
  }
}

.fm-text {
  @include primaryFontStackBold;
  font-size: 13px;
  line-height: 13px;
  text-align: center;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--imFlashMessageTextCol);
  margin: 0;
  display: block;
}

@keyframes flashSlide {
  0% {
    transform: translateX(100%) translateX(-38px);
  }

  30% {
    transform: translateX(0) translateX(0);
  }

  70% {
    transform: translateX(0) translateX(0);
  }

  100% {
    transform: translateX(100%) translateX(-38px);
  }
}

@keyframes flashFade {
  0% {
    opacity: 0;
  }

  30% {
    opacity: 1;
  }

  70% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.turbolinks-progress-bar {
  position: fixed;
  height: 4px;
  background: var(--imTurbolinksProgressBarCol);
}
