.new-vehicles-toggle {
  display: none;

  .new-vehicles-selected & {
    display: block;
  }
}

.url-toggle {
  display: none;

  .url-selected & {
    display: block;
  }
}

.option-none-toggle {
  display: none;

  .option-none-selected & {
    display: block;
  }
}

.form-cta-toggle {
  display: none;

  .form-cta-selected & {
    display: block;
  }
}

.internal-url-toggle {
  display: none;

  .internal-url-selected & {
    display: block;
  }
}

.external-url-toggle {
  display: none;

  .external-url-selected & {
    display: block;
  }
}

.iframe-url-toggle {
  display: none;

  .iframe-url-selected & {
    display: block;
  }
}

.powertorque-finance-toggle {
  display: none;

  .powertorque-finance-selected & {
    display: block;
  }
}

.dynamic-form-toggle {
  display: none;

  .dynamic-form-selected & {
    display: block;
  }
}

.marketo-form-toggle {
  display: none;

  .marketo-form-selected & {
    display: block;
  }
}

.dealer-link-toggle {
  display: none;

  .dealer-link-selected & {
    display: block;
  }
}

.permanent-redirect-wrapper,
.new-vehicles-wrapper {
  margin-top: auto;
}

.external-url-selected {
  .section-hidden {
    display: none;
  }
}

.external-url-selected,
.pagetype-non-standard {
  .tab-item { // animate opacity in site sections and pages
    opacity: 1;
    transition: opacity 150ms ease-in-out;
  }

  .tab-item:not(:first-child) {
    opacity: 0;
    visibility: hidden;
  }
}

// specificity needed to override media-quary selectors
.form-cta-selected .form-cta-toggle.form-cta-margin-override {
  margin-right: 0;
}

// specificity needed to override media-quary selectors
.imf-input-remove-margin.imf-input-remove-margin.imf-input-remove-margin {
  margin-right: 0;
}

// specificity needed to override media-quary selectors
.imf-input-add-margin.imf-input-add-margin.imf-input-add-margin {
  margin-right: 2%;
}

.label-pageload-hide {
  .form-cta-selected & {
    display: none;
  }
}

.external-url-hide {
  display: block;

  .external-url-selected & {
    display: none;
  }
}

.mega-menu-none-toggle {
  display: none;

  .mega-menu-none-selected & {
    display: block;
  }
}

.mega-menu-standard-toggle {
  display: none;

  .mega-menu-standard-selected & {
    display: block;
  }
}

.mega-menu-image-enhanced-toggle {
  display: none;

  .mega-menu-image-enhanced-selected & {
    display: block;
  }
}

.mega-menu-icon-grid-toggle {
  display: none;

  .mega-menu-icon-grid-selected & {
    display: block;
  }
}
