.lr-icon {
  width: 30px;
  height: 30px;
  fill: var(--imIndexIconNeutralFill);

  &.icon-blue {
    fill: var(--imColSecondary);
  }

  &.lr-action-blue {
    fill: var(--imColSecondary);
  }

  &.lr-static-icon {
    fill: var(--imColBlack30);
  }

  .restore_fields & {
    fill: var(--imColSecondary);
  }

  .lr-link:hover & {
    fill: var(--imIndexIconNeutralHoverFill);
  }

  &.icon-permission {
    fill: var(--imColBlack40);
  }

  &.icon-tick {
    fill: var(--imIconTickFill);
  }

  &.icon-important {
    fill: var(--imColRed);
  }

  &.icon-clear {
    fill: var(--imColBlack40);

    &:hover {
      fill: var(--imColRed);
    }
  }
}

.icon-info-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;

  .icon-info-circle {
    width: 30px;
    height: 30px;
    fill: var(--imIconInfoFill);

    .lr-no-link:hover & {
      fill: var(--imIndexIconActionHoverFill);
      transform: var(--imIndexIconActionHoverTransform);
    }
  }
}
