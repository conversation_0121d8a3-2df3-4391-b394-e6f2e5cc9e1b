.batch-uploads-wrapper {
  display: none;
  position: relative;
  flex-wrap: wrap;
  background-color: var(--imColWhite);
  padding: 10px 15px 15px 15px;
  margin-bottom: 10px;
  border: 1px solid var(--imColBlack15);
  border-left: 5px solid var(--imColRed);
  height: 40px;
  overflow: hidden;

  &.bu-visible {
    display: flex;
  }

  &.bu-expanded {
    height: auto;
    overflow: visible;
  }

  &.completed {
    border-left: 5px solid var(--imThemeColSuccess);
  }
}

.bu-heading-message {
  @include primaryFontStack;
  font-size: 16px;
  margin: 0;
  color: var(--imColRed);
  cursor: pointer;
  padding-left: 30px;

  &::after {
    content: 'view';
  }

  .bu-expanded & {
    &::after {
      content: 'hide';
    }
  }

  .pending &.completed,
  .completed &.pending {
    display: none;
  }

  .pending &.pending,
  .completed &.completed {
    display: block;
  }

  .completed & {
    color: var(--imThemeColSuccess);
  }

  .completed &::after {
    content: '';
  }
}

.bu-col {
  width: 100%;
  visibility: hidden;
  opacity: 0;

  .bu-expanded & {
    visibility: visible;
    opacity: 1;

    @include Transition(opacity, 250, ease-in);
  }
}

.bu-heading {
  @include primaryFontStack;
  font-size: 16px;
  color: var(--imColWhite);
  padding: 5px;
  margin: 20px 0 0 0;
  background-color: var(--imColWhite);

  .pending &.completed,
  .completed &.pending {
    display: none;
  }

  .pending &.pending,
  .completed &.completed {
    display: block;
  }
}

.bu-heading-alert {
  background-color: var(--imColRed);
}

.bu-heading-success {
  background-color: var(--imThemeColSuccess);
}

.bu-heading-warning {
  background-color: var(--imColSecondary);
}

.bu-sub-heading {
  @include primaryFontStack;
  font-size: var(--imGlobalSubHeadingFontSize);
  line-height: 14px;
  color: var(--imColBlack);
  padding-bottom: 8px;
  border-bottom: 1px solid var(--imColBlack30);
  margin: 15px 0 10px 0;
}

.batch-uploads-action-wrapper {
  padding: 0 0 20px 0;
}

.bu-icon-wrapper {
  position: absolute;
  top: 0;
  left: 10px;

  .icon-important {
    width: 35px;
    height: 35px;
  }

  .icon-tick {
    width: 35px;
    height: 35px;
  }
}
