.search-bar-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;

  @include mediaQueryMin($bp-lg) {
    width: 25%;
  }

  .ss-search-toggle-wrapper & {
    margin-bottom: 0;
    width: 100%;
    justify-content: center;

    @include mediaQueryMin($bp-sm) {
      max-width: 300px;
      justify-content: flex-start;
    }

    @include mediaQueryMin($bp-md) {
      max-width: 370px;
    }
  }

  .dealership-stock & {
    flex: 1;

    @include mediaQueryMin($bp-xl) {
      margin-bottom: 0;
      margin-right: 30px;
    }
  }
}

.dealership-stock.bulk-comments {
  .search-bar-wrapper {
    margin-bottom: 1rem;
  }
}

.search-bar-label {
  font-family: var(--imHomeSearchLabelFontStack);
  @include fontSmoothing;
  color: var(--imHomeSearchLabelCol);
  margin: 0 10px 0 0;
}

.search-bar-input {
  max-width: 24rem;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 50 40" preserveAspectRatio="xMidYMid meet"><path d="M12.6,32.3c1,1,2.7,0.9,3.9-0.2l5.7-5.7c0.1-0.1,0.2-0.3,0.3-0.4c1.6,1,3.4,1.5,5.4,1.5c5.7,0,10.3-4.6,10.3-10.3S33.5,7,27.9,7s-10.3,4.6-10.3,10.3c0,1.9,0.5,3.6,1.4,5.1c-0.2,0.1-0.3,0.2-0.5,0.4l-5.7,5.7C11.7,29.6,11.6,31.3,12.6,32.3z M27.9,24.2c-3.8,0-6.9-3.1-6.9-6.9s3.1-6.9,6.9-6.9s6.9,3.1,6.9,6.9S31.7,24.2,27.9,24.2z" fill="%23b2b2b2"/></svg>');
  background-repeat: no-repeat;
  background-position: right center;

  .dealership-stock & {
    max-width: none;
  }
}
