// 😡 Removed container query here because of the unintended containment side effects with modals
.section-forms,
.section-users {
  background-color: var(--imfFormBgCol);
  flex: 1;
  order: 4;
  position: relative;

  @include mediaQueryMin($bp-md) {
    order: 3;
  }
}

.imf-flex-wrapper {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;

  @include mediaQueryMin($bp-lg) {
    flex-direction: row; // change to row when imfsectionforms exceeds specified inline-size (width)
  }
}

.flex-row-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.imf-flex-self-end {
  align-self: flex-end;
}

.imf-radio-wrapper-simple + .imf-radio-wrapper-simple {
  margin: var(--imGapMedium) 0 0 0;

  @include mediaQueryMin($bp-lg) {
    margin: 0 0 0 50px;
  }
}

.imf-radio-wrapper-simple {
  display: flex;
  align-items: center;

  label,
  .imf-label {
    margin: 0;
  }
}

.imf-flex-block {
  margin: var(--imGapMedium) 0;

  @include mediaQueryMin($bp-md) {
    margin: var(--imGapLarge) 0;
  }

  .imf-check-radio-nested-wrapper {
    @include mediaQueryMin($bp-xl) {
      margin-bottom: 0;
    }
  }
}

.imf-input-wrapper {
  margin-bottom: var(--imfFormItemMargin);
  display: flex;
  flex-direction: column;

  &.imf-input-wrapper-block {
    display: block;
  }
}

// specificity needed to override selectors
.nomargin.nomargin {
  margin-block: 0;
  margin-inline: 0;
}

.imf-input-gap-top {
  margin-top: var(--imfFormItemMargin);
}

label,
.imf-label,
.imf-label-fake {
  font-family: var(--imfLabelFontStack);
  @include fontSmoothing;
  font-size: var(--imfLabelTextSize);
  color: var(--imfLabelCol);
  cursor: pointer;
  margin-bottom: var(--imfLabelMarginBottom);
  display: inline-block;

  &.disabled {
    color: var(--imColBlack25);
  }

  // specificity needed to override .imf-label
  &.imf-label-fake {
    line-height: unset; // needed if fake label is p or other element with explicit line-height
    cursor: auto;
  }
}

.imf-label-text:not(.imf-label-text-inline) {
  display: block;
  margin-bottom: 5px;
}

.imf-label-check {
  display: flex;
  align-items: center;
}

.imf-label-horizontal {
  font-size: 13px;
  margin: 0;

  input[type='checkbox'],
  input[type='radio'] {
    transform: translateY(2px);
  }
}

.imf-textarea {
  height: 100px;
}

.imf-textarea-extra-large {
  height: 500px;
}

.imf-message {
  @include imfMessage;
  padding: 5px;
  margin-bottom: var(--imfFormItemMargin);
}

.imf-message-relaxed {
  @include imfMessage;
  padding: 10px;
}

.imf-mandatory {
  font-size: var(--imfLabelMandatorySize);
  color: var(--imfLabelMandatoryCol);
  line-height: 0;
}

fieldset {
  border: 0;
  margin: 0;
  padding: 0;
}

input,
textarea,
keygen,
select,
button {
  font-size: 12px;
}

input[type='checkbox'],
input[type='radio'] {
  cursor: pointer;
  transform: scale(1.1);
  margin: 0 5px 0 0;
}

input[type='checkbox'] {
  &.checkbox-visible {
    margin: 0;
  }
}

select {
  appearance: menulist;
}

select,
input {
  &:disabled {
    cursor: not-allowed;
  }
}

.list-recurring {
  select,
  input[type='text'],
  input[type='number'] {
    flex-grow: unset;
    margin-left: 5px;
    width: calc(100% - 10px);
  }

  .fit-item {
    select,
    input {
      margin-left: 0;
    }
  }
}

.shortcodes input[type='text'] {
  margin: 0;
}

.col-wrapper-timepicker select {
  margin-right: 10px;
}

textarea,
.imf-textarea-lg {
  height: auto;
}

input[type='email'],
input[type='search'],
input[type='text'],
input[type='tel'],
input[type='url'],
input[type='password'],
input[type='color'],
input[type='image'],
input[type='number'],
input[type='date'],
input[type='month'],
input[type='week'],
input[type='time'],
input[type='datetime'],
input[type='datetime-local'],
textarea,
select {
  @include secondaryFontStack;
  color: var(--imfInputCol);
  font-size: var(--imGlobalFontSize);
  flex-grow: 3;
  height: var(--imfInputHeight);
  padding: 6px 10px;
  background-color: var(--imfInputBgCol);
  border: 1px solid var(--imfInputBorderCol);
  box-shadow: none;
  border-radius: var(--imfInputBorderRadius);
  width: 100%;
  appearance: none;

  @include Transition(border, 150);

  &:focus {
    border: 1px solid var(--imfInputFocusBorderCol);
    color: var(--imfInputFocusCol);
    outline: 0 none;
    outline: -webkit-focus-ring-color auto 0;
  }

  &::placeholder {
    color: var(--imfInputPlaceholderCol);
  }
}

// Used for Dark Theme
input[type='date']::-webkit-calendar-picker-indicator,
input[type='month']::-webkit-calendar-picker-indicator,
input[type='week']::-webkit-calendar-picker-indicator,
input[type='time']::-webkit-calendar-picker-indicator,
input[type='datetime']::-webkit-calendar-picker-indicator,
input[type='datetime-local']::-webkit-calendar-picker-indicator {
  filter: var(--imputIconFilterDkTheme); // invert(1)
}

input[type='color']::-webkit-color-swatch-wrapper,
input[type='color']::-webkit-color-swatch {
  border: 1px solid var(--imputColorSwatchBorderCol); // e.g var(--imColBlack75)
}

select::-ms-expand,
select::-webkit-inner-spin-button,
select::-webkit-outer-spin-button,
select::-webkit-input-placeholder {
  filter: var(--imputIconFilterDkTheme); // invert(1)
}

input[type='color'] {
  cursor: pointer;
}

input:read-only {
  background-color: var(--imfInputReadOnlyBgCol);
}

// hide permissions
.access-wrapper.no-access {
  display: none;
}

.imf-row-wrapper-border {
  border: 1px solid var(--imfRowWrapperBorderCol);
  border-radius: var(--imfRowWrapperBorderRadius);
  background-color: var(--imfRowWrapperBgCol);
}

.lr-row-dynamic-bordered {
  width: 100%;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  padding: 10px;
  border-top: 1px solid var(--imColBlack10);
}

.edit-special-vehicle .imf-row-wrapper-border {
  margin-bottom: 15px;

  .imf-row {
    padding: 10px 15px;

    .imf-radio-wrapper {
      margin: 0 10px 0 0;
      min-width: 100px;
    }
  }
}

.imf-row-wrapper-half {
  @include mediaQueryMin($bp-lg) {
    display: flex;
    align-items: center;
    width: 100%;
  }

  @include mediaQueryMin($bp-xl) {
    width: 460px;
  }

  select {
    margin-top: 10px;

    @include mediaQueryMin($bp-lg) {
      margin-top: 0;
    }
  }
}

.imf-row-wrapper-half + .imf-row-wrapper-half {
  margin-top: 10px;

  @include mediaQueryMin($bp-xl) {
    margin-top: 0;
    width: calc(100% - 460px);
  }
}

.imf-row {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
  border-bottom: 1px solid var(--imColBlack15);
  min-height: 50px;

  &:last-child {
    border-bottom: 0;
  }

  &:nth-child(odd) {
    background-color: var(--imColBlack3);
  }

  input[type='text'],
  select {
    flex-grow: 0;
    width: 300px;
  }

  .imf-radio-wrapper {
    margin: 5px 10px 5px 15px;
  }
}

.stock {
  .imf-radio-wrapper + .is-visible {
    padding-top: 5px;
  }

  .imf-input-group-wrapper-flex {
    ~ .imf-btn-wrapper {
      margin-top: 20px;
    }
  }

  .new_export_setup input[type='hidden'] + .imf-sub-heading-1 {
    margin-top: 30px;
  }

  label + input[type='radio'] {
    margin-left: 25px;
  }
}

.imf-single-form-input-wrapper {
  width: 100%;

  .imf-single-input-form {
    display: flex;
    flex-flow: column;
    justify-content: stretch;
    align-items: flex-start;
    width: 100%;

    @include mediaQueryMin($bp-lg) {
      flex-flow: row nowrap;
      justify-content: space-between;
      align-items: center;
    }

    .imf-input-wrapper {
      flex-grow: 1;
      margin-right: 0;

      @include mediaQueryMin($bp-lg) {
        margin-right: 10px;
      }
    }

    .imf-btn {
      margin-bottom: 0;
    }
  }
}

.colour-picker {
  display: flex;

  & :first-child {
    width: 80px;
    margin-right: 10px;
    padding: 2px;
  }
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

form.button_to {
  display: inline;
}
