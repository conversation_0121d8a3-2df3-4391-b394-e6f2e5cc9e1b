.lr-header-row.sei-header-row {
  @include mediaQueryMax($bp-xl-2) {
    display: none;
  }
}

.sei-promo-label-form {
  width: 100%;

  .col-wrapper-2 {
    opacity: 0.5;
    position: relative;
    transition: opacity 200ms ease-in-out;

    &:hover,
    &:focus-within {
      opacity: 1;
    }

    &::before {
      content: '';
      border-left-width: 4px;
      border-left-style: solid;
      border-left-color: var(--imColSecondary);
      height: 0;
      position: absolute;
      left: -4px;
    }
  }

  .col-wrapper-2.has-promo {
    opacity: 1;

    &::before {
      border-left-color: var(--imColSecondary);
      height: 100%;
    }
  }

  .col-wrapper-2.created {
    &::before {
      animation: hasPromoGrow 1s ease-out forwards;
    }
  }

  .col-wrapper-2.modified {
    &::before {
      animation: hasSuccessGrow 1s ease-out forwards;
    }
  }

  .col-wrapper-2.deleted {
    animation: hasDeletedOpacity 0.5s ease-out forwards;

    &::before {
      animation: hasDeletedShrink 0.5s ease-out forwards;
    }
  }

  .col-wrapper-2.error {
    &::before {
      animation: hasErrorGrow 0.5s ease-out forwards;
    }
  }
}

@keyframes hasPromoGrow {
  0% {
    height: 0;
  }

  50% {
    height: 100%;
  }

  100% {
    height: 100%;
  }
}

@keyframes hasSuccessGrow {
  0% {
    height: 100%;
    border-left-color: var(--imColSecondary);
  }

  30% {
    height: 0;
    border-left-color: var(--imColSecondary);
  }

  60% {
    height: 0;
    border-left-color: var(--imThemeColSuccess);
  }

  100% {
    height: 100%;
    border-left-color: var(--imThemeColSuccess);
  }
}

@keyframes hasErrorGrow {
  0% {
    height: 0;
    border-left-color: var(--imColRed);
  }

  100% {
    height: 100%;
    border-left-color: var(--imColRed);
  }
}

@keyframes hasDeletedShrink {
  0% {
    height: 100%;
    border-left-color: var(--imColRed);
  }

  100% {
    height: 0;
    border-left-color: var(--imColRed);
  }
}

@keyframes hasDeletedOpacity {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0.5;
  }
}

.sei-action-wrapper {
  padding-left: 5px;
  flex-direction: row;
}

.sei-action-btn {
  @include primaryFontStack;
  font-size: var(--imGlobalFontSize);
  color: var(--imColLinkPrimary);
  -webkit-font-smoothing: auto;
  -moz-osx-font-smoothing: auto;
  border: 0;
  padding: 0;
  background-color: transparent;
  cursor: pointer;
  text-align: left;
  margin: 0 0 0 -4px;
}

.sei-action-link-delete {
  margin-left: 40px;
}

.sei-action-text {
  margin-left: -5px;
}

.sei-item {
  @include mediaQueryMax($bp-xl-2) {
    display: block;
  }

  &.sei-item-heading {
    background-color: var(--imfListRowHeadingBgCol);
    border: 1px solid transparent;
    border-bottom: 2px solid var(--imfListRowHeadingBorderCol);
    height: var(--imfListRowHeight);
  }

  .field_with_errors {
    width: 100%;
  }

  .character-counter {
    margin: 0 0 5px 5px;
    font-size: 12px;
  }

  select {
    @include mediaQueryMax($bp-xl-2) {
      margin-bottom: 8px;
    }
  }
}

.sei-item-heading {
  // extra class needed for specificity

  .lr-text-heading {
    color: var(--imfListRowHeadingCol);
  }

  .lr-col-start {
    justify-content: flex-start;
  }

  .sei-col-lg {
    justify-content: center;
  }
}

.sei-item .lr-col {
  padding: 8px;
  min-height: 30px;

  @include mediaQueryMax($bp-xl-2) {
    align-items: flex-start;
    position: relative;
    white-space: normal;
    border-bottom: 1px solid var(--imfPlaceholderListRowBorderCol);
    border-left: 0;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
  }

  &::before {
    @include primaryFontStackBold;
    content: attr(data-title);
    font-size: 12px;
    line-height: 13px;
    min-width: 30%;
    width: 30%;
    padding-right: 10px;
    display: block;

    @include mediaQueryMin($bp-xl-2) {
      content: none;
    }
  }

  .imf-input-wrapper {
    margin: 0;
  }
}

.lr-item-heading-props .lr-col {
  padding: 7px 8px;
}

// this must come after block above to reduce specificity (.sei-item .lr-col)
.lr-item-props .lr-col {

  @include mediaQueryMax($bp-xl-2) {
    align-items: center;
  }
}

.stock-edit-item-wrapper {
  @include mediaQueryMax($bp-xl-2) {
    width: 70%;
  }

  .lp-item {
    font-size: 13px;
  }
}

.sei-summary-wrapper {
  @include mediaQueryMin($bp-sm) {
    display: flex;
  }

  @include mediaQueryBetween($bp-lg, $bp-xl-2) {
    display: block;
  }
}

.sei-summary-item {
  .lr-link {
    @include mediaQueryMax($bp-xl-2) {
      display: block;
    }
  }

  .lr-thumb {
    margin-left: 0;

    @include mediaQueryMax($bp-xl-2) {
      margin-bottom: 5px;
    }
  }

  .lr-text {
    line-height: 18px;
    color: var(imBlack45);

    &.highlight {
      color: var(--imColTextStrong);
    }
  }
}

.sei-summary-image {
  .lr-thumb {
    height: 64px;
  }
}

.sei-col-lg,
.sei-col-md-2,
.sei-col-md,
.sei-col-sm-2,
.sei-col-sm {
  .lr-link,
  .lr-no-link {
    flex: 1 0 auto;
  }
}

.sei-col-lg {
  flex: none;
  justify-content: flex-start;

  @include mediaQueryMin($bp-xl-2) {
    width: 350px;
  }
}

.sei-col-md-2 {
  flex: 0 0 130px;
}

.sei-col-md {
  flex: 1;
}

.sei-col-sm-3 {
  flex: 0 0 90px;
}

.sei-col-sm-3-fixed {

  @include mediaQueryMin($bp-xl-2) {
    max-width: 110px;
    flex-basis: 110px; /* default value */
  }
}

.sei-col-sm-2 {
  flex: 0 0 60px;
}

.sei-col-sm {
  flex: 0 0 var(--imfListRowHeight);
}

.sei-col-action {
  .lr-link {
    flex: none;
    display: block;
    text-align: center;
  }

  .lr-link:hover {
    background-color: transparent;
  }

  .icon-toggle {
    display: block;
  }
}

.sei-qualifier {
  @include mediaQueryMin($bp-xl-2) {
    display: block;
  }
}

.imf-input-wrapper .sei-checkbox {
  margin: 0;
}

.sei-checkbox-label {
  color: var(--imColBlack60);
  display: flex;
  margin-bottom: 0;

  @include mediaQueryMin($bp-xl-2) {
    display: block;
    text-align: center;
  }

  .lr-text {
    margin-left: 5px;
    line-height: 16px;

    @include mediaQueryMin($bp-xl-2) {
      display: block;
      margin-top: 10px;
      margin-left: 0;
    }
  }
}

.dealership-stock input[type='checkbox']:disabled {
  opacity: 0.75;
}

.dealership-stock input[type='checkbox']:disabled + .lr-text {
  color: var(--imColBlack20);
  cursor: auto;
}

.sei-col .check-trigger {
  color: var(--imColWhite);
}

.sei-text-center {
  @include mediaQueryMin($bp-xl-2) {
    text-align: center;
  }
}

// extra class need for specificity
.lr-text.sei-text-time-stamp {
  font-size: 10px;
  padding-top: 2px;
  font-style: italic;
  color: var(--imColBlack35);
}

.lr-col.lr-col-pseudo {
  padding: 0;
  border: 0;

  @include mediaQueryMax($bp-xl-2) {
    display: none;
  }
}
