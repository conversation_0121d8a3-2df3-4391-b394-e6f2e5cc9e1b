.toast-section-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.toast-status-bar {
  border-left: 10px solid $yellow;

  .success & {
    border-left-color: var(--imThemeColSuccess);
  }

  .fail & {
    border-left-color: var(--imThemeColAlert);
  }

  .pending & {
    border-left-color: $yellow;
  }
}

.tsh-status-text-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.tsh-status-icon {
  margin-left: 12px;
  width: var(--imToastStatusIconSize);
  height: var(--imToastStatusIconSize);
  display: none;

  .success & {
    fill: var(--imMessageSuccessIconFill);
  }

  .fail & {
    fill: var(--imThemeColAlert);
  }

  .success &.success {
    display: flex;
  }

  .fail &.fail {
    display: flex;
  }
}

.tsh-heading {
  color: var(--imColWhite);
  font-size: var(--imGlobalFontSize);
  padding: 0 16px;
  width: 100%;
}

.tsh-close {
  padding: 10px;
  cursor: pointer;
  display: flex;
  height: 55px;
  width: 55px;
  fill: var(--imColWhite);

  &:hover {
    background: var(--imColBlack);
  }

  .pending & {
    visibility: hidden;
    padding: 7px;
    width: 0;
  }
}
