.nav4-wrapper {
  background: var(--nav4WrapperBgCol);
  border-right: 1px solid var(--nav4WrapperBorderRightCol);
  flex: 0 0 50%;
  order: 2;

  @include mediaQueryMin($bp-md) {
    flex: 0 0 12rem;
  }

  @include mediaQueryMin($bp-lg) {
    &.nav4Collapsed {
      flex: 0 0 50px;
    }
  }
}

.n4-list {
  margin: 0;
}

.n4-link {
  padding: var(--nav3PaddingMobile);
  display: flex;
  align-items: center;
  position: relative;
  background-color: var(--nav4BgCol);
  transition: background-color 150ms ease-in;

  @include mediaQueryMin($bp-md) {
    padding: var(--nav4PaddingDesktop);
  }

  &:hover {
    background-color: var(--nav4HoverBgCol);
  }

  .selected & {
    @include nav4ActiveStyles;
  }

  &.dead-link,
  &.dead-link:hover {
    background-color: transparent;

    .n4-icon {
      fill: var(--imColBlack15);
    }

    .n4-text {
      color: var(--imColBlack15);
    }
  }
}

.n4-icon {
  fill: var(--nav4IconFill);
  width: 30px;
  height: 30px;
  transition: fill 150ms ease-in, transform 150ms ease-in;

  &.icon-important {
    fill: var(--nav4IconFill);
  }

  .n4-link:hover & {
    fill: var(--nav4HoverIconFill);
    transform: var(--nav4HoverIconScale);
  }
}

.n4-text {
  font-family: var(--nav4TextFontStack);
  @include fontSmoothing;
  font-size: 13px;
  line-height: 14px;
  color: var(--nav4TextCol);
  margin-left: 3px;
  flex: 1;
  transition: color 150ms ease-in;

  @include mediaQueryMin($bp-lg) {
    .nav4Collapsed & {
      @include nav3Nav4CollapsedTooltip;
    }

    .nav4Collapsed .n4-link:hover & {
      @include nav3Nav4CollapsedTooltipHover;
    }
  }

  .n4-link:hover & {
    color: var(--nav4HoverTextCol);
  }
}

// hard-coded `selected` items

.statistics.overview,
.enquiries.index,
.enquiries.show {
  .n4-link-1,
  .selected {
    @include nav4ActiveStyles;
  }
}

.analytics {
  .n4-link-2 {
    @include nav4ActiveStyles;
  }
}

.engagements {
  .n4-link-3 {
    @include nav4ActiveStyles;
  }
}

.content {
  .n4-link-4 {
    @include nav4ActiveStyles;
  }
}

.technology {
  .n4-link-5 {
    @include nav4ActiveStyles;
  }
}

.traffic {
  .n4-link-6 {
    @include nav4ActiveStyles;
  }
}

.reports {
  .n4-link-7 {
    @include nav4ActiveStyles;
  }
}

.scheduled-reports {
  .n4-link-2 {
    @include nav4ActiveStyles;
  }
}
