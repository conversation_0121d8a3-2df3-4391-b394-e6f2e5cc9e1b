.radios-wrapper-columns-3 {
  display: block;

  @include mediaQueryMin($bp-sm) {
    column-gap: var(--imGapMedium);
    columns: 2;

    .imf-control-flex-wrapper {
      width: 100%;
      display: inline-flex;
      align-items: flex-start;
    }
  }

  @include mediaQueryMin($bp-md) {
    columns: 1;

    .imf-control-flex-wrapper {
      display: flex;
    }
  }

  @include mediaQueryMin($bp-md-2) {
    columns: 2;

    .imf-control-flex-wrapper {
      display: inline-flex;
    }
  }

  @include mediaQueryMin($bp-lg) {
    columns: 3;
  }
}
