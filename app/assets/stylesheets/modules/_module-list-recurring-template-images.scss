// lr = list-recurring
.lr-col.inactive-banner,
.lr-col.inactive-promo {
  .icon-no-image,
  .lr-image {
    opacity: 0.2;
  }

  .icon-no-image {
    color: var(--imColBlack40);
  }
}

.template-enabled-true {
  & .template-enabled {
    @include mediaQueryMin($bp-sm) {
      display: flex;
    }

    @include mediaQueryMin($bp-md) {
      display: none;
    }

    @include mediaQueryMin($bp-lg) {
      display: flex;
    }
  }

  & .template-disabled {
    display: none;
  }
}

.template-enabled-false {
  & .template-enabled {
    display: none;
  }

  & .template-disabled {
    @include mediaQueryMin($bp-sm) {
      display: flex;
    }

    @include mediaQueryMin($bp-md) {
      display: none;
    }

    @include mediaQueryMin($bp-lg) {
      display: flex;
    }
  }
}
