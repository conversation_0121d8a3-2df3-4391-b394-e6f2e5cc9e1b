// Nav2 View Build Logs Link
.menu-basic-variant {
  border-color: var(--nav2BuildTriggerBorderCol);

  &:hover {
    background-color: var(--nav2BuildTriggerHoverBgCol);
    border-color: var(--nav2BuildTriggerHoverBorderCol);
    color: var(--imColWhite);
  }
}

// Nav2 Build & Deploy Trigger
.menu-basic-wrapper {
  position: relative;
  display: flex;
  width: fit-content;

  &:hover .menu-basic-dropdown {
    display: block;
  }

  &:not(.disabled) {
    &:hover .build-trigger,
    &:focus .build-trigger,
    &:focus:not(:active) .build-trigger {
      background-color: var(--nav2BuildTriggerHoverBgCol);
      border-color: var(--nav2BuildTriggerHoverBorderCol);
    }
  }

  &.disabled {
    &:hover .menu-basic-dropdown {
      display: none;
    }
  }
}

// used for View Build Logs link and Build & Deploy Trigger
.build-trigger {
  @include primaryFontStackSemiBold;
  @include fontSmoothing;
  font-size: var(--imGlobalFontSize);
  cursor: pointer;
  color: var(--imColWhite);
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: var(--nav2BuildTriggeBorderRadius);
  border-width: 2px;
  border-style: solid;
  border-color: var(--nav2BuildTriggerBorderCol);
  background-color: var(--nav2BuildTriggerBgCol);
  width: max-content;
  align-self: stretch;
  margin-left: 10px;
  height: 45px;
  transition: background-color 150ms ease-in-out, border-color 150ms ease-in-out;

  .disabled & {
    color: var(--nav2BuildTriggerDisabledTextCol);
    background-color: var(--nav2BuildTriggerDisabledBgCol);
    border-color: var(--nav2BuildTriggerDisabledBorderCol);
    cursor: not-allowed;
  }
}

.menu-basic-wrapper:not(.disabled) {
  .build-trigger-link {
    &:hover {
      background-color: var(--imColSecondary);
    }
  }
}

.mb-icon {
  fill: var(--nav2BuildTriggerIconFill);
  width: 25px;
  height: 25px;
  margin-left: 4px;

  .button-icon-only & {
    margin: 0;
  }

  .disabled & {
    fill: var(--nav2BuildTriggerDisabledIconFill);
  }
}

.menu-basic-dropdown {
  padding: 8px 0;
  background-color: var(--nav2BuildMenuWrapperBgCol);
  border-radius: var(--nav2BuildMenuWrapperBorderRadius);
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
  display: none;
  position: absolute;
  top: 100%;
  width: max-content;
  min-width: 250px;
  animation: translateFadeIn 0.25s ease;
  right: 0;

  &.show {
    display: block;
  }
}

.mbd-item {
  font-family: inherit;
  font-size: inherit;
  text-align: left;
  color: var(--nav2BuildMenuItemTextCol);
  cursor: pointer;
  border: 0;
  display: block;
  width: 100%;
  padding: 12px 20px;
  transition: background-color 150ms ease-in-out;

  &:hover {
    background-color: var(--nav2BuildMenuItemHoverBgCol);

    .disabled & {
      background-color: var(--nav2BuildMenuItemDisabledBgCol);
    }
  }

  .disabled & {
    cursor: not-allowed;
    color: var(--nav2BuildMenuItemDisabledTextCol);
    background-color: var(--nav2BuildMenuItemDisabledBgCol);
  }
}

.mbd-section-heading {
  @include primaryFontStackBold;
  @include fontSmoothing;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.6px;
  color: var(--imColBlack30);
  margin: 0 16px;
  padding: 8px 0;
  border-bottom: 1px solid var(imBlack45);

  &:last-child {
    margin-top: 12px;
  }
}
