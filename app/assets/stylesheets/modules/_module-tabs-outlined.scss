.tabs-outlined-tabs-list {
  display: flex;
  padding: 0;
  margin: 30px 0;
  border-bottom: 1px solid var(--imfTabsOutlinedBorderCol);
}

.tabs-outlined-tab-item {
  list-style: none;
  margin-left: 5px;
  margin-bottom: -1px;
  border-radius: 3px 3px 0 0;

  &:hover {
    .tabs-outlined-tab-text {
      color: var(--imfTabsOutlinedHoverTextCol);
    }

    .icon-svg-tab-outline {
      fill: var(--imfTabsOutlinedHoverIconCol);
      transform: scale3d(1.15, 1.15, 1.15);
    }
  }

  &:first-of-type {
    margin-left: 15px;
  }

  &.active-tab {
    border: 1px solid var(--imfTabsOutlinedBorderCol);
    margin-bottom: -1px;
    border-bottom: 1px solid var(--imfTabsOutlinedActiveBorderCol);

    .tabs-outlined-tab-text {
      color: var(--imfTabsOutlinedActiveTextCol);
    }

    .icon-svg-tab-outline {
      fill: var(--imfTabsOutlinedActiveIconCol);
    }
  }
}

.tabs-outlined-tab-link {
  text-decoration: none;
  padding: 6px 14px;
  display: flex;
  align-items: center;
}

.icon-svg-tab-outline {
  width: 30px;
  height: 30px;
  fill: var(--imfTabsOutlinedIconCol);
  transition: transform 150ms ease-in-out;
  transform: scale3d(1);
}

.tabs-outlined-tab-text {
  font-size: var(--imfTabsOutlinedTextSize);
  color: var(--imfTabsOutlinedTextCol);
  margin-left: 5px;
  transition: color 150ms ease-in-out;
}
