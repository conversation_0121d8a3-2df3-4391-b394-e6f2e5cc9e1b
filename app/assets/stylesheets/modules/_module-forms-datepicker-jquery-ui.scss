// jQuery UI CSS Framework 1.12.1
// This is an adapted custom theme based on the URL below. It's been modified to include `.cms` to increase the specificity.
// http://jqueryui.com/themeroller/?scope=&folderName=custom-theme&bgImgOpacityHeader=0&bgImgOpacityContent=0&bgImgOpacityDefault=0&bgImgOpacityHover=0&bgImgOpacityActive=0&bgImgOpacityHighlight=55&bgImgOpacityError=95&cornerRadiusShadow=0&offsetLeftShadow=0&offsetTopShadow=0&thicknessShadow=0&opacityShadow=0&bgImgOpacityShadow=0&bgTextureShadow=flat&bgColorShadow=%23aaaaaa&opacityOverlay=30&bgImgOpacityOverlay=0&bgTextureOverlay=flat&bgColorOverlay=%23aaaaaa&iconColorError=%23cd0a0a&fcError=%23cd0a0a&borderColorError=%23cd0a0a&bgTextureError=glass&bgColorError=%23fef1ec&iconColorHighlight=%232e83ff&fcHighlight=%23363636&borderColorHighlight=%23fcefa1&bgTextureHighlight=glass&bgColorHighlight=%23fbf9ee&iconColorActive=%23ffffff&fcActive=%23ffffff&borderColorActive=%23579EC8&bgTextureActive=glass&bgColorActive=%23579EC8&iconColorHover=%23002848&fcHover=%23ffffff&borderColorHover=%23579EC8&bgTextureHover=glass&bgColorHover=%23579EC8&iconColorDefault=%23ffffff&fcDefault=%23555555&borderColorDefault=%23e9e9e9&bgTextureDefault=glass&bgColorDefault=%23e9e9e9&iconColorContent=%23222222&fcContent=%23222222&borderColorContent=%23dadada&bgTextureContent=flat&bgColorContent=%23ffffff&iconColorHeader=%23ffffff&fcHeader=%23ffffff&borderColorHeader=%23579EC8&bgTextureHeader=highlight_soft&bgColorHeader=%23579EC8&cornerRadius=0&fwDefault=normal&fsDefault=1em&ffDefault=Arial%2Csans-serif

// Component containers
.cms .ui-widget.ui-widget-content {
  border: 1px solid #e9e9e9;
}

.cms .ui-widget-content {
  border: 1px solid #dadada;
  color: #222;
}

.cms .ui-widget-content a {
  color: #222;
}

.cms .ui-widget-header {
  border: 1px solid var(--imColSecondary);
  background: var(--imColSecondary) url(asset_path('jquery-ui-custom/ui-bg_highlight-soft_0_579EC8_1x100.png')) 50% 50% repeat-x;
  color: var(--imColWhite);
}

.cms .ui-widget-header a {
  color: var(--imColWhite);
}

// Interaction states

// We use html here because we need a greater specificity to make sure disabled
// works properly when clicked or hovered
html .cms .ui-button.ui-state-disabled:hover,
html .cms .ui-button.ui-state-disabled:active {
  border: 1px solid #e9e9e9;
  background: #e9e9e9 url(asset_path('jquery-ui-custom/ui-bg_glass_0_e9e9e9_1x400.png')) 50% 50% repeat-x;
  color: #555;
}

.cms .ui-state-default a,
.cms .ui-state-default a:link,
.cms .ui-state-default a:visited,
.cms a.ui-button,
.cms a:link.ui-button,
.cms a:visited.ui-button,
.cms .ui-button {
  color: #555;
}

.cms .ui-state-hover,
.cms .ui-widget-content .ui-state-hover,
.cms .ui-widget-header .ui-state-hover,
.cms .ui-state-focus,
.cms .ui-widget-content .ui-state-focus,
.cms .ui-widget-header .ui-state-focus,
.cms .ui-button:hover,
.cms .ui-button:focus {
  border: 1px solid var(--imColSecondary);
  background: var(--imColSecondary) url(asset_path('jquery-ui-custom/ui-bg_glass_0_579EC8_1x400.png')) 50% 50% repeat-x;
  color: var(--imColWhite);
}

.cms .ui-state-hover a,
.cms .ui-state-hover a:hover,
.cms .ui-state-hover a:link,
.cms .ui-state-hover a:visited,
.cms .ui-state-focus a,
.cms .ui-state-focus a:hover,
.cms .ui-state-focus a:link,
.cms .ui-state-focus a:visited,
.cms a.ui-button:hover,
.cms a.ui-button:focus {
  color: var(--imColWhite);
}

.cms .ui-state-active,
.cms .ui-widget-content .ui-state-active,
.cms .ui-widget-header .ui-state-active,
.cms a.ui-button:active,
.cms .ui-button:active,
.cms .ui-button.ui-state-active:hover {
  color: var(--imColWhite);
  border: 1px solid var(--imColSecondary);
  background: var(--imColSecondary) url(asset_path('jquery-ui-custom/ui-bg_glass_0_579EC8_1x400.png')) 50% 50% repeat-x;
}

.cms .ui-icon-background,
.cms .ui-state-active .ui-icon-background {
  border: var(--imColSecondary);
}

// Interaction Cues
.cms .ui-state-highlight,
.cms .ui-widget-content .ui-state-highlight,
.cms .ui-widget-header .ui-state-highlight {
  border: 1px solid #fcefa1;
  background: #fbf9ee url(asset_path('jquery-ui-custom/ui-bg_glass_55_fbf9ee_1x400.png')) 50% 50% repeat-x;
  color: #363636;
}

.cms .ui-state-checked {
  border: 1px solid #fcefa1;
  background: #fbf9ee;
}

.cms .ui-state-highlight a,
.cms .ui-widget-content .ui-state-highlight a,
.cms .ui-widget-header .ui-state-highlight a {
  color: #363636;
}

.cms .ui-state-error,
.cms .ui-widget-content .ui-state-error,
.cms .ui-widget-header .ui-state-error {
  border: 1px solid #cd0a0a;
  background: #fef1ec url(asset_path('jquery-ui-custom/ui-bg_glass_95_fef1ec_1x400.png')) 50% 50% repeat-x;
  color: #cd0a0a;
}

.cms .ui-state-error a,
.cms .ui-widget-content .ui-state-error a,
.cms .ui-widget-header .ui-state-error a {
  color: #cd0a0a;
}

.cms .ui-state-error-text,
.cms .ui-widget-content .ui-state-error-text,
.cms .ui-widget-header .ui-state-error-text {
  color: #cd0a0a;
}

// Icons
// states and images
.cms .ui-icon,
.cms .ui-widget-content .ui-icon {
  background-image: url(asset_path('jquery-ui-custom/ui-icons_222222_256x240.png'));
}

.cms .ui-widget-header .ui-icon {
  background-image: url(asset_path('jquery-ui-custom/ui-icons_ffffff_256x240.png'));
}

.cms .ui-state-hover .ui-icon,
.cms .ui-state-focus .ui-icon,
.cms .ui-button:hover .ui-icon,
.cms .ui-button:focus .ui-icon {
  background-image: url(asset_path('jquery-ui-custom/ui-icons_002848_256x240.png'));
}

.cms .ui-state-active .ui-icon,
.cms .ui-button:active .ui-icon {
  background-image: url(asset_path('jquery-ui-custom/ui-icons_ffffff_256x240.png'));
}

.cms .ui-state-highlight .ui-icon,
.cms .ui-button .ui-state-highlight.ui-icon {
  background-image: url(asset_path('jquery-ui-custom/ui-icons_2e83ff_256x240.png'));
}

.cms .ui-state-error .ui-icon,
.cms .ui-state-error-text .ui-icon {
  background-image: url(asset_path('jquery-ui-custom/ui-icons_cd0a0a_256x240.png'));
}

.cms .ui-button .ui-icon {
  background-image: url(asset_path('jquery-ui-custom/ui-icons_ffffff_256x240.png'));
}

// Misc visuals
// Corner radius
.cms .ui-corner-all,
.cms .ui-corner-top,
.cms .ui-corner-left,
.cms .ui-corner-tl {
  border-top-left-radius: 0;
}

.cms .ui-corner-all,
.cms .ui-corner-top,
.cms .ui-corner-right,
.cms .ui-corner-tr {
  border-top-right-radius: 0;
}

.cms .ui-corner-all,
.cms .ui-corner-bottom,
.cms .ui-corner-left,
.cms .ui-corner-bl {
  border-bottom-left-radius: 0;
}

.cms .ui-corner-all,
.cms .ui-corner-bottom,
.cms .ui-corner-right,
.cms .ui-corner-br {
  border-bottom-right-radius: 0;
}

// Overlays
.cms .ui-widget-shadow {
  box-shadow: 0 0 0 #aaa;
}
