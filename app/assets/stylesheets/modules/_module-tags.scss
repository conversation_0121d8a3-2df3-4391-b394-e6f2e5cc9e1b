.im-tags-wrapper {
  max-width: 100%; // reduces to parent column width
}

.im-tag-wrapper {
  padding: 0 7px 0 3px;
  border-radius: 2em;
  margin-bottom: 4px;
  margin-right: 4px;
  max-width: 100%; // reduces to parent width
  background-color: var(--imTagColStrong); // default iMotor blue
  display: flex;
  align-items: center;

  @include mediaQueryMin($bp-xl) {
    display: inline-flex;
  }
}

.im-tag-wrapper.no-icon {
  padding-left: 7px;
}

.im-tag-bg-colour-alert { // red
  background-color: var(--imTagColAlert);
}

.im-tag-bg-colour-strong { // navy
  background-color: var(--imTagColStrong);
}

.im-tag-bg-colour-approved { // green
  background-color: var(--imTagColApproved);
}

.im-tag-bg-colour-sunny { // yellow
  background-color: var(--imTagColSunny);
}

.im-tag-bg-colour-neutral { // grey
  background-color: var(--imTagColNeutral);
}

.im-tag-text {
  font-size: 12px;
  line-height: 18px;
  color: #fff;
  overflow: hidden; // needed for ellipsis to work
  text-overflow: ellipsis; // needed for ellipsis to work
  white-space: nowrap; // needed for ellipsis to work
}

.im-tag-icon-wrapper {
  width: 20px;
  height: 20px;
}

.im-tag-icon {
  width: 20px;
  height: 20px;
  fill: #fff;
}
