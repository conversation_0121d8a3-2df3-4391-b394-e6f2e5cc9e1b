.action-edit-wrapper {
  margin-bottom: 20px;

  &.ae-wrapper-large {
    margin-bottom: var(--imGapSmall);
  }

  &.ae-wrapper-stock-export,
  &.ae-wrapper-stock-import {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

.action-edit-multi-wrapper {
  display: flex;
  margin-bottom: 20px;

  .ae-link {
    margin-right: 30px;
  }
}

.ae-link {
  display: flex;
  align-items: center;

  &.ae-link-large {
    display: inline-block;
    padding: 10px;
    background-color: var(--imColBlack3);
    border: 1px solid var(--imColBlack12);
    transition: border-color 150ms ease-in, color 150ms ease-in;

    &:hover {
      border-color: var(--imColSecondary);
    }
  }

  .ae-link-group & {
    border-left: 1px solid var(--imActionTriggerMultipleBorderLeftCol);
  }

  .ae-wrapper-stock-export &,
  .ae-wrapper-stock-import & {
    padding: 6px; // TODO:
    padding-left: 10px; // TODO:
    justify-content: left;
    align-items: flex-start;
    transition: background-color 150ms ease-in, color 150ms ease-in;
  }
}

.ae-text {
  @include secondaryFontStack;
  margin-left: 5px;

  .ae-link & {
    font-family: var(--imActionTriggerFontStack);
    @include fontSmoothing;
    color: var(--imActionTriggerTextCol);
  }

  .ae-link:hover & {
    color: var(--imActonTriggerHoverTextCol);
  }
}

.ae-link:hover {
  .ae-wrapper-stock-export &,
  .ae-wrapper-stock-import & {
    background-color: var(--imActionTriggerMultipleBgCol);

    .ae-icon {
      fill: var(--imColSecondary);
      color: var(--imColWhite);
    }
  }
}

.ae-icon {
  fill: var(--imIconActionFill);
  width: 20px;
  height: 20px;

  .ae-link:hover & {
    fill: var(--imIconActionHoverFill);
    transform: scale(1.1);
  }
}

.ae-link-fb-marketplace {
  color: var(--imfBtnTertiaryOutlineTextCol);
  padding: 0 10px 0 5px;
  width: auto;
  display: inline-flex;
  align-items: center;
  border: 1px solid var(--imfBtnTertiaryOutlineBorderCol);
  background-color: var(--imfBtnTertiaryOutlineBgCol);
  margin-bottom: 20px;
  transition: border 150ms ease-in, background-color 150ms ease-in;

  &:hover {
    background-color: var(--imfBtnTertiaryOutlineHoverBgCol);
    border-color: var(--imfBtnTertiaryOutlineHoverBorderCol);

    .ae-text {
      color: var(--imfBtnTertiaryOutlineHoverTextCol);
    }

    .ae-icon {
      fill: var(--imfBtnTertiaryOutlineHoverIconFill);
    }
  }

  .ae-icon {
    fill: var(--imfBtnTertiaryOutlineIconFill);
    width: 30px;
    height: 30px;
  }
}
