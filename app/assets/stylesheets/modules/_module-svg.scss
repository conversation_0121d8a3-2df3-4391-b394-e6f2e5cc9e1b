svg[class*='-icon'] {
  position: relative;
  top: 0;
  left: 0;
  transition: fill 150ms ease-in, transform 150ms ease-in;

  img {
    display: inline;
  }
}

svg {
  position: relative;
  top: 0;
  left: 0;
  @include Transition(fill, 150);
  pointer-events: none;

  img {
    display: inline;
  }
}

.lt-IE9 {
  svg[class*='-icon'] img {
    display: inline;
    vertical-align: top;
  }
}

.fill-white {
  fill: var(--imColWhite);
}

.filter-50 {
  filter: invert(0.5);
}

.logo-imotor {
  color: var(--imLogoCol);
  fill: var(--imLogoFill);
  width: var(--imLogoWidth);
  height: var(--imLogoHeight);
  position: relative;
  top: 4px;

  @include mediaQueryMin($bp-lg) {
    top: 0;
  }
}

.icon-toggle-switch {
  width: 35px;
  height: 22.2px;
  fill: var(--imColBlack40);
  color: var(--imColWhite);
}

.icon-trigger {
  fill: var(--imColWhite);
  margin-right: 0;

  @include mediaQueryBetween($bp-sm, $bp-md) {
    margin-right: 5px;
  }

  @include mediaQueryMin($bp-lg) {
    margin-right: 5px;
  }
}

.icon-hamburger-sm {
  fill: var(--imColWhite);
  width: 19px;
  height: 16px;
}

.icon-more-dots-sm {
  fill: var(--imIndexIconNeutralFill);
  width: 4px;
  height: 16px;
}

.icon-circle-x {
  color: var(--imColWhite);

  &:hover {
    fill: var(--imColRed);
  }
}

.icon-alert {
  &:hover {
    fill: var(--imColRed);
  }
}

.icon-pencil-edit-line {
  fill: var(--imIndexIconNeutralFill);
}

.icon-grabber {
  width: 30px;
  height: 30px;
  fill: var(--imfListRowGrabberIconFill);
  margin-right: 5px;

  .sortable-chosen & {
    fill: var(--imfListRowSelectedGrabberIconFill);
  }

  .dealership-stock & {
    margin-right: 0;
  }
}

.icon-eye {
  width: 30px;
  height: 30px;

  &.lr-icon {
    fill: var(--imIndexIconNeutralFill);
  }

  .lr-link:hover & {
    fill: var(--imIndexIconActionHoverFill);
    transform: var(--imIndexIconActionHoverTransform);
  }
}

.icon-component-list {
  fill: var(--imColBlack20);
}

.users .icon-pencil-edit-line,
.profile .icon-pencil-edit-line {
  width: 30px;
  height: 30px;
}

.icon-circle-plus {
  width: 18px;
  height: 18px;
  fill: var(--imActionIconCirclePlusFill);
  color: var(--imColWhite);
}

.btn-refresh-icon {
  width: 25px;
  height: 25px;
  fill: var(--imIndexIconNeutralFill);
}

.icon-publish {
  width: 20px;
  height: 20px;
  fill: var(--imIndexIconNeutralFill);
}

.icon-export-all {
  width: 23px;
  height: 20px;
  fill: var(--imIndexIconNeutralFill);
}

.icon-export-clear {
  width: 23px;
  height: 20px;
  fill: var(--imIndexIconNeutralFill);
}

.icon-upload {
  width: 17.5px;
  height: 20px;
  fill: var(--imIndexIconNeutralFill);
}

.am-icon {
  height: 19px;
  color: var(--imColWhite);
  fill: var(--imColRed);
}

.icon-dash-desktop {
  width: 30px;
}

.icon-dash-tablet {
  width: 21px;
}

.icon-dash-mobile {
  width: 19px;
}

.icon-no-image {
  width: 50px;
  height: 50px;
  fill: var(--imColWhite);
  color: var(--imColBlack10);
}

.icon-no-asset {
  width: 50px;
  height: 50px;
  fill: var(--imColWhite);
  color: var(--imColBlack10);
}

.icon-img-upload {
  width: 35px;
  height: 35px;
  fill: var(--imIndexIconNeutralFill);
}

.icon-arrow-width {
  width: 20px;
  height: 20px;
  fill: var(--imIndexIconNeutralFill);
}

.icon-close {
  width: 30px;
  height: 30px;
  fill: var(--imColBlack40);
}

.icon-image-module {
  width: 30px;
  height: 30px;
  fill: var(--imIndexIconNeutralFill);
}

// asset uploader
.icon-asset-module {
  width: 25px;
  height: 25px;
  fill: var(--imIndexIconNeutralFill);

  .au-action-wrapper:hover & {
    fill: var(--imIndexIconNeutralHoverFill);
    transform: var(--imIndexIconActionHoverTransform);
  }
}

.icon-im-passive {
  width: 30px;
  height: 30px;
  fill: var(--imIndexIconNeutralFill);

  .fam-action-wrapper:hover & {
    fill: var(--imColSecondary120);
  }
}

.icon-trash {
  width: 30px;
  height: 30px;
  fill: var(--imIndexIconNeutralFill);
}

.icon-iuc {
  width: 35px;
  height: 35px;
  fill: var(--imfAssetUploaderToolboxIconFill);
}

.icon-iu-alert.icon-alert,
.icon-au-alert.icon-alert {
  width: 18px;
  height: 18px;
  fill: var(--imColRed);
  top: -7px;
}

.lr-icon.icon-permission {
  width: 35px;
  height: 35px;
}

.icon-btn-40 {
  width: 40px;
  height: 40px;
  fill: var(--imColWhite);
}

.icon-btn-30 {
  width: 30px;
  height: 30px;
  fill: var(--imColWhite);
}

.icon-important {
  fill: var(--imThemeColAlert);
}

.icon-tick {
  fill: var(--imIconTickFill);
}

.icon-sei-action {
  width: 35px;
  height: 35px;
  fill: var(--imIndexIconNeutralFill);

  .sei-action-link:hover & {
    fill: var(--imIndexIconActionHoverAlertFill);
    transform: var(--imIndexIconActionHoverTransform);
  }
}

.icon-colour-trigger {
  fill: var(--imIndexIconNeutralFill);

  &:hover,
  a:hover & {
    fill: var(--imIndexIconNeutralHoverFill);
    transform: var(--imIndexIconActionHoverTransform);
  }
}

.sei-action-label:hover {
  .icon-sei-action-confirm {
    fill: var(--imColSecondary120);
  }

  .sei-action-btn {
    color: var(--imColSecondary);
  }
}

.sei-action-link-delete:hover {
  .icon-sei-action-delete {
    fill: var(--imColRed);
  }

  .sei-action-text {
    color: var(--imColRed);
  }
}

.clone-icon-wrapper {
  cursor: pointer;
}

.iconSize-30 {
  width: 30px;
  height: 30px;
}

.iconSize-25 {
  width: 25px;
  height: 25px;
}

.iconSize-23 {
  width: 23px;
  height: 23px;
}

.icon-20,
.iconSize-20 {
  width: 20px;
  height: 20px;
}

.icon-disabled {
  fill: var(--imColRed);
}

.qr-code-wrapper {
  display: inline-block;
  border: 1px solid var(--imColBlack8);
  background: var(--imColWhite);
  padding: 10px;
}

.fill-text {
  fill: var(--imColTextPrimary);
}

.fill-green {
  fill: var(--imColSecondary);
}

.icon-status {
  width: 30px;
  height: 30px;
  fill: var(--imColWhite);
}

.svg-icon-image {
  padding: 10px;

  .lr-col & {
    filter: var(--imfListRowColumnSvgImgFilter);
  }
}
