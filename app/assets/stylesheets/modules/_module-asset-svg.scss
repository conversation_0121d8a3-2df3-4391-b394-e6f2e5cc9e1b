.imf-asset-svg {
  height: 30px;
  margin-bottom: 10px;
  filter: var(--imfImgSvgFilter);
  transition: transform 150ms ease-in-out, filter 150ms ease-in-out;

  .imf-gg-child:hover & {
    transform: var(--imfImgSvgHoverTransform);
    filter: var(--imfImgSvgHoverFilter);
  }
  // Firefox doesn't support :has - must be separate from hover pseudo class or Firefox won't recognise hover styles
  .imf-radio-label-wrapper:has(:checked) + & {
    filter: var(--imfImgSvgActiveFilter);
  }
}

.imf-radio-label-wrapper {
  display: flex;
}

.imf-label-nested-svg {
  font-size: var(--imfImgSvgTextSize);
}
