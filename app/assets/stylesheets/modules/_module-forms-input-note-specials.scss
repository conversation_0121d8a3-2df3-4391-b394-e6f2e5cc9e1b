.imf-row-input-note-wrapper {
  padding: 15px;
  border-top: 1px solid var(--imfListRowBorderCol);

  &:first-child {
    border: 0;
  }
}

.imf-row-input-note-item {
  &:first-child {
    margin-bottom: 6px;
  }
}

.imf-row-input-note-flex-wrapper {
  display: flex;

  .imf-select-basic {
    margin-left: 10px;

    @include mediaQueryMin($bp-lg-2) {
      max-width: 350px;
    }
  }
}

.imf-input-specials-flex-wrapper {
  display: flex;
  align-items: center;
  min-width: 70px;

  .imf-input-basic[type='radio'] {
    margin: 0;
  }
}

.imf-label-specials {
  @include fontSmoothing;
  @include primaryFontStackBold;
  font-size: var(--imGlobalFontSize);
  cursor: pointer;
  margin: 0;
  padding-left: 5px;

  &.weighted-bold {
    @include primaryFontStackBold;
  }
}
