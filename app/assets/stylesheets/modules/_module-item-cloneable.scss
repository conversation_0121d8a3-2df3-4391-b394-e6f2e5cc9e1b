.item-cloneable.ls-item {
  background-color: var(--imHomeLinkCloneBgCol);
  border-radius: var(--imHomeLinkCloneBgRadius);
  display: flex;
  justify-content: space-between;
  margin: 0 0 2px -7px;
  align-items: center;
  padding: 2px 4px 2px 7px;
}

// reset styles for Regular Users
.user-regular {
  &.ls-item {
    background-color: transparent;
    display: block;
    margin: 0;
    padding: 0;
  }
}

.item-cloneable .ls-link {
  font-family: var(--imHomeLinkCloneFontStack);
  color: var(--imHomeLinkCloneTextCol);

  &:hover {
    color: var(--imHomeLinkCloneHoverTextCol);
  }
}

.ls-link {
  color: var(--imHomeLinkTextCol);
}

.item-cloneable .icon-duplicate {
  fill: var(--imHomeLinkCloneIconFill);
  transition: fill 150ms ease-in-out, transform 150ms ease-in-out;
}

.lr-link-inline:hover .clone-icon-wrapper .icon-duplicate {
  fill: var(--imHomeLinkCloneHoverIconFill);
  transform: var(--imHomeLinkCloneHoverIconTransform);
}
