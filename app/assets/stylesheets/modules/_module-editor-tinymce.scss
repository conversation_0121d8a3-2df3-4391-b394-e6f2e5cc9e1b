/* Hide by default */
.tinymce {
  visibility: hidden;
}

// Override the tinymce styles coming from the (light) oxide skin.css
// that causes a flash of white on the dark theme
html[data-selected-theme='theme-dark'] .tox.tox .tox-edit-area__iframe {
  background-color: var(--imfFormBgCol);
}

// extra claasses needed for specificity to override default tinymce styles
.page-builder .tox.tox.tox-tinymce-aux {
  z-index: $heaven;  // 9000
}
// extra claasses needed for specificity to override default tinymce styles
.tox.tox-tinymce-aux .tox-dialog-wrap__backdrop {
  background-color: rgba(0, 0, 0, 0.7);
}

// reduce the size of the tinyMCE logo and link
// extra claasses needed for specificity to override default tinymce styles
.tox.tox .tox-statusbar__branding svg {
  width: 37px;
}

.tox.tox .tox-tbtn:hover {
  cursor: pointer;
}

.tox.tox .tox-tbtn--enabled {
  background-color: var(--imColRed);
}

.tox.tox .tox-tbtn--enabled svg {
  fill: var(--imColWhite);
}
