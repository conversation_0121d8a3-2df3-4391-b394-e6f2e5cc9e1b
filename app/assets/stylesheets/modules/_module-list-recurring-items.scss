.lr-item {
  display: flex;
  background-color: var(--imfListRowBgCol);

  &:nth-child(2n) {
    background-color: var(--imfListRowContrastBgCol);
  }

  .working & {
    background-color: var(--imfListRowBgCol);
  }

  .list-recurring-rules & {
    border-top: 1px solid var(--imfListRowBorderCol);

    &:first-child {
      border-top: 0;
    }
  }
}

.lr-item.lr-item-wrap {
  flex-wrap: wrap;
}

// specificity needed to orverride standard pages
.list-placeholder .lr-item.highlighted-item-blue,
.list-placeholder .lr-item.source {
  background-color: var(--imThemePageSecondaryBgCol);
  border-top: 1px solid var(--imThemePageSecondaryBorderCol);

  .icon-grabber {
    fill: var(--imNeutralSoftFill);
  }

  .lr-text {
    color: var(--imThemePageBrandTextCol);
  }
}

// specificity needed to orverride standard pages
.list-placeholder .lr-item.highlighted-item-grn {
  background-color: $green10;
  border-top: 1px solid $green15;

  .icon-grabber {
    fill: var(--imNeutralSoftFill);
  }

  .lr-text,
  .lr-no-link {
    color: var(--imThemePageBrandTextCol);
  }
}

.lr-item.source,
.lr-item.target {
  border-top: 1px solid var(--imfListRowBorderCol);
}

.lr-item.lr-bordered {
  border-top: 1px solid var(--imfListRowBorderCol);
  background-color: var(--imColWhite);

  &:first-child {
    border-top: 0;
  }
}

.lr-item.highlighted-item-blue {
  background-color: var(--imThemePageSecondaryBgCol);

  .icon-grabber {
    fill: var(--imColSecondary);
  }

  .lr-text {
    color: var(--imColSecondary);
  }
}
