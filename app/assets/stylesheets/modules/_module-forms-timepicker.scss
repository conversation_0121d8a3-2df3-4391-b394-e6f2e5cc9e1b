.timepicker-col-wrapper {
  display: flex;
  flex-wrap: wrap;
}

.tp-col-wrapper-2 {
  .tp-col {
    @include gridMachine(1, 0, 15px);

    @include mediaQueryMin($bp-sm) {
      @include gridMachine(2, 2%, 15px);
    }

    @include mediaQueryMin($bp-md) {
      @include gridMachine(1, 0, 15px);
    }

    @include mediaQueryMin($bp-lg) {
      @include gridMachine(2, 2%, 15px);
    }
  }
}

.tp-col {
  .imf-label {
    display: block;
  }
}

.tp-row {
  display: flex;
  justify-content: center;
  align-items: center;
}

.tp-row-item {
  width: 45%;
  display: flex;
  align-items: center;

  .field_with_errors {
    width: 100%;
    flex-direction: row;
    align-items: center;
  }
}

.tp-row-item-sm {
  text-align: center;
  width: 10%;
}

.tp-time-separator {
  font-size: 18px;
  padding: 0 5px;
  align-items: center;
}
