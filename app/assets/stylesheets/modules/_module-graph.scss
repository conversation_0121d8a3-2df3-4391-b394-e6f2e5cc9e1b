.graph-wrapper .lead-item {
  display: none;
}

.lead-item.graph-selected {
  display: block;
}

.dl-item.add_hover_effect {
  background-color: var(--imfDlWrapperHoverBgCol);
}

.dashboard-graph-wrapper {
  margin-bottom: var(--imfFormItemMargin);

  .graph.dashboard-graph-item {
    float: none;
    margin: auto;
    width: 345px;

    @include mediaQueryMin(1130px) {
      float: left;
    }
  }
}

.dashboard-graph-item {
  width: 320px;
  margin: auto;
  float: none;

  @include mediaQueryMin(1130px) {
    float: left;
  }

  table {
    border: 0;

    th,
    td {
      border: 0;
    }

    tr:first-child {
      border-bottom: 1px solid var(--imColBlack7);
    }

    tr:nth-child(2n) {
      background: var(--imColWhite);
    }

    tr td:nth-child(2n),
    tr th:nth-child(2n) {
      width: 60px;
    }

    @include mediaQueryMin($bp-lg) {
      margin: 0;
    }
  }

  .graph-sub-heading {
    color: var(--imColRed);
    margin-bottom: var(--imfFormItemMargin);
  }
}

.legend-table.dashboard-graph-item {
  padding-top: var(--imfFormItemMargin);
}
