.theme-switcher-drawer-knob {
  position: absolute;
  left: var(--imThemeSwitcherKnobLeft);
  top: 50%;
  transform: translateY(-50%);
  width: var(--imThemeSwitcherKnobSize);
  height: var(--imThemeSwitcherKnobSize);
  border-radius: var(--imThemeSwitcherKnobRadius);
  background-color: var(--imThemeSwitcherKnobBgCol);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: var(--imThemeSwitcherKnobPadding);
  transition: background-color 150ms ease-in-out;
}

.icon-theme-brush {
  width: var(--imThemeSwitcherIconSize);
  height: var(--imThemeSwitcherIconSize);
  fill: var(--imThemeSwitcherIconFill);
}

.theme-switcher-wrapper {
  position: fixed;
  top: var(--imThemeSwitcherWrapperTop);
  left: 100%;
  transform: translateX(0);
  cursor: pointer;
  transition: transform 150ms ease-in-out;
  z-index: $space;

  &:hover,
  &.drawer-open {
    transform: translateX(-56px);
  }
}

.theme-switcher-btns-wrapper {
  display: flex;
  flex-direction: column;
  gap: 5px;
  border-radius: 5px 0 0 5px;
  background-color: var(--imThemeSwitcherBtnsWrapperBgCol);
  padding: 10px;
  position: relative;
}

.theme-switcher-btn {
  @include btnReset;
  @include btnBase;
  font-family: var(--imPrimaryFontStack);
  font-size: 12px;
  color: var(--imThemeSwitcherBtnTextCol);
  border: 0;
  border-radius: var(--imThemeBorderRadiusSm);
  background-color: var(--imThemeSwitcherBtnBgCol);
  padding: 10px;
  cursor: pointer;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: var(--imThemeSwitcherBtnHoverBgCol);
    background-color: var(--imColBlack70);
  }

  &.active {
    background-color: var(--imThemeSwitcherBtnActiveBgCol);
  }
}

.theme-variant-btn {
  background-color: var(--imThemeVariantBtnBgCol);
  padding: 0;

  &.active {
    background-color: var(--imThemeSwitcherBtnActiveBgCol);
  }
}

.icon-theme-variant {
  width: var(--imThemeVariantBtnIconSize);
  height: var(--imThemeVariantBtnIconSize);
  fill: var(--imThemeVariantBtnIconFill);

  .theme-switcher-btn:hover &,
  .theme-switcher-btn.active & {
    fill: var(--imThemeVariantBtnHoverIconFill);
  }
}
