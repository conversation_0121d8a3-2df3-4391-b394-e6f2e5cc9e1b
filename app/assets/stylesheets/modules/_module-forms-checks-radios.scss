.imf-control-flex-wrapper {
  display: flex;

  &.imf-label {
    white-space: nowrap;
    margin-right: 30px;
  }
}

.imf-check-radio-nested-wrapper,
.imf-check-radio-nested-relaxed {
  display: flex;
  align-items: center;
  flex-direction: row;
  flex-wrap: wrap;

  label,
  .imf-radio-label,
  .imf-check-label {
    margin: 0;
  }
}

.imf-label-nested-simple-wrapper {
  display: flex;
  align-items: center;
  margin: 0 15px 0 0;

  strong {
    display: contents;
  }
}

.imf-label-nested .imf-input-sm {
  max-width: 90px;
  margin-right: 5px;
}

.imf-check-radio-nested-wrapper {
  margin-right: 15px;
}

.imf-check-radio-nested-relaxed {
  margin-right: 30px;
}

.imf-check-wrapper,
.imf-radio-wrapper {
  display: flex;
  align-items: center;
  white-space: nowrap; // stops label text from breaking on words when label's parent is display: flex

  label { // for form controls wrapped in <label>
    margin: 0;
    display: flex;
    align-items: center;
  }
}

.imf-radio-wrapper,
.imf-check-wrapper,
.check-wrapper-hor {
  margin: 0 0 var(--imfFormItemMargin);

  .imf-radio-label {
    margin: 0;
  }

  &.single-row {
    .imf-input-wrapper {
      margin-bottom: 0;
    }
  }
}

.imf-input-group-wrapper-flex {
  margin-bottom: var(--imfFormItemMargin);

  @include mediaQueryMin($bp-lg) {
    display: flex;

    .imf-radio-wrapper + .imf-radio-wrapper,
    .imf-check-wrapper + .imf-check-wrapper {
      margin-left: 25px;
    }
  }

  &.no-gap {
    margin: 0;
  }
}

.ff-body-wrapper .imf-check-wrapper {
  flex-direction: row;
  align-items: center;

  input[type='checkbox'],
  input[type='radio'] {
    flex-basis: auto;
    margin: 0 8px 0 0;
  }

  label {
    margin: 0;
  }
}

.radios-wrapper-columns-3,
.checks-wrapper-columns-4 {
  .imf-input-wrapper {
    display: block;
    width: 100%;
    margin-bottom: var(--imGapSmall);
  }

  .imf-label,
  .imf-radio-label {
    white-space: break-spaces;
  }

  .imf-label {
    width: 100%;
    display: inline-flex;
    align-items: flex-start;
  }

  input[type='checkbox'],
  input[type='radio'] {
    flex-basis: initial;
    margin: 1px 6px 0 0;
    transform: translateY(2px);
  }
}
