.toast-section-body {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s ease;

  &.hide {
    display: none;
  }
}

.tsb-text-items-wrapper {
  margin: 0 8px;
  padding: 15px 0 15px 60px;
  border-top: 1px solid lightgray;
}

.tsb-text-item {
  padding: 2px 0;
  color: var(--imColWhite);
}

.tsb-text-item-strong {
  color: var(--imColWhite);
}

.toast-netlify-build-user {
  text-transform: capitalize;
}

.tsb-links-wrapper {
  margin-top: 12px;
}

.tsb-link-item {
  color: var(--imColWhite);
  text-decoration: none;
  display: block;

  &:hover {
    text-decoration: underline;
  }
}

.icon-go-link {
  font-family: -apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
  text-decoration: underline;
}
