.icon-garage {
  background-repeat: no-repeat;
  background-position: center center;
  // background-image property cannot be animated using CSS transitions
}

.icon-garage-open {
  // icon colour hack
  background-color: var(--nav2LinkActiveIconFill);
  mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25' viewBox='0 0 50 30' preserveAspectRatio='xMidYMid meet' %3E%3Cpath d='M9.2,12.3h1.6V14v10.1v1.7c0,0.8,0.7,1.7,1.7,1.7h2.8V14.7c0-0.9,0.8-1.7,1.7-1.7h16.2c0.9,0,1.6,0.7,1.7,1.7v12.7h2.8c0.9,0,1.7-0.8,1.7-1.7V24V14v-1.7H41c1.1,0,1.4-1.4,0.5-1.9C35.9,7.5,26.1,2.3,25,2.3S14.1,7.5,8.7,10.4C7.7,10.9,8.1,12.3,9.2,12.3z M31.2,27c0,0.4-0.3,0.7-0.7,0.7h-0.7c-0.4,0-0.7-0.3-0.7-0.7v-0.7h-8.3V27c0,0.4-0.3,0.7-0.7,0.7h-0.7c-0.4,0-0.7-0.3-0.7-0.7v-5.5l1.4-4.2c0.2-0.5,0.5-0.7,1-0.7h7.6c0.5,0,0.9,0.2,1,0.7l1.4,4.2V27H31.2z M20.5,24c0.2,0.2,0.4,0.3,0.7,0.3c0.3,0,0.5-0.1,0.7-0.3c0.2-0.2,0.3-0.4,0.3-0.7c0-0.3-0.1-0.6-0.3-0.7s-0.4-0.3-0.7-0.3c-0.3,0-0.5,0.1-0.7,0.3c-0.2,0.2-0.3,0.4-0.3,0.7C20.2,23.5,20.3,23.8,20.5,24L20.5,24z M28.1,24c0.2,0.2,0.4,0.3,0.7,0.3c0.3,0,0.5-0.1,0.7-0.3c0.2-0.2,0.3-0.4,0.3-0.7c0-0.3-0.1-0.6-0.3-0.7c-0.2-0.2-0.4-0.3-0.7-0.3c-0.3,0-0.5,0.1-0.7,0.3s-0.3,0.4-0.3,0.7C27.8,23.5,27.9,23.8,28.1,24L28.1,24z M20.2,20.8h9.7l-1-3.1h-7.6L20.2,20.8z'/%3E%3C/svg%3E");
  mask-repeat: no-repeat;
  mask-position: center;
  mask-size: contain;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25' viewBox='0 0 50 30' preserveAspectRatio='xMidYMid meet' %3E%3Cpath d='M9.2,12.3h1.6V14v10.1v1.7c0,0.8,0.7,1.7,1.7,1.7h2.8V14.7c0-0.9,0.8-1.7,1.7-1.7h16.2c0.9,0,1.6,0.7,1.7,1.7v12.7h2.8c0.9,0,1.7-0.8,1.7-1.7V24V14v-1.7H41c1.1,0,1.4-1.4,0.5-1.9C35.9,7.5,26.1,2.3,25,2.3S14.1,7.5,8.7,10.4C7.7,10.9,8.1,12.3,9.2,12.3z M31.2,27c0,0.4-0.3,0.7-0.7,0.7h-0.7c-0.4,0-0.7-0.3-0.7-0.7v-0.7h-8.3V27c0,0.4-0.3,0.7-0.7,0.7h-0.7c-0.4,0-0.7-0.3-0.7-0.7v-5.5l1.4-4.2c0.2-0.5,0.5-0.7,1-0.7h7.6c0.5,0,0.9,0.2,1,0.7l1.4,4.2V27H31.2z M20.5,24c0.2,0.2,0.4,0.3,0.7,0.3c0.3,0,0.5-0.1,0.7-0.3c0.2-0.2,0.3-0.4,0.3-0.7c0-0.3-0.1-0.6-0.3-0.7s-0.4-0.3-0.7-0.3c-0.3,0-0.5,0.1-0.7,0.3c-0.2,0.2-0.3,0.4-0.3,0.7C20.2,23.5,20.3,23.8,20.5,24L20.5,24z M28.1,24c0.2,0.2,0.4,0.3,0.7,0.3c0.3,0,0.5-0.1,0.7-0.3c0.2-0.2,0.3-0.4,0.3-0.7c0-0.3-0.1-0.6-0.3-0.7c-0.2-0.2-0.4-0.3-0.7-0.3c-0.3,0-0.5,0.1-0.7,0.3s-0.3,0.4-0.3,0.7C27.8,23.5,27.9,23.8,28.1,24L28.1,24z M20.2,20.8h9.7l-1-3.1h-7.6L20.2,20.8z'/%3E%3C/svg%3E");
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  -webkit-mask-size: contain;
}

.icon-garage-closed {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25' viewBox='0 0 50 30' preserveAspectRatio='xMidYMid meet' %3E%3Cpath d='M9,12.4h1.6v1.7v10.1v1.7c0,0.8,0.8,1.7,1.7,1.7h3.1V17c0-1.2,1-2.1,2.1-2.1h14.9c1.2,0,2.1,0.9,2.1,2.1v10.6h3.1c0.9,0,1.7-0.8,1.7-1.7v-1.7V14.1v-1.7H41c1.1,0,1.4-1.4,0.5-1.9C36.1,7.6,26.2,2.3,25,2.3S14,7.6,8.5,10.5C7.6,11,8,12.4,9,12.4z M17.6,17v2.1h14.9V17H17.6z M17.6,21.2v2.1h14.9v-2.1H17.6z M17.6,25.5v2.1h14.9v-2.1H17.6z' fill='#{svg-encode-hex($garageIconFill)}' /%3E%3C/svg%3E");

  .n2-link:hover & {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25' viewBox='0 0 50 30' preserveAspectRatio='xMidYMid meet' %3E%3Cpath d='M9,12.4h1.6v1.7v10.1v1.7c0,0.8,0.8,1.7,1.7,1.7h3.1V17c0-1.2,1-2.1,2.1-2.1h14.9c1.2,0,2.1,0.9,2.1,2.1v10.6h3.1c0.9,0,1.7-0.8,1.7-1.7v-1.7V14.1v-1.7H41c1.1,0,1.4-1.4,0.5-1.9C36.1,7.6,26.2,2.3,25,2.3S14,7.6,8.5,10.5C7.6,11,8,12.4,9,12.4z M17.6,17v2.1h14.9V17H17.6z M17.6,21.2v2.1h14.9v-2.1H17.6z M17.6,25.5v2.1h14.9v-2.1H17.6z' fill='#{svg-encode-hex($garageIconHoverFill)}' /%3E%3C/svg%3E");
  }
}

// garage open
.garage {
  .icon-garage {
    // icon colour hack
    background-color: var(--nav2LinkActiveIconFill);
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25' viewBox='0 0 50 30' preserveAspectRatio='xMidYMid meet' %3E%3Cpath d='M9.2,12.3h1.6V14v10.1v1.7c0,0.8,0.7,1.7,1.7,1.7h2.8V14.7c0-0.9,0.8-1.7,1.7-1.7h16.2c0.9,0,1.6,0.7,1.7,1.7v12.7h2.8c0.9,0,1.7-0.8,1.7-1.7V24V14v-1.7H41c1.1,0,1.4-1.4,0.5-1.9C35.9,7.5,26.1,2.3,25,2.3S14.1,7.5,8.7,10.4C7.7,10.9,8.1,12.3,9.2,12.3z M31.2,27c0,0.4-0.3,0.7-0.7,0.7h-0.7c-0.4,0-0.7-0.3-0.7-0.7v-0.7h-8.3V27c0,0.4-0.3,0.7-0.7,0.7h-0.7c-0.4,0-0.7-0.3-0.7-0.7v-5.5l1.4-4.2c0.2-0.5,0.5-0.7,1-0.7h7.6c0.5,0,0.9,0.2,1,0.7l1.4,4.2V27H31.2z M20.5,24c0.2,0.2,0.4,0.3,0.7,0.3c0.3,0,0.5-0.1,0.7-0.3c0.2-0.2,0.3-0.4,0.3-0.7c0-0.3-0.1-0.6-0.3-0.7s-0.4-0.3-0.7-0.3c-0.3,0-0.5,0.1-0.7,0.3c-0.2,0.2-0.3,0.4-0.3,0.7C20.2,23.5,20.3,23.8,20.5,24L20.5,24z M28.1,24c0.2,0.2,0.4,0.3,0.7,0.3c0.3,0,0.5-0.1,0.7-0.3c0.2-0.2,0.3-0.4,0.3-0.7c0-0.3-0.1-0.6-0.3-0.7c-0.2-0.2-0.4-0.3-0.7-0.3c-0.3,0-0.5,0.1-0.7,0.3s-0.3,0.4-0.3,0.7C27.8,23.5,27.9,23.8,28.1,24L28.1,24z M20.2,20.8h9.7l-1-3.1h-7.6L20.2,20.8z'/%3E%3C/svg%3E");
    mask-repeat: no-repeat;
    mask-position: center;
    mask-size: contain;
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25' viewBox='0 0 50 30' preserveAspectRatio='xMidYMid meet' %3E%3Cpath d='M9.2,12.3h1.6V14v10.1v1.7c0,0.8,0.7,1.7,1.7,1.7h2.8V14.7c0-0.9,0.8-1.7,1.7-1.7h16.2c0.9,0,1.6,0.7,1.7,1.7v12.7h2.8c0.9,0,1.7-0.8,1.7-1.7V24V14v-1.7H41c1.1,0,1.4-1.4,0.5-1.9C35.9,7.5,26.1,2.3,25,2.3S14.1,7.5,8.7,10.4C7.7,10.9,8.1,12.3,9.2,12.3z M31.2,27c0,0.4-0.3,0.7-0.7,0.7h-0.7c-0.4,0-0.7-0.3-0.7-0.7v-0.7h-8.3V27c0,0.4-0.3,0.7-0.7,0.7h-0.7c-0.4,0-0.7-0.3-0.7-0.7v-5.5l1.4-4.2c0.2-0.5,0.5-0.7,1-0.7h7.6c0.5,0,0.9,0.2,1,0.7l1.4,4.2V27H31.2z M20.5,24c0.2,0.2,0.4,0.3,0.7,0.3c0.3,0,0.5-0.1,0.7-0.3c0.2-0.2,0.3-0.4,0.3-0.7c0-0.3-0.1-0.6-0.3-0.7s-0.4-0.3-0.7-0.3c-0.3,0-0.5,0.1-0.7,0.3c-0.2,0.2-0.3,0.4-0.3,0.7C20.2,23.5,20.3,23.8,20.5,24L20.5,24z M28.1,24c0.2,0.2,0.4,0.3,0.7,0.3c0.3,0,0.5-0.1,0.7-0.3c0.2-0.2,0.3-0.4,0.3-0.7c0-0.3-0.1-0.6-0.3-0.7c-0.2-0.2-0.4-0.3-0.7-0.3c-0.3,0-0.5,0.1-0.7,0.3s-0.3,0.4-0.3,0.7C27.8,23.5,27.9,23.8,28.1,24L28.1,24z M20.2,20.8h9.7l-1-3.1h-7.6L20.2,20.8z'/%3E%3C/svg%3E");
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
    -webkit-mask-size: contain;
  }

  .n2-link-1 {
    background-color: var(--nav2LinkActiveBgCol);

    .n2-text {
      color: var(--nav2LinkActiveTextCol);
    }

    .n2-icon {
      fill: var(--nav2LinkActiveIconFill);
    }
  }
}

// garage closed
.dashboard,
.cms,
.dealership-stock,
.config {
  .icon-garage {
    // icon colour hack
    background-color: var(--nav2LinkIconFill);
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25' viewBox='0 0 50 30' preserveAspectRatio='xMidYMid meet' %3E%3Cpath d='M9,12.4h1.6v1.7v10.1v1.7c0,0.8,0.8,1.7,1.7,1.7h3.1V17c0-1.2,1-2.1,2.1-2.1h14.9c1.2,0,2.1,0.9,2.1,2.1v10.6h3.1c0.9,0,1.7-0.8,1.7-1.7v-1.7V14.1v-1.7H41c1.1,0,1.4-1.4,0.5-1.9C36.1,7.6,26.2,2.3,25,2.3S14,7.6,8.5,10.5C7.6,11,8,12.4,9,12.4z M17.6,17v2.1h14.9V17H17.6z M17.6,21.2v2.1h14.9v-2.1H17.6z M17.6,25.5v2.1h14.9v-2.1H17.6z' /%3E%3C/svg%3E");
    mask-repeat: no-repeat;
    mask-position: center;
    mask-size: contain;
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25' viewBox='0 0 50 30' preserveAspectRatio='xMidYMid meet' %3E%3Cpath d='M9,12.4h1.6v1.7v10.1v1.7c0,0.8,0.8,1.7,1.7,1.7h3.1V17c0-1.2,1-2.1,2.1-2.1h14.9c1.2,0,2.1,0.9,2.1,2.1v10.6h3.1c0.9,0,1.7-0.8,1.7-1.7v-1.7V14.1v-1.7H41c1.1,0,1.4-1.4,0.5-1.9C36.1,7.6,26.2,2.3,25,2.3S14,7.6,8.5,10.5C7.6,11,8,12.4,9,12.4z M17.6,17v2.1h14.9V17H17.6z M17.6,21.2v2.1h14.9v-2.1H17.6z M17.6,25.5v2.1h14.9v-2.1H17.6z' /%3E%3C/svg%3E");
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
    -webkit-mask-size: contain;
  }

  .n2-link:hover {
    .icon-garage {
      // icon colour hack
      background-color: var(--nav2LinkHoverIconFill);
      mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25' viewBox='0 0 50 30' preserveAspectRatio='xMidYMid meet' %3E%3Cpath d='M9,12.4h1.6v1.7v10.1v1.7c0,0.8,0.8,1.7,1.7,1.7h3.1V17c0-1.2,1-2.1,2.1-2.1h14.9c1.2,0,2.1,0.9,2.1,2.1v10.6h3.1c0.9,0,1.7-0.8,1.7-1.7v-1.7V14.1v-1.7H41c1.1,0,1.4-1.4,0.5-1.9C36.1,7.6,26.2,2.3,25,2.3S14,7.6,8.5,10.5C7.6,11,8,12.4,9,12.4z M17.6,17v2.1h14.9V17H17.6z M17.6,21.2v2.1h14.9v-2.1H17.6z M17.6,25.5v2.1h14.9v-2.1H17.6z'/%3E%3C/svg%3E");
      mask-repeat: no-repeat;
      mask-position: center;
      mask-size: contain;
      -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%25' height='100%25' viewBox='0 0 50 30' preserveAspectRatio='xMidYMid meet' %3E%3Cpath d='M9,12.4h1.6v1.7v10.1v1.7c0,0.8,0.8,1.7,1.7,1.7h3.1V17c0-1.2,1-2.1,2.1-2.1h14.9c1.2,0,2.1,0.9,2.1,2.1v10.6h3.1c0.9,0,1.7-0.8,1.7-1.7v-1.7V14.1v-1.7H41c1.1,0,1.4-1.4,0.5-1.9C36.1,7.6,26.2,2.3,25,2.3S14,7.6,8.5,10.5C7.6,11,8,12.4,9,12.4z M17.6,17v2.1h14.9V17H17.6z M17.6,21.2v2.1h14.9v-2.1H17.6z M17.6,25.5v2.1h14.9v-2.1H17.6z'/%3E%3C/svg%3E");
      -webkit-mask-repeat: no-repeat;
      -webkit-mask-position: center;
      -webkit-mask-size: contain;
    }
  }
}

.dashboard {
  .n2-link-2 {
    background-color: var(--nav2LinkActiveBgCol);

    .n2-text {
      color: var(--nav2LinkActiveTextCol);
    }

    .n2-icon {
      fill: var(--nav2LinkActiveIconFill);
    }
  }
}

.cms {
  .n2-link-3 {
    background-color: var(--nav2LinkActiveBgCol);

    .n2-text {
      color: var(--nav2LinkActiveTextCol);
    }

    .n2-icon {
      fill: var(--nav2LinkActiveIconFill);
    }
  }
}

.dealership-stock {
  .n2-link-4 {
    background-color: var(--nav2LinkActiveBgCol);

    .n2-text {
      color: var(--nav2LinkActiveTextCol);
    }

    .n2-icon {
      fill: var(--nav2LinkActiveIconFill);
    }
  }
}

.config {
  .n2-link-5 {
    background-color: var(--nav2LinkActiveBgCol);

    .n2-text {
      color: var(--nav2LinkActiveTextCol);
    }

    .n2-icon {
      fill: var(--nav2LinkActiveIconFill);
    }
  }
}

.nav-2-wrapper {
  background-color: var(--nav2WrapperBgCol);
  border-bottom: var(--nav2WrapperBorderBottom);
  box-shadow: var(--nav2WrapperBoxShadow);
  z-index: $smog;
  flex: none;
  position: relative;
}

.n2-list-buttons-wrapper {
  display: flex;
  flex-direction: column;

  @include mediaQueryMin($bp-lg) {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}

.n2-build-links-wrapper {
  display: flex;
  justify-content: center;
  flex-direction: row;
  padding: 8px 0;

  @include mediaQueryMax($bp-sm) {
    flex-direction: column;
    align-items: center;

    .toggleAdvancedWrapper,
    .menu-basic-variant {
      margin: 0 auto 5px auto;
    }
  }

  @include mediaQueryMin($bp-lg) {
    align-items: center;
    justify-content: flex-end;
    padding: 0;
  }
}

.n2-list {
  height: 100%;
  display: flex;
  flex-basis: 100%;
  border-bottom: 1px solid var(--nav2ListBorderBottomColMobile);

  @include mediaQueryMin($bp-lg) {
    border-bottom: 0;
  }
}

.n2-item {
  flex-basis: 25%;

  @include mediaQueryMin($bp-lg) {
    flex-basis: 120px;
  }
}

.n2-link {
  height: 53px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--nav2LinkBgCol);
  transition: background-color 150ms ease-in-out;

  @include mediaQueryMin($bp-md) {
    height: 70px;
  }

  &:hover {
    background-color: var(--nav2LinkHoverBgCol);

    .n2-text {
      color: var(--nav2LinkActiveTextCol);
    }

    .n2-icon {
      fill: var(--nav2LinkHoverIconFill);
    }
  }
}

.n2-icon {
  width: calc(60px / 1.4);
  height: calc(30px / 1.4);
  fill: var(--nav2LinkIconFill);

  @include mediaQueryMin($bp-md) {
    width: 60px;
    height: 30px;
  }
}

.n2-text {
  font-family: var(--nav2LinkTextFontStack);
  @include fontSmoothing;
  font-size: 13px;
  line-height: 22px;
  color: var(--nav2LinkTextCol);
  transition: color 150ms ease-in-out;
  height: 21px;

  @include mediaQueryMin($bp-md) {
    font-size: 13px;
  }
}
