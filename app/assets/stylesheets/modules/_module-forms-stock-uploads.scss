.stock-uploads-action-wrapper {
  padding: 10px 0 20px 0;
}

.su-action-group {
  display: flex;
  justify-content: flex-end;

  .su-action-wrapper {
    margin-right: 0;
    margin-left: 25px;
  }
}

.su-action-wrapper {
  padding: 10px 0;
  margin-right: 15px;
  cursor: pointer;

  &:hover {
    .icon-circle-plus,
    .icon-upload {
      fill: var(--imColSecondary);
    }
  }
}

.su-pending-wrapper {
  margin-bottom: 15px;
}

.stock {
  .form-image-module-wrapper:first-child {
    border-top: 1px solid var(--imColBlack12);
  }
}

.su-action-text {
  font-weight: bold;
  color: var(--imActionTriggerCol);

  .su-action-wrapper:hover & {
    color: var(--imActionTriggerHoverTextCol);
  }
}

.su-upload-error-wrapper {
  padding: 0 10px;
}

.su-error-text {
  font-size: 12px;
  line-height: 16px;
  margin: 0;
}

.su-error-item {
  color: var(--imColRed);
}

.form-image-module-wrapper {
  &.status-error .form-asset-module-footer {
    border-left: 5px solid var(--imColRed);
  }
}

.su-filter-wrapper {
  margin-bottom: 10px;

  @include mediaQueryMin($bp-xl) {
    display: flex;
    justify-content: space-between;
  }

  .dealership-stock & {
    @include mediaQueryMin($bp-xl) {
      justify-content: flex-start;
      flex-wrap: wrap;
      background-color: var(--imfStockFilterWrapperBgCol);
      border: 1px solid var(--imfStockFilterWrapperBorderCol);
      padding: 15px;
    }
  }
}

.su-keywords-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  @include mediaQueryMin($bp-xl) {
    flex: 1.5;
    margin-bottom: 0;
    margin-right: 10px;
  }

  .dealership-stock & {
    @include mediaQueryMin($bp-xl) {
      margin-bottom: 0;
      margin-right: 30px;
      flex: 1;
    }
  }

  .search {
    flex-grow: 0;
    width: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" viewBox="0 0 50 40" preserveAspectRatio="xMidYMid meet"><path d="M12.6,32.3c1,1,2.7,0.9,3.9-0.2l5.7-5.7c0.1-0.1,0.2-0.3,0.3-0.4c1.6,1,3.4,1.5,5.4,1.5c5.7,0,10.3-4.6,10.3-10.3S33.5,7,27.9,7s-10.3,4.6-10.3,10.3c0,1.9,0.5,3.6,1.4,5.1c-0.2,0.1-0.3,0.2-0.5,0.4l-5.7,5.7C11.7,29.6,11.6,31.3,12.6,32.3z M27.9,24.2c-3.8,0-6.9-3.1-6.9-6.9s3.1-6.9,6.9-6.9s6.9,3.1,6.9,6.9S31.7,24.2,27.9,24.2z" fill="%23b2b2b2"/></svg>');
    background-repeat: no-repeat;
    background-position: right center;
    margin: 1rem 0;
  }
}

.su-sort-wrapper {
  flex: 1;
  display: flex;
  align-items: center;
}

.su-sort-label,
.su-filter-label {
  @include primaryFontStackBold;
  white-space: nowrap;
  margin: 0 10px 0 0;
}

.su-checkboxes-wrapper {
  @include mediaQueryMin($bp-sm) {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  @include mediaQueryMin($bp-lg) {
    flex: 3;
  }

  .dealership-stock & {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: flex-start;
    flex: initial;
    width: 100%;
    padding-top: 5px;
  }
}

.su-check-item-wrapper {
  display: flex;
  width: 50%;
  padding-top: 5px;

  @include mediaQueryMin($bp-lg) {
    width: 25%;
  }

  @include mediaQueryMin($bp-xl) {
    width: 20%;
  }
}

.su-filter-label {
  margin: 0 10px 0 0;
}
