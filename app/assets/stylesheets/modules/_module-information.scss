.info-heading-cta-wrapper {
  background-color: var(--imCmsSupportCtaWrapperBgCol);
  text-align: center;
  position: relative;

  @include mediaQueryMin($bp-lg) {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    flex: none;
  }
}

.info-heading-cta {
  @include primaryFontStackLight;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  color: var(--imColTextPrimary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  padding: 10px;
  margin: 0;

  @include mediaQueryMin($bp-lg) {
    font-size: 18px;
    line-height: 26px;
    text-align: left;
    padding: 25px 0 25px 1.5rem;
  }
}

.info-sidebar {
  background-color: var(--imCmsSupportAsideBgCol);

  @include mediaQueryMin($bp-md) {
    flex: 0.7 1 200px;
  }
}

.is-heading {
  @include primaryFontStackLight;
  font-size: 24px;
  line-height: 26px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 10px 0 20px 0;
}

.is-list {
  padding-left: 20px;
}

.is-item {
  @include primaryFontStackLight;
  font-size: 20px;
  line-height: 24px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  list-style: disc;
  margin-bottom: 20px;
}

.info-content.section-no-nav {
  flex: 2;
  display: block;
}

.ic-image {
  border: 1px solid var(--imCmsSupportImgBorderCol);
  box-shadow: 8px 8px 0 0 var(--imCmsSupportImgShadow);
}

.ic-image-dashboard {
  max-width: 850px;
  width: 100%;
}

.ic-image-cms {
  max-width: 1026px;
  width: 100%;
}

.ic-image-stock {
  max-width: 1109px;
  width: 100%;
  border: 0;
  box-shadow: none;
}
