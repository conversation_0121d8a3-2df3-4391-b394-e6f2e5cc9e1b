.list-placeholder {
  min-height: var(--imfListRowHeight); // min height of one item
  background-color: var(--imfPlaceholderListWrapperBgCol);
  position: relative;

  &::after {
    text-align: center;
    position: absolute;
    width: 100%;
    left: 50%;
    transform: translateX(-50%);
    top: 10px;
    z-index: $default; // 0 place text behind lr-item
    overflow: hidden; // needed for ellipsis to work
    text-overflow: ellipsis; // needed for ellipsis to work
    white-space: nowrap; // needed for ellipsis to work
    padding: 0 10px; // needed so text doesn't touch wrapper

    .hero-banners-dealership & {
      content: 'There are currently no dealership banners.';
    }

    .hero-banners-brand & {
      content: 'There are currently no brand banners.';
    }

    .dashboard-links & {
      content: 'There are currently no dashboard links.';
    }

    .dashboard-links-global & {
      content: 'There are currently no unused global dashboard links.';
    }

    .dealership-valuation-questions & {
      content: 'There are currently no dealership valuation questions.';
    }

    .dealership-valuation-questions-global & {
      content: 'There are currently no global valuation questions.';
    }

    .dynamic-forms-global & {
      content: 'There are currently no unused global dynamic forms.';
    }

    .promos-dealership & {
      content: 'There are currently no dealership promos.';
    }

    .promos-brand & {
      content: 'There are currently no brand promos.';
    }

    .list-component-props & {
      content: 'There are currently no props.';
    }
  }

  &.page-section::after {
    content: 'There are currently no pages in this section.';
  }

  &.pages-without-sections::after {
    content: 'There are currently no uncategorised pages.';
  }

  &.footer-links-section::after {
    content: 'There are currently no footer links in this section.';
  }

  &.footer-links-without-sections::after {
    content: 'There are currently no uncategorised footer links.';
  }

  &.list-new-vehicle-showroom {
    border: 1px solid var(--imThemePageTertiaryBorderCol);
    background-color: var(--imThemePageTertiaryBgCol);
    border-top: 0;

    &::after {
      @include primaryFontStackBold;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      color: var(--imColSecondary);
      content: 'Vehicle links will appear in this section.';
    }
  }

  .hero-banners-brand & {
    .banner-edit-btn {
      display: none;
    }
  }
}

.list-placeholder .lr-item {
  position: relative;
  z-index: $surface; // 1 place lr-item above placeholder text
  background-color: var(--imfPlaceholderListRowBgCol); // all standard page bgs are white to contrast with non-standard and shared page bgs
  border-top: 1px solid var(--imfPlaceholderListRowBorderCol);

  &:first-child {
    border-top: 0;
  }

  // Non-Standard Pages
  &.highlighted-item-blue {
    background-color: var(--imfPlaceholderListRowSecondaryBgCol);
    border-top: 1px solid var(--imfPlaceholderListRowSecondaryBorderCol);

    &:first-child {
      border-top: 0;
    }
  }

  // Brand, Group and Global Items
  &.highlighted-item-grn {
    background-color: var(--imfPlaceholderListRowTertiaryBgCol);
    border-top: 1px solid var(--imfPlaceholderListRowBorderCol);

    &:first-child {
      border-top: 0;
    }
  }

  .lr-col {
    border-left-color: var(--imfPlaceholderListRowBorderCol);
  }
}
