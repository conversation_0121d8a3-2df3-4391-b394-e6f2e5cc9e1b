.home .n1-link-1,
.users .n1-link-2,
.profile .n1-link-3,
.help .n1-link-4,
.stock-admin .n1-link-6,
.site-admin .n1-link-7 {
  background-color: var(--nav1ActiveBgCol);

  &::before {
    width: var(--nav1ActiveborderBottomWidth);
    border-bottom-color: var(--nav1ActiveborderBottomCol);
  }

  &:hover::before {
    width: var(--nav1ActiveborderBottomWidth);
  }

  .n1-icon {
    fill: var(--nav1ActiveIconFill);
  }

  .n1-text {
    color: var(--nav1ActiveTextCol);
  }
}

.n1-list {
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 5px;

  @include mediaQueryMin($bp-lg) {
    margin-top: 0;
  }
}

.n1-item {
  flex: auto;
}

.n1-link {
  background-color: var(--nav1BgCol);
  padding: var(--nav1PaddingMobile);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;

  @include mediaQueryMin($bp-lg) {
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: var(--nav1PaddingDesktop);
  }

  &:hover::before {
    width: var(--nav1HoverBorderBottomWidth);
    border-bottom-color: var(--nav1HoverBorderBottomCol);
  }

  &::before {
    content: '';
    border-bottom-width: var(--nav1borderBottomThickness);
    border-bottom-style: solid;
    border-bottom-color: transparent;
    position: absolute;
    bottom: var(--nav1borderBottomPosition);
    width: 0;
    margin-left: var(--nav1borderBottomMarginLeft);
    transition: var(--nav1borderBottomTransition);
  }

  &:hover {
    text-decoration: none;

    .n1-icon {
      fill: var(--nav1HoverIconFill);
      transform: var(--nav1HoverIconScale);
    }
  }
}

.n1-icon {
  width: 30px;
  height: 30px;
  fill: var(--nav1IconFill);
  transition: fill 150ms ease-in, transform 150ms ease-in;
}

.n1-text {
  font-family: var(--nav1TextFontStack);
  @include fontSmoothing;
  font-size: 12px;
  line-height: 16px;
  color: var(--nav1TextCol);
  margin-bottom: 5px;
  transition: color 150ms ease-in-out;

  @include mediaQueryMin($bp-md) {
    font-size: 12px;
    margin-left: 4px;
    margin-bottom: 0;
  }

  .n1-link:hover & {
    color: var(--nav1HoverTextCol);
  }
}
