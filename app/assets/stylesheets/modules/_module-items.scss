.item-enabled,
.item-true {
  color: var(--imfItemEnabledTextCol);
}

.item-disabled,
.item-false {
  color: var(--imfItemDisabledTextCol);
}

.icon-enabled {
  fill: var(--imfItemEnabledFillCol);
}

.item-brand {
  padding: 2px 8px 2px 3px; // compensate for width of icon on the left
  background-color: var(--imfINoteBrandBgCol);
  border-radius: 10px;
  border: 1px solid var(--imfINoteBrandBorderCol);
  height: 20px;
  display: inline-flex;
  align-items: center;
  margin-left: 5px;

  &.item-brand-no-icon {
    padding: 2px 8px; // even padding when there is no icon
    margin-left: 0;
  }
}
