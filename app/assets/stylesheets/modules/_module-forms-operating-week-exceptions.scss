.list-recurring.ow-exceptions-list {
  border: 0;
}

.ow-exceptions-list {
  .lr-item-heading.lr-item {
    border: 0;
  }

  .lr-item {
    display: flex;
    border: 1px solid var(--imfListRowBgHoursBorderCol);
    border-top: 0;
  }

  .lr-item:nth-child(2n) {
    background-color: var(--imfListRowBgHoursContrastBgCol);
  }

  .lr-item:last-child {
    border: 0;
    background-color: var(--imfListRowBgHoursContrastBgCol);
  }

  .lr-col-sm {
    align-items: center;
  }

  .lr-time-unit {
    flex-direction: row;
    align-items: center;

    select {
      width: calc(48% - 10px);
      margin: 0;
    }

    .field_with_errors {
      flex-direction: row;
    }
  }

  .action-edit-wrapper {
    margin: 10px 0;
  }

  input[type='checkbox'] {
    margin: 0;
  }
}

.ow-exceptions-wrapper {
  display: flex;
  flex-wrap: wrap;

  .lr-item {
    width: 100%;
  }

  .field_with_errors {
    width: 100%;
  }

  .lr-time-separator {
    font-size: 18px;
    padding: 0 5px;
  }
}
