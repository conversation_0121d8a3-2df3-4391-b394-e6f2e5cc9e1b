.lr-action-wrapper {
  cursor: pointer;
  padding: 5px;

  .profile &,
  .users & {
    display: flex;
    align-items: center;
  }

  &:hover {
    .lr-action-icon {
      fill: var(--imIconTriggersHoverFill);
      transform: var(--imIconTransform);
    }

    .icon-trash {
      fill: var(--imColRed);
    }
  }

  .lr-action-icon {
    margin-right: 8px;
  }
}

.lr-action-text {
  color: var(--imActionTriggerTextCol);
  @include Transition(color, 150);

  .lr-action-wrapper:hover & {
    color: var(--imActionTriggerHoverTextCol);
  }
}
