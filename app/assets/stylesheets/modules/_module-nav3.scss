.nav3-nav4-wrapper {
  display: flex;
}

.nav3-wrapper {
  background-color: var(--nav3WrapperBgCol);
  flex: 0 0 50%;
  order: 1;
  position: relative;

  @include mediaQueryMin($bp-md) {
    flex: 0 0 12rem;
  }

  @include mediaQueryMin($bp-lg) {
    &.nav3Collapsed {
      flex: 0 0 56px;
    }
  }
}

.n3-list {
  margin: 0 0 0 0;
}

.n3-link {
  padding: var(--nav3PaddingMobile);
  display: flex;
  align-items: center;
  background-color: var(--nav3BgCol);
  position: relative;
  transition: background-color 150ms ease-in;

  @include mediaQueryMin($bp-md) {
    padding: var(--nav3PaddingDesktop);
  }

  &:hover {
    background-color: var(--nav3HoverBgCol);
  }

  .selected & {
    @include nav3ActiveStyles;
  }

  &.dead-link,
  &.dead-link:hover {
    background-color: transparent;

    .n3-icon {
      fill: var(--imColBlack60);
    }

    .n3-text {
      color: var(--imColBlack60);
    }
  }
}

.n3-icon {
  fill: var(--nav3IconFill);
  width: 30px;
  height: 30px;
  transition: fill 150ms ease-in, transform 150ms ease-in;

  @include mediaQueryMin($bp-lg) {
    width: 36px;
    height: 36px;
  }

  .n3-link:hover & {
    fill: var(--nav3HoverIconFill);
    transform: var(--nav3HoverIconScale);
  }
}

.n3-text {
  font-family: var(--nav3TextFontStack);
  @include fontSmoothing;
  font-size: 13px;
  line-height: 14px;
  color: var(--nav3TextCol);
  margin-left: 4px;
  flex: 1;
  transition: color 150ms ease-in;

  @include mediaQueryMin($bp-md) {
    font-size: 13px;
  }

  @include mediaQueryMin($bp-lg) {
    .nav3Collapsed & {
      @include nav3Nav4CollapsedTooltip;
    }

    .nav3Collapsed .n3-link:hover & {
      @include nav3Nav4CollapsedTooltipHover;
    }
  }

  .n3-link:hover & {
    color: var(--nav3HoverTextCol);
  }
}

// hard-coded `selected` items

.statistics {
  .n3-link-1 {
    @include nav3ActiveStyles;
  }
}

.enquiries {
  .n3-link-2 {
    @include nav3ActiveStyles;
  }
}
