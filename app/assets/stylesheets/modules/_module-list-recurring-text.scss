.lr-link,
.lr-no-link {
  display: flex;
  flex: 0 0 var(--imfListRowHeight);
  align-items: center;
  justify-content: center;

  .garage & {
    flex: 0 0 40px;
  }
}

.lr-link-basic {
  font-family: -apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
  text-decoration: underline;
}

.lr-link-inline {
  &:hover .icon-duplicate {
    fill: var(--imColSecondary);
  }
}

.lr-simple-compressed {
  display: flex;
  flex: 0 0 30px;
  align-items: center;
  justify-content: center;
}

.lr-simple-left {
  justify-content: start;
}

.lr-simple-start {
  justify-content: flex-start;
}

.lr-link {
  cursor: pointer;

  &:hover {
    .icon-pencil-edit-line {
      fill: var(--imIconTriggersHoverFill);
      transform: var(--imIndexIconActionHoverTransform);
    }
  }
}

.lr-link:hover,
.lr-content-block-link:hover {
  .icon-circle-x,
  .icon-alert,
  .icon-clear {
    fill: var(--imIndexIconActionHoverAlertFill);
    transform: var(--imIconTransform);
  }

  .icon-permission {
    fill: var(--imIndexIconActionHoverFill);
    transform: var(--imIconTransform);
  }

  .icon-permission-true {
    fill: var(--imColSecondary);
    transform: var(--imIconTransform);
  }
}

.lr-link-add-item {
  width: 100%;
  justify-content: flex-start;
  border: 0;
  background: none;
}

.lr-text {
  .lr-col-lg &,
  .lr-col-md-2 &,
  .lr-col-md & {
    margin: 0 5px;
    display: block;
  }
}

.lr-text-ellipses {
  overflow: hidden; // needed for ellipsis to work
  text-overflow: ellipsis; // needed for ellipsis to work
  white-space: nowrap; // needed for ellipsis to work
}

.lr-text-ellipses-wrap {
  width: 100%;
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  // Disable ellipsis clipping and enable text wrapping to prevent long string causing horizontal scrollbar.
  @include mediaQueryMin($bp-xl-2) {
    max-width: none;
    width: 500px;
    word-break: break-all;
    white-space: normal;
    overflow-wrap: break-word;
    overflow: visible;
    text-overflow: clip;
  }
}

.lr-text-alert {
  color: var(--imThemeColAlert);
}

.lr-text-heading,
.lr-text,
.lr-no-link {
  .lr-col-md-left & {
    padding-left: 5px;
  }
}

.lr-text,
.lr-no-link,
.users .lr-text-heading,
.profile .lr-text-heading {
  font-family: var(--imfListRowTextFontStack);
  font-size: var(--imfListRowTextsize);
  line-height: var(--imfListRowTextlineheight);
}

.lr-text-left {
  text-align: left;
  margin: 0 5px;
}

.lr-text-center {
  text-align: center;
}

.lr-text-right {
  text-align: right;
}

.lr-text-center-vert-start {
  padding: 5px;
  margin: 0;
  align-items: center;
  justify-content: flex-start;
}

.lr-text-right {
  text-align: right;
}

.lr-text-first {
  margin: 0 10px;
}

.lr-text-important {
  color: var(--imIndexIconActionHoverAlertFill);
}

.stock-index {
  .lr-text {
    font-size: 12px;
    line-height: 13px;
  }

  .lr-col,
  .lr-text {
    .lr-text-heading,
    .lr-text-heading-left,
    .lr-text-heading-right,
    .lr-text-left,
    .lr-text-right {
      font-size: 12px;
      line-height: 13px;
    }
  }
}

.lr-image-link {
  height: var(--imfListRowHeight);
  overflow: hidden;
  max-width: 100px;
}

.lr-text-row-reverse {
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
}
