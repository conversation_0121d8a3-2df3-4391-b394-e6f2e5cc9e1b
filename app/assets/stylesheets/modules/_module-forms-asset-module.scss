.form-asset-module-header {
  font-size: 12px;
  line-height: 14px;
  color: var(--imfAssetUploaderHeaderTextCol);
  border: 1px solid var(--imfAssetUploaderHeaderBorderCol);
  background-color: var(--imfAssetUploaderHeaderBgCol);
  border-radius: var(--imfAssetUploaderHeaderBorderRadius);
  padding: 4px 10px;
}

.form-asset-module-footer {
  display: flex;
  flex-wrap: wrap;
  background-color: var(--imfAssetUploaderFooterBgCol);
  border: 1px solid var(--imfAssetUploaderFooterBordertCol);
  border-top: 0;
  border-radius: 0 0 3px 3px;

  &.status-new .asset-existing {
    display: none;
  }

  &.status-existing .asset-new {
    display: none;
  }

  .dealership-stock & {
    background-color: var(--imfAssetUploaderFooterStockBgCol);
  }
}

.file-name-date {
  color: var(--imfAssetUploader<PERSON>ooterTextCol);
}

.fam-wrapper {
  width: 100%;
  display: flex;
}

.fam-wrapper-first,
.fam-wrapper-last {
  width: 100%;
  display: flex;
  align-items: stretch;
}

.fam-wrapper-first {
  border-bottom: 1px solid var(--imColBlack12);
  flex-wrap: wrap;

  @include mediaQueryMin($bp-xl) {
    flex-wrap: nowrap;
    border-bottom: 0;
    width: 58%;
    margin-right: 2%;
  }

  .fam-image-link {
    margin-right: 5px;
  }

  .fam-img-thumb {
    height: 60px;

    @supports (object-fit: cover) {
      object-fit: cover;
    }
  }

  .dealership-stock & {
    @include mediaQueryMin($bp-xl) {
      width: 100%;
    }
  }
}

.fam-wrapper-last {
  border-left: 1px solid var(--imColBlack12);
  padding: 10px;

  @include mediaQueryMin($bp-xl) {
    width: 40%;
    margin-right: 0;
  }

  .fam-input-wrapper {
    display: block;
  }
}

.fam-input-wrapper {
  display: flex;
  width: 100%;
  align-items: center;

  label {
    padding-right: 10px;
  }
}

.fam-label {
  display: block;
}

.fam-input {
  width: 100%;
}

.fam-icon-no-asset {
  margin-right: 10px;
  width: 60px;
  height: 60px;
}

.fam-wrapper-sub {
  display: flex;
  align-items: center;
  padding: 10px;
  width: 50%;

  @include mediaQueryMin($bp-md) {
    width: auto;
  }

  @include mediaQueryMin(1500px) {
    width: 50%;
  }

  &:first-child {
    width: 100%;
    border-bottom: 1px solid var(--imColBlack12);

    @include mediaQueryMin($bp-lg) {
      flex: 1;
      border-bottom: 0;
      border-right: 1px solid var(--imColBlack12);
    }

    @include mediaQueryMin(1500px) {
      flex: initial;
    }
  }

  &.sub-single {
    border-right: 0;
    border-bottom: 0;
    padding-right: 0;
  }
}

.fam-action-wrapper {
  cursor: pointer;
  padding: 15px 10px 15px 2px;
  border-radius: 3px;
  border: 1px solid transparent;
  transition: background-color 150 ease-in-out;

  &:hover {
    background-color: var(--imColWhite);
    border: 1px solid var(--imColBlack10);
  }
}

.fam-label-focus {
  display: flex;
  align-items: center;
}

.fam-action-text {
  color: var(--imColSecondary);

  @include Transition(color, 150);
}

.action-text-passive {
  color: var(--imColBlack50);

  .fam-action-wrapper:hover & {
    color: var(--imColSecondary);
  }
}
