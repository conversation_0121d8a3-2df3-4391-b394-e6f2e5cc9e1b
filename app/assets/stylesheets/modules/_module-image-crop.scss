.image-upload-crop {
  display: none;
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  margin: auto;
  z-index: $space; // 100
}

.iuc-cropper-wrapper {
  margin: auto;
  max-height: calc(100% - 300px);
}

.iuc-notification {
  text-align: center;
  margin: auto;
  background-color: var(--imfAssetUploaderCropNoteBgCol);
  min-height: var(--imfListRowHeight);
  padding: 5px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.iuc-notification-text {
  color: var(--imColTextPrimary);
  line-height: initial;
  margin: 0;
}

.iuc-notification-text-warning {
  display: none;
  color: red;
}

.image-upload-crop-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: var(--imfAssetUploaderCropWrapperBgCol);
}

.iuc-toolbox-wrapper {
  width: 366px;
  height: 104px;
  margin: 35px auto 0 auto;
  background-color: var(--imColBlack80);
  z-index: $heaven; // 9000
}

.iuc-tool-wrapper {
  margin: 15px;
  display: flex;
  flex-direction: row;
}

.iuc-toolbox {
  width: 25%;
  height: 74px;
  background-color: var(--imfAssetUploaderToolboxItemBgCol);
  padding: 8px;
  margin-top: 15px;
  text-align: center;
  border-left: 1px solid var(--imfAssetUploaderToolboxItemBorderCol);
  cursor: pointer;

  &:first-child {
    border-left: 0;
  }
}

.iuc-tool-text {
  @include primaryFontStack;
  font-size: 13px;
  padding-top: 3px;
  color: var(--imfAssetUploaderToolboxTextCol);
}

.iuc-button-wrapper {
  width: 329px;
  margin: auto;
  margin-top: 25px;

  .imf-btn {
    width: 47%;

    &:nth-child(2) {
      margin: 0;
    }
  }
}

.cropper-wrap-box {
  background-color: var(--imColBlack15);
}
