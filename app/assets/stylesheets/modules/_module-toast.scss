.toast-wrapper {
  position: fixed;
  z-index: 4;
  bottom: 0;
  left: 50%;
  transform: translate(-50%, 0);
  width: max-content;
  max-width: 1200px;
  background: var(--imColBlack);
  transform-origin: 80px bottom;
  animation: fadeIn 0.3s;

  &.animate {
    animation: toastFoldBack 1s ease 1;
  }

  &.hide {
    display: none;
  }

  &:hover .toast-section-body {
    max-height: 250px;
    transition: max-height 0.5s ease;
  }
}
