.list-recurring .col-hide,
.lr-item-heading .col-hide {
  display: none;

  @include mediaQueryMin($bp-sm) {
    display: flex;
  }

  @include mediaQueryMin($bp-md) {
    display: none;
  }

  @include mediaQueryMin($bp-lg) {
    display: flex;
  }
}

.list-recurring .col-hide-extra,
.lr-item-heading .col-hide-extra {
  display: none;

  @include mediaQueryMin($bp-xl) {
    display: flex;
  }
}

.list-recurring .col-hide-aggressive,
.lr-item-heading .col-hide-aggressive {
  display: none;

  @include mediaQueryMin($bp-xxl) {
    display: flex;
  }
}

.list-user-edit .col-hide {
  display: none;

  @include mediaQueryMin($bp-md) {
    display: flex;
  }
}

.list-table .col-hide,
.list-table-simple .col-hide {
  display: none;

  @include mediaQueryMin($bp-md) {
    display: flex;
  }
}

.stock-index {
  .list-recurring .col-hide,
  .lr-item-heading .col-hide {
    display: none;

    @include mediaQueryMin(800px) {
      display: flex;
    }

    @include mediaQueryMin($bp-md) {
      display: none;
    }

    @include mediaQueryMin(1200px) {
      display: flex;
    }
  }
}
