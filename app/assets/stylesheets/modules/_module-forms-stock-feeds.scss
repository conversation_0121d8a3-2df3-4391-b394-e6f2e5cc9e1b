.feeds {
  .search-filter-wrapper {
    margin-bottom: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    @include mediaQueryMin($bp-lg) {
      flex-direction: row;
    }
  }

  .filter-form-wrapper {
    margin-bottom: 1rem;
  }

  .search-bar-wrapper {
    width: 100%;
    justify-content: center;

    @include mediaQueryMin($bp-lg) {
      width: 25%;
      justify-content: unset;
    }
  }
}

.import-rules {
  .stock-import-rules {
    display: flex;
    flex-grow: 1;
    align-items: center;
    border: 1px solid var(--imColBlack20);
    border-radius: 5px;
    padding: 5px 10px;
    margin: 10px 5px;
    gap: 5px;

    @include mediaQueryMax($bp-lg) {
      flex-direction: column;
    }
  }

  .action-edit-rule-add-wrapper {
    display: flex;
    align-items: center;
    padding: 8px;

    .imf-btn {
      margin: 0;
      padding: 11px 22px;
    }
  }

  .stock-rule-form-control {
    width: 100%;
  }

  .stock-rule-text {
    text-align: center;
    word-break: break-all;
    color: initial;
    background-color: initial;
  }

  .import-rule-column {
    color: var(--imColWhite);
    display: inline-block;
    background-color: var(--imColBlack40);
    padding: 2px 5px;
    border-radius: 10px;
    margin: 3px 3px 0 3px;
  }

  .import-rule-value {
    color: var(--imColSecondary);
    display: inline-block;
    background-color: var(--imColBlack8);
    padding: 2px 5px;
    border-radius: 10px;
    border: 1px solid var(--imColBlack20);
    margin: 3px;
  }

  .import-rule-empty-value {
    @include primaryFontStackBold;
    color: var(--imColRed);
  }

  .import-rule-text {
    @include primaryFontStackBold;
    -webkit-font-smoothing: antialiased;
    color: #fff;
    padding: 1px 6px;
    background-color: var(--imColBlack70);
    display: inline-block;
    border-radius: 9px;
  }

  .import-rule-select {
    @include primaryFontStackBold;
    -webkit-font-smoothing: antialiased;
    padding: 1px 6px;
    text-align: center;
    cursor: pointer;
  }
}
