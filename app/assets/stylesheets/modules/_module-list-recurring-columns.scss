.lr-col {
  border-left: 1px solid var(--imfListRowColumnBorderLeftCol);
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;

  &:first-child {
    border: 0;
  }
}

.lr-col-100 {
  flex: 1;

  @include mediaQueryMin($bp-lg) {
    flex: 0 0 100px;
    max-width: 100px;
  }

  .lr-text {
    overflow: hidden; // needed for ellipsis to work
    text-overflow: ellipsis; // needed for ellipsis to work
    white-space: nowrap; // needed for ellipsis to work
    max-width: 100px; // needed to match width of parent wrapper
    padding: 0 5px; // needed so text doesn't touch wrapper
  }
}

.lr-col-120 {
  flex: 1;

  @include mediaQueryMin($bp-lg) {
    flex: 0 0 120px;
    max-width: 120px;
  }

  .lr-text {
    overflow: hidden; // needed for ellipsis to work
    text-overflow: ellipsis; // needed for ellipsis to work
    white-space: nowrap; // needed for ellipsis to work
    max-width: 120px; // needed to match width of parent wrapper
    padding: 0 5px; // needed so text doesn't touch wrapper
  }
}

.lr-col-150 {
  flex: 1;

  @include mediaQueryMin($bp-lg) {
    flex: 0 0 150px;
    max-width: 150px;
  }

  .lr-text {
    overflow: hidden; // needed for ellipsis to work
    text-overflow: ellipsis; // needed for ellipsis to work
    white-space: nowrap; // needed for ellipsis to work
    max-width: 150px; // needed to match width of parent wrapper
    padding: 0 5px; // needed so text doesn't touch wrapper
  }
}

.lr-col-180 {
  flex: 1;

  @include mediaQueryMin($bp-lg) {
    flex: 0 0 180px;
    max-width: 180px;
  }

  .lr-text {
    overflow: hidden; // needed for ellipsis to work
    text-overflow: ellipsis; // needed for ellipsis to work
    white-space: nowrap; // needed for ellipsis to work
    max-width: 180px; // needed to match width of parent wrapper
    padding: 0 5px; // needed so text doesn't touch wrapper
  }
}

.lr-col-static-180 {
  flex: 0 0 180px;

  .lr-text {
    overflow: hidden; // needed for ellipsis to work
    text-overflow: ellipsis; // needed for ellipsis to work
    white-space: nowrap; // needed for ellipsis to work
    max-width: 180px; // needed to match width of parent wrapper
    padding: 0 5px; // needed so text doesn't touch wrapper
  }
}

.lr-col-200 {
  flex: 1;

  @include mediaQueryMin($bp-lg) {
    flex: 0 0 200px;
    max-width: 200px;
  }

  .lr-text {
    overflow: hidden; // needed for ellipsis to work
    text-overflow: ellipsis; // needed for ellipsis to work
    white-space: nowrap; // needed for ellipsis to work
    max-width: 200px; // needed to match width of parent wrapper
    padding: 0 5px; // needed so text doesn't touch wrapper
  }
}

.lr-col-300 {
  flex: 1;
  min-width: 100px; // needed for ellipsis to work for children text on small viewports

  @include mediaQueryMin($bp-lg) {
    flex: 0 0 300px;
    max-width: 300px;
  }

  .lr-text {
    overflow: hidden; // needed for ellipsis to work
    text-overflow: ellipsis; // needed for ellipsis to work
    white-space: nowrap; // needed for ellipsis to work
    max-width: 300px; // needed to match width of parent wrapper
    padding: 0 5px; // needed so text doesn't touch wrapper
  }
}

.lr-col-fit-custom {
  flex: 1;
  min-width: 100px; // needed for ellipsis to work for children text on small viewports
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;

  @include mediaQueryMin($bp-lg-2) {
    flex: 1.5;
  }

  .lr-text {
    overflow: hidden; // needed for ellipsis to work
    text-overflow: ellipsis; // needed for ellipsis to work
    white-space: nowrap; // needed for ellipsis to work
    max-width: 150px; // needed to match width of parent wrapper
    padding: 0 5px; // needed so text doesn't touch wrapper

    @include mediaQueryMin($bp-lg) {
      max-width: 250px;
    }

    @include mediaQueryMin($bp-lg-2) {
      max-width: 350px;
    }

    @include mediaQueryMin($bp-xl) {
      max-width: 500px;
    }

    @include mediaQueryMin($bp-xxl) {
      max-width: 700px;
    }
  }
}

.lr-col-lg {
  flex: 3;
  align-items: flex-start;

  @include mediaQueryMin($bp-sm) {
    flex: 2;
  }

  @include mediaQueryMin($bp-md) {
    flex: 3;
  }

  @include mediaQueryMin($bp-lg) {
    flex: 2;
  }

  &.flexible-300 {

    @include mediaQueryMin($bp-xl) {
      min-width: 300px;
      max-width: 300px;
    }
  }

  // override .r-col-lg flex
  &.flex1 {
    flex: 1;

    @include mediaQueryMin($bp-sm) {
      flex: 1;
    }

    @include mediaQueryMin($bp-md) {
      flex: 1;
    }

    @include mediaQueryMin($bp-lg) {
      flex: 1;
    }
  }
}

.lr-col-md-flex {
  flex: 1;
  align-items: flex-start;
}

.lr-col-md-2 {
  flex: 1;

  @include mediaQueryMin($bp-sm) {
    flex: 0 0 150px;
  }

  @include mediaQueryMin($bp-md) {
    flex: 1;
  }

  @include mediaQueryMin($bp-lg) {
    flex: 0 0 150px;
  }
}

.lr-col-md {
  flex: 1;

  @include mediaQueryMin($bp-sm) {
    flex: 0 0 90px;
  }

  @include mediaQueryMin($bp-md) {
    flex: 1;
  }

  @include mediaQueryMin($bp-lg) {
    flex: 0 0 90px;
  }
}

.lr-col-md-left {
  flex: 1;
  align-items: flex-start;
}

.lr-col-sm-md {
  flex: 0 0 70px;
}

.lr-col-sm {
  flex: 0 0 var(--imfListRowHeight);
}

.lr-col-center {
  align-items: center;
}

.lr-col-start {
  justify-content: flex-start;
}

.section-users,
.section-stock-details,
.profile {
  .lr-col-lg {
    flex: 2;

    @include mediaQueryMin($bp-sm) {
      flex: 4;
    }

    @include mediaQueryMin($bp-lg) {
      flex: 5;
    }
  }

  .lr-col-md {
    flex: 1;
    min-height: 40px;

    @include mediaQueryMin($bp-sm) {
      flex: 0 0 180px;
    }

    @include mediaQueryMin($bp-md) {
      flex: 1;
    }

    @include mediaQueryMin($bp-lg) {
      flex: 0 0 180px;
    }
  }
}
