// lr = list-recurring
@supports (object-fit: cover) {
  .lr-image,
  .lr-video-poster-image-wrapper {
    object-fit: cover;
    height: var(--imfListRowHeight);
  }
}

.lr-video-poster-image-wrapper {
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  &::before {
    content: '';
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='21' height='21 ' viewBox='0 0 21 21'%3E %3Cpath d='M21,10.5C21,16.3,16.3,21,10.5,21S0,16.31,0,10.5S4.7,0,10.5,0S21,4.7,21,10.5z M10.5,2.25c-4.56,0-8.25,3.7-8.25,8.25s3.69,8.25,8.25,8.25s8.25-3.7,8.25-8.25S15.06,2.25,10.5,2.25z M14.62,11.18c0.49-0.37,0.49-0.98,0-1.36L8.89,5.51c-0.49-0.37-0.9-0.17-0.9,0.45v9.09c0,0.62,0.4,0.82,0.9,0.45L14.62,11.18z' fill='%23fff'/%3E%3C/svg%3E");
    background-size: 30px 30px;
    opacity: 0.6;
    position: absolute;
    width: 30px;
    height: 30px;
    background-repeat: no-repeat;
  }

  .lr-image {
    width: 100%;
  }
}

.lr-thumb-link {
  display: block;
  width: 130px;
  height: var(--imfListRowHeight);
  overflow: hidden;

  @supports (object-fit: cover) {
    width: auto;
    height: auto;
    overflow: initial;
  }
}

.lr-thumb {
  max-width: initial;
  height: var(--imfListRowHeight);
  display: inline-block;

  .dealership-stock &,
  .dealerships.specials & {
    margin-left: 5px;
    height: var(--imfListRowHeight);
    font-size: 13px;
    line-height: 16px;
    margin-right: 5px;

    @supports (object-fit: cover) {
      object-fit: contain;
      object-position: 0 0;
    }
  }
}

.lr-image-clip {
  overflow: hidden;
  width: 45px;
  height: var(--imfListRowHeight);
}

.lr-col.lr-col-image {
  flex: 0 0 60px;

  .icon-no-image {
    width: 100%;
  }
}

.lr-col-mobile {
  overflow: hidden;
}

.no-image-mobile {
  width: 0;
  height: 0;
  border-top: 24px solid var(--imfListRowNoImageMobileBorderColDk);
  border-right: 24px solid var(--imfListRowNoImageMobileBorderColDk);
  border-left: 24px solid var(--imfListRowNoImageMobileBorderColLt);
  border-bottom: 24px solid var(--imfListRowNoImageMobileBorderColLt);

  .source & {
    border-top-color: $blue6;
    border-right-color: $blue6;
    border-left-color: $blue3;
    border-bottom-color: $blue3;
  }
}
