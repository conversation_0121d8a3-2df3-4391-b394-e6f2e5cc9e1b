.asset-upload-wrapper {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: $air; // 10
  display: none;
  animation-name: displayContainer;
  animation-duration: 0.3s;
  animation-iteration-count: 1;
  flex-direction: column;
  justify-content: center;
}

@keyframes displayContainer {
  0% {
    left: 125%;
  }

  25% {
    left: 50%;
  }

  100% {
    left: 0;
  }
}

.au-visible {
  display: flex;
}

.au-overlay {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.au-drop-area-wrapper {
  text-align: center;
  position: relative;
  margin: 50% auto;
  background-color: var(--imfAssetUploaderModalWrapperBgCol);
  box-shadow: 0 0 10px 4px rgba(0, 0, 0, 0.2);
  width: 300px;

  @include mediaQueryMin(400px) {
    width: calc(400px - 15px);
  }

  @include mediaQueryMin(450px) {
    width: calc(450px - 15px);
  }

  @include mediaQueryMin($bp-md) {
    width: 90%;
  }

  @include mediaQueryMin($bp-lg) {
    width: 500px;
  }

  .inputFile {
    display: none;
  }
}

.au-drop-area-active {
  width: calc(100% - 50px);
  margin: 0 auto;
  border: 2px dashed var(--imfAssetUploaderModalTargetBorderCol);
  background-color: var(--imfAssetUploaderModalTargetBgCol);

  @include mediaQueryMin($bp-xs) {
    width: calc(100% - 60px);
  }
}

.upload-icon {
  display: block;
  font-size: 3em;
  color: var(--imColBlack95);

  @include mediaQueryMin($bp-sm) {
    font-size: 9vw;
  }
}

.asset-upload-btn {
  margin: 0 auto;
}

.au-clickable-area {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: $clouds; // 20
  opacity: 0;
  cursor: pointer;
}

.au-text-sub-heading {
  font-size: 16px;
  color: var(--imColBlack60);
  margin: 1.7rem 0;
}

.au-close-wrapper {
  text-align: right;
}

.au-close-link {
  cursor: pointer;
  display: inline-block;
  width: 30px;
  height: 30px;
  position: relative;
  transition: transform 150ms ease-in-out;

  @include mediaQueryMin($bp-xs) {
    width: 40px;
    height: 40px;
  }

  &:hover {
    transform: scale(1.2);
  }

  &::before,
  &::after {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    width: 60%;
    height: 2px;
    background-color: var(--imfAssetUploaderModalCloseBtnBgCol);
    border-radius: 1px;
    transition: background-color 150ms ease-in-out;
  }

  &::before {
    transform: translate(-50%, -50%) rotate(45deg);
  }

  &::after {
    transform: translate(-50%, -50%) rotate(-45deg);
  }

  &:hover::before,
  &:hover::after {
    background-color: var(--imfAssetUploaderModalCloseBtnHoverCol);
  }
}

.au-notification-text {
  font-size: 13px;
  margin: 7px 0 6px 0;
}

.au-notification-text-warning-wrapper {
  display: none;

  &.au-loading {
    display: block;
    background-color: var(--imColSecondary);
  }

  &.au-visible {
    display: block;
  }
}

.au-warning-wrapper {
  padding: 0 0 5px 0;
}

.au-notification-text-warning {
  color: var(--imfAssetUploaderModalTextWarningCol);
  margin: -6px 0 0 0;
}

.au-hover {
  background-color: var(--imColBlack15);
  background-color: pink;

  .au-text-sub-heading {
    color: var(--imColBlack80);
  }
}

.img-upload-btn {
  margin: 0 auto;
}

// Hover colour when dragging image into the drop zone
.iu-hover {
  background-color: var(--imfAssetUploaderModalTargetActiveBgCol);

  .iu-text-sub-heading {
    color: var(--imColBlack80);
  }
}
