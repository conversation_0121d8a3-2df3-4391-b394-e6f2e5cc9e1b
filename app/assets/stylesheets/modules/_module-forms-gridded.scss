.imf-group-gridded-wrapper {
  padding: 15px;
  background-color: var(--imGroupGridWrapperBgCol);
  border: 1px solid var(--imGroupGridWrapperBorderCol);
  margin-bottom: 15px;
}

.imf-items-gridded {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;

  @include mediaQueryMin($bp-xs) {
    grid-template-columns: repeat(2, 1fr);
  }

  @include mediaQueryMin($bp-md) {
    grid-template-columns: repeat(1, 1fr);
  }

  @include mediaQueryMin($bp-md-2) {
    grid-template-columns: repeat(2, 1fr);
  }

  @include mediaQueryMin($bp-xl) {
    grid-template-columns: repeat(3, 1fr);
  }

  @include mediaQueryMin($bp-xxl) {
    grid-template-columns: repeat(4, 1fr);
  }
}

.imf-label-nested-asset-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  flex-direction: column-reverse;
  margin: 0;
  padding: 50px 15px 15px 15px;
  border-radius: 5px;
  background-color: var(--imGroupLabelAssetWrapperBgCol);
  border: 1px solid var(--imGroupLabelAssetWrapperHoverBgCol);
  transition: background-color 250ms ease-in-out, border-color 250ms ease-in-out;

  &:hover {
    background-color: var(--imGroupLabelAssetWrapperBorderCol);
    border: 1px solid var(--imGroupLabelAssetWrapperHoverBorderCol);

    .imf-gridded-asset-img,
    .item-gridded-info-panel {
      opacity: 100%;
    }
  }

  // Firefox doesn't support :has - must be separate from hover pseudo class or Firefox won't recognise hover styles
  &:has(:checked) {
    background-color: var(--imGroupLabelAssetWrapperActiveBgCol);
    border: 1px solid var(--imGroupLabelAssetWrapperActiveBorderCol);

    .imf-gridded-asset-img,
    .item-gridded-info-panel {
      opacity: 100%;
    }
  }
}

.item-gridded-info-panel {
  position: absolute;
  top: 0;
  width: 100%;
  height: 35px;
  padding: 2px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 2px;
  background-color: var(--imColSecondary);
  opacity: 40%;
  transition: opacity 250ms ease-in-out;

  // target firefox only - doesn't support :has (yet)
  @-moz-document url-prefix() {
    opacity: 100%;
  }
}

.imf-gridded-link-icon-wrapper {
  display: block;
}

.imf-gridded-icon-wrapper {
  display: block;
}

.icon-svg-gridded {
  fill: var(--imColWhite);
  width: 28px;
  height: 28px;
  display: block;
}

.imf-label-nested-text-input-wrapper {
  font-size: 13px;
  display: flex;
  align-items: center;
  margin-top: 15px;
}

.imf-gridded-asset-img {
  opacity: 40%;
  transition: opacity 250ms ease-in-out;

  // target firefox only - doesn't support :has (yet)
  @-moz-document url-prefix() {
    opacity: 100%;
  }
}

.gridded-asset-img-box {
  box-shadow: 1px 1px 2px 1px rgba(0, 0, 0, 0.2);
}
