table {
  width: 100%;
  border: 1px solid var(--imColBlack10);
  border-collapse: collapse;
}

tbody {
  border-color: transparent;
}

table > thead > tr > th,
table > tbody > tr > th,
table > tfoot > tr > th,
table > thead > tr > td,
table > tbody > tr > td,
table > tfoot > tr > td {
  font-size: 13px;
  line-height: 1.4;
  text-align: left;
  color: var(--imColBlack60);
  vertical-align: top;
  padding: 8px;
  border-left: 1px solid var(--imColBlack10);
}

table > tbody > tr > th,
table > tfoot > tr > th {
  color: var(--imColWhite);
  background-color: var(--imColBlack35);
}

table tr {
  background-color: var(--imColWhite);

  &:nth-child(2n) {
    background-color: var(--imColBlack3);
  }
}
