.toggle-lists {
  .action-edit-wrapper {
    display: flex;
    justify-content: space-between;
  }

  .toggle_status {
    margin: 0;
    text-align: right;

    .icon-toggle {
      margin-left: 10px;
    }
  }
}

.toggle-lists.brand-active {
  .ae-icon-wrapper,
  .ae-text {
    display: none;  // hide children without losing the parent .action-edit-wrapper so layout doesn't break
  }

  .hero-banners-dealership,
  .promos-dealership {
    display: none; // hide dealership list of items when .brand-active
  }

  .lr-item * { // change cursor for all children
    cursor: not-allowed; // switch off cursor: grabber for ListJS items
  }

  .icon-grabber {
    display: none; // hide grabber icon for ListJS items
  }
}
