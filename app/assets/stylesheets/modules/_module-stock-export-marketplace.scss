.marketplace-status-completed,
.marketplace-status-pending,
.marketplace-status-error {
  display: flex;
}

.marketplace-status-completed {
  color: var(--imfStockExportStatusCompleteTextCol);

  &::before {
    content: '';
    display: inline-block;
    width: 30px;
    height: 26px;
    // icon colour hack
    background-color: var(--imfStockExportStatusCompleteIconFill);
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='100%25' height='100%25' viewBox='0 0 30 30'%3E %3Cpath d='M11.3,19.5L23.5,7.4L25,9L11.3,22.6L5,16.3l1.6-1.6L11.3,19.5z' fill='%23388e3c'/%3E%3C/svg%3E");
    mask-repeat: no-repeat;
    mask-position: center;
    mask-size: contain;
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='100%25' height='100%25' viewBox='0 0 30 30'%3E %3Cpath d='M11.3,19.5L23.5,7.4L25,9L11.3,22.6L5,16.3l1.6-1.6L11.3,19.5z' fill='%23388e3c'/%3E%3C/svg%3E");
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
    -webkit-mask-size: contain;
  }
}

.marketplace-status-pending {
  &::before {
    content: '';
    display: inline-block;
    width: 30px;
    height: 26px;
    // icon colour hack
    background-color: var(--imfStockExportStatusPendingIconFill);
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='100%25' height='100%25' viewBox='0 0 30 30'%3E %3Cpath d='M5.8,13.2c0.5-0.5,1.1-0.8,1.8-0.8c0.7,0,1.2,0.2,1.8,0.8S10,14.3,10,15s-0.2,1.2-0.8,1.8s-1.1,0.8-1.8,0.8c-0.7,0-1.2-0.2-1.8-0.8S5,15.7,5,15S5.2,13.8,5.8,13.2L5.8,13.2z M20.7,13.2c0.5-0.5,1.1-0.8,1.8-0.8c0.7,0,1.2,0.2,1.8,0.8S25,14.3,25,15s-0.2,1.2-0.8,1.8s-1.1,0.8-1.8,0.8c-0.7,0-1.2-0.2-1.8-0.8S20,15.7,20,15S20.2,13.8,20.7,13.2L20.7,13.2z M13.2,13.2c0.5-0.5,1.1-0.8,1.8-0.8s1.2,0.2,1.8,0.8s0.8,1.1,0.8,1.8s-0.2,1.2-0.8,1.8s-1.1,0.8-1.8,0.8s-1.2-0.2-1.8-0.8s-0.8-1.1-0.8-1.8S12.7,13.8,13.2,13.2z' fill='%23579ec8'/%3E%3C/svg%3E");
    mask-repeat: no-repeat;
    mask-position: center;
    mask-size: contain;
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='100%25' height='100%25' viewBox='0 0 30 30'%3E %3Cpath d='M5.8,13.2c0.5-0.5,1.1-0.8,1.8-0.8c0.7,0,1.2,0.2,1.8,0.8S10,14.3,10,15s-0.2,1.2-0.8,1.8s-1.1,0.8-1.8,0.8c-0.7,0-1.2-0.2-1.8-0.8S5,15.7,5,15S5.2,13.8,5.8,13.2L5.8,13.2z M20.7,13.2c0.5-0.5,1.1-0.8,1.8-0.8c0.7,0,1.2,0.2,1.8,0.8S25,14.3,25,15s-0.2,1.2-0.8,1.8s-1.1,0.8-1.8,0.8c-0.7,0-1.2-0.2-1.8-0.8S20,15.7,20,15S20.2,13.8,20.7,13.2L20.7,13.2z M13.2,13.2c0.5-0.5,1.1-0.8,1.8-0.8s1.2,0.2,1.8,0.8s0.8,1.1,0.8,1.8s-0.2,1.2-0.8,1.8s-1.1,0.8-1.8,0.8s-1.2-0.2-1.8-0.8s-0.8-1.1-0.8-1.8S12.7,13.8,13.2,13.2z' fill='%23579ec8'/%3E%3C/svg%3E");
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
    -webkit-mask-size: contain;
  }
}

.marketplace-status-error {
  color: var(--imfStockExportStatusErrorIconFill);

  &::before {
    content: '';
    display: inline-block;
    width: 30px;
    height: 26px;
    // icon colour hack
    background-color: var(--imColRed);
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='100%25' height='100%25' viewBox='0 0 30 30'%3E %3Cpath d='M5,23.6L15,6.4l10,17.3H5z M15.9,20.9v-1.8h-1.8v1.8H15.9z M15.9,17.3v-3.7h-1.8v3.7H15.9z' fill='%23e64a19'/%3E%3C/svg%3E");
    mask-repeat: no-repeat;
    mask-position: center;
    mask-size: contain;
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='100%25' height='100%25' viewBox='0 0 30 30'%3E %3Cpath d='M5,23.6L15,6.4l10,17.3H5z M15.9,20.9v-1.8h-1.8v1.8H15.9z M15.9,17.3v-3.7h-1.8v3.7H15.9z' fill='%23e64a19'/%3E%3C/svg%3E");
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
    -webkit-mask-size: contain;
  }
}
