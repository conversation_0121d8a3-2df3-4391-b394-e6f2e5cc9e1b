
input[type='submit'].imf-btn,
.imf-btn {
  @include mixinBtnPrimary;

  &.imf-btn-outline-flex {
    background-color: var(--btnOutlineFlexBgCol);
    color: var(--btnOutlineFlexTextCol);
    padding: 12px 10px;
    border: 2px solid var(--btnOutlineFlexBorderCol);
    box-shadow: none;
    display: inline-flex;
    align-items: center;
    margin: 0;

    &:hover,
    &:focus,
    &:focus:not(:active) {
      color: var(--btnOutlineFlexHoverTextCol);
      background-color: var(--btnOutlineFlexHoverBgCol);
      border-color: var(--btnOutlineFlexHoverBorderCol);

      .ae-icon {
        fill: var(--btnOutlineFlexHoverIconFill);
      }
    }

    .ae-icon {
      fill: var(--btnOutlineFlexIconFill);
    }
  }

  &::-moz-focus-inner {
    padding: 0;
    border: 0;
  }

  &.imf-btn-sm {
    padding: 10px 22px;
  }

  &.imf-btn-xs {
    font-size: 13px;
    padding: 4px 17px;
    margin: 0 0 2px 0;
    min-width: unset;
  }

  .list-input & {
    margin: 0;
  }

  &.height-sm {
    padding-block: 10px;
  }

  &.navy {
    background-color: var(--imColBlack);

    &:hover,
    &:focus,
    &:focus:not(:active) {
      background-color: var(--imColBlack);
    }

    &:active {
      transform: scale(0.97);
    }
  }

  &.dark-red {
    background-color: var(--imColRed);

    &:hover,
    &:focus,
    &:focus:not(:active) {
      background-color: var(--imColRed);
    }

    &:active {
      transform: scale(0.97);
    }
  }

  &.icon-grow {
    .lg-icon {
      transform: scale(1);
      transform-origin: left center;
      transition: transform 150ms ease-in-out;
    }

    &:hover {
      .lg-icon {
        transform: scale(1.15);
      }
    }
  }
}

.btn-action {
  font-family: var(--imPrimaryFontStack);
  @include fontSmoothing;
  font-size: 16px;
  text-transform: none;
  padding: 8px 16px;
  margin: 0 auto 20px auto;

  @include mediaQueryMin($bp-lg) {
    margin: 0 0 0 10px;
  }
}

.wrapper-50 {
  .imf-btn-sm {
    margin-top: 17px;
  }
}

.imf-btn-ml {
  margin-left: var(--imfFormItemMargin);
}

.list-input {
  .imf-btn-sm {
    padding: 0 22px;
  }
}

.sessions .imf-btn,
.login .imf-btn {
  margin: 0.5rem 0 0 0;
}

.imf-btn-kill {
  color: var(--btnSecondaryTextCol);
  background-color: var(--btnSecondaryBgCol);

  &:hover,
  &:active,
  &:focus:not(:active) {
    color: var(--btnSecondaryHoverTextCol);
    background-color: var(--btnSecondaryHoverBgCol);
  }

  &.disabled,
  &.disabled:hover,
  &.disabled:active {
    pointer-events: none;
  }
}

a.imf-btn:hover {
  text-decoration: none;
}

.action-link {
  position: absolute;
  right: 0;
  top: 0;
}

.al-icon {
  width: 30px;
  height: 30px;
}

.btn-toggle-view {
  &.imf-btn-outline-flex {
    padding-block: 6px;
  }

  &::before {
    content: '';
    width: 25px;
    height: 25px;
    // icon colour hack
    background-color: var(--imfBtnTertiaryOutlineIconFill);
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='100%25' height='100%25' viewBox='0 0 30 30'%3E %3Cpath d='M25,15c-0.8,2-2.1,3.7-3.9,4.9c-1.8,1.2-3.8,1.9-6.1,1.9s-4.3-0.6-6.1-1.9C7.1,18.7,5.8,17,5,15c0.8-2,2.1-3.7,3.9-4.9c1.8-1.3,3.8-1.9,6.1-1.9s4.3,0.6,6.1,1.9C22.9,11.3,24.2,13,25,15L25,15z M11.8,18.2c0.9,0.9,2,1.3,3.2,1.3s2.3-0.4,3.2-1.3c0.9-0.9,1.3-2,1.3-3.2s-0.4-2.3-1.3-3.2c-0.9-0.9-2-1.4-3.2-1.4s-2.3,0.5-3.2,1.4s-1.3,2-1.3,3.2S10.9,17.3,11.8,18.2L11.8,18.2z M13.1,13.1c1.1-1.1,2.8-1.1,3.9,0c1.1,1,1.1,2.8,0,3.9c-1.1,1-2.8,1-3.9,0C12,15.9,12,14.1,13.1,13.1z' fill='%23428fbd'/%3E%3C/svg%3E");
    mask-repeat: no-repeat;
    mask-position: center;
    mask-size: contain;
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='100%25' height='100%25' viewBox='0 0 30 30'%3E %3Cpath d='M25,15c-0.8,2-2.1,3.7-3.9,4.9c-1.8,1.2-3.8,1.9-6.1,1.9s-4.3-0.6-6.1-1.9C7.1,18.7,5.8,17,5,15c0.8-2,2.1-3.7,3.9-4.9c1.8-1.3,3.8-1.9,6.1-1.9s4.3,0.6,6.1,1.9C22.9,11.3,24.2,13,25,15L25,15z M11.8,18.2c0.9,0.9,2,1.3,3.2,1.3s2.3-0.4,3.2-1.3c0.9-0.9,1.3-2,1.3-3.2s-0.4-2.3-1.3-3.2c-0.9-0.9-2-1.4-3.2-1.4s-2.3,0.5-3.2,1.4s-1.3,2-1.3,3.2S10.9,17.3,11.8,18.2L11.8,18.2z M13.1,13.1c1.1-1.1,2.8-1.1,3.9,0c1.1,1,1.1,2.8,0,3.9c-1.1,1-2.8,1-3.9,0C12,15.9,12,14.1,13.1,13.1z' fill='%23428fbd'/%3E%3C/svg%3E");
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
    -webkit-mask-size: contain;
  }

  &.btn-sm {
    padding: 0 9px 0 27px;
  }
}

.imf-btn.btn-mt {
  margin-bottom: 0;
  margin-top: var(--imfFormItemMargin);
  margin-right: var(--imfFormItemMargin);
}

.imf-btn-wrapper-sticky {
  margin-top: 20px;
  padding: 15px 10px;
  position: sticky;
  z-index: $heavensGate; // 8999
  bottom: 0;
  backdrop-filter: blur(1px);
  background-color: var(--btnStickyWrapperBgCol);
  border-top: 1px solid var(--btnStickyWrapperBorderTopCol); // define the top edge

  input[type='submit'],
  .imf-btn {
    margin-bottom: 0;
  }
}
