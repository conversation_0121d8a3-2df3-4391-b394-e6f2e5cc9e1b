.toggleCheck {
  position: absolute;
  left: -9999px;
}

.toggleCheckLabel {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.toggleCheckPseudoToggle {
  position: relative;
  width: 32px;
  height: 16px;
  border-radius: 15px;
  background: var(--redDark);
}

.toggleCheckPseudoToggle::before,
.toggleCheckPseudoToggle::after {
  content: '';
  position: absolute;
}

.toggleCheckPseudoToggle::before {
  left: 3px;
  top: 3px;
  width: 10px;
  height: 10px;
  background: var(--imColWhite);
  border-radius: 50%;
  z-index: $surface; // 1
  transition: transform 150ms ease-in-out;
}

.toggleCheckPseudoToggle::after {
  top: 50%;
  right: 6px;
  width: 10px;
  height: 10px;
  transform: translateY(-50%);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='24' height='24' viewBox='0 0 24 24'%3E %3Cpath d='M24 20.188l-8.315-8.209 8.2-8.282-3.697-3.697-8.212 8.318-8.31-8.203-3.666 3.666 8.321 8.24-8.206 8.313 3.666 3.666 8.237-8.318 8.285 8.203z' fill='%23fff'/%3E%3C/svg%3E");
  background-size: 10px 10px;
  opacity: 0.7;
}

.toggleCheck:checked + .toggleCheckLabel .toggleCheckPseudoToggle {
  background: var(--imColSecondary);
}

.toggleCheck:checked + .toggleCheckLabel .toggleCheckPseudoToggle::before {
  transform: translateX(16px);
}

.toggleCheck:checked + .toggleCheckLabel .toggleCheckPseudoToggle::after {
  left: 6px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' preserveAspectRatio='xMidYMid slice' width='24' height='24' viewBox='0 0 24 24'%3E %3Cpath d='M9 21.035l-9-8.638 2.791-2.87 6.156 5.874 12.21-12.436 2.843 2.817z' fill='%23fff'/%3E%3C/svg%3E");
}

.toggleCheckText {
  margin-left: 4px;
  display: inline-block;
}
