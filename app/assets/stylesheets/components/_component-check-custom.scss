@keyframes growScale {
  50% {
    transform: scale(0.9);
  }
}

.checkCustomInput {
  // hide actual input checkbox
  display: none;
}

.checkCustomLabel {
  user-select: none;
  cursor: pointer;
  display: flex;
  align-items: center;

  &:hover .checkCustomIconWrapper {
    border-color: var(--imColSecondary);
  }
}

.checkCustomIconWrapper {
  position: relative;
  width: 20px;
  height: 20px;
  transform: scale(1);
  border-radius: 3px;
  border-width: 2px;
  border-style: solid;
  border-color: var(--imColBlack30);
  transition: border-color 150ms ease-in-out;

  &::before {
    content: '';
    width: 100%;
    height: 100%;
    background: var(--imColSecondary);
    display: block;
    transform: scale(0);
    opacity: 1;
    border-radius: 50%;
  }
}

.checkCustomIcon {
  position: absolute;
  top: 3px;
  left: 2px;
  fill: none;
  stroke: var(--imColWhite);
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-dasharray: 16px;
  stroke-dashoffset: 16px;
  transform: translate3d(0, 0, 0);
  transition-delay: 100ms;
  transition-duration: 150ms;
  transition-timing-function: ease-in-out;
  transition-property: stroke-dasharray, stroke-dashoffset, transform;
}

.checkCustomInput:checked + .checkCustomLabel {
  .checkCustomIconWrapper {
    background-color: var(--imColSecondary);
    border-color: var(--imColSecondary);
    animation: growScale 0.4s ease;

    &::before {
      opacity: 0;
      transform: scale(3.5);
      transition-duration: 600ms;
      transition-timing-function: ease-in-out;
      transition-property: opacity, transform;
    }

    .checkCustomIcon {
      stroke-dashoffset: 0;
    }
  }
}

.checkCustomText {
  display: inline-block;
  padding-left: 6px;
}
