.link-toggle-status {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  transition: background-color 150ms ease-in-out;

  &:hover {
    background-color: var(--imActionToggleHoverBgCol);

    .icon-toggle {
      transform: scale(1.1);
    }
  }

  &.toggle-active-success {
    background-color: var(--imActionToggleSuccessBgCol);

    &:hover {
      background-color: var(--imActionToggleSuccessHoverBgCol);
    }
  }

  .lg-text {
    color: var(--imColSecondary);
    border-right: 1px solid #d9d9d9;
    padding: 5px 0 5px 10px;
    flex: 2;
  }
}

.toggle-active-disabled {
  cursor: not-allowed;
  background-color: var(--imActionToggleActiveDisabledBgCol);

  &:hover {
    background-color: var(--imActionToggleActiveDisabledHoverBgCol);
  }
}

.lg-item-wrapper {
  .link-toggle-status {
    border-bottom: 1px solid var(--imColBlack15);
    background-color: var(--imColBlack5);
    flex: inherit;
    align-items: center;

    &:hover {
      background-color: var(--imColBlack8);
    }
  }

  .icon-toggle {
    margin: 0 10px;
  }
}

.link-toggle-banners {
  display: flex;
  margin-bottom: 1rem;

  // specificity required to override .active-false color
  &.link-toggle-banners {
    color: var(--imMessageNeutralCol);

    &:hover {
      color: var(--imColSecondary);
    }
  }

  .icon-toggle {
    margin: 0;
  }

  .status-message {
    padding-left: 15px;
  }
}

.icon-toggle {
  position: relative;
  width: 32px;
  height: 18px;
  display: inline-block;

  // Slot
  &::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
    height: 20px;
    border-radius: 10px;
    border: 1px solid var(--imfToggleSlotBorderCol);
  }

  // Knob (thumb)
  &::after {
    content: '';
    position: absolute;
    top: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 1px solid var(--imfToggleKnobBorderCol);
    transition: left 200ms ease-in-out, background-color 200ms ease-in-out;
  }

  .users & {
    width: 25px;
    height: 16px;
  }
}

.active-true {
  &:hover .icon-toggle {
    // Knob (thumb)
    &::after {
      background-color: var(--imfToggleKnobActiveHoverBgCol);
    }
  }

  .icon-toggle {
    // Slot
    &::before {
      background-color: var(--imfToggleSlotActiveBgCol);
    }

    // Knob (thumb)
    &::after {
      left: 12px;
      background-color: var(--imfToggleKnobActiveBgCol);
    }
  }
}

.active-, // default class on page load
.active-false {
  &:hover .icon-toggle {
    // Knob (thumb)
    &::after {
      background-color: var(--imfToggleKnobInactiveHoverBgCol);
    }
  }

  .icon-toggle {
    // Slot
    &::before {
      background-color: var(--imfToggleSlotInactiveBgCol);
    }

    // Knob (thumb)
    &::after {
      left: 0;
      background-color: var(--imfToggleKnobInactiveBgCol);
    }
  }
}

.users,
.profile {
  .icon-toggle {
    margin: 0 5px;
  }

  .icon-circle-x {
    fill: var(--imColSecondary);
  }

  .link-toggle-status:hover {
    background: 0;
  }
}
