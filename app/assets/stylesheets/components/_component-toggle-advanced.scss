$slotWidth: 44px;
$knobSize: 24px;
$slotBorder: 2px;
$positionRight: 18px;
$slotHeight: calc(#{$knobSize} + (#{$slotBorder} * 2));

.toggleAdvancedWrapper {
  display: flex;
  align-items: center;
  width: -moz-max-content;
  width: max-content;
}

.section-heading-wrapper .toggleAdvancedWrapper {
  padding: 0 30px;
}

.toggleAdvText {
  @include fontSmoothing;
  font-family: var(--imHeadingToggleAdvLabelFontStack);
  font-size: var(--imHeadingToggleAdvLabelSize);
  color: var(--imToggleAdvLabelCol);
  margin-right: 6px;

  .section-heading-wrapper & {
    color: var(--imHeadingToggleAdvLabelCol);
  }
}

.toggleAdvSlotKnob,
.toggleAdvSlot {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.toggleAdvSlotWrapper {
  position: relative;
  width: $slotWidth;
  height: $slotHeight;
  overflow: hidden;
}

.toggleAdvSlotWrapper,
.toggleAdvSlot {
  border-radius: 100px;
}

.toggleAdvSlotKnob {
  z-index: 2;
}

// default position is .active-false
.toggleAdvSlotKnob::before {
  @include fontStackCondensedBold;
  @include fontSmoothing;
  font-size: 10.5px;
  text-align: center;
  line-height: 0.7;
  position: absolute;
  padding: 9px 4px;
  border-radius: 50%;
  top: $slotBorder;
  left: $slotBorder;
  width: $knobSize;
  height: $knobSize;

  .toggleAdvSlotWrapper:hover & {
    background-color: var(--imToggleAdvKnobHoverBgCol);
  }

  .section-heading-wrapper .toggleAdvSlotWrapper:hover & {
    background-color: var(--imHeadingToggleAdvKnobHoverBgCol);
  }
}

// Nav 2
.active-false .toggleAdvSlotKnob::before {
  content: 'OFF';
  color: var(--imToggleAdvKnobTextCol);
  text-indent: -1px;
  background-color: var(--imToggleAdvKnobBgCol);

  // Heading 1
  .section-heading-wrapper & {
    content: 'NO';
    color: var(--imHeadingToggleAdvKnobTextCol);
    background-color: var(--imHeadingToggleAdvKnobBgCol);
  }
}

// Nav 2
.active-true .toggleAdvSlotKnob::before {
  content: 'ON';
  color: var(--imToggleAdvKnobActiveTextCol);
  text-indent: 1px;
  left: $positionRight;
  background-color: var(--imToggleAdvKnobActiveBgCol);

  // Heading 1
  .section-heading-wrapper & {
    content: 'YES';
    color: var(--imHeadingToggleAdvKnobActiveTextCol);
    background-color: var(--imHeadingToggleAdvKnobActiveBgCol);
  }
}

.toggleAdvSlotKnob,
.toggleAdvSlotKnob::before,
.toggleAdvSlot {
  transition: 0.3s ease all;
}

.toggleAdvSlot {
  width: 100%;
  background-color: var(--imToggleAdvSlotBgCol);
  transition: 0.3s ease all;
  box-shadow: 0 0 0 2px var(--imToggleAdvSlotBorderCol) inset; // x y blur spread style
  z-index: $surface; // 1

  .active-true & {
    box-shadow: 0 0 0 2px var(--imToggleAdvSlotActiveBorderCol) inset; // x y blur spread style
    background-color: var(--imToggleAdvSlotActiveBgCol);
  }

  .section-heading-wrapper & {
    box-shadow: 0 0 0 2px var(--imHeadingToggleAdvSlotBorderCol) inset; // x y blur spread style
    background-color: var(--imHeadingToggleAdvSlotBgCol);
  }

  .section-heading-wrapper .active-true & {
    box-shadow: 0 0 0 2px var(--imHeadingToggleAdvSlotActiveBorderCol) inset; // x y blur spread style
    background-color: var(--imHeadingToggleAdvSlotActiveBgCol);
  }
}
