.toggleCheck {
  position: absolute;
  left: -9999px;
}

.toggleCheckLabel {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.toggleCheckPseudoToggle {
  position: relative;
  width: 28px;
  height: 16px;
  border-radius: 15px;
  background: var(--redDark);
}

.toggleCheckPseudoToggle::before {
  content: '';
  position: absolute;
  left: 3px;
  top: 3px;
  width: 10px;
  height: 10px;
  background: var(--imColWhite);
  border-radius: 50%;
  z-index: $surface; // 1
  transition: transform 150ms ease-in-out;
}

.toggleCheck:checked + .toggleCheckLabel .toggleCheckPseudoToggle {
  background: var(--imColSecondary);
}

.toggleCheck:checked + .toggleCheckLabel .toggleCheckPseudoToggle::before {
  transform: translateX(12px);
}

.toggleCheckText {
  margin-left: 5px;
  display: inline-block;
}
