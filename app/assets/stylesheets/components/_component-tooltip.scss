// https://kazzkiq.github.io/balloon.css/
// https://github.com/kazzkiq/balloon.css

div[data-balloon] {
  overflow: visible;
}

[data-balloon] {
  @include mediaQueryMin($bp-sm-2) {
    cursor: help;
    position: relative;
  }
}

[data-balloon]::after {

  @include mediaQueryMin($bp-sm-2) {
    font-family: var(--imTooltipFontStack);
    font-size: var(--imTooltipFontSize);
    line-height: var(--imTooltipLineHeight);
    text-align: center;
    font-style: normal;
    text-shadow: none;
    color: var(--imTooltipTextCol);
    filter: alpha(opacity = 0);
    position: absolute;
    opacity: 0;
    pointer-events: none;
    background-color: var(--imTooltipBgCol);
    border-radius: var(--imTooltipBorderRadius);
    content: attr(data-balloon);
    padding: var(--imTooltipPadding);
    width: var(--imTooltipWidth);
    white-space: normal;
    z-index: $air; // 10
    transition: all 0.18s ease-out 0.18s;
  }
}

// <span class="icon-info-wrapper data-no-width" data-balloon="..." data-balloon-pos="left">
.data-no-width[data-balloon]::after {
  width: auto;
}

[data-balloon]::before {
  @include mediaQueryMin($bp-sm-2) {
    width: 18px;
    height: 6px;
    filter: alpha(opacity = 0);
    opacity: 0;
    pointer-events: none;
    content: '';
    position: absolute;
    z-index: $air; // 10
    transition: all 0.18s ease-out 0.18s;
    // icon colour hack
    background-color: var(--imTooltipBgCol);
    mask-image: url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http://www.w3.org/2000/svg%22%20width%3D%2236px%22%20height%3D%2212px%22%3E%3Cpath%20 %20transform%3D%22rotate(0)%22%20d%3D%22M2.658,0.000%20C-13.615,0.000%2050.938,0.000%2034.662,0.000%20C28.662,0.000%2023.035,12.002%2018.660,12.002%20C14.285,12.002%208.594,0.000%202.658,0.000%20Z%22/%3E%3C/svg%3E');
    mask-repeat: no-repeat;
    mask-position: center;
    mask-size: contain;
    -webkit-mask-image: url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http://www.w3.org/2000/svg%22%20width%3D%2236px%22%20height%3D%2212px%22%3E%3Cpath%20 %20transform%3D%22rotate(0)%22%20d%3D%22M2.658,0.000%20C-13.615,0.000%2050.938,0.000%2034.662,0.000%20C28.662,0.000%2023.035,12.002%2018.660,12.002%20C14.285,12.002%208.594,0.000%202.658,0.000%20Z%22/%3E%3C/svg%3E');
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
    -webkit-mask-size: contain;
  }
}

[data-balloon]:hover::before,
[data-balloon]:hover::after,
[data-balloon][data-balloon-visible]::before,
[data-balloon][data-balloon-visible]::after {
  filter: alpha(opacity = 100);
  opacity: 1;
  pointer-events: auto;
}

[data-balloon][data-balloon-break]::after {
  white-space: pre;
}

[data-balloon][data-balloon-pos='up-left']::after {
  bottom: 100%;
  left: 0;
  margin-bottom: 11px;
  transform: translate(0, 10px);
  transform-origin: top;
}

[data-balloon][data-balloon-pos='up-left']::before {
  bottom: 100%;
  left: 5px;
  margin-bottom: 5px;
  transform: translate(0, 10px);
  transform-origin: top;
}

[data-balloon][data-balloon-pos='up-left']:hover::after,
[data-balloon][data-balloon-pos='up-left'][data-balloon-visible]::after {
  transform: translate(0, 0);
}

[data-balloon][data-balloon-pos='up-left']:hover::before,
[data-balloon][data-balloon-pos='up-left'][data-balloon-visible]::before {
  transform: translate(0, 0);
}

[data-balloon][data-balloon-pos='up-right']::after {
  bottom: 100%;
  right: 0;
  margin-bottom: 11px;
  transform: translate(0, 10px);
  transform-origin: top;
}

[data-balloon][data-balloon-pos='up-right']::before {
  bottom: 100%;
  right: 5px;
  margin-bottom: 5px;
  transform: translate(0, 10px);
  transform-origin: top;
}

[data-balloon][data-balloon-pos='up-right']:hover::after,
[data-balloon][data-balloon-pos='up-right'][data-balloon-visible]::after {
  transform: translate(0, 0);
}

[data-balloon][data-balloon-pos='up-right']:hover::before,
[data-balloon][data-balloon-pos='up-right'][data-balloon-visible]::before {
  transform: translate(0, 0);
}

[data-balloon][data-balloon-pos='up']::after {
  bottom: 100%;
  left: 50%;
  margin-bottom: 11px;
  transform: translate(-50%, 10px);
  transform-origin: top;
}

[data-balloon][data-balloon-pos='up']::before {
  bottom: 100%;
  left: 50%;
  margin-bottom: 5px;
  transform: translate(-50%, 10px);
  transform-origin: top;
}

[data-balloon][data-balloon-pos='up']:hover::after,
[data-balloon][data-balloon-pos='up'][data-balloon-visible]::after {
  transform: translate(-50%, 0);
}

[data-balloon][data-balloon-pos='up']:hover::before,
[data-balloon][data-balloon-pos='up'][data-balloon-visible]::before {
  transform: translate(-50%, 0);
}

[data-balloon][data-balloon-pos='left']::after {
  margin-right: 11px;
  right: 100%;
  top: 50%;
  transform: translate(10px, -50%);
}

[data-balloon][data-balloon-pos='left']::before {
  width: 6px;
  height: 18px;
  margin-right: 5px;
  right: 100%;
  top: 50%;
  transform: translate(10px, -50%);
  // icon colour hack
  background-color: var(--imTooltipBgCol);
  mask-image: url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http://www.w3.org/2000/svg%22%20width%3D%2212px%22%20height%3D%2236px%22%3E%3Cpath%20transform%3D%22rotate(-90 18 18)%22%20d%3D%22M2.658,0.000%20C-13.615,0.000%2050.938,0.000%2034.662,0.000%20C28.662,0.000%2023.035,12.002%2018.660,12.002%20C14.285,12.002%208.594,0.000%202.658,0.000%20Z%22/%3E%3C/svg%3E');
  mask-repeat: no-repeat;
  mask-position: center;
  mask-size: contain;
  -webkit-mask-image: url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http://www.w3.org/2000/svg%22%20width%3D%2212px%22%20height%3D%2236px%22%3E%3Cpath%20transform%3D%22rotate(-90 18 18)%22%20d%3D%22M2.658,0.000%20C-13.615,0.000%2050.938,0.000%2034.662,0.000%20C28.662,0.000%2023.035,12.002%2018.660,12.002%20C14.285,12.002%208.594,0.000%202.658,0.000%20Z%22/%3E%3C/svg%3E');
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  -webkit-mask-size: contain;
}

[data-balloon][data-balloon-pos='left']:hover::after,
[data-balloon][data-balloon-pos='left'][data-balloon-visible]::after {
  transform: translate(0, -50%);
}

[data-balloon][data-balloon-pos='left']:hover::before,
[data-balloon][data-balloon-pos='left'][data-balloon-visible]::before {
  transform: translate(0, -50%);
}

[data-balloon][data-balloon-pos='none']::after,
[data-balloon][data-balloon-pos='none']::before {
  display: none;
}

[data-balloon][data-balloon-pos='down']::after {
  left: 50%;
  margin-top: 11px;
  top: 100%;
  transform: translate(-50%, -10px);
}

[data-balloon][data-balloon-pos='down']::before {
  width: 18px;
  height: 6px;
  left: 50%;
  margin-top: 5px;
  top: 100%;
  transform: translate(-50%, -10px);
  // icon colour hack
  background-color: var(--imTooltipBgCol);
  mask-image: url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http://www.w3.org/2000/svg%22%20width%3D%2236px%22%20height%3D%2212px%22%3E%3Cpath%20transform%3D%22rotate(180%2018%206)%22%20d%3D%22M2.658,0.000%20C-13.615,0.000%2050.938,0.000%2034.662,0.000%20C28.662,0.000%2023.035,12.002%2018.660,12.002%20C14.285,12.002%208.594,0.000%202.658,0.000%20Z%22/%3E%3C/svg%3E');
  mask-repeat: no-repeat;
  mask-position: center;
  mask-size: contain;
  -webkit-mask-image: url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http://www.w3.org/2000/svg%22%20width%3D%2236px%22%20height%3D%2212px%22%3E%3Cpath%20transform%3D%22rotate(180%2018%206)%22%20d%3D%22M2.658,0.000%20C-13.615,0.000%2050.938,0.000%2034.662,0.000%20C28.662,0.000%2023.035,12.002%2018.660,12.002%20C14.285,12.002%208.594,0.000%202.658,0.000%20Z%22/%3E%3C/svg%3E');
  -webkit-mask-repeat: no-repeat;
  -webkit-mask-position: center;
  -webkit-mask-size: contain;
}

[data-balloon][data-balloon-pos='down']:hover::after,
[data-balloon][data-balloon-pos='down'][data-balloon-visible]::after {
  transform: translate(-50%, 0);
}

[data-balloon][data-balloon-pos='down']:hover::before,
[data-balloon][data-balloon-pos='down'][data-balloon-visible]::before {
  transform: translate(-50%, 0);
}
