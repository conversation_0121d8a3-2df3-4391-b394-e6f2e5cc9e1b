@keyframes fadeInOne {
  0% {
    opacity: 0;
    visibility: visible;
  }

  100% {
    opacity: 1;
    visibility: visible;
  }
}

@keyframes fadeInTwo {
  0% {
    opacity: 0;
    visibility: visible;
  }

  100% {
    opacity: 1;
    visibility: visible;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fadeInOut {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes translateFadeIn {
  0% {
    opacity: 0;
    transform: translate(0, 10px);
  }

  100% {
    opacity: 1;
    transform: translate(0, 0);
  }
}

@keyframes toastFoldBack {
  0% {
    transform: perspective(1000px) rotateX(0deg) translate(-50%, 0);
  }

  50% {
    transform: perspective(1000px) rotateX(90deg) translate(-50%, 0);
  }

  100% {
    transform: perspective(1000px) rotateX(0deg) translate(-50%, 0);
  }
}

@keyframes statusSpinner {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes blowUp {
  0% {
    transform: translate(0, 100px) scale(0.5);
  }

  100% {
    transform: translate(0, 0) scale(1);
  }
}

@keyframes rainbow {
  to {
    --hue: 360;
  }
}

@keyframes rainbow-linear {
  0% {
    background-position: 0% 50%;
  }

  100% {
    background-position: 100% 50%;
  }
}
