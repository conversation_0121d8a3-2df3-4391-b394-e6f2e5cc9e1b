.note-info-wrapper {
  padding: var(--imGapSmall) var(--imGapSmall) var(--imGapSmall) var(--imGapMedium);
  margin-bottom: var(--imGapMedium);
  background-color: var(--imNoteInfoBgCol);
  border: 1px solid var(--imNoteInfoBorderCol);
  border-left: 5px solid var(--imNoteInfoBorderLeftCol);
  border-bottom: 1px solid var(--imNoteInfoBorderBottomCol);

  + .imf-btn-wrapper {
    padding-top: 5px;
  }

  &.note-warning {
    border-left-color: var(--imNoteWarningBorderLeftCol);
  }

  &.note-alert {
    border-left-color: var(--imNoteAlertBorderLeftCol);
  }

  &.note-strong {
    border-left-color: var(--imNoteStrongBorderLeftCol);
  }

  &.note-approved {
    border-left-color: var(--imNoteApprovedBorderLeftCol);
  }

  &.note-sunny {
    border-left-color: var(--imNoteSunnyBorderLeftCol);
  }

  &.note-neutral {
    border-left-color: var(--imNoteNeutralBorderLeftCol);
  }

  &.note-asset {
    display: flex;
    flex-wrap: wrap;
    column-gap: var(--imGapSmall);
    row-gap: var(--imGapSmall);
  }

  img {
    filter: var(--imNoteInfoImgFilter);
  }
}

.note-info-plain-wrapper {
  padding: var(--imGapSmall) var(--imGapSmall) var(--imGapSmall) var(--imGapMedium);
  margin-bottom: var(--imGapMedium);
  background: var(--imMessageAccentedWrapperBgCol);
  border: 1px solid var(--imMessageAccentedWrapperBorderCol);

  .ni-text {
    color: var(--imMessageAccentedTextCol);
  }
}

.note-info-basic {
  padding: var(--imGapSmall) var(--imGapSmall) var(--imGapSmall) var(--imGapMedium);
  background: var(--imColWhite);
}

// ni = note-info
.ni-heading {
  color: var(--imColBlack);
}

.ni-list {
  .stock-index & {
    padding-left: var(--imGapLarge);
  }
}

.list-ordered,
.list-unordered {
  @include listReset;

  &.ni-list {
    margin-bottom: 0;
  }
}

.nested-list {
  list-style-type: circle;
  padding-left: 20px;
}

.ni-text {
  color: var(--imNoteInfoTextCol);
  margin: 0;

  strong {
    color: var(--imNoteInfoTextStrongCol);
  }

  .stock-index & {
    list-style: disc;
    margin-left: var(--imGapLarge);
  }

  &.ni-text-flexed {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }
}

.ni-note-icon {
  width: 20px;
  height: 20px;
  margin-right: -3px;
  margin-top: -3px;

  .note-warning & {
    fill: var(--imColRed);
  }

  .note-alert & {
    fill: var(--imColRed);
  }

  .note-strong & {
    fill: var(--imMessageStrongCol);
  }

  .note-approved & {
    fill: var(--imMessageSuccessIconFill);
  }

  .note-sunny & {
    fill: $yellow;
  }

  .note-neutral & {
    fill: var(--imColBlack55);
  }
}

.note-sticky {
  position: sticky;
  top: 0;
  z-index: $surface;
}

.message-risky {
  color: var(--imColSecondary);
  border: 2px solid var(--imColSecondary);
  border-bottom: 0;
  padding: 8px;
  background-color: var(--imColWhite);
}

.section-risky .imf-row-wrapper-border {
  border: 2px solid var(--imColSecondary);
  border-radius: 0;
}

.note-info-wrapper-subdued {
  width: 100%;
}

.message-admin-wrapper {
  margin-bottom: var(--imGapLarge);
}

.message-admin-text {
  font-size: 12px;
  line-height: 1.2rem;
  margin: 0;
}

.feature-status {
  opacity: 0.7;
  border: 1px solid #ffffff25;
  border-radius: 5px;
  background-color: #ffffff20;
  padding: 0 3px;
  margin-left: 3px;
}
