.error-wrapper {
  background: var(--imNoteErrorWrapperBgCol);
  border-left: 5px solid var(--imNoteErrorBorderLeftCol);
  padding: 10px 10px 10px 15px;
  margin-bottom: 20px;

  .error-list {
    padding-left: 15px;
  }

  .error-item {
    list-style: initial;
    list-style-type: decimal;
  }
}

.error-heading,
.error-sub-heading {
  color: var(--imNoteErrorHeading);
  margin: 0;
}

.field_with_errors {
  display: flex;
  flex-direction: column;

  label,
  strong,
  .imf-mandatory {
    color: var(--imThemeColAlert);
  }

  input[type='email'],
  input[type='search'],
  input[type='text'],
  input[type='tel'],
  input[type='url'],
  input[type='password'],
  input[type='color'],
  input[type='image'],
  input[type='number'],
  input[type='date'],
  input[type='month'],
  input[type='week'],
  input[type='time'],
  input[type='datetime'],
  input[type='datetime-local'],
  select {
    border: 1px solid var(--imThemeColAlert);

    &:focus {
      border: 1px solid var(--imThemeColAlert);
    }
  }
}

.form-image-module.field_with_errors {
  border: 1px solid var(--imColRed);
  flex-direction: row;

  .icon-image-module {
    fill: var(--imColRed);
  }

  .fam-action-text {
    color: var(--imColRed);
  }
}

.form-asset-module-footer.field_with_errors {
  border: 1px solid var(--imColRed);
  flex-direction: row;

  .icon-asset-module {
    fill: var(--imColRed);
  }

  .fam-action-text {
    color: var(--imColRed);
  }
}
