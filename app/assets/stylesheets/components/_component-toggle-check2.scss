.toggleCheck {
  position: absolute;
  left: -9999px;
}

.toggleCheckLabel {
  cursor: pointer;
  display: flex;
  align-items: center;
}

.toggleCheckPseudoToggle {
  position: relative;
  width: 44px;
  height: 20px;
  border-radius: 15px;
  background: var(--redDark);
}

.toggleCheckPseudoToggle::before,
.toggleCheckPseudoToggle::after {
  content: '';
  position: absolute;
}

.toggleCheckPseudoToggle::before {
  left: 3px;
  top: 3px;
  width: 14px;
  height: 14px;
  background: var(--imColWhite);
  border-radius: 50%;
  z-index: $surface; // 1
  transition: transform 300ms ease-in-out;
}

.toggleCheckPseudoToggle::after {
  content: 'NO';
  @include primaryFontStackBold;
  font-size: 10px;
  color: #fff;
  top: 3.5px;
  right: 11px;
  width: 10px;
  height: 10px;
  transform: translateY(-50%);
  background-size: 10px 10px;
}

.toggleCheck:checked + .toggleCheckLabel .toggleCheckPseudoToggle {
  background: var(--imColSecondary);
}

.toggleCheck:checked + .toggleCheckLabel .toggleCheckPseudoToggle::before {
  transform: translateX(24px);
}

.toggleCheck:checked + .toggleCheckLabel .toggleCheckPseudoToggle::after {
  content: 'YES';
  color: #fff;
  left: 6px;
}

.toggleCheckText {
  margin-left: 5px;
  display: inline-block;
}
