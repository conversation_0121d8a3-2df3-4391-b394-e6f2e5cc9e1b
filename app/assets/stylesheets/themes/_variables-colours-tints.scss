:root {
  // Black Abbadon
  --imColBlackAbaddon150: #000;
  --imColBlackAbaddon145: #030000;
  --imColBlackAbaddon140: #070303;
  --imColBlackAbaddon135: #0a0707;
  --imColBlackAbaddon130: #0e0a0b;
  --imColBlackAbaddon125: #110e0e;
  --imColBlackAbaddon120: #151112;
  --imColBlackAbaddon115: #181415;
  --imColBlackAbaddon110: #1c1819;
  --imColBlackAbaddon105: #1f1b1c;
  --imColBlackAbaddon100: #231f20;
  --imColBlackAbaddon98: #252223;
  --imColBlackAbaddon96: #272526;
  --imColBlackAbaddon95: #2a2627;
  --imColBlackAbaddon94: #292829;
  --imColBlackAbaddon92: #2b2b2c;
  --imColBlackAbaddon90: #312d2e;
  --imCol<PERSON>lackAbaddon85: #383435;
  --imColBlackAbaddon80: #3f3b3c;
  --imColBlackAbaddon75: #464243;
  --imColBlackAbaddon70: #4d494a;
  --imColBlackAbaddon65: #545051;
  --imColBlackAbaddon60: #5b5758;
  --imColBlackAbaddon55: #625e5f;
  --imColBlackAbaddon50: #696566;
  --imColBlackAbaddon45: #706c6d;
  --imColBlackAbaddon40: #777374;
  --imColBlackAbaddon35: #7e7a7b;
  --imColBlackAbaddon30: #858182;
  --imColBlackAbaddon25: #8c8889;
  --imColBlackAbaddon20: #939090;
  --imColBlackAbaddon15: #9a9797;
  --imColBlackAbaddon10: #a19e9e;
  --imColBlackAbaddon9: #b1aead;
  --imColBlackAbaddon8: #c1bdbc;
  --imColBlackAbaddon7: #d1cccb;
  --imColBlackAbaddon6: #e1dbda;
  --imColBlackAbaddon5: #f1eae9;
  --imColBlackAbaddon4: #f6f3f2;
  --imColBlackAbaddon3: #faf7f6;
  --imColBlackAbaddon2: #fdfbfa;
  --imColBlackAbaddon1: #fefdfd;
  --imColBlackAbaddon0: #fff;

  // SECONDARY COLOURS
  --imColSecondary200: #{tone-range($imColSecondaryHex, 200)};
  --imColSecondary195: #{tone-range($imColSecondaryHex, 195)};
  --imColSecondary190: #{tone-range($imColSecondaryHex, 190)};
  --imColSecondary185: #{tone-range($imColSecondaryHex, 185)};
  --imColSecondary180: #{tone-range($imColSecondaryHex, 180)};
  --imColSecondary175: #{tone-range($imColSecondaryHex, 175)};
  --imColSecondary170: #{tone-range($imColSecondaryHex, 170)};
  --imColSecondary165: #{tone-range($imColSecondaryHex, 165)};
  --imColSecondary160: #{tone-range($imColSecondaryHex, 160)};
  --imColSecondary155: #{tone-range($imColSecondaryHex, 155)};
  --imColSecondary150: #{tone-range($imColSecondaryHex, 150)};
  --imColSecondary145: #{tone-range($imColSecondaryHex, 145)};
  --imColSecondary140: #{tone-range($imColSecondaryHex, 140)};
  --imColSecondary135: #{tone-range($imColSecondaryHex, 135)};
  --imColSecondary130: #{tone-range($imColSecondaryHex, 130)};
  --imColSecondary125: #{tone-range($imColSecondaryHex, 125)};
  --imColSecondary120: #{tone-range($imColSecondaryHex, 120)};
  --imColSecondary115: #{tone-range($imColSecondaryHex, 115)};
  --imColSecondary110: #{tone-range($imColSecondaryHex, 110)};
  --imColSecondary105: #{tone-range($imColSecondaryHex, 105)};
  --imColSecondary100: #{tone-range($imColSecondaryHex, 100)};
  --imColSecondary95: #{tone-range($imColSecondaryHex, 95)};
  --imColSecondary90: #{tone-range($imColSecondaryHex, 90)};
  --imColSecondary85: #{tone-range($imColSecondaryHex, 85)};
  --imColSecondary80: #{tone-range($imColSecondaryHex, 80)};
  --imColSecondary75: #{tone-range($imColSecondaryHex, 75)};
  --imColSecondary70: #{tone-range($imColSecondaryHex, 70)};
  --imColSecondary65: #{tone-range($imColSecondaryHex, 65)};
  --imColSecondary60: #{tone-range($imColSecondaryHex, 60)};
  --imColSecondary55: #{tone-range($imColSecondaryHex, 55)};
  --imColSecondary50: #{tone-range($imColSecondaryHex, 50)};
  --imColSecondary45: #{tone-range($imColSecondaryHex, 45)};
  --imColSecondary40: #{tone-range($imColSecondaryHex, 40)};
  --imColSecondary35: #{tone-range($imColSecondaryHex, 35)};
  --imColSecondary30: #{tone-range($imColSecondaryHex, 30)};
  --imColSecondary25: #{tone-range($imColSecondaryHex, 25)};
  --imColSecondary20: #{tone-range($imColSecondaryHex, 20)};
  --imColSecondary15: #{tone-range($imColSecondaryHex, 15)};
  --imColSecondary10: #{tone-range($imColSecondaryHex, 10)};
  --imColSecondary7: #{tone-range($imColSecondaryHex, 7)};
  --imColSecondary5: #{tone-range($imColSecondaryHex, 5)};
}
