:root {
  // Generates a full set of CSS custom properties for black tints (from pure black to white) based on the given base color.
  @include imColTintsBlack(#000000);
  // LOGO
  --imLogoCol: var(--imColPrimary); // dot
  --imLogoFill: var(--imColWhite);
  --imLogoWidth: 120px;
  --imLogoHeight: 22px;
  --imLogoBlockPaddingMobile: 3px;
  --imLogoBlockPaddingDesktop: 20px;

  // NAV 1 - Welcome Message
  --nav1WelcomeMessageFontStack: var(--imPrimaryFontStackSemiBold);
  --nav1WelcomeMessageCol: var(--imColWhite);
  // NAV 1
  --nav1WrapperBg: var(--imColBlack);
  --nav1WrapperBgSize: auto;
  --nav1WrapperBgAnimation: none;
  --nav1BgCol: transparent;
  --nav1HoverBgCol: transparent;
  --nav1ActiveBgCol: transparent;
  --nav1PaddingMobile: var(--imLogoBlockPaddingMobile) 3px;
  --nav1PaddingDesktop: var(--imLogoBlockPaddingDesktop) 14px;
  --nav1borderBottomPosition: 0; // bottom position of the border
  --nav1borderBottomThickness: 0;
  --nav1borderBottomMarginLeft: 8px; // required to visually center the border
  --nav1borderBottomTransition: width 300ms ease-in-out;
  --nav1HoverBorderBottomWidth: 70%;
  --nav1HoverBorderBottomCol: var(--imColWhite);
  --nav1ActiveborderBottomWidth: 70%;
  --nav1ActiveborderBottomCol: var(--imColWhite);
  // NAV 1 - Icon
  --nav1IconFill: var(--imColBlackAbaddon30);
  --nav1HoverIconFill: var(--imThemeColNavAccent);
  --nav1HoverIconScale: none;
  --nav1ActiveIconFill: var(--imThemeColNavAccent);
  // NAV 1 - Text
  --nav1TextFontStack: var(--imPrimaryFontStackBold);
  --nav1TextCol: var(--imColBlackAbaddon30);
  --nav1HoverTextCol: var(--imThemeColNavAccent);
  --nav1ActiveTextCol: var(--imThemeColNavAccent);

  // HEADER - h1 Heading
  --heading1BgCol: var(--imColPrimary);
  --heading1FontStack: var(--imPrimaryFontStack);
  --heading1Size: 20px;
  --heading1LineHeight: 1;
  --heading1Col: var(--imColBlack);
  --heading1Padding: 12px 0;
  // HEADER = Toggles - see Toggles
  // HEADER - Flash Message

  --imFlashMessageWrapperBgCol: var(--imColPrimary);
  --imFlashMessageTextCol: var(--imColBlack);
  --imFlashMessageIconFill: var(--imColBlack);

  // HOME SEARCH
  --imHomeSearchWrapperBgCol: var(--imColBlack8);
  --imHomeSearchLabelFontStack: var(--imPrimaryFontStackSemiBold);
  --imHomeSearchLabelCol: var(--imColBlack);
  // HOME - Site Filter
  --imHomeSiteFilterWrapperBorderCol: var(--imColBlack15);
  --imHomeSiteFilterWrapperBgCol: var(--imGlobalWrapperBgCol);
  --imHomeSiteFilterLabelFontStack: var(--imPrimaryFontStackSemiBold);
  --imHomeSiteFilterLabelSize: 13px;
  --imHomeSiteFilterLabelCol: var(--imColTextPrimary);
  --imHomeSiteFilterLabelHoverCol: var(--imColSecondary);
  --imHomeSiteFilterLabelActiveCol: var(--imColBlack);
  // HOME - Sections (Dealer / Group / Brand)
  --imHomeSectionBaseBgCol: var(--imColWhite);
  --imHomeSectionContrastBgCol: var(--imColBlack3);
  // HOME - Listing Links
  --imHomeLinkTextCol: var(--imColBlackAbaddon);
  --imHomeLinkHoverTextCol: var(--imColSecondary);
  --imHomeLinkCloneBgCol: var(--imColSecondary7);
  --imHomeLinkCloneBgRadius: 15px;
  --imHomeLinkCloneFontStack: var(--imSecondaryFontStackSemiBold);
  --imHomeLinkCloneTextCol: var(--imColBlackAbaddon);
  --imHomeLinkCloneHoverTextCol: var(--imColSecondary);
  --imHomeLinkCloneIconFill: var(--imColBlackAbaddon);
  --imHomeLinkCloneHoverIconFill: var(--imColSecondary);
  --imHomeLinkCloneHoverIconTransform: var(--imIconTransform);

  // NAV 2
  --nav2WrapperBgCol: var(--imColBlackAbaddon95);
  --nav2WrapperBorderBottom: 0; // e.g. 10px solid var(--imColPrimary)
  --nav2WrapperBoxShadow: 0 2px 5px 1px rgba(0, 0, 0, 0.15);
  // NAV 2 - List
  --nav2ListBorderBottomColMobile: var(--imColBlackAbaddon80);
  // NAV2 - Link
  --nav2LinkBgCol: transparent;
  --nav2LinkHoverBgCol: transparent;
  --nav2LinkActiveBgCol: transparent;
  // NAV 2 - Icon
  --nav2LinkIconFill: var(--imColBlackAbaddon30);
  --nav2LinkHoverIconFill: var(--imThemeColNavAccent);
  --nav2LinkActiveIconFill: var(--imThemeColNavAccent);
  // NAV 2 - Text
  --nav2LinkTextFontStack: var(--imPrimaryFontStackBold);
  --nav2LinkTextCol: var(--imColBlackAbaddon30);
  --nav2LinkHoverTextCol: var(--imThemeColNavAccent);
  --nav2LinkActiveTextCol: var(--imThemeColNavAccent);
  // NAV 2 - Build Trigger
  --nav2BuildTriggeBorderRadius: var(--imThemeBorderRadiusSm);
  --nav2BuildTriggerBgCol: var(--imColBlack);
  --nav2BuildTriggerHoverBgCol: var(--imColSecondary);
  --nav2BuildTriggerBorderCol: var(--imColBlackAbaddon80);
  --nav2BuildTriggerHoverBorderCol: var(--imColSecondary80);
  --nav2BuildTriggerIconFill: var(--imColWhite);
  --nav2BuildTriggerDisabledTextCol: var(--imColBlackAbaddon60);
  --nav2BuildTriggerDisabledBgCol: var(--imColBlackAbaddon100);
  --nav2BuildTriggerDisabledBorderCol: var(--imColBlackAbaddon60);
  --nav2BuildTriggerDisabledIconFill: var(--imColBlackAbaddon60);
  // NAV 2 - Build Trigger Dropdown
  --nav2BuildMenuWrapperBorderRadius: var(--imThemeBorderRadiusSm);
  --nav2BuildMenuWrapperBgCol: var(--imColWhite);
  --nav2BuildMenuItemHoverBgCol: var(--imColBlack7);
  --nav2BuildMenuItemDisabledBgCol: var(--imColBlack10);
  --nav2BuildMenuItemTextCol: var(--imColBlack);
  --nav2BuildMenuItemDisabledTextCol: var(--imColBlack40);

  // NAV 3/4 - Collapse Button
  --navCollapseBtnPaddingBlock: 3px;
  --navCollapseBtnHoverBgCol: var(--imColSecondary);
  --navCollapseBtnHoverIconFill: var(--imColWhite);
  --navCollapseBtnIconSize: 25px;

  // NAV 3/4 - Collapse Tooltip
  --navCollapsedTooltipFontStack: var(--imPrimaryFontStackSemiBold);
  --navCollapsedTooltipTextSize: 11px;
  --navCollapsedTooltipTextCol: var(--imColWhite);
  --navCollapsedTooltipLeft: 90%;
  --navCollapsedTooltipWidth: max-content;
  --navCollapsedTooltipMinWidth: 50px;
  --navCollapsedTooltipMaxWidth: 150px;
  --navCollapsedTooltipPadding: 6px 15px;
  --navCollapsedTooltipBgCol: var(--imColBlack);
  --navCollapsedTooltipBorderRadius: var(--imThemeBorderRadiusSm);

  // NAV 3 - Collapse Button
  --nav3CollapseBtnBgCol: var(--imColBlackAbaddon95);
  --nav3CollapseBtnIconFill: var(--imColBlackAbaddon70);
  // NAV 3 - Wrapper
  --nav3WrapperBgCol: var(--imColBlackAbaddon90);
  --nav3BgCol: transparent;
  --nav3HoverBgCol: transparent;
  --nav3ActiveBgCol: transparent;
  --nav3ActiveBorderLeftWidth: 3px;
  --nav3ActiveBorderLeftCol: var(--imColPrimary);
  --nav3ActiveBorderLeftHeight: 45%;
  --nav3PaddingMobile: 10px;
  --nav3PaddingDesktop: 12px 10px;
  // NAV 3 - Icon
  --nav3IconFill: var(--imColBlackAbaddon30);
  --nav3HoverIconFill: var(--imColWhite);
  --nav3HoverIconScale: none; // var(--imIconTransform);
  --nav3ActiveIconFill: var(--imColWhite);
  // NAV 3 - Text
  --nav3TextFontStack: var(--imPrimaryFontStack);
  --nav3TextCol: var(--imColBlackAbaddon30);
  --nav3HoverTextCol: var(--imColWhite);
  --nav3ActiveTextCol: var(--imColWhite);

  // NAV 4 - Collapse Button
  --nav4CollapseBtnBgCol: var(--imColBlackAbaddon90);
  --nav4CollapseBtnIconFill: var(--imColBlackAbaddon60);
  // NAV 4 -Wrapper
  --nav4WrapperBgCol: var(--imColBlackAbaddon80);
  --nav4WrapperBorderRightCol: var(--imColBlack10);
  --nav4BgCol: transparent;
  --nav4HoverBgCol: transparent;
  --nav4ActiveBgCol: transparent;
  --nav4ActiveBorderLeftWidth: 3px;
  --nav4ActiveBorderLeftCol: var(--imColPrimary);
  --nav4ActiveBorderLeftHeight: 45%;
  --nav4PaddingMobile: 10px;
  --nav4PaddingDesktop: 12px 10px;
  // NAV 4 - Icon
  --nav4IconFill: var(--imColBlackAbaddon20);
  --nav4HoverIconFill: var(--imColWhite);
  --nav4HoverIconScale: none; // var(--imIconTransform);
  --nav4ActiveIconFill: var(--imColWhite);
  // NAV 4 - Text
  --nav4TextFontStack: var(--imPrimaryFontStack);
  --nav4TextCol: var(--imColBlackAbaddon20);
  --nav4HoverTextCol: var(--imColWhite);
  --nav4ActiveTextCol: var(--imColWhite);

  // GARAGE
  --imGaragePageWrapperBgCol: var(--imGlobalWrapperBgCol);
  // GARAGE  - Big Icon Links
  --imGarageLinkBgCol: var(--imColBlack3);
  --imGarageLinkBorderCol: var(--imColBlack10);
  --imGarageLinkHoverBorderCol: var(--imColBlack50);
  --imGarageLinkRadius: var(--imThemeBorderRadiusMd);
  // GARAGE  - Big Icon
  --imGarageLinkIconFill: var(--imColBlack40);
  --imGarageLinkHoverIconFill: var(--imColBlack60);
  // GARAGE  - Big Icon Text
  --imGarageLinkTextFontStack: var(--imPrimaryFontStackBlack);
  --imGarageLinkTextSize: var(--imGlobalFontSize);
  --imGarageLinkTextTransform: uppercase;
  --imGarageLinkTextCol: var(--imColBlack40);
  --imGarageLinkHoverTextCol: var(--imColBlack60);

  // --- THEME & GLOBAL GROUPS ---

  // GLOBAL WRAPPERS
  --imGlobalWrapperBgCol: var(--imColWhite);

  // GLOBAL COLOURS
  --imThemeColNavAccent: var(--imColWhite);
  --imThemeColSuccess: var(--imColSecondary);
  --imThemeColAlert: var(--imColRed);
  // Text Accent Colours
  --imfTextAccentNeutraTextCol: var(--imColTextPrimary);
  --imfTextAccentNeutralBgCol: var(--imColBlack5);
  --imfTextAccentNeutralBorderCol: var(--imColBlack20);
  --imfTextAccentSecondaryBgCol: var(--imColSecondary7);
  --imfTextAccentSecondaryBorderCol: var(--imColSecondary70);
  // CMS Config Accents
  --imfCmsConfigAccentBgCol: var(--imColBlack5);
  --imfCmsConfigAccentBorderCol: var(--imColBlack15);
  --imfCmsConfigAccentHoverBorderCol: var(--imColBlack40);
  --imfCmsConfigAccentRadius: var(--imThemeBorderRadiusSm);

  // GLOBAL FONTS
  --imColTextPrimary: var(--imColBlackAbaddon50);
  --imColTextStrong: var(--imColBlackAbaddon);
  --imColLinkPrimary: var(--imColBlackAbaddon); // Global Links
  --imColLinkHoverPrimary: var(--imColSecondary); // Global Links Hover
  --imGlobalFontSize: 13px;
  --imGlobalSubHeadingFontSize: 13px;
  --imTextStrongFontStack: var(--imSecondaryFontStackSemiBold);
  --imTextSelectionTextCol: var(--imColWhite); // Text colour of selected text
  --imTextSelectionBgCol: var(--imColBlackAbaddon); // Background colour of selected text

  // GLOBAL Gaps
  --imGapTiny: 5px;
  --imGapSmall: 10px;
  --imGapMedium: 15px;
  --imGapLarge: 20px;
  --imGapExtraLarge: 25px;

  // GLOBAL PUBLISHING
  --imToastStatusIconSize: 40px; // Netlify Publishing - Build Menu and Toast
  --imToastStatusSpinnerSize: calc(var(--imToastStatusIconSize) - 5px);
  --imPublishBtnPadding: 8px; // consistent padding for button variants with or without Text

  // GLOBAL HELP WIKI
  --imHelpWikiTransitionDuration: 150ms; // coordinate help section transition animations
  --imHelpWikiOpenWidthMobile: 100%;
  --imHelpWikiOpenWidthDesktop: 40%;
  --imHelpWikiFontStack: var(--imSecondaryFontStack);
  --imHelpWikiTextSize: 14px;
  --imHelpWikiTextCol: var(--imColTextPrimary);
  --imHelpWikiAccent: var(--imColBlack);
  --imHelpWikiBgCol: var(--imColWhite);
  --imHelpWikiBorderLeft: var(--imColBlack15);
  --imHelpWikiCloseBtnCol: var(--imColTextPrimary);
  --imHelpWikiCloseBtnHoverCol: var(--imColSecondary);
  --imHelpWikiCloseBtnBgCol: var(--imColBlack3);
  --imHelpWikiCloseBtnHoverBgCol: var(--imColBlack10);

  // THEME PAGES
  --imThemePageSecondaryBgCol: var(--imColSecondary7);
  --imThemePageSecondaryBorderCol: var(--imColSecondary20);
  --imThemePageTertiaryBgCol: var(--imColGreenLt);
  --imThemePageTertiaryBorderCol: var(--imColGreenDk);
  --imThemePageBrandTextCol: var(--imColBlackAbaddon);

  // THEME ICONS
  --imThemeIconFill: var(--imColBlackAbaddon50);
  --imThemeIconHoverFill: var(--imColSecondary);
  --imNeutralSoftTextCol: var(--imColBlackAbaddon50);
  --imNeutralSoftFill: var(--imColBlackAbaddon50);
  --imNeutralFill: var(--imColBlackAbaddon);
  --imNeutralTextCol: var(--imColBlackAbaddon);
  // Standard Icons
  --imfImgSvgFilter: invert(0.5);
  --imfImgSvgHoverFilter: invert(0);
  --imfImgSvgHoverTransform: var(--imIconTransform);
  --imfImgSvgActiveFilter: invert(0);
  --imfImgSvgTextSize: 12px;

  // THEME RADIUS
  --imThemeBorderRadiusSm: 4px;
  --imThemeBorderRadiusMd: 6px;
  --imThemeBorderRadiusLg: 10px;

  // THEME SWITCHER
  --imThemeSwitcherWrapperTop: 180px;
  --imThemeSwitcherBtnsWrapperBgCol: var(--imColBlack);
  --imThemeSwitcherBtnTextCol: var(--imColWhite);
  --imThemeSwitcherBtnBgCol: var(--imColBlack80);
  --imThemeSwitcherBtnHoverBgCol: var(--imColSecondary);
  --imThemeSwitcherBtnActiveBgCol: var(--imColSecondary);
  --imThemeSwitcherBtnBorderRadius: 3px;
  --imThemeSwitcherKnobLeft: -20px;
  --imThemeSwitcherKnobSize: 36px;
  --imThemeSwitcherKnobRadius: 50% 0 0 50%;
  --imThemeSwitcherKnobBgCol: var(--imColBlack);
  --imThemeSwitcherKnobPadding: 3px;
  --imThemeSwitcherIconSize: 18px;
  --imThemeSwitcherIconFill: var(--imColWhite);
  --imThemeVariantBtnBgCol: var(--imColBlack90);
  --imThemeVariantBtnIconSize: 20px;
  --imThemeVariantBtnIconFill: var(--imColBlack40);
  --imThemeVariantBtnHoverIconFill: var(--imColWhite);

  // RAILS ALERTS - Modal, Turbolinks, Login Flash
  --imModalAlertBgCol: rgba(255, 22, 68, 0.75);
  --imModalAlertMessageBgCol: var(--imColWhite);
  --imModalAlertMessageBorderCol: var(--imColBlack10);
  --imTurbolinksProgressBarCol: var(--imColSecondary);
  --imFwFlashMessageWrapperBgCol: var(--imColSecondary);
  --imFwFlashMessageTextCol: var(--imColWhite);

  // TOOLTIP - Balloon
  --imTooltipFontStack: var(--imSecondaryFontStack);
  --imTooltipFontSize: 11px;
  --imTooltipLineHeight: 1.2;
  --imTooltipTextCol: var(--imColWhite);
  --imTooltipBgCol: var(--imColBlackAbaddon);
  --imTooltipBorderRadius: var(--imThemeBorderRadiusSm);
  --imTooltipPadding: 0.5em 1em;
  --imTooltipWidth: 200px;

  // Items Enabled
  --imfItemEnabledTextCol: var(--imColSecondary);
  --imfItemEnabledBgCol: var(--imColBlack90);
  --imfItemEnabledFillCol: var(--imColSecondary);
  // Disabled Items
  --imfItemDisabledTextCol: var(--imColRed);
  --imfItemDisabledBgCol: var(--imColBlack70);
  --imfItemDisabledFillCol: var(--imColBlack60);
  // Note Item Brand Info
  --imfINoteBrandBgCol: var(--imColBlack5);
  --imfINoteBrandBorderCol: var(--imColBlack15);

  // FORMS
  --imfFormBgCol: var(--imColBlack2);
  --imfFormItemMargin: 15px;

  // FORMS - Breadcrumbs
  --imfBreadCrumbWrapperBgCol: var(--imColBlack5);
  --imfBreadCrumbWrapperBorderBottom: var(--imColBlack12);
  --imfBreadCrumbLinkFontStack: var(--imPrimaryFontStack);
  --imfBreadCrumbLinkCol: var(--imColSecondary);
  --imfBreadCrumbLinkHoverCol: var(--imColSecondary);
  --imfBreadCrumbFontStack: var(--imPrimaryFontStack);
  --imfBreadCrumbTextCol: var(--imColTextPrimary);
  --imfBreadCrumbTriangleRightCol: var(--imColBlack30); // The colour of the arrow
  --imfBreadCrumbTriangleRightBgCol: var(--imColBlack5); // Should match the background colour (mask)
  // FORMS - Heading
  --imfFormHeadingFontStack: var(--imPrimaryFontStackLight);
  --imfFormHeadingSizeMobile: 22px;
  --imfFormHeadingSizeDesktop: 24px;
  --imfFormHeadingCol: var(--imColTextPrimary);
  // FORMS - Labels
  --imfLabelFontStack: var(--imSecondaryFontStack);
  --imfLabelTextSize: 13px;
  --imfLabelCol: var(--imColBlack);
  --imfLabelMarginBottom: 5px;
  --imfLabelMandatoryCol: var(--imColSecondary);
  --imfLabelMandatorySize: 14px;
  // FORMS - Inputs
  --imfInputHeight: 38px;
  --imfInputCol: var(--imColBlack50);
  --imfInputFocusCol: var(--imColBlack80);
  --imfInputBgCol: var(--imColWhite);
  --imfInputBorderCol: var(--imColBlack10);
  --imfInputFocusBorderCol: var(--imColBlack);
  --imfInputBorderRadius: var(--imThemeBorderRadiusSm);
  --imfInputPlaceholderCol: var(--imColBlack20);
  --imfInputReadOnlyBgCol: var(--imColBlack2);
  --imputIconFilterDkTheme: none; // not required for light themes
  --imputColorSwatchBorderCol: none;  // not required for light themes
  // FORMS - TinyMCE
  // dark theme only  - doesn't apply to light themes
  // theme files copied from original TinyMCE
  // app/assets/javascripts/tinymce/skins/ui/oxide-dark/skin.css

  // See theme_switcher_tinymce.js
  //content_style: isDark
  //    ? "body { background-color: #141414; color: #fff; }"
  //    : "body { background-color: #fff; color: #222; }",

  // FORMS - Choices Heading
  --imfChoicesHeadingCol: var(--imColTextPrimary);
  --imfChoicesHeadingBorderBottomCol: var(--imColBlack3);
  // FORMS - Choices Button
  --imfChoicesBtnFocusBoxShadowCol: var(--imColBlack);
  --imfChoicesBtnBorderLeftCol: var(--imColBlack50);
  // FORMS - Choices Input
  --imfChoicesInputBorderBottomCol: var(--imColBlack15);
  --imfChoicesSelectedInputBgCol: var(--imColWhite);
  --imfChoicesDisabledInnerInputBgCol: var(--imColBlack8);
  --imfChoicesInputTextSize: var(--imGlobalFontSize);
  --imfChoicesInputBgCol: var(--imColBlack3);
  // FORMS - Choices Selected
  --imfChoicesSelectedBorderCol: var(--imColBlack80);
  --imfChoicesSelectedOpenBorderCol: var(--imColBlack80);
  // FORMS - Choices Inner
  --imfChoicesInnerBgCol: var(--imColBlack3);
  --imfChoicesInnerBorderCol: var(--imColBlack15);
  --imfChoicesFocusOpenInnerBorderCol: var(--imColBlack30);
  --imfChoicesInnerTextSize: var(--imGlobalFontSize);
  // FORMS - Choices Item
  --imfChoicesItemTextCol: var(--imColWhite);
  --imfChoicesItemBgCol: var(--imColBlack);
  --imfChoicesDisabledItemBgCol: var(--imColBlack35);
  --imfChoicesItemBgBorderCol: var(--imColBlack);
  --imfChoicesDisabledItemBorderCol: var(--imColBlack40);
  --imfChoicesItemMultipleBgCol: var(--imColBlack);
  --imfChoicesItemMultipleBorderCol: var(--imColBlack);
  // FORMS - Choices Dropdown
  --imfChoicesDropdownBgCol: var(--imColWhite);
  --imfChoicesOpenDropdownBorderCol: var(--imColBlack30);
  --imfChoicesDropdownBorderCol: var(--imColBlack15);
  --imfChoicesDropdownTextSize: var(--imGlobalFontSize);
  --imfChoicesDropdowItemHighlightBgCol: var(--imColBlack5);

  // FORMS - Recurring List Placeholder
  --imfPlaceholderListWrapperBgCol: var(--imColWhite);
  // FORMS - Placeholder List
  --imfPlaceholderListRowBgCol: var(--imColWhite);
  --imfPlaceholderListRowBorderCol: rgba(0, 0, 0, 0.08);  // List Rows
  --imfPlaceholderListRowSecondaryBgCol: var(--imThemePageSecondaryBgCol);
  --imfPlaceholderListRowSecondaryBorderCol: var(--imThemePageSecondaryBorderCol);
  --imfPlaceholderListRowTertiaryBgCol: var(--imThemePageTertiaryBgCol);
  --imfPlaceholderListRowTertiaryBorderCol: var(--imThemePageTertiaryBorderCol);

  // FORMS - Recurring List Border
  --imfListRowBorderCol: var(--imColBlack12);
  --imfListRowBorderRadius: none; // not possible because markup is inconsistent
  // FORMS - Recurring List
  --imfListRowHeadingFontStack: var(--imSecondaryFontStackSemiBold);
  --imfListRowHeadingSize: 11px;
  --imfListRowHeadingLineHeight: 1.1;
  --imfListRowHeadingTextAlign: center;
  --imfListRowHeadingCol: var(--imColBlackAbaddon);
  --imfListRowHeadingBgCol: var(--imColBlack5);
  --imfListRowHeadingBorderCol: var(--imColBlack15);
  --imfListRowHeadingColumnBorderLeftCol: rgba(0, 0, 0, 0.1); // List Heading
  // FORMS - Recurring List Rows
  --imfListRowHeight: 48px; // control the height of list-recurring items in one place
  --imfListRowBgCol: var(--imColWhite);
  --imfListRowContrastBgCol: var(--imColBlack3);
  --imfListRowBgStockBorderCol: var(--imColBlack10);
  --imfListRowBgHoursBorderCol: var(--imColBlack10);
  --imfListRowBgHoursContrastBgCol: var(--imColWhite);
  --imfListRowGrabberIconFill: var(--imColBlack15);
  --imfListRowSelectedGrabberIconFill: var(--imColBlack40);
  // FORMS - Recurring List Rows Text
  --imfListRowTextFontStack: var(--imSecondaryFontStack);
  --imfListRowTextsize: 12px;
  --imfListRowTextlineheight: 1.1;
  // FORMS - Recurring List Columns
  --imfListRowColumnBorderLeftCol: var(--imColBlack10);
  --imfListRowColumnSvgImgFilter: none;
  // FORMS - Stock Recurring List Columns No Image
  --imfListRowStockNoImageFilter: none;
  // FORMS - Recurring List Columns No Image
  --imfListRowNoImageMobileBorderColLt: var(--imColBlack3); // Creates a missing image with CSS borders
  --imfListRowNoImageMobileBorderColDk: var(--imColBlack10);

  // FORMS - STANDARD PAGE DROP ZONE
  --imfCompDropZoneWrapperBgCol: var(--imColWhite);
  --imfCompDropZoneWrapperDisabledBgCol: var(--imColBlack30);
  --imfCompDropZoneWrapperBorderCol: var(--imColBlack20);
  // FORMS - STANDARD PAGE DROP ZONE - Component
  --imfCompDropZoneLrItemBgCol: var(--imColWhite);
  --imfCompDropZoneLrItemBorderCol: var(--imColBlack20);
  --imfCompDropZoneLrItemShadowCol: var(--imColBlack20);
  --imfCompDropZoneLrItemTextCol: var(--imColTextPrimary);
  --imfCompDropZoneLrItemEditBorderCol: var(--imColBlack20);
  --imfCompDropZoneLrItemEditHoverBgCol: rgba(0, 0, 0, 0.1);
  // FORMS - STANDARD PAGE - Component Edit Modal
  --imfCompEditModalWrapperBgCol: var(--imColWhite);
  --imfCompEditModalWrapperBorderCol: var(--imColBlack20);
  --imfCompEditModalWrapperShadowCol: 0 0 10px 4px rgba(0, 0, 0, 0.2);
  // ADMIN - Component Edit
  --imfCompEditModalPropsBgCol: var(--imColBlack5);
  --imfCompEditModalPropsBorderCol: var(--imColWhite); // border bottom for small viewports to delineate items visually
  // ADMIN - SVG Icon Management
  --imfSvgIconBgCol: var(--imColWhite);
  --imfSvgIconBorderCol: var(--imColBlack10);
  // FORMS - STANDARD PAGE COMPONENTS
  --imfCompDragItemsWrapperBorderCol: var(--imColBlack20);
  --imfCompDragItemDisabledTextCol: var(--imColBlack15);
  --imfCompDragItemBgCol: var(--imColWhite);
  --imfCompDragItemBorderCol: var(--imColBlack20);
  --imfCompDragItemHoverShadowCol: var(--imColBlack20);
  --imfCompDragItemIconFill: var(--imColSecondary);
  --imfCompDragItemHoverIconFill: var(--imColSecondary);

  // FORMS - CMS Config
  --imfCmsConfigCheckedBgColRadius: var(--imThemeBorderRadiusSm);
  --imfCmsConfigSectionBorderCol: var(--imColBlack20);
  // FORMS - Sub Heading - e.g. page section table headings
  --imfSubHeadingFontStack: var(--imPrimaryFontStack);
  --imfSubHeadingSize: 15px;
  // FORMS - Sub Heading 1 - e.g. section separator in forms
  --imfSubHeading1FontStack: var(--imPrimaryFontStackSemiBold);
  --imfSubHeading1Size: 13.5px;
  --imfSubHeading1TextTransform: none;
  --imfSubheading1Col: var(--imColBlackAbaddon);
  --imfSubHeading1BgCol: transparent;
  --imfSubHeading1Radius: var(--imThemeBorderRadiusSm);
  --imfSubHeading1Padding: 5px 0;
  // FORMS ACCORDION - Icon
  --imfSubHeading1AccordionTextCol: var(--imfSubheading1Col);
  --imfSubHeading1AccordionHoverTextCol: var(--imColSecondary);
  --imfSubHeading1AccordionBgCol: var(--imColBlack5);
  --imfSubHeading1AccordionHoverBgCol: var(--imColBlack7);
  --imfSubHeading1AccordionTriggerIconsFill: var(--imColTextPrimary);
  --imfSubHeading1AccordionHoverTriggerIconsFill: var(--imColSecondary);

  // FORMS - Sub Heading 2
  --imfSubHeading2Col: var(--imColBlack);
  // FORMS - Sub Heading 3
  --imfSubHeading3Col: var(--imColBlack);
  // FORMS - Sub Heading 4 - background colour
  --imfSubHeading4Col: var(--imColWhite);
  --imfSubHeading4BgCol: var(--imColBlack);
  --imfSubHeading4Padding: 3px 10px;
  --imfSubHeading4Radius: var(--imThemeBorderRadiusSm);
  // FORMS - Sub Heading 5
  --imfSubHeading5FontStack: var(--imPrimaryFontStack);
  --imfSubHeading5Size: 14px;
  --imfSubHeading5Col: var(--imColBlackAbaddon);

  // FORM - BUTTONS
  --btnPrimaryFontStack: var(--imPrimaryFontStackBold);
  --btnPrimaryTextSize: 13px;
  --btnPrimaryTextTransform: uppercase;
  --btnPrimaryLetterSpacing: 0.5px;
  --btnPrimaryTextCol: var(--imColWhite);
  --btnPrimaryHoverTextCol: var(--imColWhite);
  --btnPrimaryRadius: var(--imThemeBorderRadiusSm);
  --btnPrimaryBgCol: var(--imColBlackAbaddon);
  --btnPrimaryHoverBgCol: var(--imColSecondary);
  --btnPrimaryHoverTransform: scale(0.975);
  --btnPrimaryActiveBgCol: var(--imColSecondary120);
  --btnPrimaryActiveTop: 2px;
  --btnPrimaryPadding: 15px 22px;
  --btnPrimaryMinWidth: 120px;
  --btnPrimaryIconFill: var(--imColWhite);
  // Button Outline Flex
  --btnOutlineFlexBgCol: var(--imColWhite);
  --btnOutlineFlexHoverBgCol: var(--imColSecondary7);
  --btnOutlineFlexTextCol: var(--imActionTriggerCol);
  --btnOutlineFlexHoverTextCol: var(--imColSecondary);
  --btnOutlineFlexBorderCol: var(--imActionTriggerCol);
  --btnOutlineFlexHoverBorderCol: var(--imColSecondary);
  --btnOutlineFlexIconFill: var(--imColSecondary);
  --btnOutlineFlexHoverIconFill: var(--imColSecondary);
  // Button Tertiary Outline Flex Stock - Exports Facebook
  --imfBtnTertiaryOutlineTextCol: var(--imColSecondary);
  --imfBtnTertiaryOutlineHoverTextCol: var(--imColWhite);
  --imfBtnTertiaryOutlineIconFill: var(--imColSecondary);
  --imfBtnTertiaryOutlineHoverIconFill: var(--imColWhite);
  --imfBtnTertiaryOutlineBorderCol: var(--imColSecondary);
  --imfBtnTertiaryOutlineHoverBorderCol: var(--imColSecondary);
  --imfBtnTertiaryOutlineBgCol: var(--imColWhite);
  --imfBtnTertiaryOutlineHoverBgCol: var(--imColSecondary);
  // Sticky Wrapper Button
  --btnStickyWrapperBgCol: rgba(229, 229, 229, 0.65);
  --btnStickyWrapperBorderTopCol: var(--imColBlack15);
  // Secondary Button (Cancel Button)
  --btnSecondaryBgCol: var(--imColBlack40);
  --btnSecondaryHoverBgCol: var(--imColBlack50);
  --btnSecondaryTextCol: var(--imColWhite);
  --btnSecondaryHoverTextCol: var(--imColWhite);

  // FORM - Row Wrapper
  --imfRowWrapperBgCol: var(--imColWhite);
  --imfRowWrapperBorderCol: var(--imColBlack15);
  --imfRowWrapperBorderRadius: var(--imThemeBorderRadiusSm);

  // FORM - TABS
  --imfTabsBgCol: var(--imColBlack7);
  --imfTabsBorderRadius: var(--imThemeBorderRadiusSm);
  --imfTabsBorderRightCol: var(--imColWhite);
  --imfTabsBoxShadow: none;
  --imfTabBgCol: var(--imColBlackAbaddon10);
  --imfTabHoverBgCol: var(--imColBlackAbaddon70);
  --imfTabActiveBgCol: var(--imColBlackAbaddon70);
  --imfTabAlertBgCol: var(--imColRed);
  --imfTabDisabledBgCol: var(--imColBlackAbaddon7);
  // FORM - TABS Text
  --imfTabTextFontStack: var(--imSecondaryFontStackSemiBold);
  --imfTabTextSize: 13px;
  --imfTabTextCol: var(--imColWhite);
  --imfTabHoverTextCol: var(--imColWhite);
  --imfTabActiveTextCol: var(--imColWhite);

  // FORM - TABS OUTLINED
  // Border

  --imfTabsOutlinedBorderCol: var(--imColBlack30);
  --imfTabsOutlinedActiveBorderCol: var(--imfFormBgCol); // hidden border
  // Text
  --imfTabsOutlinedTextSize: var(--imGlobalFontSize);
  --imfTabsOutlinedTextCol: var(--imColTextPrimary);
  --imfTabsOutlinedHoverTextCol: var(--imColBlack);
  --imfTabsOutlinedActiveTextCol: var(--imColBlack);
  // Icon
  --imfTabsOutlinedIconCol: var(--imColTextPrimary);
  --imfTabsOutlinedHoverIconCol: var(--imColBlack);
  --imfTabsOutlinedActiveIconCol: var(--imColBlack);

  // FORM - DATE PICKER
  --imfDatePickerDisabledTextCol: var(--imColBlack15);
  --imfDatePickerDisabledBgCol: var(--imColSecondary);
  // header
  --imfDatePickerHeaderTextCol: var(--imColWhite);
  --imfDatePickerHeaderBgCol: var(--imColBlack95);
  // header hover
  --imfDatePickerHeaderHoverTextCol: var(--imColWhite);
  --imfDatePickerHeaderHoverBgCol: var(--imColSecondary85);
  // day
  --imfDatePickerDayTextCol: var(--imColBlack);
  --imfDatePickerDayBgCol: var(--imColBlack5);
  // minute
  --imfDatePickerMinuteHoverTextCol: var(--imColWhite);
  --imfDatePickerMinuteHoverBgCol: var(--imColSecondary90);
  // Previous/Next Month
  --imfDatePickerOldTextCol: var(--imColBlack35);
  // active
  --imfDatePickerActiveTextCol: var(--imColWhite);
  --imfDatePickerActiveBgCol: var(--imColSecondary);
  // td disabled
  --imfDatePickerTdDisabledTextCol: var(--imColBlack5);
  --imfDatePickerTdDisabledBgCol: var(--imColBlack15);
  // span disabled
  --imfDatePickerSpanDisabledBgCol: var(--imColWhite);
  // TOGGLE - Advanced with text - HEADING1
  --imHeadingToggleAdvLabelFontStack: var(--imPrimaryFontStack);
  --imHeadingToggleAdvLabelSize: 13px;
  --imHeadingToggleAdvLabelCol: var(--imColBlack);
  // knob
  --imHeadingToggleAdvKnobBgCol: var(--imColBlackAbaddon);
  --imHeadingToggleAdvKnobHoverBgCol: var(--imColSecondary);
  --imHeadingToggleAdvKnobActiveBgCol: var(--imColSecondary);
  --imHeadingToggleAdvKnobTextCol: var(--imColWhite);
  --imHeadingToggleAdvKnobActiveTextCol: var(--imColWhite);
  // slot
  --imHeadingToggleAdvSlotBgCol: var(--imColBlackAbaddon60);
  --imHeadingToggleAdvSlotActiveBgCol: var(--imColWhite);
  --imHeadingToggleAdvSlotBorderCol: var(--imColPrimary105);
  --imHeadingToggleAdvSlotActiveBorderCol: var(--imColPrimary105);
  // TOGGLE - Advanced with text - NAV2 AND SHARED
  --imToggleAdvLabelCol: var(--imColWhite);
  // knob
  --imToggleAdvKnobBgCol: var(--imColBlackAbaddon);
  --imToggleAdvKnobHoverBgCol: var(--imColSecondary);
  --imToggleAdvKnobActiveBgCol: var(--imColSecondary);
  --imToggleAdvKnobTextCol: var(--imColWhite);
  --imToggleAdvKnobActiveTextCol: var(--imColWhite);
  // slot
  --imToggleAdvSlotBgCol: var(--imColBlackAbaddon60);
  --imToggleAdvSlotActiveBgCol: var(--imColWhite);
  --imToggleAdvSlotBorderCol: var(--imColBlackAbaddon70);
  --imToggleAdvSlotActiveBorderCol: var(--imColBlackAbaddon70);
  // TOGGLE FORM - Simple
  --imfToggleSlotBorderCol: var(--imColBlack15);
  --imfToggleSlotInactiveBgCol: var(--imColBlack70);
  --imfToggleSlotActiveBgCol: var(--imColSecondary);
  // knob
  --imfToggleKnobBorderCol: var(--imColBlack15);
  --imfToggleKnobActiveBgCol: var(--imColWhite);
  --imfToggleKnobActiveHoverBgCol: var(--imColWhite);
  --imfToggleKnobInactiveBgCol: var(--imColWhite);
  --imfToggleKnobInactiveHoverBgCol: var(--imColWhite);

  // FORM - ASSET UPLOADER
  --imfAssetUploaderThumbSize: 65px;
  --imfAssetUploaderIconNoAssetFill: var(--imColWhite);
  --imfAssetUploaderIconNoAssetCol: var(--imColBlack15);
  --imfAssetUploaderIconNoAssetRadius: var(--imThemeBorderRadiusSm);
  --imfAssetUploaderHeaderTextCol: var(--imColBlack);
  --imfAssetUploaderHeaderBgCol: var(--imColBlack5);
  --imfAssetUploaderHeaderBorderCol: var(--imColBlack12);
  --imfAssetUploaderHeaderBorderRadius: var(--imThemeBorderRadiusSm) var(--imThemeBorderRadiusSm) 0 0;
  --imfAssetUploaderBodyBgCol: var(--imColBlack5);
  --imfAssetUploaderBodyBorderCol: var(--imColBlack12);
  --imfAssetUploaderBodyFirstBorderCol: var(--imColBlack12);
  --imfAssetUploaderTriggerHoverBgCol: var(--imColWhite);
  --imfAssetUploaderTriggerHoverBorderCol: var(--imColBlack10);
  --imfAssetUploaderTriggerFontStack: var(--imSecondaryFontStackSemiBold);
  --imfAssetUploaderTriggerTextCol: var(--imActionTriggerTextCol);
  --imfAssetUploaderTriggerHoverTextCol: var(--imActionTriggerHoverTextCol);
  --imfAssetUploaderFooterTextCol: var(--imColTextPrimary);
  --imfAssetUploaderFooterBgCol: var(--imColBlack5);
  --imfAssetUploaderFooterStockBgCol: var(--imColWhite);
  --imfAssetUploaderFooterBordertCol: var(--imColBlack12);
  // FORM - ASSET UPLOADER - Modal
  --imfAssetUploaderModalWrapperBgCol: var(--imColBlack3);
  --imfAssetUploaderModalCloseBtnBgCol: var(--imColSecondary);
  --imfAssetUploaderModalCloseBtnHoverCol: var(--imColSecondary);
  // FORM - ASSET UPLOADER - Modal Target
  --imfAssetUploaderModalTargetBorderCol: var(--imColSecondary);
  --imfAssetUploaderModalTargetBgCol: var(--imColWhite);
  --imfAssetUploaderModalTargetActiveBgCol: var(--imColBlack15);
  --imfAssetUploaderModalTextWarningCol: var(--imColRed);
  // FORM - ASSET UPLOADER - Crop
  --imfAssetUploaderCropWrapperBgCol: var(--imColBlack80);
  --imfAssetUploaderCropNoteBgCol: var(--imColWhite);
  --imfAssetUploaderToolboxItemBgCol: var(--imColBlack5);
  --imfAssetUploaderToolboxItemBorderCol: var(--imColBlack12);
  --imfAssetUploaderToolboxIconFill: var(--imColSecondary);
  --imfAssetUploaderToolboxTextCol: var(--imColSecondary);
  // FORM - Standard Page Column Selector
  --imfColumnSelectorImgFilter: none; // one, two, three column pngs

  // FORM - LOGIN
  --imLoginFormWrapperBgCol: var(--imGlobalWrapperBgCol);
  --imLoginFormWrapperBorderCol: var(--imColBlack8);
  --imLoginFormWrapperHeadingBgCol: var(--imColBlackAbaddon);
  --imLoginFormWrapperHeadingFontStack: var(--imPrimaryFontStackLight);
  --imLoginFormWrapperHeadingSizeMobile: 18px;
  --imLoginFormWrapperHeadingSizeDesktop: 22px;
  --imLoginFormWrapperHeadingCol: var(--imColWhite);

  // ICONS
  --imIconTransform: scale(1.1);
  --imActionIconCirclePlusFill: var(--imNeutralSoftFill);
  --imActionIconCirclePlusCol: var(--imColWhite); // The `+` colour
  --imIconInfoFill: var(--imNeutralSoftFill);
  --imIconActionFill: var(--imNeutralSoftFill);
  --imIconActionHoverFill: var(--imColSecondary);
  --imIndexIconActionHoverFill: var(--imColSecondary);
  --imIndexIconActionHoverTransform: var(--imIconTransform);
  --imIndexIconActionHoverAlertFill: var(--imColRed);
  --imIconTickFill: var(--imColSecondary);
  --imIconTriggersFill: var(--imNeutralSoftFill);
  --imIconTriggersHoverFill: var(--imColSecondary);
  --imIndexIconNeutralFill: var(--imNeutralSoftFill);
  --imIndexIconNeutralHoverFill: var(--imThemeIconHoverFill);

  // ACTION TRIGGERS
  --imActionTriggerFontStack: var(--imPrimaryFontStackBold);
  --imActionTriggerTextCol: var(--imColTextPrimary);
  --imActionTriggerCol: var(--imColTextPrimary);
  --imActionTriggerHoverTextCol: var(--imColSecondary);
  --imActionTriggerMultipleBorderLeftCol: var(--imColBlack15);
  --imActionTriggerMultipleBgCol: var(--imColBlack5);

  // ACTION TOGGLES - Active/Inactive
  --imActionToggleHoverBgCol: rgba(0, 0, 0, 0.1);
  --imActionToggleSuccessBgCol: var(--imColSecondary7);
  --imActionToggleSuccessHoverBgCol: var(--imColSecondary7);
  --imActionToggleActiveDisabledBgCol: var(--imColRed5);
  --imActionToggleActiveDisabledHoverBgCol: var(--imColRed5);

  // GLOBAL MESSAGES
  --imMessageSuccessBorderCol: var(--imColSecondary);
  --imMessageSuccessIconFill: var(--imColSecondary);
  --imMessageInfoCol: var(--imColSecondary);
  --imMessageStrongCol: var(--imColBlack);
  --imMessageSuccessCol: var(--imColTextPrimary);
  --imMessageWarningCol: var(--imColOrange);
  --imMessageAlertCol: var(--imColRed);
  --imMessageNeutralCol: var(--imColTextPrimary);
  --imMessageWrapperBgCol: var(--imColBlack3);
  --imMessageWrapperBorderCol: var(--imColBlack10);
  --imMessageAccentedTextCol: var(--imColTextPrimary);
  --imMessageAccentedWrapperBgCol: var(--imColWhite);
  --imMessageAccentedWrapperBorderCol: var(--imMessageSuccessBorderCol);

  // NOTE INFO
  --imNoteInfoTextCol: var(--imColTextPrimary);
  --imNoteInfoTextStrongCol: var(--imColTextStrong);
  --imNoteInfoBgCol: var(--imColWhite);
  --imNoteInfoBorderCol: var(--imColBlack8);
  --imNoteInfoBorderLeftCol: var(--imMessageInfoCol);
  --imNoteInfoBorderBottomCol: var(--imColBlack20);
  --imNoteInfoImgFilter: none;
  --imNoteWarningBorderLeftCol: var(--imColRed);
  --imNoteAlertBorderLeftCol: var(--imColRed);
  --imNoteStrongBorderLeftCol: var(--imColBlack);
  --imNoteApprovedBorderLeftCol: var(--imThemeColSuccess);
  --imNoteSunnyBorderLeftCol: var(--imColPrimary);
  --imNoteNeutralBorderLeftCol: var(--imColBlack55);
  // NOTE ERROR - Rails Validation Error
  --imNoteErrorWrapperBgCol: var(--imColBlack5);
  --imNoteErrorBorderLeftCol: var(--imColRed);
  --imNoteErrorHeading: var(--imColBlack);
  // NOTE Highlight Code
  --imCodeHighlightTextSize: 12px;
  --imCodeHighlightLineHeight: 1.5;
  --imCodeHighlightTextCol: var(--imColBlack);
  --imCodeHighlightBorderCol: var(--imColBlack20);
  --imCodeHighlightBgCol: var(--imColBlack5);

  // TAGS (stock) used to indicate stock status
  --imTagColAlert: var(--imColRed);
  --imTagColStrong: var(--imColBlack);
  --imTagColApproved: var(--imColSecondary);
  --imTagColSunny: var(--imColPrimary);
  --imTagColNeutral: var(--imColBlackAbaddon20);

  // GROUP GRID
  --imGroupGridWrapperBgCol: var(--imGlobalWrapperBgCol);
  --imGroupGridWrapperBorderCol: var(--imColBlack10);
  --imGroupGridChildImgFilter: none; // UI Pattern PNGs
  // GROUP LABEL ASSET WRAPPER - theme pickers
  --imGroupLabelAssetWrapperBgCol: var(--imColBlack3);
  --imGroupLabelAssetWrapperHoverBgCol: var(--imColBlack10);
  --imGroupLabelAssetWrapperActiveBgCol: var(--imColBlack3);
  --imGroupLabelAssetWrapperBorderCol: var(--imColBlack5);
  --imGroupLabelAssetWrapperHoverBorderCol: var(--imColBlack50);
  --imGroupLabelAssetWrapperActiveBorderCol: var(--imColBlack50);

  // DASHBOARD
  --imfDlMargin: 17px; // dl=data-large/dashboard
  // DASHBOARD - Page Sub Heading
  --imDBaseSubHeadingCol: var(--imColTextPrimary);
  // DASHBOARD - Action Module
  --imfDashActionItemBgCol: var(--imColWhite);
  --imfDashActionItemBorderCol: var(--imColBlack15);
  --imfDashActionItemBorderRadius: var(--imThemeBorderRadiusSm);
  // Item Heading
  --imfDashActionItemHeadingBgCol: var(--imColBlack8);
  // Radio Buttons Wrapper
  --imfDashActionRadioWrapperBorderCol: var(--imColBlack25);
  // Radio Buttons Label
  --imfDashActionRadioLabelBgCol: var(--imColWhite);
  --imfDashActionRadioLabelHoverBgCol: var(--imColBlack3);
  --imfDashActionRadioLabelBorderRightCol: var(--imColBlack25);
  --imfDashAlertRadioLabelBgCol: var(--red5);
  --imfDashAlertRadioLabelBorderCol: var(--imThemeColAlert);
  // Radio Buttons Checked
  --imfDashActionRadioCheckeCol: var(--imColWhite);
  --imfDashActionRadioCheckeBgCol: var(--imColRed);
  // DASHBOARD - Large Item Wrapper
  --imfDlWrapperBgCol: var(--imGlobalWrapperBgCol);
  --imfDlWrapperHoverBgCol: var(--imColBlack7);
  --imfDlWrapperBorderRadius: var(--imThemeBorderRadiusSm);
  --imfDlWrapperBorderCol: var(--imColBlack15);
  // DASHBOARD - Large Item Heading
  --imfDlHeadingFontStack: var(--imPrimaryFontStackLight);
  --imfDlHeadingSize: 55px;
  --imfDlHeadingLineHeight: 75px;
  --imfDlHeadingCol: var(--imColSecondary);
  // DASHBOARD - Tables Heading
  --imfDlTableHeadingFontStack: var(--imPrimaryFontStack);
  --imfDlTableHeadingSize: 20px;
  --imfDlTableHeadingCol: var(--imColTextPrimary);
  --imfDlChartLeadsCountTextCol: var(--imColSecondary);
  --imfDlChartLeadsCountWrapperBgCol: var(--imColBlack7);
  --imfDlChartLeadsHeadingFontStack: var(--imPrimaryFontStackLight);
  --imfDlChartLeadsHeadingSize: 24px;
  --imfDlChartLeadsHeadingCol: var(--imColSecondary);
  // DASHBOARD - Charts
  --imDdLineChartGraphCol: var(--imColSecondary); // Line Chart graph colour in dashboard_data.js
  --imDdLineChartXAxisLabelCol: var(--imColTextPrimary); // Line Chart X Axis label colour in dashboard_data.js
  --imDdPieBorderCol: var(--imColBlack10); // Pie Chart Border in dashboard_data.js
  --imDdLegendTextCol: var(--imColTextPrimary);
  // DASHBOARD - 3rd Party Service Links
  --imfDlServiceLinkWrapperBgCol: var(--imColBlack5);
  --imfDlServiceLinkWrapperBorderCol: var(--imColBlack15);
  --imfDlServiceLinkItemBgCol: var(--imColWhite);
  --imfDlServiceLinkItemBorderCol: var(--imColBlack15);
  --imfDlServiceLinkItemHoverBorderCol: var(--imColBlack);
  --imfDlServiceLinkItemRadius: var(--imThemeBorderRadiusSm);
  --imfDlServiceLinkLogoWrapperBgCol: var(--imColBlack3);

  // DASHBOARD - Total
  --imfDlTotalCol: var(--imColWhite);
  --imfDlTotalBgCol: var(--imColSecondary);
  // STOCK - Uploads Filter
  --imfStockFilterWrapperBgCol: var(--imColWhite);
  --imfStockFilterWrapperBorderCol: var(--imColBlack10);
  // STOCK - Exports Report
  --imfStockExportReportWrapperBgCol: var(--imColWhite);
  --imfStockExportReportWrapperBorderCol: var(--imColBlack10);
  --imfStockExportTypeLogoFilter: none;
  // Stock - Exports Status
  --imfStockExportStatusCompleteTextCol: var(--imColSecondary);
  --imfStockExportStatusCompleteIconFill: var(--imColSecondary);
  --imfStockExportStatusPendingIconFill: var(--imColBlack);
  --imfStockExportStatusErrorIconFill: var(--imColRed);
  // Support - CTA
  --imCmsSupportCtaWrapperBgCol: var(--imColBlack8);
  --imCmsSupportAsideBgCol: var(--imColBlack3);
  --imCmsSupportImgBorderCol: var(--imColBlack15);
  --imCmsSupportImgShadow: var(--imColBlack8);
}
