// DARK THEME - Variant 1
[data-selected-theme='theme-dark'][data-dark-variant='variant1'] {
  // Generates a full set of CSS custom properties for black tints (from pure black to white) based on the given base color.
  @include imColTintsBlack(#0a1016);

  --imLogoCol: var(--imColBlack); // dot
  --imLogoFill: var(--imColBlack);
  // NAV 1 - Welcome Message
  --nav1WelcomeMessageCol: var(--imColBlack);
  // NAV 1
  --nav1WrapperBg: var(--imColPrimary);
  --nav1WrapperBgAnimation: none;
  --nav1borderBottomPosition: 10px; // bottom position of the border
  --nav1HoverBorderBottomCol: var(--imColBlack);
  --nav1ActiveborderBottomCol: var(--imColBlack);
  // NAV 1 - Icon
  --nav1IconFill: var(--imColBlack);
  --nav1HoverIconFill: var(--imColBlack);
  --nav1ActiveIconFill: var(--imColBlack);
  // NAV 1 - Text
  --nav1TextCol: var(--imColBlack);
  --nav1HoverTextCol: var(--imColBlack);
  --nav1ActiveTextCol: var(--imColBlack);
}

// DARK THEME - Variant 2
[data-selected-theme='theme-dark'][data-dark-variant='variant2'] {
  // Generates a full set of CSS custom properties for black tints (from pure black to white) based on the given base color.
  @include imColTintsBlack(#0b121a);  //#1a2233 ••, #0d1624 •••, #0d1624, #080d14, #0b121a

  --imLogoCol: var(--imColBlack); // dot
  --imLogoFill: var(--imColBlack);
  // NAV 1 - Welcome Message
  --nav1WelcomeMessageCol: var(--imColBlack);
  // NAV 1
  // Used for rainbow-linear animation

  --nav1WrapperBg:
    linear-gradient(
      90deg,
      #e1e43c,
      #fd963c,
      #ff4591,
      #a038fb,
      #2499ff,
      #7df36d,
      #e1e43c,
      #fd963c,
      #ff4591
    );
  --nav1WrapperBgAnimation: rainbow-linear 14s linear infinite;
  --nav1borderBottomThickness: 5px;
  --nav1HoverBorderBottomCol: var(--imColBlack);
  --nav1ActiveborderBottomCol: var(--imColBlack);
  // NAV 1 - Icon
  --nav1IconFill: var(--imColBlack);
  --nav1HoverIconFill: var(--imColBlack);
  --nav1ActiveIconFill: var(--imColBlack);
  // NAV 1 - Text
  --nav1TextCol: var(--imColBlack);
  --nav1HoverTextCol: var(--imColBlack);
  --nav1ActiveTextCol: var(--imColBlack);
}

// DARK THEME - Base
[data-selected-theme='theme-dark'] {
  // Generates a full set of CSS custom properties for black tints (from pure black to white) based on the given base color.
  @include imColTintsBlack(#0a1016);
  // LOGO
  --imLogoCol: var(--imColPrimary); // dot
  --imLogoFill: var(--imColWhite);
  --imLogoBlockPaddingDesktop: 20px;
  // NAV 1 - Welcome Message
  --nav1WelcomeMessageCol: var(--imColWhite);
  // NAV 1
  --nav1WrapperBg: var(--imColBlack);
  --nav1WrapperBgSize: 400% 100%;
  --nav1WrapperBgAnimation: none;
  --nav1borderBottomPosition: 0; // bottom position of the border
  --nav1borderBottomThickness: 4px;
  --nav1borderBottomMarginLeft: 4px; // required to visually center the border
  --nav1borderBottomTransition: width 300ms ease-in-out;
  --nav1HoverBorderBottomWidth: 75%;
  --nav1HoverBorderBottomCol: var(--imColWhite);
  --nav1ActiveborderBottomWidth: 75%;
  --nav1ActiveborderBottomCol: var(--imColWhite);
  // NAV 1 - Icon
  --nav1IconFill: var(--imColBlack60);
  --nav1HoverIconFill: var(--imThemeColNavAccent);
  --nav1ActiveIconFill: var(--imThemeColNavAccent);
  // NAV 1 - Text
  --nav1TextCol: var(--imColBlack60);
  --nav1HoverTextCol: var(--imThemeColNavAccent);
  --nav1ActiveTextCol: var(--imThemeColNavAccent);
  // HEADER - h1 Heading
  --heading1BgCol: var(--imColBlack97);
  --heading1FontStack: var(--imPrimaryFontStackLight);
  --heading1Col: var(--imColWhite);
  // HEADER - Flash Message
  --imFlashMessageWrapperBgCol: var(--imColBlack97);
  --imFlashMessageTextCol: var(--imColWhite);
  --imFlashMessageIconFill: var(--imColWhite);
  // HOME SEARCH
  --imHomeSearchWrapperBgCol: var(--imColBlack90);
  --imHomeSearchLabelCol: var(--imColWhite);
  // HOME - Site Filter
  --imHomeSiteFilterWrapperBorderCol: var(--imColBlack80);
  --imHomeSiteFilterWrapperBgCol: var(--imColBlack);
  --imHomeSiteFilterLabelCol: var(--imColBlack60);
  --imHomeSiteFilterLabelHoverCol: var(--imColSecondary70);
  --imHomeSiteFilterLabelActiveCol: var(--imColWhite);
  // HOME - Sections (Dealer / Group / Brand)
  --imHomeSectionBaseBgCol: var(--imfFormBgCol);
  --imHomeSectionContrastBgCol: var(--imColBlack85);
  // HOME - Listing Links
  --imHomeLinkTextCol: var(--imColWhite);
  --imHomeLinkHoverTextCol: var(--imColSecondary70);
  --imHomeLinkCloneBgCol: var(--imColBlack83);
  --imHomeLinkCloneTextCol: var(--imColWhite);
  --imHomeLinkCloneHoverTextCol: var(--imColSecondary70);
  --imHomeLinkCloneIconFill: var(--imColSecondary80);
  --imHomeLinkCloneHoverIconFill: var(--imColSecondary80);
  // NAV 2
  --nav2WrapperBgCol: var(--imColBlack93);
  --nav2WrapperBorderBottom: 1px solid var(--imColBlack82); // e.g. 10px solid var(--imColPrimary)
  --nav2WrapperBoxShadow: none;
  // NAV 2 - List
  --nav2ListBorderBottomColMobile: var(--imColBlack70);
  // NAV 2 - Icon
  --nav2LinkIconFill: var(--imColBlack70);
  --nav2LinkHoverIconFill: var(--imThemeColNavAccent);
  --nav2LinkActiveIconFill: var(--imThemeColNavAccent);
  // NAV 2 - Text
  --nav2LinkTextCol: var(--imColBlack70);
  --nav2LinkHoverTextCol: var(--imThemeColNavAccent);
  --nav2LinkActiveTextCol: var(--imThemeColNavAccent);
  // NAV 2 - Build Trigger
  --nav2BuildTriggerBgCol: var(--imColBlack);
  --nav2BuildTriggerHoverBgCol: var(--imColSecondary);
  --nav2BuildTriggerBorderCol: var(--imColBlack80);
  --nav2BuildTriggerHoverBorderCol: var(--imColSecondary80);
  --nav2BuildTriggerIconFill: var(--imColWhite);
  --nav2BuildTriggerDisabledTextCol: var(--imColBlack60);
  --nav2BuildTriggerDisabledBgCol: var(--imColBlack88);
  --nav2BuildTriggerDisabledBorderCol: var(--imColBlack75);
  --nav2BuildTriggerDisabledIconFill: var(--imColBlack75);
  // NAV 2 - Build Trigger Dropdown
  --nav2BuildMenuWrapperBgCol: var(--imColBlack97);
  --nav2BuildMenuItemHoverBgCol: var(--imColBlack94);
  --nav2BuildMenuItemDisabledBgCol: var(--imColBlack92);
  --nav2BuildMenuItemTextCol: var(--imColWhite);
  --nav2BuildMenuItemDisabledTextCol: var(--imColBlack65);
  // NAV 3/4 - Collapse Tooltip
  --navCollapsedTooltipBgCol: var(--imColBlack);
  // NAV 3 - Collapse Button
  --nav3CollapseBtnBgCol: var(--imColBlack95);
  --nav3CollapseBtnIconFill: var(--imColBlack70);
  // NAV 3 - Wrapper
  --nav3WrapperBgCol: var(--imColBlack93);
  --nav3ActiveBorderLeftCol: var(--imColSecondary);
  // NAV 3 - Icon
  --nav3IconFill: var(--imColBlack70);
  --nav3HoverIconFill: var(--imColWhite);
  --nav3ActiveIconFill: var(--imColWhite);
  // NAV 4 - Collapse Button
  --nav4CollapseBtnBgCol: var(--imColBlack93);
  --nav4CollapseBtnIconFill: var(--imColBlack60);
  // NAV 4 - Wrapper
  --nav4WrapperBgCol: var(--imColBlack91);
  --nav4ActiveBorderLeftCol: var(--imColSecondary);
  --nav4WrapperBorderRightCol: var(--imColBlack90);
  // NAV 4 - Icon
  --nav4IconFill: var(--imColBlack65);
  --nav4HoverIconFill: var(--imColWhite);
  --nav4ActiveIconFill: var(--imColWhite);
  // GARAGE
  --imGaragePageWrapperBgCol: var(--imfFormBgCol);
  // GARAGE  - Big Icon Links
  --imGarageLinkBgCol: var(--imColBlack90);
  --imGarageLinkBorderCol: var(--imColBlack82);
  --imGarageLinkHoverBorderCol: var(--imColBlack75);
  // GARAGE  - Big Icon
  --imGarageLinkIconFill: var(--imColBlack70);
  --imGarageLinkHoverIconFill: var(--imColBlack30);
  // GARAGE  - Big Icon Text
  --imGarageLinkTextCol: var(--imColBlack70);
  --imGarageLinkHoverTextCol: var(--imColBlack30);

  // --- THEME & GLOBAL GROUPS ---

  // GLOBAL COLOURS
  --imThemeColNavAccent: var(--imColWhite);
  // Text Accent Colours
  --imfTextAccentNeutraTextCol: var(--imColWhite);
  --imfTextAccentNeutralBgCol: var(--imColBlack5);
  --imfTextAccentNeutralBorderCol: var(--imColBlack20);
  --imfTextAccentSecondaryBgCol: var(--imColBlack75);
  --imfTextAccentSecondaryBorderCol: var(--imColBlack50);
  // CMS Config Accents
  --imfCmsConfigAccentBgCol: var(--imColBlack85);
  --imfCmsConfigAccentBorderCol: var(--imColBlack75);
  --imfCmsConfigAccentHoverBorderCol: var(--imColBlack40);
  --imfCmsConfigAccentRadius: var(--imThemeBorderRadiusSm);

  // GLOBAL FONTS
  --imColTextPrimary: var(--imColWhite);
  --imColTextStrong: var(--imColWhite);
  --imColLinkPrimary: var(--imColSecondary70); // Global Links
  --imColLinkHoverPrimary: var(--imColSecondary60); // Global Links Hover
  --imTextSelectionTextCol: var(--imColBlack); // Text colour of selected text
  --imTextSelectionBgCol: var(--imColWhite); // Background colour of selected text

  // GLOBAL HELP WIKI
  --imHelpWikiTextCol: var(--imColBlack25);
  --imHelpWikiBgCol: var(--imfFormBgCol);
  --imHelpWikiBorderLeft: var(--imColBlack75);
  --imHelpWikiCloseBtnCol: var(--imColSecondary70);
  --imHelpWikiCloseBtnHoverCol: var(--imColWhite);
  --imHelpWikiCloseBtnBgCol: transparent;
  --imHelpWikiCloseBtnHoverBgCol: var(--imColBlack);

  // THEME PAGES
  --imThemePageSecondaryBgCol: var(--imColDarkModeNeutralBlue);
  --imThemePageSecondaryBorderCol: rgba(255, 255, 255, 0.15);
  --imThemePageTertiaryBgCol: var(--imColDarkModeNeutralGreen);
  --imThemePageTertiaryBorderCol: rgba(255, 255, 255, 0.15);
  --imThemePageBrandTextCol: var(--imColWhite);

  // THEME ICONS
  --imThemeIconFill: var(--imColBlack50);
  --imThemeIconHoverFill: var(--imColSecondary60);
  --imNeutralSoftTextCol: var(--imColBlack50);
  --imNeutralSoftFill: rgba(255, 255, 255, 0.4); // Soft fill for icons
  --imNeutralFill: var(--imColBlack);
  --imNeutralTextCol: var(--imColBlack);
  // Standard Icons
  --imfImgSvgFilter: invert(0.5);
  --imfImgSvgHoverFilter: invert(1);
  --imfImgSvgHoverTransform: scale(1.1); // not as big as imIconTransform
  --imfImgSvgActiveFilter: invert(1);

  // RAILS ALERTS - Modal, Turbolinks, Login Flash
  --imModalAlertBgCol: rgba(255, 22, 68, 0.75);
  --imModalAlertMessageBgCol: var(--imColBlack80);
  --imModalAlertMessageBorderCol: var(--imColBlack70);

  // TOOLTIP - Balloon
  --imTooltipBgCol: var(--imColSecondary);

  // Items Enabled
  --imfItemEnabledTextCol: var(--imColSecondary70);
  --imfItemEnabledBgCol: var(--imColBlack90);
  --imfItemEnabledFillCol: var(--imColSecondary70);
  // Disabled Items
  --imfItemDisabledTextCol: var(--imColRed);
  --imfItemDisabledBgCol: var(--imColBlack70);
  --imfItemDisabledFillCol: var(--imColBlack60);
  // Note Item Brand Info
  --imfINoteBrandBgCol: var(--imColBlack90);
  --imfINoteBrandBorderCol: var(--imColBlack75);

  // FORMS
  --imfFormBgCol: var(--imColBlack88); // TODO: Geoff

  // FORMS - Breadcrumbs
  --imfBreadCrumbWrapperBgCol: var(--imColBlack90);
  --imfBreadCrumbWrapperBorderBottom: var(--imColBlack80);
  --imfBreadCrumbLinkCol: var(--imColBlack60);
  --imfBreadCrumbLinkHoverCol: var(--imColSecondary70);
  --imfBreadCrumbFontStack: var(--imPrimaryFontStack);
  --imfBreadCrumbTextCol: var(--imColBlack30);
  --imfBreadCrumbTriangleRightCol: var(--imColBlack70); // The colour of the arrow
  --imfBreadCrumbTriangleRightBgCol: var(--imColBlack90); // Should match the background colour (mask)
  // FORMS - Heading
  --imfFormHeadingCol: var(--imColWhite);
  --imfLabelMandatoryCol: var(--imColPrimary);
  // FORMS - Labels
  --imfLabelCol: var(--imColWhite);
  // FORMS - Inputs
  --imfInputCol: var(--imColWhite);
  --imfInputFocusCol: var(--imColWhite);
  --imfInputBgCol: var(--imColBlack82);
  --imfInputBorderCol: var(--imColBlack75);
  --imfInputFocusBorderCol: var(--imColBlack70);
  --imfInputPlaceholderCol: var(--imColBlack60);
  --imfInputReadOnlyBgCol: var(--imColBlack90);
  --imputIconFilterDkTheme: invert(1); // Invert icon colours for dark theme
  --imputColorSwatchBorderCol: var(--imColBlack75); // Colour swatch border col for dark theme
  // FORMS - TinyMCE
  // dark theme only  - doesn't apply to light themes
  // theme files copied from original TinyMCE
  // app/assets/javascripts/tinymce/skins/ui/oxide-dark/skin.css

  // See theme_switcher_tinymce.js
  //content_style: isDark
  //    ? "body { background-color: #262626; color: #fff; }"
  //    : "body { background-color: #fff; color: #222; }",
  --tinymceColPrimaryDark: var(--imColBlack90); // Chrome
  --tinymceColPrimaryDarkAccent: var(--imColBlack85); // Buttons
  --tinymceColPrimaryDarkHover: var(--imColSecondary); // Button Hover

  // FORMS - Selects
  // Choices Heading

  --imfChoicesHeadingCol: var(--imColTextPrimary);
  --imfChoicesHeadingBorderBottomCol: var(--imColBlack3);
  // Choice Button
  --imfChoicesBtnFocusBoxShadowCol: var(--imColBlack);
  --imfChoicesBtnBorderLeftCol: var(--imColBlack50);
  // Choices Input
  --imfChoicesInputBorderBottomCol: var(--imColBlack15);
  --imfChoicesSelectedInputBgCol: var(--imColBlack85);
  --imfChoicesDisabledInnerInputBgCol: var(--imColBlack8);
  --imfChoicesInputBgCol: var(--imColBlack3);
  // Choices Selected
  --imfChoicesSelectedBorderCol: var(--imColBlack80);
  --imfChoicesSelectedOpenBorderCol: var(--imColBlack80);
  // Choices Inner
  --imfChoicesInnerBgCol: var(--imColBlack82); // same as global inputs
  --imfChoicesInnerBorderCol: var(--imColBlack75);
  --imfChoicesFocusOpenInnerBorderCol: var(--imColBlack70);
  // Choices Item
  --imfChoicesItemTextCol: var(--imColWhite);
  --imfChoicesItemBgCol: var(--imColBlack);
  --imfChoicesDisabledItemBgCol: var(--imColBlack35);
  --imfChoicesItemBgBorderCol: var(--imColBlack80);
  --imfChoicesDisabledItemBorderCol: var(--imColBlack40);
  --imfChoicesItemMultipleBgCol: var(--imColBlack85);
  --imfChoicesItemMultipleBorderCol: var(--imColBlack85);
  // Choices Dropdown
  --imfChoicesDropdownBgCol: var(--imColBlack85);
  --imfChoicesOpenDropdownBorderCol: var(--imColBlack70);
  --imfChoicesDropdownBorderCol: var(--imColBlack70);
  --imfChoicesDropdowItemHighlightBgCol: var(--imfFormBgCol);

  // FORMS - Recurring List Placeholder
  --imfPlaceholderListWrapperBgCol: var(--imColBlack83);
  // FORMS - Placeholder List
  --imfPlaceholderListRowBgCol: var(--imColDarkModeNeutral);
  --imfPlaceholderListRowBorderCol: rgba(255, 255, 255, 0.1);  // List Rows
  --imfPlaceholderListRowSecondaryBgCol: var(--imThemePageSecondaryBgCol);
  --imfPlaceholderListRowSecondaryBorderCol: var(--imThemePageSecondaryBorderCol);
  --imfPlaceholderListRowTertiaryBgCol: var(--imThemePageTertiaryBgCol); // won't be used if gradient is set
  --imfPlaceholderListRowTertiaryBorderCol: var(--imThemePageTertiaryBorderCol);

  // FORMS - Recurring List Border
  --imfListRowBorderCol: rgba(255, 255, 255, 0.15);
  // FORMS - Recurring List Heading
  --imfListRowHeadingCol: var(--imColBlack40);
  --imfListRowHeadingBgCol: var(--imColBlack90);
  --imfListRowHeadingBorderCol: rgba(255, 255, 255, 0.15);
  --imfListRowHeadingColumnBorderLeftCol: var(--imColBlack80);
  // FORMS - Recurring List Rows
  --imfListRowBgCol: var(--imColBlack83);
  --imfListRowContrastBgCol: var(--imColBlack85);
  --imfListRowBgStockBorderCol: rgba(255, 255, 255, 0.15);
  --imfListRowBgHoursBorderCol: rgba(255, 255, 255, 0.15);
  --imfListRowBgHoursContrastBgCol: var(--imfListRowBgCol);
  --imfListRowGrabberIconFill: var(--imColBlack65);
  --imfListRowSelectedGrabberIconFill: var(--imColBlack20);
  // FORMS - Recurring List Columns
  --imfListRowColumnBorderLeftCol: rgba(255, 255, 255, 0.15);
  --imfListRowColumnSvgImgFilter: invert(1);
  // FORMS - Stock Recurring List Columns No Image
  --imfListRowStockNoImageFilter: invert(0.8);
  // FORMS - Recurring List Columns No Image
  --imfListRowNoImageMobileBorderColLt: var(--imColBlack75); // Creates a missing image with CSS borders
  --imfListRowNoImageMobileBorderColDk: var(--imColBlack80);

  // FORMS - STANDARD PAGE DROP ZONE
  --imfCompDropZoneWrapperBgCol: var(--imColBlack93);
  --imfCompDropZoneWrapperDisabledBgCol: var(--imColBlack85);
  --imfCompDropZoneWrapperBorderCol: var(--imColBlack80);
  // FORMS - STANDARD PAGE DROP ZONE - Component
  --imfCompDropZoneLrItemBgCol: var(--imColBlack90);
  --imfCompDropZoneLrItemBorderCol: var(--imColBlack75);
  --imfCompDropZoneLrItemShadowCol: var(--imColBlack);
  --imfCompDropZoneLrItemTextCol: var(--imColTextPrimary);
  --imfCompDropZoneLrItemEditBorderCol: var(--imColBlack75);
  --imfCompDropZoneLrItemEditHoverBgCol: rgba(255, 255, 255, 0.1);
  // FORMS - STANDARD PAGE - Component Edit Modal
  --imfCompEditModalWrapperBgCol: var(--imColBlack80);
  --imfCompEditModalWrapperBorderCol: var(--imColBlack75);
  --imfCompEditModalWrapperShadowCol: none;
  // ADMIN - Component Edit
  --imfCompEditModalPropsBgCol: var(--imColBlack85);
  --imfCompEditModalPropsBorderCol: var(--imColBlack93); // border bottom for small viewports to delineate items visually
  // ADMIN - SVG Icon Management
  --imfSvgIconBgCol: var(--imfFormBgCol);
  --imfSvgIconBorderCol: var(--imColBlack75);
  // FORMS - STANDARD PAGE COMPONENTS
  --imfCompDragItemsWrapperBorderCol: var(--imColBlack75);
  --imfCompDragItemDisabledTextCol: var(--imColBlack15);
  --imfCompDragItemBgCol: var(--imColBlack80);
  --imfCompDragItemBorderCol: var(--imColBlack75);
  --imfCompDragItemHoverShadowCol: var(--imColBlack);
  --imfCompDragItemIconFill: var(--imColWhite);
  --imfCompDragItemHoverIconFill: var(--imColWhite);

  // FORMS - CMS Config
  --imfCmsConfigSectionBorderCol: var(--imColBlack75);
  // FORMS - Sub Heading 1 - e.g. section separator in forms
  --imfSubheading1Col: var(--imColWhite);
  --imfSubHeadingSize: 15px;
  // FORMS ACCORDION - Icon
  --imfSubHeading1AccordionTextCol: var(--imColSecondary70);
  --imfSubHeading1AccordionHoverTextCol: var(--imColSecondary70);
  --imfSubHeading1AccordionBgCol: var(--imColBlack90);
  --imfSubHeading1AccordionHoverBgCol: var(--imColBlack92);
  --imfSubHeading1AccordionTriggerIconsFill: var(--imColSecondary70);
  --imfSubHeading1AccordionHoverTriggerIconsFill: var(--imColSecondary70);
  // FORMS - Sub Heading 2
  --imfSubHeading2Col: var(--imColWhite);
  // FORMS - Sub Heading 3
  --imfSubHeading3Col: var(--imColWhite);
  // FORMS - Sub Heading 4 - background colour
  --imfSubHeading4Col: var(--imColWhite);
  --imfSubHeading4BgCol: var(--imColBlack80);
  // FORMS - Sub Heading 5
  --imfSubHeading5Col: var(--imColWhite);
  // FORM - BUTTONS
  --btnPrimaryBgCol: var(--imColSecondary);
  --btnPrimaryHoverBgCol: var(--imColSecondary120);
  --btnPrimaryActiveBgCol: var(--imColSecondary120);
  // Button Outline Flex
  --btnOutlineFlexBgCol: var(--imfFormBgCol);
  --btnOutlineFlexHoverBgCol: var(--imColSecondary7);
  --btnOutlineFlexTextCol: var(--imActionTriggerCol);
  --btnOutlineFlexHoverTextCol: var(--imColSecondary);
  --btnOutlineFlexBorderCol: var(--imActionTriggerCol);
  --btnOutlineFlexHoverBorderCol: var(--imColSecondary);
  --btnOutlineFlexIconFill: var(--imColSecondary80);
  --btnOutlineFlexHoverIconFill: var(--imColSecondary);
  // Button Tertiary Outline Flex Stock - Exports Facebook
  --imfBtnTertiaryOutlineTextCol: var(--imColWhite);
  --imfBtnTertiaryOutlineHoverTextCol: var(--imColWhite);
  --imfBtnTertiaryOutlineIconFill: var(--imColSecondary70);
  --imfBtnTertiaryOutlineHoverIconFill: var(--imColWhite);
  --imfBtnTertiaryOutlineBorderCol: var(--imColSecondary70);
  --imfBtnTertiaryOutlineHoverBorderCol: var(--imColSecondary);
  --imfBtnTertiaryOutlineBgCol: var(--imfFormBgCol);
  --imfBtnTertiaryOutlineHoverBgCol: var(--imColSecondary);
  // Sticky Wrapper Button
  --btnStickyWrapperBgCol: rgba(255, 255, 255, 0.03);
  --btnStickyWrapperBorderTopCol: var(--imColBlack90);
  // Secondary Button (Cancel Button)
  --btnSecondaryBgCol: var(--imColBlack70);
  --btnSecondaryHoverBgCol: var(--imColBlack75);

  // FORM - Row Wrapper
  --imfRowWrapperBgCol: var(--imColBlack85);
  --imfRowWrapperBorderCol: var(--imColBlack80);

  // FORM - TABS
  --imfTabsBgCol: var(--imColBlack84);
  --imfTabsBorderRightCol: var(--imColBlack75);
  --imfTabBgCol: var(--imColBlack80);
  --imfTabHoverBgCol: var(--imColBlack91);
  --imfTabActiveBgCol: var(--imColBlack93);
  --imfTabDisabledBgCol: var(--imColBlack60);
  // FORM - TABS Text
  --imfTabTextCol: var(--imColBlack40);
  --imfTabHoverTextCol: var(--imColWhite);
  --imfTabActiveTextCol: var(--imColWhite);

  // FORM - TABS OUTLINED
  // Border

  --imfTabsOutlinedBorderCol: var(--imColBlack75);
  --imfTabsOutlinedActiveBorderCol: var(--imfFormBgCol); // hidden border
  // Text
  --imfTabsOutlinedTextSize: var(--imGlobalFontSize);
  --imfTabsOutlinedTextCol: var(--imColBlack40);
  --imfTabsOutlinedHoverTextCol: var(--imColWhite);
  --imfTabsOutlinedActiveTextCol: var(--imColWhite);
  // Icon
  --imfTabsOutlinedIconCol: var(--imColBlack40);
  --imfTabsOutlinedHoverIconCol: var(--imColWhite);
  --imfTabsOutlinedActiveIconCol: var(--imColWhite);

  // TOGGLE - Advanced with text - HEADING1
  --imHeadingToggleAdvLabelCol: var(--imColWhite);
  // knob
  --imHeadingToggleAdvKnobBgCol: var(--imColBlack90);
  // slot
  --imHeadingToggleAdvSlotBgCol: var(--imColBlack80);
  --imHeadingToggleAdvSlotActiveBgCol: var(--imColBlack93);
  --imHeadingToggleAdvSlotBorderCol: var(--imColBlack70);
  --imHeadingToggleAdvSlotActiveBorderCol: var(--imColBlack70);
  // TOGGLE - Advanced with text - NAV2 AND SHARED
  // knob

  --imToggleAdvKnobBgCol: var(--imColBlack90);
  // slot
  --imToggleAdvSlotBgCol: var(--imColBlack80);
  --imToggleAdvSlotActiveBgCol: var(--imColBlack93);
  --imToggleAdvSlotBorderCol: var(--imColBlack70);
  --imToggleAdvSlotActiveBorderCol: var(--imColBlack70);
  // TOGGLE FORM - Simple
  --imfToggleSlotBorderCol: var(--imColBlack70);
  --imfToggleSlotInactiveBgCol: var(--imColBlack70);
  --imfToggleSlotActiveBgCol: var(--imColSecondary50);
  // knob
  --imfToggleKnobBorderCol: var(--imColBlack70);
  --imfToggleKnobActiveBgCol: var(--imColSecondary);
  --imfToggleKnobActiveHoverBgCol: var(--imColSecondary);
  --imfToggleKnobInactiveBgCol: var(--imColBlack);
  --imfToggleKnobInactiveHoverBgCol: var(--imColBlack);

  // FORM - ASSET UPLOADER
  --imfAssetUploaderIconNoAssetFill: var(--imColBlack80);
  --imfAssetUploaderIconNoAssetCol: var(--imColBlack75);
  --imfAssetUploaderHeaderTextCol: var(--imColBlack50);
  --imfAssetUploaderHeaderBgCol: var(--imColBlack85);
  --imfAssetUploaderHeaderBorderCol: var(--imColBlack80);
  --imfAssetUploaderBodyBgCol: var(--imColBlack85);
  --imfAssetUploaderBodyBorderCol: var(--imColBlack80);
  --imfAssetUploaderBodyFirstBorderCol: var(--imColBlack80);
  --imfAssetUploaderTriggerHoverBgCol: var(--imColBlack90);
  --imfAssetUploaderTriggerHoverBorderCol: var(--imColBlack80);
  --imfAssetUploaderTriggerTextCol: var(--imActionTriggerTextCol);
  --imfAssetUploaderTriggerHoverTextCol: var(--imActionTriggerHoverTextCol);
  --imfAssetUploaderFooterTextCol: var(--imColBlack50);
  --imfAssetUploaderFooterBgCol: var(--imColBlack85);
  --imfAssetUploaderFooterStockBgCol: var(--imColBlack90);
  --imfAssetUploaderFooterBordertCol: var(--imColBlack80);
  // FORM - ASSET UPLOADER - Modal
  --imfAssetUploaderModalWrapperBgCol: var(--imfFormBgCol);
  --imfAssetUploaderModalCloseBtnBgCol: var(--imColSecondary70);
  --imfAssetUploaderModalCloseBtnHoverCol: var(--imColSecondary70);
  // FORM - ASSET UPLOADER - Modal Target
  --imfAssetUploaderModalTargetBorderCol: var(--imColBlack30);
  --imfAssetUploaderModalTargetBgCol: var(--imfFormBgCol);
  --imfAssetUploaderModalTargetActiveBgCol: var(--imColBlack83);
  --imfAssetUploaderModalTextWarningCol: var(--imColRed);
  // FORM - ASSET UPLOADER - Crop
  --imfAssetUploaderCropWrapperBgCol: var(--imfFormBgCol);
  --imfAssetUploaderCropNoteBgCol: var(--imColBlack83);
  --imfAssetUploaderToolboxItemBgCol: var(--imfFormBgCol);
  --imfAssetUploaderToolboxItemBorderCol: var(--imColBlack75);
  --imfAssetUploaderToolboxIconFill: var(--imColSecondary80);
  --imfAssetUploaderToolboxTextCol: var(--imColSecondary70);
  // FORM - Standard Page Column Selector
  --imfColumnSelectorImgFilter: invert(0.9) sepia(0.6) saturate(4) hue-rotate(190deg); // one, two, three column pngs

  // FORM - LOGIN
  --imLoginFormWrapperBgCol: var(--imColBlack82);
  --imLoginFormWrapperBorderCol: var(--imColBlack75);
  --imLoginFormWrapperHeadingBgCol: var(--imColBlack);
  --imLoginFormWrapperHeadingFontStack: var(--imPrimaryFontStackLight);
  --imLoginFormWrapperHeadingSizeMobile: 18px;
  --imLoginFormWrapperHeadingSizeDesktop: 22px;
  --imLoginFormWrapperHeadingCol: var(--imColWhite);

  // ICONS
  --imIconTransform: scale(1.2);
  --imActionIconCirclePlusFill: var(--imColBlack70);
  --imActionIconCirclePlusCol: var(--imColWhite); // The `+` colour
  --imIconInfoFill: var(--imColSecondary80);
  --imIconActionFill: var(--imNeutralSoftFill);
  --imIconActionHoverFill: var(--imColSecondary80);
  --imIndexIconActionHoverFill: var(--imColSecondary80);
  --imIndexIconActionHoverAlertFill: var(--imColRed);
  --imIconTickFill: var(--imColSecondary70);
  --imIconTriggersFill: var(--imNeutralSoftFill);
  --imIconTriggersHoverFill: var(--imColSecondary80);
  --imIndexIconNeutralFill: var(--imNeutralSoftFill);
  --imIndexIconNeutralHoverFill: var(--imThemeIconHoverFill);

  // ACTION TRIGGERS
  --imActionTriggerTextCol: var(--imColSecondary80);
  --imActionTriggerCol: var(--imColSecondary80);
  --imActionTriggerHoverTextCol: var(--imColSecondary60);
  --imActionTriggerMultipleBorderLeftCol: var(--imColBlack70);
  --imActionTriggerMultipleBgCol: var(--imColBlack84);

  // ACTION TOGGLES - Active/Inactive
  --imActionToggleHoverBgCol: rgba(255, 255, 255, 0.05);
  --imActionToggleSuccessBgCol: var(--imColDarkModeNeutralGreen);
  --imActionToggleSuccessHoverBgCol: var(--imColDarkModeNeutralGreen);
  --imActionToggleActiveDisabledBgCol: var(--imColDarkModeNeutralRed);
  --imActionToggleActiveDisabledHoverBgCol: var(--imColDarkModeNeutralRed);

  // GLOBAL MESSAGES
  --imMessageSuccessBorderCol: var(--imColSecondary);
  --imMessageSuccessIconFill: var(--imColSecondary);
  --imMessageInfoCol: var(--imColSecondary);
  --imMessageStrongCol: var(--imColWhite);
  --imMessageSuccessCol: var(--imColTextPrimary);
  --imMessageWarningCol: var(--imColOrange);
  --imMessageAlertCol: var(--imColRed);
  --imMessageNeutralCol: var(--imColTextPrimary);
  --imMessageWrapperBgCol: var(--imColBlack86);
  --imMessageWrapperBorderCol: var(--imColBlack82);
  --imMessageAccentedTextColor: var(--imColWhite);
  --imMessageAccentedWrapperBgCol: var(--imColBlack90);
  --imMessageAccentedWrapperBorderCol: var(--imMessageSuccessBorderCol);

  // NOTE INFO
  --imNoteInfoTextCol: var(--imColBlack40);
  --imNoteInfoTextStrongCol: var(--imColBlack40);
  --imNoteInfoBgCol: var(--imColBlack90);
  --imNoteInfoBorderCol: var(--imColBlack90);
  --imNoteInfoBorderLeftCol: var(--imMessageInfoCol);
  --imNoteInfoBorderBottomCol: var(--imColBlack90);
  --imNoteInfoImgFilter: hue-rotate(400deg) invert(0.8);
  --imNoteStrongBorderLeftCol: var(--imColBlack30);
  // NOTE ERROR - Rails Validation Error
  --imNoteErrorWrapperBgCol: var(--imColBlack);
  --imNoteErrorBorderLeftCol: var(--imColRed);
  --imNoteErrorHeading: var(--imColWhite);
  // Highlight Code
  --imCodeHighlightTextCol: var(--imColWhite);
  --imCodeHighlightBorderCol: var(--imColBlack75);
  --imCodeHighlightBgCol: var(--imColBlack80);

  // GROUP GRID
  --imGroupGridWrapperBgCol: var(--imColBlack85);
  --imGroupGridWrapperBorderCol: var(--imColBlack80);
  --imGroupGridChildImgFilter: invert(0.8); // UI Pattern PNGs
  // GROUP LABEL ASSET WRAPPER - theme pickers
  --imGroupLabelAssetWrapperBgCol: var(--imColBlack80);
  --imGroupLabelAssetWrapperHoverBgCol: var(--imColBlack75);
  --imGroupLabelAssetWrapperActiveBgCol: var(--imColBlack75);
  --imGroupLabelAssetWrapperBorderCol: var(--imColBlack75);
  --imGroupLabelAssetWrapperHoverBorderCol: var(--imColBlack65);
  --imGroupLabelAssetWrapperActiveBorderCol: var(--imColBlack65);

  /// DASHBOARD - Page Sub Heading
  --imDBaseSubHeadingCol: var(--imColTextPrimary);
  // DASHBOARD - Action Module
  --imfDashActionItemBgCol: var(--imColBlack85);
  --imfDashActionItemBorderCol: var(--imColBlack80);
  --imfDashActionItemBorderRadius: var(--imThemeBorderRadiusSm);
  // Item Heading
  --imfDashActionItemHeadingBgCol: var(--imfFormBgCol);
  // Radio Buttons Wrapper
  --imfDashActionRadioWrapperBorderCol: var(--imColBlack60);
  // Radio Buttons Label
  --imfDashActionRadioLabelBgCol: var(--imColBlack75);
  --imfDashActionRadioLabelHoverBgCol: var(--imColBlack70);
  --imfDashActionRadioLabelBorderRightCol: var(--imColBlack60);
  --imfDashAlertRadioLabelBgCol: var(--imColDarkModeNeutralRed);
  --imfDashAlertRadioLabelBorderCol: var(--imColDarkModeRed);
  // Radio Buttons Checked
  --imfDashActionRadioCheckeCol: var(--imColWhite);
  --imfDashActionRadioCheckeBgCol: var(--imColSecondary);
  // DASHBOARD - Large Item Wrapper
  --imfDlWrapperBgCol: var(--imColBlack85);
  --imfDlWrapperBorderCol: var(--imColBlack75);
  // DASHBOARD - Large Item Heading
  --imfDlHeadingSize: 55px;
  --imfDlHeadingLineHeight: 75px;
  --imfDlHeadingCol: var(--imColWhite);
  // DASHBOARD - Tables Heading
  --imfDlTableHeadingCol: var(--imColTextPrimary);
  --imfDlChartLeadsCountTextCol: var(--imColWhite);
  --imfDlChartLeadsCountWrapperBgCol: var(--imColBlack85);
  --imfDlChartLeadsHeadingCol: var(--imColWhite);
  // DASHBOARD - Charts
  --imDdLineChartGraphCol: var(--imColLime); // Line Chart graph colour in dashboard_data.js
  --imDdLineChartXAxisLabelCol: var(--imColBlack20); // Line Chart X Axis label colour in dashboard_data.js
  --imDdPieBorderCol: var(--imColBlack20); // Pie Chart Border in dashboard_data.js
  --imDdLegendTextCol: var(--imColWhite);
  // DASHBOARD - 3rd Party Service Links
  --imfDlServiceLinkWrapperBgCol: var(--imColBlack85);
  --imfDlServiceLinkWrapperBorderCol: var(--imColBlack80);
  --imfDlServiceLinkItemBgCol: var(--imColBlack85);
  --imfDlServiceLinkItemBorderCol: var(--imColBlack80);
  --imfDlServiceLinkItemHoverBorderCol: var(--imColBlack70);
  --imfDlServiceLinkLogoWrapperBgCol: var(--imColBlack78);
  // STOCK - Uploads Filter
  --imfStockFilterWrapperBgCol: var(--imColBlack85);
  --imfStockFilterWrapperBorderCol: var(--imColBlack80);
  // STOCK - Exports Report
  --imfStockExportReportWrapperBgCol: var(--imColBlack85);
  --imfStockExportReportWrapperBorderCol: rgba(255, 255, 255, 0.15);
  --imfStockExportTypeLogoFilter: brightness(0) invert(1);
  // Stock - Exports Status
  --imfStockExportStatusCompleteTextCol: var(--imColSecondary70);
  --imfStockExportStatusCompleteIconFill: var(--imColSecondary70);
  --imfStockExportStatusPendingIconFill: var(--imColPrimary);
  --imfStockExportStatusErrorIconFill: var(--imColRed);
  // Support - CTA
  --imCmsSupportCtaWrapperBgCol: var(--imColBlack85);
  --imCmsSupportAsideBgCol: var(--imfFormBgCol);
  --imCmsSupportImgBorderCol: var(--imColBlack80);
  --imCmsSupportImgShadow: var(--imColBlack95);
}
