// ------------------------------------------------
// Light Colours
$blue80: #79b1d3;
$blue15: #cee9f3;
$blue6: #e1f0f9;
$blue3: #f3fbff;
$red5: #fdeee9;
$yellow: #e4b23a;
$green15: #e6ffd1;
$green10: #f9fbe6;
// ------------------------------------------------

// Hex Colours
/* stylelint-disable color-hex-length */
$imColPrimaryHex: #e1e43c; // Yellow
$imColSecondaryHex: #3f3ce4; // Purple Blue
$imColBlackHex: #000000 !default;
$imColWhiteHex: #ffffff;
$imColNeutralHex: #231f20; // Black Abbadon
// SVGs Must be Hex Colours
$garageIconFill: #696566;
$garageIconHoverFill: $imColWhiteHex;
$garageIconActiveFill: $imColWhiteHex;
$iconToggleFillOn: $imColSecondaryHex;  // TODO: is this needed?
$iconToggleFillOff: #696566;
// ------------------------------------------------

:root {
  // iMotor Colours
  --imColPrimary: #e1e43c;
  --imColPrimary105: #c9cd36;
  --imColSecondary: #3f3ce4;
  // Neutral Colours
  --imColBlackAbaddon: #231f20;
  --imColWhite: #fff;
  // Auxiliary Colours
  --imColBlueLt: #b3d4fc;
  --imColCyan: #00bcd4;
  --imColCyanLt: #e0f3fe;
  --imColMagenta: #e040fb;
  --imColRed: #ff1644;
  --imColRed5: #fdeee9;
  --imColLime: #b2ff59;
  --imColGreenLt: #e9fce9;
  --imColGreenDk: #cddfcd;
  --imColLime5: #e6ffd1;
  --imColOrange: #ffa600;
  --imColDarkModeRed: #822659;
  --imColDarkModePurple: #42366c;
  --imColDarkModeGreen: #004d61;
  --imColDarkModeBlueSteel: #165185;
  --imColDarkModeNeutral: #23272a;
  --imColDarkModeNeutralGreen: #1c2930;
  --imColDarkModeNeutralBlue: #1e273b;
  --imColDarkModeNeutralRed: #2a2325; // more red

  // Blue citrus family (dark to light)
  --imColBlueDeep: #1e1c6b; // deep blue
  --imColBluePowder: #2499ff; // citrus blue
  --imColBlueLight: #8fd3fa; // light blue
  --imColBluePale: #b9e5ff; // pale blue

  // Lime citrus family (dark to light)
  --imColLimeDeep: #42ab12; // deep lime
  --imColLimeGreen: #7dd019; // lime green
  --imColLimeCitrus: #b2ff59; // citrus lime
  --imColLimePale: #cffcaa; // pale lime

  // Yellow citrus family (dark to light)
  --imColYellowStrong: #ecec18; // Strong yellow
  --imColYellowCitrus: #e1e43c; // citrus yellow (primary)
  --imColYellowLemon: #ffe066; // light lemon
  --imColYellowPale: #f7f6b2; // pale yellow

  // Orange citrus family (dark to light)
  --imColOrangeDeep: #bb3e03; // deep orange
  --imColOrangeCitrus: #ee9b00; // citrus orange
  --imColOrangePale: #ffe5b4; // pale orange
  --imColOrangeLight: #f9bd98; // light orange

  // Red citrus family (dark to light)
  --imColRedStrong: #ae2012; // strong red
  --imColRedCitrus: #ff1644; // citrus magenta/red (primary)
  --imColRedLight: #ff7a96; // light magenta/red

  // Purple citrus family (dark to light)
  --imColPurpleDeep: #7c2a8c; // deep purple
  --imColPurpleitrus: #e040fb; // citrus purple
}
