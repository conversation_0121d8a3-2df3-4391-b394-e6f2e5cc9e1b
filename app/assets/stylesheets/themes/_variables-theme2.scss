
[data-selected-theme='theme2'] {
  // Generates a full set of CSS custom properties for black tints (from pure black to white) based on the given base color.
  @include imColTintsBlack(#000000);
  // NAV 1
  --nav1borderBottomThickness: 4px;
  --nav1HoverBorderBottomCol: var(--imColPrimary);
  --nav1ActiveborderBottomCol: var(--imColPrimary);
  // NAV 1 - Icon
  --nav1HoverIconFill: var(--imColPrimary);
  --nav1ActiveIconFill: var(--imColPrimary);
  // NAV 2
  --nav2WrapperBorderBottom: 10px solid var(--imColPrimary); // e.g. 10px solid var(--imColPrimary)
  // NAV 2 - Icon
  --nav2LinkHoverIconFill: var(--imColPrimary);
  --nav2LinkActiveIconFill: var(--imColPrimary);
  // HEADER - h1 Heading
  --heading1BgCol: var(--imColBlackAbaddon105);
  --heading1FontStack: var(--imPrimaryFontStackLight);
  --heading1Col: var(--imColWhite);
  // HEADER - Flash Message
  --imFlashMessageWrapperBgCol: var(--imColBlackAbaddon105);// e.g. var(--imColBlackAbaddon105)
  --imFlashMessageTextCol: var(--imColWhite);
  --imFlashMessageIconFill: var(--imColWhite);
  // NAV 3 - Icon
  --nav3HoverIconFill: var(--imColPrimary);
  --nav3ActiveIconFill: var(--imColPrimary);
  // NAV 4 - Icon
  --nav4HoverIconFill: var(--imColPrimary);
  --nav4ActiveIconFill: var(--imColPrimary);
  // FORM - BUTTONS
  --btnPrimaryTextCol: var(--imColBlack);
  --btnPrimaryHoverTextCol: var(--imColWhite);
  --btnPrimaryBgCol: var(--imColPrimary);
  --btnPrimaryHoverBgCol: var(--imColBlackAbaddon);
  --btnPrimaryActiveBgCol: var(--imColBlackAbaddon);
  --btnPrimaryIconFill: var(--imColBlack);
  // TOGGLES - Advanced with words - HEADING1
  --imHeadingToggleAdvLabelCol: var(--imColWhite);
  // knob
  --imHeadingToggleAdvKnobBgCol: var(--imColBlackAbaddon);
  --imHeadingToggleAdvKnobHoverBgCol: var(--imColSecondary);
  --imHeadingToggleAdvKnobActiveBgCol: var(--imColSecondary);
  --imHeadingToggleAdvSlotBgCol: var(--imColBlackAbaddon40);
  --imHeadingToggleAdvSlotActiveBgCol: var(--imColBlackAbaddon40);
  --imHeadingToggleAdvSlotBorderCol: var(--imColBlackAbaddon60);
  --imHeadingToggleAdvSlotActiveBorderCol: var(--imColBlackAbaddon60);
  // TOGGLES - Advanced with words - NAV2 AND SHARED
  --imToggleAdvLabelCol: var(--imColWhite);
  --imToggleAdvKnobBgCol: var(--imColBlackAbaddon);
  --imToggleAdvKnobHoverBgCol: var(--imColSecondary);
  --imToggleAdvKnobActiveBgCol: var(--imColSecondary);
  --imToggleAdvSlotBgCol: var(--imColBlackAbaddon40);
  --imToggleAdvSlotActiveBgCol: var(--imColBlackAbaddon40);
  --imToggleAdvSlotBorderCol: var(--imColBlackAbaddon70);
  --imToggleAdvSlotActiveBorderCol: var(--imColBlackAbaddon70);
}
