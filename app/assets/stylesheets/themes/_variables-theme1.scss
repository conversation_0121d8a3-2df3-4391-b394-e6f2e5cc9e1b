
[data-selected-theme='theme1'] {
  // Generates a full set of CSS custom properties for black tints (from pure black to white) based on the given base color.
  @include imColTintsBlack(#000000);
  // LOGO
  --imLogoCol: var(--imColBlack);
  --imLogoFill: var(--imColBlack);
  // NAV 1 - Welcome Message
  --nav1WelcomeMessageCol: var(--imColBlack);
  // NAV 1
  --nav1WrapperBg: var(--imColPrimary);
  --nav1borderBottomPosition: 10px; // bottom position of the border
  --nav1borderBottomThickness: 4px;
  --nav1borderBottomMarginLeft: 8px; // required to visually center the border
  --nav1borderBottomTransition: width 300ms ease-in-out;
  --nav1HoverBorderBottomWidth: 75%;
  --nav1HoverBorderBottomCol: var(--imColBlack);
  --nav1ActiveborderBottomWidth: 70%;
  --nav1ActiveborderBottomCol: var(--imColBlack);
  // NAV 1 - Icon
  --nav1IconFill: var(--imColBlack);
  --nav1HoverIconFill: var(--imColBlack);
  --nav1ActiveIconFill: var(--imColBlack);
  // NAV 1 - Text
  --nav1TextCol: var(--imColBlack);
  --nav1HoverTextCol: var(--imColBlack);
  --nav1ActiveTextCol: var(--imColBlack);
  // NAV 2 - Icon
  --nav2LinkHoverIconFill: var(--imColPrimary);
  --nav2LinkActiveIconFill: var(--imColPrimary);
  // HEADER - h1 Heading
  --heading1BgCol: var(--imColBlackAbaddon105);
  --heading1FontStack: var(--imPrimaryFontStackLight);
  --heading1Col: var(--imColWhite);
  // HEADER - Flash Message
  --imFlashMessageWrapperBgCol: var(--imColBlackAbaddon105);// e.g. var(--imColBlackAbaddon105)
  --imFlashMessageTextCol: var(--imColWhite);
  --imFlashMessageIconFill: var(--imColWhite);
  // NAV 3 - Icon
  --nav3HoverIconFill: var(--imColPrimary);
  --nav3ActiveIconFill: var(--imColPrimary);
  // NAV 4 - Icon
  --nav4HoverIconFill: var(--imColPrimary);
  --nav4ActiveIconFill: var(--imColPrimary);
  // FORM - BUTTONS
  --btnPrimaryTextCol: var(--imColBlack);
  --btnPrimaryHoverTextCol: var(--imColWhite);
  --btnPrimaryBgCol: var(--imColPrimary);
  --btnPrimaryHoverBgCol: var(--imColBlackAbaddon);
  --btnPrimaryActiveBgCol: var(--imColBlackAbaddon);
  --btnPrimaryIconFill: var(--imColBlack);
  // TOGGLES - Advanced with words - HEADING1
  --imHeadingToggleAdvLabelCol: var(--imColWhite);
  // knob
  --imHeadingToggleAdvKnobBgCol: var(--imColBlackAbaddon);
  --imHeadingToggleAdvKnobHoverBgCol: var(--imColSecondary);
  --imHeadingToggleAdvKnobActiveBgCol: var(--imColSecondary);
  --imHeadingToggleAdvSlotBgCol: var(--imColBlackAbaddon40);
  --imHeadingToggleAdvSlotActiveBgCol: var(--imColBlackAbaddon40);
  --imHeadingToggleAdvSlotBorderCol: var(--imColBlackAbaddon60);
  --imHeadingToggleAdvSlotActiveBorderCol: var(--imColBlackAbaddon60);
  // TOGGLES - Advanced with words - NAV2 AND SHARED
  --imToggleAdvLabelCol: var(--imColWhite);
  --imToggleAdvKnobBgCol: var(--imColBlackAbaddon);
  --imToggleAdvKnobHoverBgCol: var(--imColSecondary);
  --imToggleAdvKnobActiveBgCol: var(--imColSecondary);
  --imToggleAdvSlotBgCol: var(--imColBlackAbaddon40);
  --imToggleAdvSlotActiveBgCol: var(--imColBlackAbaddon40);
  --imToggleAdvSlotBorderCol: var(--imColBlackAbaddon70);
  --imToggleAdvSlotActiveBorderCol: var(--imColBlackAbaddon70);
}
