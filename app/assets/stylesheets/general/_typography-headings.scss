.section-heading-wrapper {
  background-color: var(--heading1BgCol);
  position: relative;
  overflow: hidden;
  flex: none;

  &.section-status {
    display: flex;
    justify-content: space-between;
  }
}

.section-heading {
  @include fontSmoothing;
  font-family: var(--heading1FontStack);
  font-size: var(--heading1Size);
  line-height: var(--heading1LineHeight);
  color: var(--heading1Col);
  padding: var(--heading1Padding);
  margin: 0;
}

.im-page-heading,
.imf-page-heading {
  font-family: var(--imfFormHeadingFontStack);
  @include fontSmoothing;
  font-size: var(--imfFormHeadingSizeMobile);
  color: var(--imfFormHeadingCol);
  margin: 0 0 1rem;
  line-height: 1.7rem;

  @include mediaQueryMin($bp-lg) {
    font-size: var(--imfFormHeadingSizeDesktop);
    margin: 0.6rem 0 1.8rem;
  }
}

.im-page-sub-heading,
.imf-page-sub-heading {
  @include primaryFontStackLight;
  font-size: 16px;
  margin: 0;
  padding: 0 0 10px 0;

  @include mediaQueryMin($bp-lg) {
    font-size: 18px;
    padding: 5px 0 15px 0;
  }
}

.section-sub-heading {
  background-color: var(--imColBlack5);
  border-bottom: 1px solid var(--imColBlack12);
  margin: 0;
  flex: none;
}

.db-sub-heading {
  @include primaryFontStackLight;
  @include fontSmoothing;
  font-size: 22px;
  color: var(--imDBaseSubHeadingCol);
  margin-bottom: var(--imfFormItemMargin);
}

.sub-heading-home {
  @include primaryFontStack;
  font-size: var(--imGlobalSubHeadingFontSize);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  padding: 9px 0;
  margin: 5px 0 5px 0;
}

.imf-group-heading {
  @include subHeading;
  @include formSubHeadingReverse;
  margin: 0;
}

.imf-sub-heading {
  font-family: var(--imfSubHeadingFontStack);
  @include fontSmoothing;
  font-size: var(--imfSubHeadingSize);
  margin: 10px 0 8px 0;

  em {
    font-size: 15px;
  }
}

.imf-sub-heading-1 {
  font-family: var(--imfSubHeading1FontStack);
  font-size: var(--imfSubHeading1Size);
  text-transform: var(--imfSubHeading1TextTransform);
  color: var(--imfSubheading1Col);
  border-radius: var(--imfSubHeading1Radius);
  background-color: var(--imfSubHeading1BgCol);
  padding: var(--imfSubHeading1Padding);
  margin-top: var(--imfFormItemMargin);
  margin-bottom: var(--imfFormItemMargin);
  width: 100%;

  .col-wrapper-2 + & {
    margin-top: 0;
  }

  fieldset & {
    &:first-child {
      margin-block-start: 0;
    }
  }
}

.imf-sub-heading-2 {
  @include subHeading;
  color: var(--imfSubHeading2Col);
  padding-top: var(--imfFormItemMargin);
  margin-bottom: var(--imfFormItemMargin);

  &.no-gap {
    margin: 0;
    padding: 0;
  }
}

// created as a bold version of a label or imf-label
.imf-sub-heading-3 {
  @include subHeading;
  color: var(--imfSubHeading3Col);
  margin-bottom: 5px;
}

// created as a bold version with a background colour
.imf-sub-heading-4 {
  @include subHeading;
  color: var(--imfSubHeading4Col);
  background-color: var(--imfSubHeading4BgCol);
  margin-bottom: var(--imfFormItemMargin);
  padding: var(--imfSubHeading4Padding);
  display: inline-block;
  border-radius: var(--imfSubHeading4Radius);
}

.imf-sub-heading-5 {
  font-family: var(--imfSubHeading5FontStack);
  font-size: var(--imfSubHeading5Size);
  color: var(--imfSubHeading5Col);
  margin: 22px 0 12px 0;
}

.summary-sub-heading-1 {
  box-sizing: border-box; // important! override user-agent style content-box on <summary>
  display: block; // required to hide native details-marker (strange)
  cursor: pointer; // user-agent doesn't apply cursor: pointer

  // hide native details-marker on webkit? (doesn't seem to work - display: block is the only thing that works)
  &::-webkit-details-marker {
    display: none;
  }

  &:hover .imf-sub-heading-1 {
    color: var(--imfSubHeading1AccordionHoverTextCol);
    background-color: var(--imfSubHeading1AccordionHoverBgCol);

    &::before,
    &::after {
      background-color: var(--imfSubHeading1AccordionHoverTriggerIconsFill); // color of plus / minus lines
    }
  }

  .imf-sub-heading-1 {
    color: var(--imfSubHeading1AccordionTextCol);
    position: relative;
    display: inline-flex;
    align-items: center;
    padding-inline-start: 35px;
    padding-block: 10px;
    background-color: var(--imfSubHeading1AccordionBgCol);
    box-sizing: border-box; // important! Keep to override user-agent style content-box on <summary>
    transition: background-color 150ms ease-in-out;

    // plus / minus symbols
    &::before,
    &::after {
      content: '';
      background-color: var(--imfSubHeading1AccordionTriggerIconsFill); // color of plus / minus lines
      width: 18px; // length of plus / minus line
      height: 2px; // thickness of plus / minus line
      margin-left: 10px;
      position: absolute;
      left: 0;
      top: 50%; // vertically center absolute positioned element
      transform: translateY(-50%); // vertically center absolute positioned element
      transition: transform 200ms ease-in-out;
    }

    // minus symbol
    &::after {
      transform: translateY(-50%) rotate(90deg); // rotate one of the lines to make the minus symbol
    }

    [open] & {
      &::after {
        transform: translateY(-50%) rotate(180deg);
      }
    }
  }
}
