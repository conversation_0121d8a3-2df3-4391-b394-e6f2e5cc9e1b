// <PERSON> http://www.paulirish.com/2012/box-sizing-border-box-ftw/
html {
  box-sizing: border-box;
}

*,
*::before,
*::after {
  box-sizing: border-box; // must be explicit here because all children of <details> and <summary> will inherit user-agent box-sizing: content-box; 🤦‍♂️
}

body {
  height: 100%;
  margin: 0;
  display: flex;
  flex-direction: column;

  // hide scroll bar on body when modal is active
  &.modal-active {
    overflow: hidden;
  }
}

.main {
  display: flex;
  flex-direction: column;
  flex: 1 0 auto;
  position: relative;
  transition: margin var(--imHelpWikiTransitionDuration) ease-in-out;

  @include mediaQueryMin($bp-md) {
    flex-direction: row;
  }

  .login &,
  .users &,
  .profile &,
  .help & {
    flex-direction: column;
  }

  .sessions &,
  .login & {
    align-items: center;
  }

  &.wiki-sidebar-open {
    @include mediaQueryMin($bp-lg) {
      margin-right: var(--imHelpWikiOpenWidthDesktop); // Adjust margin to match the width of the help wiki sidebar
    }
  }
}

.section-general {
  flex: 1;
}
