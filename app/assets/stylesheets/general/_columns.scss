.col-wrapper-2 {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  align-items: flex-start;

  @include mediaQueryMin($bp-lg) {
    flex-direction: row;
  }

  &.align-bottom {
    align-items: flex-end;
  }

  &.align-center {
    align-items: center;
  }

  &.align-normal {
    align-items: normal;
  }

  .imf-inputs-wrapper, // used to group multiple inputs without margin bottom
  .imf-input-wrapper,
  .col-item,
  .wrapper-50 {
    width: 100%;

    @include mediaQueryMin(1366px) {
      @include rowCol(2, 2%);
    }

    .sei-item & {
      @include mediaQueryMin($bp-xl) {
        @include rowCol(2, 2%);
      }
    }
  }

  &.child-1-hidden .imf-input-wrapper {
    @include mediaQueryMin($bp-lg) {
      @include rowCol<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(2, 2%);
    }
  }
}

.col-wrapper-3 {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;

  @include mediaQueryMin($bp-lg) {
    flex-direction: row;
  }

  .imf-inputs-wrapper, // used to group multiple inputs without margin bottom
  .imf-input-wrapper {
    @include mediaQueryMin($bp-lg) {
      @include rowCol(2, 2%);
    }

    @include mediaQueryMin($bp-xl) {
      @include rowCol(3, 2%);
    }
  }
}

.col-wrapper-4 {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;

  @include mediaQueryMin($bp-sm) {
    flex-direction: row;
  }

  .imf-inputs-wrapper, // used to group multiple inputs without margin bottom
  .imf-input-wrapper {
    @include mediaQueryMin($bp-sm-2) {
      @include rowCol(2, 2%);
    }

    @include mediaQueryMin($bp-md-2) {
      @include rowCol(3, 2%);
    }

    @include mediaQueryMin($bp-xl) {
      @include rowCol(4, 2%);
    }
  }
}

.col-wrapper-custom {
  display: flex;
  flex-wrap: wrap;

  .imf-input-wrapper {
    @include rowColNthType(2, 2%);
  }

  .imf-label:first-child {
    margin-top: 5px;
  }
}

.wrapper-50 {
  display: flex;

  .imf-input-wrapper {
    margin-right: 2%;

    &:last-child {
      margin-right: 0;
    }
  }
}

.wrapper-flex-nowrap {
  display: flex;
}

.wrapper-flex-wrap {
  display: flex;
  flex-wrap: wrap;
}

.wrapper-flex {
  @include mediaQueryMin($bp-lg) {
    display: flex;
  }

  .flex-item {
    @include mediaQueryMin($bp-md) {
      margin-right: 2%;
    }

    &:last-child {
      @include mediaQueryMin($bp-md) {
        margin-right: 0;
      }
    }
  }

  .flex-item-small {
    flex: 0.2;
    min-width: 180px;
  }

  .flex-item-fill {
    flex: 1;
  }
}

.col-item {
  width: 100%;
  margin-bottom: var(--imfFormItemMargin);
}
