body {
  @include secondaryFontStack;
  font-size: var(--imGlobalFontSize);
  line-height: 1.4em;
  color: var(--imColTextPrimary);
}

::-moz-selection {
  color: var(--imTextSelectionTextCol);
  background: var(--imTextSelectionBgCol);
  text-shadow: none;
}

::selection {
  color: var(--imTextSelectionTextCol);
  background: var(--imTextSelectionBgCol);
  text-shadow: none;
}

h1 {
  @include primaryFontStackLight;
  @include fontSmoothing;
}

h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0;
  padding: 0;
  margin-bottom: 0.8rem;
}

h2,
h3,
h4,
h5,
h6 {
  @include secondaryFontStack;
  @include fontSmoothing;
}

p {
  font-size: var(--imGlobalFontSize);
  font-weight: 400;
  line-height: 24px;
  letter-spacing: 0;
  margin-bottom: 0.8rem;
}

a {
  color: var(--imColLinkPrimary);
  font-weight: 500;
  text-decoration: none;

  &:hover {
    color: var(--imColLinkHoverPrimary);
    text-decoration: none;
  }
}

strong {
  font-family: var(--imTextStrongFontStack);
  color: var(--imColTextStrong);
}

.text-black,
.weighted {
  color: var(--imColTextStrong);
}

// A better looking default horizontal rule
hr {
  display: block;
  height: 1px;
  border: 0;
  border-top: 1px solid var(--imColBlack20);
  margin: 1em 0;
  padding: 0;
}

sup,
sub {
  vertical-align: baseline;
  position: relative;
  top: -0.4em;
}

sub {
  top: 0.4em;
}

ul,
ol {
  font-size: var(--imGlobalFontSize);
  font-weight: 400;
  line-height: 24px;
  margin: 0;
  padding: 0;
}

li {
  list-style: none;
}

dl {
  margin: 0 0 1rem 0;
}

dt {
  @include primaryFontStackBold;
  color: var(--imColBlack);
}

dt,
dd {
  line-height: 1.6rem;
  font-weight: normal;
}

dd {
  margin: 0;
}

.list-plain {
  padding-left: 15px;
}

.lp-item {
  list-style-type: disc;
  line-height: 1.2rem;
  margin-bottom: 5px;
}

.lc {
  text-transform: none;
}

.hidden-mobile {
  display: none;

  @include mediaQueryMin($bp-sm) {
    display: inline;
  }
}

.smaller {
  font-size: 85%;
}

.text-well {
  padding: 1rem;
  background: var(--imColBlack5);
  border: 1px solid var(--imColBlack12);
  border-radius: 3px;
}

.text-blue {
  color: var(--imColSecondary);
}

.tw-text {
  margin-bottom: 0;
}

.tw-sub-heading {
  font-size: 18px;
}

.tw-content {
  font-size: 18px;
  color: var(--imColSecondary);
}

.list-roman {
  padding-left: 2em;
  margin-bottom: 1em;

  li {
    list-style-type: lower-roman;
  }
}

.notes {
  .brands &,
  .dealerships & {
    @include primaryFontStackBold;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-style: italic;
    color: var(--imColSecondary);
  }
}

// highlighted used in Dealership Configuration Form
span.highlighted {
  color: var(--imfTextAccentNeutraTextCol);
  margin-right: 4px;
}

.highlighted {
  background-color: var(--imfCmsConfigAccentBgCol);
  border: 1px solid var(--imfCmsConfigAccentBorderCol);
  padding: 5px;
  border-radius: var(--imfCmsConfigAccentRadius);
  @include Transition(border, 200);

  &:hover {
    border: 1px solid var(--imfCmsConfigAccentHoverBorderCol);
  }

  .imf-check-wrapper & {
    margin-bottom: 0;
  }
}

.text-highlighted {
  color: var(--imfTextAccentNeutraTextCol);
  background-color: var(--imfTextAccentNeutralBgCol);
  display: inline-block;
  padding: 2px 6px;
  border-radius: 10px;
  border: 1px solid var(--imfTextAccentNeutralBorderCol);

  &.highlighted-blue {
    background-color: var(--imfTextAccentSecondaryBgCol);
    border-color: var(--imfTextAccentSecondaryBorderCol);
  }
}
