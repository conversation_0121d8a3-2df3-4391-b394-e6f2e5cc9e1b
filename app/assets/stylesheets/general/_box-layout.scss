.box-nav1 {
  padding: 0;

  @include mediaQueryMin($bp-lg) {
    padding: 0 1.5rem;
  }
}

.box-nav2 {
  padding: 0;

  @include mediaQueryMin($bp-lg) {
    padding: 0 1.5rem;
  }
}

.box-t {
  padding: 1rem 0 0 0;

  @include mediaQueryMin($bp-md) {
    padding: 1.5rem 0 0 0;
  }
}

.box-t-md {
  padding: 1rem 0 0 0;

  @include mediaQueryMin($bp-md) {
    padding: 0.5rem 0 0 0;
  }
}

.box-rl {
  padding: 0 1rem;

  @include mediaQueryMin($bp-md) {
    padding: 0 1.5rem;
  }
}

.box-trl {
  padding: 1rem 1rem 0 1rem;

  @include mediaQueryMin($bp-md) {
    padding: 1.5rem 1.5rem  0 1.5rem;
  }
}

.box-rbl {
  padding: 0 1rem 1rem 1rem;

  @include mediaQueryMin($bp-md) {
    padding: 0 1.5rem 1.5rem 1.5rem;
  }
}

.box-trbl {
  padding: 1rem;

  @include mediaQueryMin($bp-md) {
    padding: 1.5rem;
  }
}
