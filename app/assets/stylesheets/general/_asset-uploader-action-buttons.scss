.au-action-wrapper {
  cursor: pointer;
  margin: 0;
  padding: 0 10px 0 2px;
  height: var(--imfAssetUploaderThumbSize);
  border-radius: 3px;
  border: 1px solid transparent;
  display: flex;
  align-items: center;
  gap: 3px;
  transition: background-color 150ms ease-in-out;

  &:hover {
    background-color: var(--imfAssetUploaderTriggerHoverBgCol);
    border: 1px solid var(--imfAssetUploaderTriggerHoverBorderCol);

    .au-action-text {
      color: var(--imfAssetUploaderTriggerHoverTextCol);
    }
  }

  .au-asset-items-wrapper & {
    margin-left: 7px;
  }
}

.au-action-text {
  font-family: var(--imfAssetUploaderTriggerFontStack);
  color: var(--imfAssetUploaderTriggerTextCol);
  @include Transition(color, 150);
}
