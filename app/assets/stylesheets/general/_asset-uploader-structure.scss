.asset-uploader-wrapper {
  display: flex;
  flex-wrap: wrap;
  background-color: var(--imfAssetUploaderBodyBgCol);
  border: 1px solid var(--imfAssetUploaderBodyBorderCol);
  border-top: 0;
  border-radius: 0 0 3px 3px;

  &.video-uploader {
    border-radius: 0;

    @include mediaQueryMax($bp-xl) {
      border-bottom: 0;
    }
  }
}

// Banner Chooser is used in Pages when a template page is selected
// It's not an asset uploader, but is styled to look like one
.banner-chooser-wrapper {
  .asset-uploader-wrapper {
    border-top: 1px solid var(--imColBlack12);
    width: 100%;
  }

  .page-banner-display {
    flex-wrap: wrap;
  }

  .asset-file-name-wrapper {
    width: 100%;
  }
}

.au-child-first-wrapper,
.au-child-last-wrapper {
  width: 100%;
  display: flex;
  align-items: stretch;
}

.au-child-first-wrapper {
  border-bottom: 1px solid var(--imColBlack12);
  flex-wrap: wrap;

  @include mediaQueryMin($bp-xl) {
    flex-wrap: nowrap;
    border-bottom: 0;
    width: 60%;

    &:only-child {
      width: 100%;
    }
  }
}

.au-child-last-wrapper {
  padding: 10px;

  @include mediaQueryMin($bp-xl) {
    width: 40%;
    margin-right: 0;
    border-left: 1px solid var(--imfAssetUploaderBodyFirstBorderCol);
  }
}

.au-child-item-wrapper {
  display: flex;
  align-items: center;
  padding: 10px;
  width: 50%;

  @include mediaQueryMin($bp-md) {
    width: auto;
  }

  @include mediaQueryMin(1500px) {
    width: 50%;
  }

  &:first-child {
    width: 100%;
    border-bottom: 1px solid var(--imfAssetUploaderBodyFirstBorderCol);

    @include mediaQueryMin($bp-lg) {
      flex: 1;
      border-bottom: 0;
      border-right: 1px solid var(--imfAssetUploaderBodyFirstBorderCol);
    }

    @include mediaQueryMin(1500px) {
      flex: initial;
    }
  }
}
