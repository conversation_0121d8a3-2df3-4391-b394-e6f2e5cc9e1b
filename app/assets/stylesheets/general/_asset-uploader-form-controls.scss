.au-asset-items-wrapper {
  display: flex;

  // keep input but minimise it so it still works when using keyboard with tab key
  input[type='file'] {
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
    clip: rect(1px, 1px, 1px, 1px);
  }

  // highlight label when using keyboard with tab key
  input[type='file']:focus ~ .au-label-focus {
    outline: 2px dotted var(--imColBlack);
  }
}

.au-input-wrapper {
  flex-direction: column;
}
