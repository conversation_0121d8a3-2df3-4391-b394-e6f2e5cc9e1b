// functions
@import 'base/functions';
@import 'mixins/mixins-tints-black';

// external fonts
@import 'base/external-fonts';
// variables
@import 'themes/variables-font-stacks';
// colours
@import 'themes/variables-colours-hex';
@import 'themes/variables-colours-tints';
@import 'themes/variables-theme-base';
// themes
@import 'themes/variables-theme1';
@import 'themes/variables-theme2';
@import 'themes/variables-theme-dark';
// other
@import 'themes/variables-media-queries';
@import 'themes/variables-zindex';

// mixins
@import 'mixins/mixins';
@import 'mixins/mixins-buttons';
@import 'mixins/mixins-fonts';
@import 'mixins/mixins-headings';
@import 'mixins/mixins-media-queries';
@import 'mixins/mixins-navs';
@import 'mixins/mixins-typography';

// 3rd party libs
@import 'libs/bootstrap-datetimepicker';
@import 'libs/bootstrap-datetimepicker-standalone';
@import 'libs/cropper'; // TODO: CSS for JS image cropper. Remove after fixing JS image cropper bugs
@import 'libs/select-choices-plugin';

// modals
@import 'modals/modal-warning';

// resets
@import 'resets/h5bp';
@import 'resets/resets';

// general
@import 'general/asset-uploader-action-buttons';
@import 'general/asset-uploader-base';
@import 'general/asset-uploader-errors';
@import 'general/asset-uploader-form-controls';
@import 'general/asset-uploader-images';
@import 'general/asset-uploader-structure';
@import 'general/helpers';
@import 'general/layout';
@import 'general/general';
@import 'general/columns';
@import 'general/gaps';
@import 'general/box-layout';
@import 'general/typography-headings';
@import 'general/typography';

// components
@import 'components/component-animations';
@import 'components/component-check-custom';
@import 'components/component-errors';
@import 'components/component-toggle-check';
@import 'components/component-links';
@import 'components/component-notes-messages';
@import 'components/component-toggle-advanced';
@import 'components/component-toggle';
@import 'components/component-tooltip';

// modules
@import 'modules/module-action';
@import 'modules/module-action-edit';
// TODO: Refactor - remove duplicate asset uploaders
@import 'modules/module-asset-image';
@import 'modules/module-asset-svg';
@import 'modules/module-asset-upload-direct';
@import 'modules/module-asset-upload';
@import 'modules/module-banner-lists-toggled';
@import 'modules/module-breadcrumb';
@import 'modules/module-buttons';
@import 'modules/module-buttons-preview-publish';
@import 'modules/module-editor-tinymce';
@import 'modules/module-filter-inputs';
@import 'modules/module-flash';
@import 'modules/module-forms';
@import 'modules/module-forms-asset-module';
@import 'modules/module-forms-autocomplete';
@import 'modules/module-forms-batch-uploads';
@import 'modules/module-forms-character-counter';
@import 'modules/module-forms-checkboxes-groups';
@import 'modules/module-forms-checkboxes';
@import 'modules/module-forms-checks-radios';
@import 'modules/module-forms-radios';
@import 'modules/module-forms-datepicker';
@import 'modules/module-forms-gridded';
@import 'modules/module-forms-datepicker-jquery-ui';
@import 'modules/module-forms-operating-week-exceptions';
@import 'modules/module-forms-input-note-specials';
@import 'modules/module-forms-stock-exports';
@import 'modules/module-forms-stock-feeds';
@import 'modules/module-forms-stock-uploads';
@import 'modules/module-forms-stock-rules';
@import 'modules/module-forms-timepicker';
@import 'modules/module-graph';
@import 'modules/module-home-banners';
@import 'modules/module-image-crop';
@import 'modules/module-information';
@import 'modules/module-item-cloneable';
@import 'modules/module-items';
@import 'modules/module-list-column-helpers';
@import 'modules/module-list-grid';
@import 'modules/module-list-input';
@import 'modules/module-list-recurring-action';
@import 'modules/module-list-recurring-base';
@import 'modules/module-list-recurring-checks-radios';
@import 'modules/module-list-recurring-columns';
@import 'modules/module-list-recurring-headings';
@import 'modules/module-list-recurring-icons';
@import 'modules/module-list-recurring-images';
@import 'modules/module-list-recurring-items';
@import 'modules/module-list-recurring-placeholders';
@import 'modules/module-list-recurring-template-images';
@import 'modules/module-list-recurring-text';
@import 'modules/module-list-simple';
@import 'modules/module-list-sortable';
@import 'modules/module-list-table';
@import 'modules/module-logs-builds';
@import 'modules/module-logs-sites';
@import 'modules/module-menu-basic';
@import 'modules/module-nav1';
@import 'modules/module-nav2';
@import 'modules/module-nav3-4-btn-toggle';
@import 'modules/module-nav3';
@import 'modules/module-nav4';
@import 'modules/module-site-sections-and-pages-urls';
@import 'modules/module-site-sections-and-pages';
@import 'modules/module-config-forms-and-integrations';
@import 'modules/module-search-bar';
@import 'modules/module-stock';
@import 'modules/module-statuses';
@import 'modules/module-stock-export';
@import 'modules/module-stock-export-marketplace';
@import 'modules/module-stock-export-report';
@import 'modules/module-stock-export-logos';
@import 'modules/module-svg';
@import 'modules/module-tables';
@import 'modules/module-tabs';
@import 'modules/module-tabs-outlined';
@import 'modules/module-help-wiki';
@import 'modules/module-tags';
@import 'modules/module-theme-switcher-btns';
@import 'modules/module-toast-section-body';
@import 'modules/module-toast-section-header';
@import 'modules/module-toast-status';
@import 'modules/module-toast';

// sections
@import 'sections/section-garage';
@import 'sections/section-general';
@import 'sections/section-header';
@import 'sections/section-global';
@import 'sections/section-login';
@import 'sections/section-search';
@import 'sections/section-sidebar-hud';

// dashboard
@import 'dashboard/dashboard-action-module';
@import 'dashboard/dashboard-chart-doughnut';
@import 'dashboard/dashboard-chart-headings';
@import 'dashboard/dashboard-chart-line';
@import 'dashboard/dashboard-data-display-large-module';
@import 'dashboard/dashboard-data-large';
@import 'dashboard/dashboard-enquiries';
@import 'dashboard/dashboard-filter-bar';
@import 'dashboard/dashboard-legends';
@import 'dashboard/dashboard-leads';
@import 'dashboard/dashboard-links-modal';
@import 'dashboard/dashboard-links';
@import 'dashboard/dashboard-reports';
@import 'dashboard/dashboard-stock-analytics';
@import 'dashboard/dashboard-tables';
@import 'dashboard/dashboard';

// pages
@import 'pages/page-help';
@import 'pages/page-stock-export';

// pages - custom layout
@import 'pages-custom-layout/page-component-configuration';
@import 'pages-custom-layout/page-component-draggables';
@import 'pages-custom-layout/page-component-form';
@import 'pages-custom-layout/page-layout-columns';
@import 'pages-custom-layout/page-layout-drop-zone';
@import 'pages-custom-layout/page-layout';

// vehicle
@import 'vehicle/vehicle';
@import 'vehicle/loading-component';

// user permissions
@import 'users/authorisations';
