@font-face {
  font-family: 'Glyphicons Halflings';
  src: font-url('glyphicons-halflings-regular.eot');
  src: font-url('glyphicons-halflings-regular.eot?#iefix') format('embedded-opentype'), font-url('glyphicons-halflings-regular.woff2') format('woff2'), font-url('glyphicons-halflings-regular.woff') format('woff'), font-url('glyphicons-halflings-regular.ttf') format('truetype'), font-url('glyphicons-halflings-regular.svg#glyphicons_halflingsregular') format('svg');
}

.glyphicon {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: 'Glyphicons Halflings', sans-serif;
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.glyphicon-time::before {
  content: '\e023';
}

.glyphicon-chevron-left::before {
  content: '\e079';
}

.glyphicon-chevron-right::before {
  content: '\e080';
}

.glyphicon-chevron-up::before {
  content: '\e113';
}

.glyphicon-chevron-down::before {
  content: '\e114';
}

.glyphicon-calendar::before {
  content: '\e109';
}

.btn {
  display: inline-block;
  padding: 6px 12px;
  margin-bottom: 0;
  font-size: var(--imGlobalFontSize);
  font-weight: normal;
  line-height: 1.42857143;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
}

.collapse {
  display: none;
}

.collapse.in {
  display: block;
}

.dropdown-menu {
  position: absolute;
  left: 0;
  z-index: $space; // 100
  display: none;
  float: left;
  min-width: 160px;
  padding: 5px 0;
  margin: 2px 0 0;
  font-size: var(--imGlobalFontSize);
  text-align: left;
  list-style: none;
  background-color: var(--imColWhite);
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 1px solid var(--imColBlack10);
  border-radius: 0;
}

.list-unstyled {
  padding-left: 0;
  list-style: none;
}
