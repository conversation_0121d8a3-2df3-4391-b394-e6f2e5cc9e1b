class CopyDamageReportAttachmentsJob < ApplicationJob
  queue_as :default

  def perform(source_id, destination_id)
    source = DamageReport.find(source_id)
    destination = DamageReport.find(destination_id)

    return if source.media_files.blank?

    source.media_files.each do |file|
      destination.media_files.attach \
        io: StringIO.new(file.download),
        filename: file.filename,
        content_type: file.content_type
    end
  end
end
