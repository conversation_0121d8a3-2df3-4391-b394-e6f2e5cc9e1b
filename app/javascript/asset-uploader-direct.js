/*global add_visibility, remove_visibility, tmpl, Sortable, Rails */

document.addEventListener("turbo:load", function () {
  "use strict";

  var stock_uploads_target = document.getElementById("stock_uploads");

  if (stock_uploads_target !== null) {
    // drag and drop event listeners
    window.stock_uploader = new window.DirectAssetUploader("stock_uploads");
    window.stock_uploader.form_data = JSON.parse(stock_uploads_target.dataset.formData);
    window.stock_uploader.url = stock_uploads_target.dataset.url;
    window.stock_uploader.host = stock_uploads_target.dataset.host;
    window.stock_uploader.post_uploads_url = stock_uploads_target.dataset.postUploadsUrl;
    window.stock_uploader.position_seed = parseInt(stock_uploads_target.dataset.positionSeed, 10);
    window.stock_uploader.attach_events();
  }

  $("#current_uploads").on("click", ".js-remove-sortable-item", function () {
    // move id and _destroy hidden form fields, to seperate div.
    // remove the js-sortable-item element from the list
    // re-index the position attributes for all remaining items
    window.stock_uploader.sortable_uploads.classList.add("working");

    var destroy_upload = document.getElementById("destroy_" + this.dataset.id);
    var id_upload = document.getElementById("identifier_" + this.dataset.id);
    destroy_upload.value = true;
    document.getElementById("deleted_uploads_container").appendChild(id_upload);
    document.getElementById("deleted_uploads_container").appendChild(destroy_upload);

    $(this)
      .closest(".js-sortable-item")
      .remove();

    window.stock_uploader.sortable_instance.toArray().forEach(function (element, index) {
      document.getElementById("position_" + element).value = index + 1;
    });

    window.DirectAssetUploader.update_published_counter();
    window.DirectAssetUploader.update_export_position();
    window.stock_uploader.sortable_uploads.classList.remove("working");
    return false;
  });

  $("#current_uploads").on("change", ".js-published", function () {
    // if published is being checked; enable the export checkbox
    // if published is being unchecked; uncheck and disable the export checkbox
    var export_checkbox = document.getElementById("exported_" + this.dataset.id);
    if (export_checkbox) {
      if (!this.checked) export_checkbox.checked = false;
      export_checkbox.disabled = !this.checked;
    }
    window.DirectAssetUploader.update_published_counter();
    window.DirectAssetUploader.update_export_position();
  });

  $("#current_uploads").on("change", ".js-exported", function () {
    // if export is being checked/unchecked; update the export position
    // if published is being unchecked; uncheck and disable the export checkbox
    window.DirectAssetUploader.update_export_position();
  });

  $("#publish_all").on("click", function () {
    var current_uploads = document.getElementById("current_uploads");
    var publish_checkboxes = Array.from(current_uploads.getElementsByClassName("js-published"));
    var export_checkboxes = Array.from(current_uploads.getElementsByClassName("js-exported"));
    if (publish_checkboxes.length > 0) {
      publish_checkboxes.forEach(window.DirectAssetUploader.mark_checked);
      export_checkboxes.forEach(window.DirectAssetUploader.mark_enabled);
    }
    window.DirectAssetUploader.update_published_counter();
    window.DirectAssetUploader.update_export_position();
  });

  $("#publish_and_export_all").on("click", function () {
    var current_uploads = document.getElementById("current_uploads");
    var publish_checkboxes = Array.from(current_uploads.getElementsByClassName("js-published"));
    var export_checkboxes = Array.from(current_uploads.getElementsByClassName("js-exported"));
    if (publish_checkboxes.length > 0) {
      publish_checkboxes.forEach(window.DirectAssetUploader.mark_checked);
      export_checkboxes.forEach(window.DirectAssetUploader.mark_enabled);
      export_checkboxes.forEach(window.DirectAssetUploader.mark_checked);
    }
    window.DirectAssetUploader.update_published_counter();
    window.DirectAssetUploader.update_export_position();
  });

  $("#unpublish_and_unexport_all").on("click", function () {
    var current_uploads = document.getElementById("current_uploads");
    var publish_checkboxes = Array.from(current_uploads.getElementsByClassName("js-published"));
    var export_checkboxes = Array.from(current_uploads.getElementsByClassName("js-exported"));
    if (publish_checkboxes.length > 0) {
      publish_checkboxes.forEach(window.DirectAssetUploader.mark_unchecked);
      export_checkboxes.forEach(window.DirectAssetUploader.mark_disabled);
      export_checkboxes.forEach(window.DirectAssetUploader.mark_unchecked);
    }
    window.DirectAssetUploader.update_published_counter();
    window.DirectAssetUploader.update_export_position();
  });
});

// Upload assets direct to S3 using jquery.fileupload plugin. See https://github.com/blueimp/jQuery-File-Upload
this.DirectAssetUploader = function (init_attribute) {
  "use strict";

  this.attribute = init_attribute;
  this.form_data = {};
  this.url = "";
  this.host = "";
  this.post_uploads_url;
  this.attribute_field = document.getElementById(this.attribute);
  this.accepted_file_types = /(\.|\/)(gif|jpe?g|png)$/i;
  this.accepted_file_types_string = "GIF, JPG and PNG";
  this.maximum_file_size_mb = 5;
  this.total_file_size = 0;
  this.error_count = 0;
  this.file_count = 0;
  this.position_seed = 1;
  this.template_data = {
    error: false,
    files: []
  };
  this.overall_progress_bar = document.getElementById("overall_progress");
  this.status = "ready";
  this.file_API_supported = !!("File" in window && "FileReader" in window && "FileList" in window && "Blob" in window);
  this.sortable_uploads = document.getElementById("current_uploads");
  this.sortable_instance = null;

  this.attach_events = function () {
    this.prepare_form();
    var $field = $(this.attribute_field);
    var _this = this;

    $field
      .fileupload({
        fileInput: $field,
        url: this.url,
        type: "POST",
        autoUpload: false,
        formData: this.form_data,
        paramName: "file", // S3 does not like nested name fields i.e. name="user[avatar_url]"
        dataType: "XML", // S3 returns XML if success_action_status is set to 201
        // Callbacks - https://github.com/blueimp/jQuery-File-Upload/wiki/Options#callback-options
        add: function (e, data) {
          _this.create_file_instance(e, data);
        },
        submit: function (e, data) {
          _this.fix_request_content_type(e, data);
          _this.upload_callback_submit(e, data);
        },
        start: function () {
          _this.upload_callback_start();
        },
        progress: function (e, data) {
          _this.upload_callback_progress(e, data);
        },
        progressall: function (e, data) {
          _this.upload_callback_progressall(e, data);
        },
        done: function (e, data) {
          _this.save_s3_response(e, data);
        },
        stop: function () {
          _this.reorder_upload_elements();
          _this.prepare_form();
        }
      })
      .prop("disabled", !_this.file_API_supported);

    if (!_this.file_API_supported) {
      remove_visibility(document.getElementById("add-items-label"));
    }

    $("#upload-items").on("click", function () {
      var filtered_files;

      if (_this.status === "ready") {
        // remove the erroneous files from the DOM pending uploads list
        _this.template_data.files.forEach(function (file) {
          if (file.error === true) {
            file.element.parentNode.removeChild(file.element);
            _this.file_count -= 1;
          }
        });

        // filter the file list array to contain only valid files, reset the position
        filtered_files = _this.template_data.files.filter(function (file) {
          return file.error === false;
        });
        filtered_files.forEach(function (file, index) {
          file.position = index + _this.position_seed;
        });

        _this.template_data.files = filtered_files;
        _this.template_data.files.forEach(function (file) {
          // submit each valid file
          file.data.submit();
        });
      }
    });

    if (this.sortable_uploads !== null) {
      this.sortable_instance = Sortable.create(this.sortable_uploads, {
        handle: ".sortable-handle",
        animation: 150,
        onStart: function () {
          _this.sortable_uploads.classList.add("working");
        },
        store: {
          // Get the order of elements. Called once during initialisation.
          get: function () {
            return [];
          },
          // Save the order of elements. Called onEnd (when the item is dropped).
          set: function (sortable) {
            sortable.toArray().forEach(function (element, index) {
              document.getElementById("position_" + element).value = index + 1;
            });
            window.DirectAssetUploader.update_published_counter();
            window.DirectAssetUploader.update_export_position();
            _this.sortable_uploads.classList.remove("working");
          }
        }
      });
      window.DirectAssetUploader.update_published_counter();
      window.DirectAssetUploader.update_export_position();
    }
  };

  // callback invoked as soon as files are added to the fileupload widget - via file input selection, drag & drop or add API call
  // called once for each file
  this.create_file_instance = function (e, data) {
    var _this = this;
    var template_file = {
      position: 0,
      status: "new",
      name: "",
      size: 0,
      base64: "",
      error: false,
      errors: [],
      data: {},
      s3_url: "",
      s3_key: "",
      element: null
    };
    this.file_count = data.originalFiles.length;

    if (data.originalFiles.indexOf(data.files[0]) === 0) {
      // current file is the first file
      this.prepare_form();
    }

    $.each(data.files, function (index, file) {
      template_file.position = data.originalFiles.indexOf(data.files[0]);
      template_file.name = file.name;
      template_file.size = file.size;

      if (!index) {
        _this.validate_file(file, template_file);

        template_file.error = template_file.errors.length > 0;

        if (template_file.error) {
          _this.error_count += 1;
          template_file.status = "error";
        } else {
          data.files[index].uploadName = _this.generate_upload_name(template_file.position, data.files[index].name);
          _this.generate_base64(file, template_file);
          _this.total_file_size += file.size;
          template_file.data = data; // make data.submit() available
          template_file.status = "pending";
        }
      }
      _this.template_data.files.push(template_file);
    });
    data.context = template_file;
    this.template_data.error = this.error_count > 0;

    if (data.originalFiles.indexOf(data.files[0]) + 1 === data.originalFiles.length) {
      // current file is the last file
      this.prepare_form_for_uploading();
    }
  };

  this.upload_callback_start = function () {
    this.status = "working";
    remove_visibility(document.getElementById("replace-items-label"));
  };

  // callback for the submit event of each file upload
  this.fix_request_content_type = function (e, data) {
    // manually set the Content-Type of the item, otherwise it becomes "binary/octet-stream"
    window.stock_uploader.form_data["Content-Type"] = data.files[0].type;
    data.formData = window.stock_uploader.form_data;
  };

  // callback for the submit event of each file upload
  this.upload_callback_submit = function (e, data) {
    data.context.status = "uploading";
    data.context.element.classList.remove("status-pending");
    data.context.element.classList.add("status-" + data.context.status);
  };

  this.upload_callback_progress = function (e, data) {
    var current_progress_bar = data.context.element.getElementsByClassName("js-progress-bar")[0];
    add_visibility(current_progress_bar);
    current_progress_bar.max = data.progress().total;
    current_progress_bar.value = data.progress().loaded;
    current_progress_bar.getElementsByClassName("js-progress-value")[0].innerHTML = parseInt(
      (data.progress().loaded / data.progress().total) * 100,
      10
    );
  };

  this.upload_callback_progressall = function (e, data) {
    this.overall_progress_bar.value = data.loaded;
    this.overall_progress_bar.getElementsByClassName("js-progress-value")[0].innerHTML = parseInt(
      (data.loaded / data.total) * 100,
      10
    );
  };

  this.save_s3_response = function (e, data) {
    var _data = data;
    var _this = this;
    var s3_location = $(data.jqXHR.responseXML)
      .find("Location")
      .text();
    var s3_bucket = $(data.jqXHR.responseXML)
      .find("Bucket")
      .text();
    var s3_key = $(data.jqXHR.responseXML)
      .find("Key")
      .text();
    var s3_etag = $(data.jqXHR.responseXML)
      .find("ETag")
      .text()
      .replace(/"/g, "");

    data.context.s3_url = s3_location;
    data.context.s3_key = s3_key;

    $.ajax({
      type: "POST",
      headers: { "X-CSRF-Token": Rails.csrfToken() },
      url: this.post_uploads_url,
      async: false, // request must be handled before moving on
      data: {
        upload: {
          type: "Stock::ImageUpload",
          position: data.context.position,
          export_position: data.context.export_position,
          s3_location: s3_location,
          s3_bucket: s3_bucket,
          s3_key: s3_key,
          s3_etag: s3_etag
        }
      },
      success: function (data, textStatus, jqXHR) {
        _data.context.status = "completed";
        _data.context.element.parentNode.removeChild(_data.context.element);
        _this.file_count -= 1;
        _this.position_seed = parseInt(jqXHR.getResponseHeader("position-seed"), 10);
        _this.remove_no_uploads();
        document.getElementById("current_uploads").insertAdjacentHTML("beforeend", data);
      },
      dataType: "html"
    });
  };

  this.reorder_upload_elements = function () {
    // re-order the uploaded list by position data attribute (originally set from filesystem order)
    var divList = Array.from(document.getElementsByClassName("js-sortable-item"));
    if (divList.length > 0) {
      divList.sort(function (a, b) {
        var a_position = parseInt(a.getElementsByClassName("js-position")[0].value, 10);
        var b_position = parseInt(b.getElementsByClassName("js-position")[0].value, 10);
        return a_position - b_position;
      });
    }

    // update the position attribute of each upload item to reflect the contiguous/sequential index
    divList.forEach(function (element, index) {
      element.getElementsByClassName("js-position")[0].value = index + 1;
      element.getElementsByClassName("js-export-position")[0].value = index + 1;
    });
    $("#current_uploads").append(divList);
    this.status = "ready";
  };

  this.prepare_form = function () {
    // The template data will need to get reset if the user is adding "more" files
    this.template_data.files = [];
    this.template_data.error = false;
    this.error_count = 0;
    this.total_file_size = 0;

    add_visibility(document.getElementById("add-items-label"));
    remove_visibility(document.getElementById("replace-items-label"));
    remove_visibility(document.getElementById("upload-items"));
    remove_visibility(this.overall_progress_bar);
  };

  this.prepare_form_for_uploading = function () {
    // The template data will get inserted, replacing any existing content
    document.getElementById("pending_uploads_wrapper").innerHTML = "";
    document.getElementById("pending_uploads_wrapper").innerHTML = tmpl("pending_uploads_list", this.template_data);

    this.template_data.files.forEach(function (file) {
      // point element attribute to actual DOM element
      file.element = document.getElementById("pending_upload_position_" + file.position);
    });

    this.overall_progress_bar.max = 0;
    if (this.file_count > 0) {
      remove_visibility(document.getElementById("add-items-label"));
      add_visibility(document.getElementById("replace-items-label"));
      this.overall_progress_bar.max = this.total_file_size;
    }
    if (this.error_count === this.file_count) {
      remove_visibility(document.getElementById("upload-items"));
      remove_visibility(this.overall_progress_bar);
    } else if (this.error_count < this.file_count) {
      add_visibility(document.getElementById("upload-items"));
      add_visibility(this.overall_progress_bar);
    }
  };

  this.validate_file = function (file, template_file) {
    var errors = [];
    if (file.size > this.maximum_file_size_mb * 1000 * 1000) {
      errors.push("File size is greater than " + this.maximum_file_size_mb + "MB. Please upload a smaller file.");
    }
    if (!this.accepted_file_types.test(file.type)) {
      errors.push("File type not allowed. Allowed file types: " + this.accepted_file_types_string + ".");
    }
    if (!/^[a-zA-Z0-9_.-]+$/.test(file.name)) {
      errors.push("File name must not contain spaces or special characters.");
    }
    template_file.errors = errors;
  };

  this.generate_base64 = function (file, template_file) {
    var reader = new FileReader();
    var _this = this;

    reader.addEventListener(
      "load",
      function () {
        template_file.base64 = reader.result;
        _this.update_template_file_image(template_file);
      },
      false
    );

    reader.readAsDataURL(file);
  };

  this.generate_upload_name = function (index, current_name) {
    var return_val;
    var match_extension = /(?:\.([^.]+))?$/;
    var current_extension = match_extension.exec(current_name)[1];
    current_name = current_name.replace(match_extension, "");

    return_val =
      index +
      "-" +
      current_name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/(^-|-$)/g, "") +
      "." +
      current_extension.toLowerCase();

    return return_val;
  };

  this.update_template_file_image = function (template_file) {
    var template_file_thumbnail = document
      .getElementById("pending_upload_position_" + template_file.position)
      .getElementsByClassName("js-upload-image-preview")[0];

    if (template_file_thumbnail !== null) {
      template_file_thumbnail.setAttribute("src", template_file.base64);
    }
  };

  this.remove_no_uploads = function () {
    // remove is-not-visible class from js-no-initial-uploads classes
    Array.from(document.getElementsByClassName("is-not-visible js-no-initial-uploads")).forEach(function (element) {
      element.classList.remove("is-not-visible", "js-no-initial-uploads");
    });
  };
};

this.DirectAssetUploader.update_published_counter = function () {
  var published_counter = 0;
  window.stock_uploader.sortable_instance.toArray().forEach(function (element) {
    if (document.getElementById("published_" + element).checked) {
      published_counter++;
      document.getElementById("counter_" + element).innerHTML = published_counter;
    } else {
      document.getElementById("counter_" + element).innerHTML = "&ndash;";
    }
  });
};

this.DirectAssetUploader.update_export_position = function () {
  var exported_counter = 0;
  window.stock_uploader.sortable_instance.toArray().forEach(function (element) {
    var export_checkbox = document.getElementById("exported_" + element);
    if (export_checkbox) {
      if (export_checkbox.checked) {
        exported_counter++;
        document.getElementById("export_position_" + element).value = exported_counter;
      } else {
        document.getElementById("export_position_" + element).value = 0;
      }
    }
  });
};

this.DirectAssetUploader.mark_checked = function (element) {
  element.checked = true;
};

this.DirectAssetUploader.mark_enabled = function (element) {
  element.disabled = false;
};

this.DirectAssetUploader.mark_unchecked = function (element) {
  element.checked = false;
};

this.DirectAssetUploader.mark_disabled = function (element) {
  element.disabled = true;
};
