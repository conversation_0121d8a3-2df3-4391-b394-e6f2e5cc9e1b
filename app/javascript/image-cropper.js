// TODO: This is jQuery image cropper. It is used for now,
// but will be removed after fixing bugs for JS image cropper
function ImageCropper(init_img, init_category_width, init_category_height, init_mime_type, callback) {
  "use strict";

  this.image = init_img;
  this.category_width = init_category_width;
  this.category_height = init_category_height;
  this.image_type = init_mime_type;
  var browserH = window.innerHeight;
  var browserW = window.innerWidth;
  this.new_width = 0;
  var new_height = 0;
  var margin_top = 0;
  this.original_width = this.image.width;
  this.original_height = this.image.height;
  var cropper_wrapper = $('.iuc-cropper-wrapper');
  var image_notification_element = document.getElementById('image-notification');

  var _this = this;

  if (this.original_height > browserH - 300 ) {
    this.new_width = (( browserH - 300 ) * this.original_width )/ this.original_height ;
    if (this.new_width > browserW * 0.8 ) { this.new_width = browserW * 0.8; }
  } else {
    if (this.original_width > browserW * 0.8 ) {
      this.new_width = browserW * 0.8;
    } else {
      this.new_width = this.original_width;
    }
  }

  image_notification_element.innerHTML = 'Final image size: ' + this.category_width + 'px wide by ' + this.category_height + 'px high';

  this.new_width = Math.round(this.new_width);
    new_height = (this.original_height * this.new_width) / this.original_width;
  cropper_wrapper.width(this.new_width);
    cropper_wrapper.height(new_height);
  $('.iuc-notification').width(this.new_width);
  margin_top = ((browserH - 250) - new_height) / 2;
  $('.iuc-cropper-wrapper').css('margin-top', margin_top + "px");

  $('#image-crop').attr('src', this.image.src);

  $('#image-crop').cropper({
    aspectRatio: this.category_width/this.category_height,
    viewMode: 1,
    autoCropArea: 1,
    zoomOnTouch:false,
    zoomOnWheel:false,
    ready: function () {
        $('#image-crop').cropper("crop");
        $(".image-upload-crop").show();
      }
  }).on({ 'cropmove.cropper': function(e) { _this.checkApertureSize(e); } });

  this.checkApertureSize = function(e) {
    var aperture = $('#image-crop').cropper("getCropBoxData");
    var aperture_width = (aperture.width * this.original_width ) / this.new_width;

    if ( aperture_width < this.category_width) {
      $(".iuc-notification-text").css('display','none');
      $(".iuc-notification-text-warning").css('display','block');
    }
    else {
      if (('.iuc-notification-text:visible').length > 0 ) { ImageCropper.resetNotification(); }
    }
  };

  $('.iuc-toolbox-lg-reset').click(function() {
    // reset button
    $('#image-crop').cropper("reset");
    ImageCropper.resetNotification();
  });

  $('.iuc-toolbox-width').click(function(){
    // auto-fit button
    var data = {
      "left" : 0,
      "top" : 0,
      "width": _this.original_width,
      "height" : _this.category_height
    };
    $('#image-crop').cropper('setCropBoxData', data);
      ImageCropper.resetNotification();
  });

  $('.iuc-toolbox-zoom-in').mousedown(function() {
    // zoom in
    $('#image-crop').cropper('zoom', 0.1);
  });

  $('.iuc-toolbox-zoom-out').mousedown(function() {
    // zoom out
    if ( $('.cropper-canvas').width() > $('.iuc-notification').width() ) {
      $('#image-crop').cropper('zoom', -0.1);
    }
  });

  $('#crop-image-cancel').off('click').on('click', function(e) {
    // cancel button
    ImageCropper.resetNotification();
    $('.image-upload-crop').hide();
    $('#image-crop').cropper("destroy");
  });

  $('#crop-image').off('click').on('click', function(e){
    // crop button
    var canvas = $('#image-crop').cropper('getCroppedCanvas', { width: _this.category_width, height: _this.category_height });
    var base64 = canvas.toDataURL(_this.image_type);

    callback(base64); // callback method initially passed to the constructor

    ImageCropper.resetNotification();
    $('.image-upload-crop').hide();
    $('#image-crop').cropper("destroy");
  });
}


ImageCropper.resetNotification = function(){
  "use strict";
  $(".iuc-notification-text").css('display','block');
  $(".iuc-notification-text-warning").css('display','none');
};
