/*global Sortable,$, Rails */
/*
Drag and Drop between lists. The URL for Fetch submission must be in the shared target list:
Tag example for source list:
  <ul class="list-recurring js-sortable-shared-source-list">
Tag example for target list:
  <ul class="list-recurring js-sortable-shared-target-list" data-url="/dealerships/2/content/forms/29/recurring/update_menu_position">
Each <li> child element must contain a data-sorting attribute, with the following structure: `{"id":5,"position":1}`:
  <li class="lr-item source" data-sorting="{"id":5,"position":4}">
Each <li> child element must contain `source` or `target` class;
  <li class="lr-item source" data-sorting="{"id":5,"position":4}">
  <li class="lr-item target" data-sorting="{"id":5,"position":4}">
On Fetch submission a `working` class is applied to the sortable-list. Sortable is disabled between Fetch request and success response.
*/

document.addEventListener('turbo:load', function () {
  'use strict';

  handleSortableSourceLists();
  handleSortableTargetLists();

  function handleSortableSourceLists() {
    var sortableSourceLists = document.getElementsByClassName('js-sortable-shared-source-list');

    Array.from(sortableSourceLists).forEach(function (sortableList) {
      Sortable.create(sortableList, {
        group: {
          name: 'shared-source',
          pull: ['shared-target'],
          put: function (to, from, item) {
            return item.dataset.type === 'source';
          }
        },
        handle: '.sortable-handle',
        animation: 150,
        dataIdAttr: 'data-sorting',
        sort: false,
        onStart: function () {
          sortableList.classList.add('working');
        },
        onChoose: function () {
          sortableList.style.opacity = 0.99;
        },
        onUnchoose: function () {
          sortableList.style.opacity = 1;
        }
      });
    });
  }

  function handleSortableTargetLists() {
    var sortableTargetLists = document.getElementsByClassName('js-sortable-shared-target-list');

    Array.from(sortableTargetLists).forEach(function (sortableList) {
      var sortable = Sortable.create(sortableList, {
        group: {
          name: 'shared-target',
          pull: ['shared-source'],
          put: ['shared-source']
        },
        handle: '.sortable-handle',
        animation: 150,
        dataIdAttr: 'data-sorting',
        onStart: function () {
          sortableList.classList.add('working');
        },
        onChoose: function () {
          sortableList.style.opacity = 0.99;
        },
        onUnchoose: function () {
          sortableList.style.opacity = 1;
        },
        // Element is dropped into the list from another list
        onAdd: function () {
          sortable.save();
        },
        store: {
          // Get the order of elements. Called once during initialisation.
          get: function (sortable) {
            return [];
          },
          // Save the order of elements. Called onEnd (when the item is dropped).
          set: function (sortable) {
            var currentOrder = calculateOrder(sortable);

            sendOrderData(sortable, currentOrder);
          }
        }
      });
    });
  }

  function calculateOrder(sortable) {
    var order = [];
    // sortable.toArray() is array of JSON strings: "{ "id": 5, "position": 1 }"
    sortable.toArray().forEach(function (element, index) {
      var listItemParams = JSON.parse(element);

      listItemParams['position'] = index + 1; // zero position avoidance
      order.push(listItemParams);
    });

    return order;
  }

  function sendOrderData(sortable, currentOrder) {
    var sortableList = sortable.el;
    var errorMessage = 'Sorry an error occurred, please try reloading the page and trying again.';

    sortable.option('disabled', true);

    fetch(sortableList.dataset.url, {
      method: 'PUT',
      mode: 'cors',
      cache: 'no-cache',
      credentials: 'same-origin',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': Rails.csrfToken()
      },
      redirect: 'error',
      referrer: 'no-referrer',
      body: JSON.stringify({ menu_positions: currentOrder })
    })
      .then(response => {
        sortable.option('disabled', false);
        sortableList.classList.remove('working');
        // Update the data-sorting attribute so that index is sequential
        Array.from(sortableList.children).forEach(function (element, index) {
          element.dataset.sorting = JSON.stringify(currentOrder[index]);
        });
      })
      .catch(error => {
        sortableList.classList.remove('working');
        alert(errorMessage);
      });
  }
});
