// Early Theme Loader for Rails (with Turbolinks)
// This script ensures the user's preferred theme is applied as soon as possible,
// even before CSS is loaded, to prevent a flash of the default or incorrect theme.
// It also keeps the theme in sync when navigating with Turbolinks.
//
// Place this script tag before your main stylesheet link tags in application.html.erb.
(function () {
  // Retrieve the value of a named cookie (e.g., "selected_theme").
  // Returns an empty string if the cookie is not found.
  function earlyGetCookie(cookieName) {
    var cookieMatch = document.cookie.match("(^|;)\\s*" + cookieName + "\\s*=\\s*([^;]+)");
    return cookieMatch ? cookieMatch.pop() : "";
  }

  // Set the data-selected-theme attribute on <html> to match the user's cookie.
  // This allows CSS to immediately apply the correct theme.
  function syncThemeAttribute() {
    // Cookie Examples:
    // 1. selected_theme = theme-dark
    // 2. selected_dark_variant = variant1
    var selectedTheme = earlyGetCookie("selected_theme");
    if (selectedTheme) {
      document.documentElement.setAttribute("data-selected-theme", selectedTheme);
    }
  }

  // Apply the theme as early as possible on the initial page load.
  syncThemeAttribute();

  // Re-apply the theme after every Turbolinks navigation or preview render.
  // This ensures the correct theme persists across fast client-side page changes.
  document.addEventListener("turbo:load", syncThemeAttribute);
  document.addEventListener("turbolinks:render", syncThemeAttribute);
})();
