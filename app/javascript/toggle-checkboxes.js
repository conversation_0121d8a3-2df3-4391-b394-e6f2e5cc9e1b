/*

Toggle the checked attribute of a set of checkboxes that match a CSS class name. e.g.

```
  <a href="#" class="check-trigger" data-checkbox-action="check" data-checkbox-class="brand-level-content-form">select all</a>
  <a href="#" class="check-trigger" data-checkbox-action="uncheck" data-checkbox-class="brand-level-content-form">select none</a>

  <label for="sample_5">
    <input class="brand-level-content-form" type="checkbox" value="5" checked="checked" name="dealership[sample][]" id="sample_5">
    Something
  </label>
  <label for="sample_6">
    <input class="brand-level-content-form" type="checkbox" value="6" name="dealership[sample][]" id="sample_6">
    Something else
  </label>
```

On page load, elements with the class 'check-trigger' will have a click event listener (`toggle_checkboxes`) attached to them.
When `toggle_checkboxes` is fired it gets the 'data-checkbox-action' and 'data-checkbox-class' attributes values from the originating click event (e.g. `check` and `brand-level-content-form`) and looks for all elements that match the class name from `data-checkbox-class`. It then sets or removes the checked attribute and value depending on the value of 'data-checkbox-action' - `check` or `uncheck`.

*/

document.addEventListener("turbo:load", function () {
  "use strict";

  var check_trigger_list = document.getElementsByClassName("check-trigger");

  if (check_trigger_list.length > 0) {
    Array.from(check_trigger_list).forEach(function (element) {
      if (element !== null) {
        element.addEventListener(
          "click",
          function (e) {
            toggle_checkboxes(e);
          },
          false
        );
      }
    });
  }

  var group_check_trigger_list = document.getElementsByClassName("specific-group-check-trigger");

  if (group_check_trigger_list.length > 0) {
    Array.from(group_check_trigger_list).forEach(function (element) {
      if (element !== null) {
        element.addEventListener(
          "click",
          function (e) {
            toggle_group_checkboxes(e);
          },
          false
        );
      }
    });
  }
});

function toggle_checkboxes(e) {
  "use strict";
  e.preventDefault();

  var check_targets = e.target.dataset.checkboxClass;
  var check_action = e.target.dataset.checkboxAction;
  var target_elements = document.getElementsByClassName(check_targets);

  if (target_elements.length > 0) {
    Array.from(target_elements).forEach(function (element) {
      if (element !== null) {
        element.checked = check_action === "check" ? true : false;
      }
    });
  }
}

function toggle_group_checkboxes(e) {
  "use strict";
  e.preventDefault();

  var check_selector = e.target.dataset.checkboxSelector;
  var check_action = e.target.dataset.checkboxAction;
  var target_elements = document.querySelectorAll(check_selector);
  if (document.querySelectorAll("input[type=checkbox]").length > 0) {
    // Do the reverse first;
    Array.from(document.querySelectorAll("input[type=checkbox]")).forEach(function (element) {
      element.checked = check_action === "check" ? false : true;
    });
  }
  if (target_elements.length > 0) {
    Array.from(target_elements).forEach(function (element) {
      if (element !== null) {
        element.checked = check_action === "check" ? true : false;
      }
    });
  }
}
