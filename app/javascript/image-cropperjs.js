// TODO: For now this function is not used, because contains time-consuming bugs
// TODO: Planned to fix this bugs in future when team has free time
// Image cropper without jQuery
function ImageCropperJS(init_img, init_category_width, init_category_height, init_mime_type, callback) {
  "use strict";

  this.image = init_img;
  this.category_width = init_category_width;
  this.category_height = init_category_height;
  this.image_type = init_mime_type;
  var browserH = window.innerHeight;
  var browserW = window.innerWidth;
  this.new_width = 0;
  var new_height = 0;
  var margin_top = 0;
  this.original_width = this.image.width;
  this.original_height = this.image.height;
  var cropper_wrapper = document.querySelector('.iuc-cropper-wrapper');
  var image_notification_element = document.getElementById('image-notification');

  var _this = this;

  if (this.original_height > browserH - 300 ) {
    this.new_width = (( browserH - 300 ) * this.original_width )/ this.original_height ;
    if (this.new_width > browserW * 0.8 ) { this.new_width = browserW * 0.8; }
  } else {
    if (this.original_width > browserW * 0.8 ) {
      this.new_width = browserW * 0.8;
    } else {
      this.new_width = this.original_width;
    }
  }

  image_notification_element.innerHTML = 'Final image size: ' + this.category_width + 'px wide by ' + this.category_height + 'px high';

  this.new_width = Math.round(this.new_width);
  new_height = (this.original_height * this.new_width) / this.original_width;
  cropper_wrapper.style.width = this.new_width + 'px';
  cropper_wrapper.style.height = new_height + 'px';
  document.querySelector('.iuc-notification').style.width = this.new_width + 'px';
  margin_top = ((browserH - 250) - new_height) / 2;

  document.querySelector('.iuc-cropper-wrapper').style.marginTop = margin_top + "px";
  document.querySelector('#image-crop').setAttribute('src', this.image.src);
  document.querySelector('#image-crop').style.display = 'block';
  document.querySelector('#image-crop').style.maxWidth = '100%';

  var cropper = new Cropper(document.querySelector('#image-crop'), {
    aspectRatio: this.category_width/this.category_height,
    viewMode: 1,
    autoCropArea: 1,
    zoomOnTouch: false,
    zoomOnWheel: false,
    autoCrop: false,
    zoomable: true,
    ready() {
        this.cropper.crop();
        document.querySelector(".image-upload-crop").style.display = 'block';
    }
  })

  document.querySelector('#image-crop').addEventListener('cropmove.cropper', function(e) { _this.checkApertureSize(e); } );;

  this.checkApertureSize = function(e) {
    var aperture = cropper.getCropBoxData()
    var aperture_width = (aperture.width * this.original_width ) / this.new_width;

    console.log(aperture_width, this.category_width)
    if ( aperture_width < this.category_width) {
      document.querySelector(".iuc-notification-text").style.display = 'none';
      document.querySelector(".iuc-notification-text-warning").style.display = 'block';
    }
    else {
      if (('.iuc-notification-text:visible').length > 0 ) { ImageCropperJS.resetNotification(); }
    }
  };

  document.querySelector('.iuc-toolbox-lg-reset').addEventListener('click', function() {
    // reset button
    cropper.reset()
    ImageCropperJS.resetNotification();
  });

  document.querySelector('.iuc-toolbox-width').addEventListener('click', function(){
    // auto-fit button
    var data = {
      "left" : 0,
      "top" : 0,
      "width": _this.original_width,
      "height" : _this.category_height
    };
    cropper.setCropBoxData(data)
    ImageCropperJS.resetNotification();
  });

  document.querySelector('.iuc-toolbox-zoom-in').addEventListener('mousedown',function() {
    // zoom in
    cropper.zoom(0.1)
  });

  document.querySelector('.iuc-toolbox-zoom-out').addEventListener('mousedown', function() {
    // zoom out
    if ( document.querySelector('.cropper-canvas').offsetWidth > document.querySelector('.iuc-notification').offsetWidth ) {
      cropper.zoom(-0.1)
    }
  });

  document.querySelector('#crop-image-cancel').removeEventListener('click', function(e) {
    // cancel button
    ImageCropperJS.resetNotification();
    document.querySelector('.image-upload-crop').style.display = 'none';
    cropper.destroy()
  });

  document.querySelector('#crop-image-cancel').addEventListener('click', function(e) {
    // cancel button
    ImageCropperJS.resetNotification();
    document.querySelector('.image-upload-crop').style.display = 'none';
    cropper.destroy()
  });

  document.querySelector('#crop-image').removeEventListener('click', function(e){
    // crop button
    var canvas = cropper.getCroppedCanvas();
    var base64 = canvas.toDataURL(_this.image_type);

    callback(base64); // callback method initially passed to the constructor

    ImageCropperJS.resetNotification();
    document.querySelector('.image-upload-crop').style.display = 'none';
    cropper.destroy()
  });

  document.querySelector('#crop-image').addEventListener('click', function(e){
    // crop button
    var canvas = cropper.getCroppedCanvas();
    var base64 = canvas.toDataURL(_this.image_type);

    callback(base64); // callback method initially passed to the constructor

    ImageCropperJS.resetNotification();
    document.querySelector('.image-upload-crop').style.display = 'none';
    cropper.destroy()
  });
}


ImageCropperJS.resetNotification = function(){
  "use strict";
  document.querySelector(".iuc-notification-text").style.display = 'block';
  document.querySelector(".iuc-notification-text-warning").style.display = 'none';
};
