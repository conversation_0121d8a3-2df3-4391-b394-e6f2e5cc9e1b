function FileUploader() {
  "use strict";
  this.allowed_types = ['application/pdf'];
  this.maximum_file_size_mb = 5; // remember, base64 will add approx 33%

  this.additional_validations = function(file, callback) {
    var additional_errors = [];

    if ((this.allowed_types.indexOf(file.type) < 0) || (file.size > this.maximum_file_size_mb*1000*1000)) {
      // file should not be read - either too large or not an acceptable type
      callback(additional_errors, '');
      return;
    }

    AssetUploader.read_file(file, function(status, base64) {
      if (status === 'error') {
        additional_errors.push('Sorry, we were unable to read your file. Please try again.');
      }
      callback(additional_errors, base64);
    });
  };

  this.post_validation_work = function(parent) {
    parent.base64_field.value = parent.base64;
    parent.attribute_link.setAttribute('href', parent.base64);
    parent.toggle_status('existing');
  };
}