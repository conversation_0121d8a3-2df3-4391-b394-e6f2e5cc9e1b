/*

Toggle the "activity" of an item and visibility of related columns when clicking on the item. e.g.

```html
    <li class="js-cg-trigger trigger-active" data-group-class='js-col-group1'>Item 1</li>
    <li class="js-cg-trigger" data-group-class='js-col-group2'>Item 2</li>

    <div class="js-col-group js-col-group1">Item 1 column</div>
    <div class="js-col-group js-col-group1">Item 1 column</div>
    <div class="js-col-group js-col-group2">Item 2 column</div>
```

Similar to `toggle-visibility`, except it will also update the class attributes of sibling tabs and panes.

*/

document.addEventListener("turbo:load", function () {
  "use strict";

  var trigger_items = document.getElementsByClassName("js-cg-trigger");

  if (trigger_items.length > 0) {
    Array.from(trigger_items).forEach(function (element) {
      if (element !== null) {
        element.addEventListener(
          "click",
          function (e) {
            toggle_group_visibility(e);
          },
          false
        );
      }
    });
  }
});

function toggle_group_visibility(e) {
  "use strict";
  e.preventDefault();

  var target_group = e.currentTarget.dataset.groupClass;
  update_column_trigger_items(e.currentTarget);
  update_group_column_list(target_group);
}

function update_column_trigger_items(active_tab_element) {
  "use strict";

  // loop through all `.js-cg-trigger > .tab-link` elements, remove the `tab-active` class for all except when the parent is `active_tab_element`
  var trigger_items = document.querySelectorAll(".js-cg-trigger > .js-tab-link");

  if (trigger_items.length > 0) {
    Array.from(trigger_items).forEach(function (element) {
      if (element !== null) {
        element.classList[element.parentNode === active_tab_element ? "add" : "remove"]("tab-active");
      }
    });
  }
}

function update_group_column_list(active_group_class) {
  "use strict";

  // loop through all `.js-col-group` elements, remove the `is-hidden` class for all except the `active_group_class`
  var group_list = document.getElementsByClassName("js-col-group");

  if (group_list.length > 0) {
    Array.from(group_list).forEach(function (element) {
      if (element !== null) {
        element.classList[element.classList.contains(active_group_class) ? "remove" : "add"]("is-hidden");
      }
    });
  }
}
