/*global List, image_lazy_loader */

this.HomePageFilter = function (text_field_id) {
  "use strict";
  this.text_field = document.getElementById(text_field_id);

  this.dealerships_element = document.getElementById("dealerships");
  this.groups_element = document.getElementById("groups");
  this.brands_element = document.getElementById("brands");

  this.dealerships_list = false;
  this.groups_list = false;
  this.brands_list = false;

  this.dealerships_counter_element = document.getElementById("dealerships_counter");
  this.groups_counter_element = document.getElementById("groups_counter");
  this.brands_counter_element = document.getElementById("brands_counter");

  this.dealership_options = {
    valueNames: [{ data: ["dealership"] }],
    item: "dealerships",
    listClass: "list-simple",
  };

  this.group_options = {
    valueNames: [{ data: ["group"] }],
    item: "groups",
    listClass: "list-simple",
  };

  this.brand_options = {
    valueNames: [{ data: ["brand"] }],
    item: "brands",
    listClass: "list-simple",
  };

  this.register = function () {
    if (this.text_field !== null) {
      var _this = this;
      this.text_field.addEventListener(
        "keyup",
        function (e) {
          _this.filter_lists(e);
        },
        false
      );

      if (this.dealerships_element !== null) this.dealerships_list = new List("dealerships", this.dealership_options);
      if (this.groups_element !== null) this.groups_list = new List("groups", this.group_options);
      if (this.brands_element !== null) this.brands_list = new List("brands", this.brand_options);
    }
  };

  this.filter_lists = function () {
    var searchString = this.text_field.value;

    if (this.dealerships_list !== false) {
      this.dealerships_list.search(searchString);
      this.dealerships_counter_element.innerHTML =
        searchString.length === 0 ? "" : this.dealerships_list.matchingItems.length + " of "; // update the filter count
    }
    if (this.groups_list !== false) {
      this.groups_list.search(searchString);
      this.groups_counter_element.innerHTML =
        searchString.length === 0 ? "" : this.groups_list.matchingItems.length + " of "; // update the filter count
    }
    if (this.brands_list !== false) {
      this.brands_list.search(searchString);
      this.brands_counter_element.innerHTML =
        searchString.length === 0 ? "" : this.brands_list.matchingItems.length + " of "; // update the filter count
    }
  };
};

this.StockFilter = function (text_field_id) {
  "use strict";
  // Initiated in BatchAssetUploader
  this.text_field = document.getElementById(text_field_id);
  this.stock_element = document.getElementById("stocks");
  this.dealerships_list = false;
  this.stocks_counter_element = document.getElementById("stock_counter");
  this.filter_type_checkboxes = document.getElementsByName("filter_type");
  this.filter_active_checkbox = document.getElementById("filter_active");
  this.filter_inactive_checkbox = document.getElementById("filter_inactive");
  this.filter_certified_checkbox = document.getElementById("filter_certified");
  this.filter_archived_checkbox = document.getElementById("filter_archived");
  this.filter_images_checkbox = document.getElementById("filter_images");
  this.filter_uploads_checkbox = document.getElementById("filter_uploads");
  this.filter_no_nvic_redbook_checkbox = document.getElementById("filter_no_nvic_redbook");
  this.filter_no_site_comments_checkbox = document.getElementById("filter_no_site_comments");
  this.filter_no_export_comments_checkbox = document.getElementById("filter_no_export_comments");
  this.filter_oem_guidelines_not_met_checkbox = document.getElementById("filter_oem_guidelines_not_met");

  this.stock_options = {
    valueNames: [
      {
        data: [
          "id",
          "stock_number",
          "year",
          "make",
          "model",
          "type",
          "active",
          "nvic_redbook",
          "photo_count",
          "allow_uploads",
          "registration_number",
          "colour",
          "site_comments",
          "export_comments",
          "price",
          "odometer",
          "created_at",
          "updated_at",
          "oem_guidelines_met",
          "views",
          "leads",
          "archived",
          "certified",
        ],
      },
    ],
    item: "stocks",
    listClass: "list-recurring",
  };

  this.registered = false;

  // called from BatchAssetUploader.retrieve_current_stock()
  this.register = function (current_stock) {
    if (this.registered) {
      this.update_list(current_stock);
    } else {
      var _this = this;
      if (this.stock_element !== null) {
        // New, Demo & Used checkboxes
        if (this.filter_type_checkboxes.length > 0) {
          Array.from(this.filter_type_checkboxes).forEach(function (element) {
            if (element !== null) {
              element.addEventListener(
                "change",
                function (e) {
                  _this.filter_list(e);
                },
                false
              );
            }
          });
        }
        // Active checkbox
        if (this.filter_active_checkbox !== null) {
          this.filter_active_checkbox.addEventListener(
            "change",
            function (e) {
              _this.filter_list(e);
            },
            false
          );
        }
        // Inactive checkbox
        if (this.filter_inactive_checkbox !== null) {
          this.filter_inactive_checkbox.addEventListener(
            "change",
            function (e) {
              _this.filter_list(e);
            },
            false
          );
        }
        // Archived checkbox
        if (this.filter_archived_checkbox !== null) {
          this.filter_archived_checkbox.addEventListener(
            "change",
            function (e) {
              _this.filter_list(e);
            },
            false
          );
        }
        // Certified checkbox
        if (this.filter_certified_checkbox !== null) {
          this.filter_certified_checkbox.addEventListener(
            "change",
            function (e) {
              _this.filter_list(e);
            },
            false
          );
        }
        // No images checkbox
        if (this.filter_images_checkbox !== null) {
          this.filter_images_checkbox.addEventListener(
            "change",
            function (e) {
              _this.filter_list(e);
            },
            false
          );
        }
        // Uploads allowed checkbox
        if (this.filter_uploads_checkbox !== null) {
          this.filter_uploads_checkbox.addEventListener(
            "change",
            function (e) {
              _this.filter_list(e);
            },
            false
          );
        }
        // No NVIC/Redbook checkbox
        if (this.filter_no_nvic_redbook_checkbox !== null) {
          this.filter_no_nvic_redbook_checkbox.addEventListener(
            "change",
            function (e) {
              _this.filter_list(e);
            },
            false
          );
        }
        // No site comments checkbox
        if (this.filter_no_site_comments_checkbox !== null) {
          this.filter_no_site_comments_checkbox.addEventListener(
            "change",
            function (e) {
              _this.filter_list(e);
            },
            false
          );
        }
        // No export comments checkbox
        if (this.filter_no_export_comments_checkbox !== null) {
          this.filter_no_export_comments_checkbox.addEventListener(
            "change",
            function (e) {
              _this.filter_list(e);
            },
            false
          );
        }
        // Brand non-compliant checkbox
        if (this.filter_oem_guidelines_not_met_checkbox !== null) {
          this.filter_oem_guidelines_not_met_checkbox.addEventListener(
            "change",
            function (e) {
              _this.filter_list(e);
            },
            false
          );
        }

        this.stocks_list = new List("stocks", this.stock_options);
        this.update_list(current_stock);

        this.stocks_list.on("updated", function (list) {
          if (_this.stocks_counter_element) {
            _this.stocks_counter_element.innerHTML = list.matchingItems.length + " of ";
          }
        });

        this.sort_list(this.stocks_list);
      }

      this.registered = true;
    }
  };

  this.update_list = function (current_stock) {
    if (typeof current_stock != "object") {
      return;
    }
    var _this = this;
    current_stock.forEach(function (stock) {
      // update stocks_list attributes with live stock data
      var item = _this.stocks_list.get("id", stock.id)[0];
      if (item) {
        item.values({
          photo_count: stock.photos_count,
        });

        var thumbnail_element = item.elm.getElementsByClassName("js-lazy-load")[0];
        var photo_summary_element = item.elm.getElementsByClassName("js-photo-summary")[0];

        if (thumbnail_element) {
          if (thumbnail_element.dataset.src !== stock.thumbnail) {
            thumbnail_element.dataset.src = stock.thumbnail;
          }
          image_lazy_loader.unobserve(thumbnail_element);
          image_lazy_loader.observe(thumbnail_element);
        }

        if (photo_summary_element && photo_summary_element.innerHTML !== stock.photo_summary) {
          photo_summary_element.innerHTML = stock.photo_summary;
        }
      }
    });
  };

  this.filter_list = function () {
    var filter_by_type = document.querySelectorAll("input[name=filter_type]:checked").length > 0;
    var filter_by_active = this.filter_active_checkbox ? this.filter_active_checkbox.checked : false;
    var filter_by_inactive = this.filter_inactive_checkbox ? this.filter_inactive_checkbox.checked : false;
    var filter_by_archived = this.filter_archived_checkbox ? this.filter_archived_checkbox.checked : false;
    var filter_by_certified = this.filter_certified_checkbox ? this.filter_certified_checkbox.checked : false;
    var filter_by_photo_count = this.filter_images_checkbox ? this.filter_images_checkbox.checked : false;
    var filter_by_uploads_allowed = this.filter_uploads_checkbox ? this.filter_uploads_checkbox.checked : false;
    var filter_by_no_nvic_redbook = this.filter_no_nvic_redbook_checkbox
      ? this.filter_no_nvic_redbook_checkbox.checked
      : false;
    var filter_by_no_site_comments = this.filter_no_site_comments_checkbox
      ? this.filter_no_site_comments_checkbox.checked
      : false;
    var filter_by_no_export_comments = this.filter_no_export_comments_checkbox
      ? this.filter_no_export_comments_checkbox.checked
      : false;
    var filter_by_oem_guidelines_not_met = this.filter_oem_guidelines_not_met_checkbox
      ? this.filter_oem_guidelines_not_met_checkbox.checked
      : false;

    var filter_by_types = [];

    Array.from(document.querySelectorAll("input[name=filter_type]:checked")).forEach(function (element) {
      if (element !== null) {
        filter_by_types.push(element.value);
      }
    });

    this.stocks_list.filter(function (item) {
      var type_filter = false;
      var photo_count_filter = false;
      var uploads_allowed_filter = false;
      var active_filter = false;
      var inactive_filter = false;
      var archived_filter = false;
      var certified_filter = false;
      var no_nvic_redbook_filter = false;
      var no_site_comments_filter = false;
      var no_export_comments_filter = false;
      var oem_guidelines_met = false;

      if (filter_by_type === false) {
        type_filter = true;
      } else {
        filter_by_types.forEach(function (veh_type) {
          type_filter = type_filter || item.values().type.includes(veh_type);
        });
      }

      if (filter_by_photo_count === false) {
        photo_count_filter = true;
      } else {
        photo_count_filter = parseInt(item.values().photo_count, 10) === 0;
      }

      if (filter_by_active === false) {
        active_filter = true;
      } else {
        active_filter = item.values().active === "true" && item.values().archived === "false";
        if (filter_by_archived) active_filter = item.values().active === "true" && item.values().archived === "true"
      }

      if (filter_by_inactive === false) {
        inactive_filter = true;
      } else {
        inactive_filter = item.values().active === "false";
      }

      if (filter_by_archived === false) {
        archived_filter = true;
      } else {
        archived_filter = item.values().archived === "true";
        if (filter_by_active) archived_filter = item.values().archived === "true" && item.values().active === "true"
      }

      if (filter_by_certified === false) {
        certified_filter = true;
      } else {
        certified_filter = item.values().certified === "true";
      }

      if (filter_by_uploads_allowed === false) {
        uploads_allowed_filter = true;
      } else {
        uploads_allowed_filter = item.values().allow_uploads === "true";
      }

      if (filter_by_no_nvic_redbook === false) {
        no_nvic_redbook_filter = true;
      } else {
        no_nvic_redbook_filter = item.values().nvic_redbook === "false";
      }

      if (filter_by_no_site_comments === false) {
        no_site_comments_filter = true;
      } else {
        no_site_comments_filter = item.values().site_comments === "false";
      }

      if (filter_by_no_export_comments === false) {
        no_export_comments_filter = true;
      } else {
        no_export_comments_filter = item.values().export_comments === "false";
      }

      if (filter_by_oem_guidelines_not_met === false) {
        oem_guidelines_met = true;
      } else {
        oem_guidelines_met = item.values().oem_guidelines_met === "false";
      }

      return (
        type_filter &&
        photo_count_filter &&
        uploads_allowed_filter &&
        active_filter &&
        inactive_filter &&
        archived_filter &&
        certified_filter &&
        no_nvic_redbook_filter &&
        no_site_comments_filter &&
        no_export_comments_filter &&
        oem_guidelines_met
      );
    });

    this.sort_list(this.stocks_list);
  };

  this.sort_list = function (list) {
    var sortMenu = document.getElementById("stock_sort");
    if (sortMenu) {
      var sortAttribute, sortOrder;
      sortAttribute = sortMenu.selectedOptions[0].dataset["sort"];
      sortOrder = sortMenu.selectedOptions[0].dataset["order"];
      if (sortAttribute) {
        list.sort(sortAttribute, { order: sortOrder });
      }

      sortMenu.addEventListener("change", function () {
        sortAttribute = this.selectedOptions[0].dataset["sort"];
        sortOrder = this.selectedOptions[0].dataset["order"];
        if (!sortOrder) {
          sortOrder = "asc";
        }
        if (sortAttribute) {
          list.sort(sortAttribute, { order: sortOrder });
        }
      });
    }
  };

  this.retrieve_current_stock = function () {
    var stock_filter_target = document.getElementById("stock_filter");
    this.pre_uploads_url = stock_filter_target.dataset.preUploadsUrl;
    this.current_stock = [];

    $.ajax({
      type: "GET",
      headers: { "X-CSRF-Token": Rails.csrfToken() },
      url: this.pre_uploads_url,
      success: function (data) {
        window.stock_filter.register(data);
      },
    });
  };
};

document.addEventListener("turbo:load", function () {
  "use strict";
  var home_filter = new window.HomePageFilter("home_filter");
  home_filter.register();
  // if (document.getElementById("js-stock-analytics-index")) {
  //   window.stock_filter = window.stock_filter || new window.StockFilter("stock_filter");
  //   window.stock_filter.retrieve_current_stock();
  // }
});
