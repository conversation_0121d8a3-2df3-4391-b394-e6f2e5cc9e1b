/*global Sortable,$, Rails */
/*

Drag and Drop menu position. The URL for AJAX submission must be in the target element:

  <ul class="list-recurring js-sortable-list" data-url="/dealerships/2/content/forms/29/recurring/update_menu_position">

Each <li> child element must contain a data-sorting attribute, with the following structure: `{"id":5,"position":1,"index":0}`, note the double quotes around JSON keys. Eg:

  <li class="lr-item" data-sorting="{"id":5,"position":4,"index":2}">

On AJAX submission a `working` class is applied to the sortable-list. Sortable is disabled between AJAX request and success response.
*/

document.addEventListener("turbo:load", function () {
  "use strict";

  reorderSortableItems();
});

function reorderSortableItems() {
  var sortable_elements_groups = document.getElementsByClassName("js-sortable-list");

  if (sortable_elements_groups.length > 0) {
    Array.from(sortable_elements_groups).forEach(function (sortable_elements) {
      if (sortable_elements !== null) {
        var previous_order = []; // add an attribute for get/set
        var group_name = sortable_elements.dataset.group;
        group_name = typeof group_name === "undefined" ? "Group" : group_name;

        Sortable.create(sortable_elements, {
          group: { group_name, pull: false, put: false },
          handle: ".sortable-handle",
          animation: 150,
          dataIdAttr: "data-sorting",
          onStart: function () {
            sortable_elements.classList.add("working");
          },
          // solves the redraw bug in safari 13
          onChoose: function () {
            sortable_elements.style.opacity = 0.99;
          },
          onUnchoose: function () {
            sortable_elements.style.opacity = 1;
          },
          store: {
            // Get the order of elements. Called once during initialisation.
            get: function (sortable) {
              sortable.toArray().forEach(function (element) {
                previous_order.push(JSON.parse(element));
              });
              return [];
            },
            // Save the order of elements. Called onEnd (when the item is dropped).
            set: function (sortable) {
              var current_order = [];
              var temp_item;
              // sortable.toArray() is array of JSON strings: "{ "id": 5, "position": 1, "index": 0 }" - note double quotes around JSON keys
              // Update current_order to reflect sequential index and updated menuposition
              sortable.toArray().forEach(function (element, index) {
                temp_item = JSON.parse(element);
                temp_item["index"] = index;
                // To prevent the error "Cannot read property 'position' of undefined" when using ajax to add and remove sortable elements.
                if (previous_order.length < sortable.toArray().length) {
                  temp_item["position"] = index;
                } else {
                  temp_item["position"] = previous_order[index]["position"];
                }
                current_order.push(temp_item);
              });

              // send the updated order via ajax
              $.ajax({
                type: "PUT",
                headers: { "X-CSRF-Token": Rails.csrfToken() },
                url: sortable_elements.dataset.url,
                data: { menu_positions: current_order },
                beforeSend: function () {
                  sortable.option("disabled", true);
                },
                error: function () {
                  sortable_elements.classList.remove("working");
                  alert("Sorry an error occurred, please try reloading the page and trying again.");
                },
                success: function () {
                  sortable.option("disabled", false);
                  sortable_elements.classList.remove("working");
                  // Update the data-sorting attribute so that index is sequential
                  Array.from(sortable_elements.children).forEach(function (element, index) {
                    if (element !== null) {
                      element.dataset.sorting = JSON.stringify(current_order[index]);
                    }
                  });
                  // set previous_order = current_order
                  previous_order = current_order;
                }
              });
            }
          }
        });
      }
    });
  }
}
