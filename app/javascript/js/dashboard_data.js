import Chart from "chart.js/auto";
import ChartDataLabels from "chartjs-plugin-datalabels";

CMS = CMS || {};

const dashboardData = () => {
  const loads = [
    "draw_line_chart",
    "set_calendar_dates",
    "draw_doughnut",
    "draw_horizontal_bar_chart",
    "draw_pie_chart",
  ];

  const setCalendarDates = () => {
    const radioFrequencyButtons = document.querySelectorAll('input[name="frequency"]');
    const filterForm = document.getElementById("js-dashboard-data-filter");
    const startDate = document.getElementById("start_date");
    const endDate = document.getElementById("end_date");
    //Manual changing of end or start date will trigger the form rendering
    if (startDate && filterForm) {
      startDate.addEventListener("change", function (event) {
        startDate.value = event.target.value;
        if (radioFrequencyButtons) {
          radioFrequencyButtons.forEach((button) => {
            button.removeAttribute("checked");
          });
        }
        filterForm.submit();
      });
    }

    if (endDate && filterForm) {
      endDate.addEventListener("change", function (event) {
        endDate.value = event.target.value;
        if (radioFrequencyButtons) {
          radioFrequencyButtons.forEach((button) => {
            button.removeAttribute("checked");
          });
        }
        filterForm.submit();
      });
    }

    if (radioFrequencyButtons && filterForm) {
      radioFrequencyButtons.forEach((button) => {
        button.addEventListener("change", function (event) {
          let frequency = event.target.value;

          if (frequency == "weekly") {
            setFirstAndLastDateOfWeek();
            filterForm.submit();
          } else if (frequency == "monthly") {
            setFirstAndLastDateOfMonth();
            filterForm.submit();
          } else if (frequency == "yearly") {
            setFirstAndLastDateOfYear();
            filterForm.submit();
          }
        });
      });
    }

    //Time zone problems with UTC mean we have to format with the covertDate function below to Remain in AEST Time Zone:
    function convertDate(date) {
      var yyyy = date.getFullYear().toString();
      var mm = (date.getMonth() + 1).toString();
      var dd = date.getDate().toString();

      var mmChars = mm.split("");
      var ddChars = dd.split("");

      return yyyy + "-" + (mmChars[1] ? mm : "0" + mmChars[0]) + "-" + (ddChars[1] ? dd : "0" + ddChars[0]);
    }

    let today = new Date();

    // Calculate 1 week from current date, and set start and end date.

    function setFirstAndLastDateOfWeek() {
      const lastWeekDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() - 7);

      startDate.value = lastWeekDate.toISOString().slice(0, 10);
      endDate.value = today.toISOString().slice(0, 10);
    }

    //Calculate Current month start and end date
    function setFirstAndLastDateOfMonth() {
      function getFirstDayOfMonth(year, month) {
        return new Date(year, month, 1);
      }

      const firstDayOfMonth = getFirstDayOfMonth(today.getFullYear(), today.getMonth());
      const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

      const formattedBeginningOfYear = convertDate(firstDayOfMonth);
      const formattedEndOfYear = convertDate(lastDayOfMonth);

      startDate.value = formattedBeginningOfYear;
      endDate.value = formattedEndOfYear;
    }

    //Calculate Current year, and set start and end date
    function setFirstAndLastDateOfYear() {
      const currentYear = new Date().getFullYear();
      const beginningOfYear = new Date(currentYear, 0, 1);
      const endOfYear = new Date(currentYear, 11, 31);

      const formattedBeginningOfYear = convertDate(beginningOfYear);
      const formattedEndOfYear = convertDate(endOfYear);

      startDate.value = formattedBeginningOfYear;
      endDate.value = formattedEndOfYear;
    }
  };

  const colourPalette = [
    // Blue citrus family (dark to light)
    "#1e1c6b", // deep blue
    "#3f3ce4", // citrus blue (primary)
    "#2499ff", // citrus blue
    "#8fd3fa", // light blue
    "#b9e5ff", // pale blue

    // Lime citrus family (dark to light)
    "#42ab12", // deep lime
    "#7dd019", // lime green
    "#b2ff59", // citrus lime
    "#cffcaa", // pale lime

    // Yellow citrus family (dark to light)
    "#ecec18", // Strong yellow
    "#e1e43c", // citrus yellow (primary)
    "#ffe066", // light lemon
    "#f7f6b2", // pale yellow

    // Orange citrus family (dark to light)
    "#bb3e03", // deep orange
    "#ee9b00", // citrus orange
    "#ffe5b4", // pale orange
    "#f9bd98", // light orange

    // Red citrus family (dark to light)
    "#ae2012", // strong red
    "#ff1644", // citrus magenta/red (primary)
    "#ff7a96", // light magenta/red

    // Purple citrus family (dark to light)
    "#7c2a8c", // deep purple
    "#e040fb", // citrus purple
  ];

  const drawHorizontalBarChart = () => {
    const barCharts = document.querySelectorAll(".js-bar-chart");
    barCharts.forEach((ctx) => {
      if (ctx) {
        new Chart(ctx.getContext("2d"), {
          type: "bar",
          data: {
            labels: JSON.parse(ctx.dataset.labels),
            datasets: [
              {
                axis: "x",
                label: ctx.dataset.name,
                data: JSON.parse(ctx.dataset.data),
                fill: false,
                borderColor: "rgb(255, 99, 132)",
                backgroundColor: colourPalette,
              },
            ],
          },
          plugins: [ChartDataLabels],
          options: {
            indexAxis: "y",
            responsive: true,
            maintainAspectRatio: true,
            aspectRatio: 1.5,
            plugins: {
              datalabels: {
                anchor: "end",
                align: "right",
                offset: 5,
                clamp: true,
                backgroundColor: "rgba(255,255,255, 0.2)",
                borderRadius: 5,
                color: "#333333",
              },
              legend: {
                display: false,
              },
            },
          },
        });
      }
    });
  };

  const drawPieChart = () => {
    const ctx = document.getElementById("js-pieChart");

    if (ctx) {
      new Chart(ctx.getContext("2d"), {
        type: "pie",
        data: {
          labels: JSON.parse(ctx.dataset.labels),
          datasets: [
            {
              label: ctx.dataset.name,
              data: JSON.parse(ctx.dataset.data),
              backgroundColor: colourPalette,
              hoverOffset: 4,
            },
          ],
        },
        options: {
          aspectRatio: 1.3,
          maintainAspectRatio: true,
          indexAxis: "y",
          responsive: true,
          radius: "80%",
          rotation: 90,
          plugins: {
            legend: {
              position: "right",
              labels: {
                usePointStyle: true,
                pointStyle: "circle",
                boxWidth: 20,
                boxHeight: 20,
              },
            },
          },
        },
      });
    }
  };

  // Store chart instance so we can destroy/redraw it
  let lineChartInstance = null;

  const drawLineChart = () => {
    const ctx = document.getElementById("js-lineChart");

    if (ctx) {
      // Destroy previous chart instance if it exists
      if (lineChartInstance) {
        lineChartInstance.destroy();
      }

      // Read the CSS variables at draw time, not at page load
      const lineChartGraphCol =
        getComputedStyle(document.documentElement).getPropertyValue("--imDdLineChartGraphCol").trim() || "#3f3ce4"; // Default to secondary colour if not set

      const lineChartXAxisLabelCol =
        getComputedStyle(document.documentElement).getPropertyValue("--imDdLineChartXAxisLabelCol").trim() || "#222";

      lineChartInstance = new Chart(ctx.getContext("2d"), {
        type: "line",
        data: {
          labels: JSON.parse(ctx.dataset.labels),
          datasets: [
            {
              label: ctx.dataset.name,
              data: JSON.parse(ctx.dataset.data),
              fill: false,
              borderColor: lineChartGraphCol, // draws the chart line
              backgroundColor: "rgba(63, 60, 228, 0.2)", // Use a lighter shade for background
              tension: 0.3,
            },
          ],
        },
        options: {
          plugins: {
            legend: {
              display: false,
            },
          },
          maintainAspectRatio: false,
          responsive: true,
          scales: {
            x: {
              grid: {
                display: false,
              },
              ticks: {
                color: lineChartXAxisLabelCol,
              },
            },
            y: {
              grid: {
                display: true,
              },
              beginAtZero: true,
              ticks: {
                stepSize: 5,
                color: lineChartXAxisLabelCol,
              },
            },
          },
        },
      });
    }
  };

  // Store chart instance so we can destroy/redraw it
  let doughnutChartInstance = null;

  const drawDoughnut = () => {
    const ctx = document.getElementById("js-doughnut");

    if (ctx) {
      // Destroy previous chart instance if it exists
      if (doughnutChartInstance) {
        doughnutChartInstance.destroy();
      }

      // Read the CSS variables at draw time, not at page load
      const pieBorderCol = getComputedStyle(document.documentElement).getPropertyValue("--imDdPieBorderCol").trim();
      const pieLegendTextCol = getComputedStyle(document.documentElement)
        .getPropertyValue("--imDdLegendTextCol")
        .trim();

      doughnutChartInstance = new Chart(ctx.getContext("2d"), {
        type: "doughnut",
        data: {
          labels: JSON.parse(ctx.dataset.labels),
          datasets: [
            {
              label: "Total Leads",
              data: JSON.parse(ctx.dataset.data),
              backgroundColor: colourPalette,
              hoverOffset: 4,
              borderColor: pieBorderCol, // Now always up-to-date
            },
          ],
        },
        options: {
          cutout: "50%",
          aspectRatio: 1.3,
          maintainAspectRatio: true,
          responsive: true,
          rotation: 90,
          plugins: {
            legend: {
              position: "right",
              labels: {
                usePointStyle: true,
                pointStyle: "circle",
                boxWidth: 20,
                boxHeight: 20,
                color: pieLegendTextCol, // Now always up-to-date
              },
            },
          },
        },
      });
    }
  };

  // Observe theme changes and redraw both the doughnut and line charts
  const observeThemeChange = () => {
    const html = document.documentElement;
    const observer = new MutationObserver((mutationsList) => {
      for (const mutation of mutationsList) {
        if (mutation.type === "attributes" && mutation.attributeName === "data-selected-theme") {
          drawDoughnut();
          drawLineChart();
        }
      }
    });
    observer.observe(html, { attributes: true });
  };

  observeThemeChange();

  return {
    loads: loads,
    draw_line_chart: drawLineChart,
    draw_horizontal_bar_chart: drawHorizontalBarChart,
    draw_pie_chart: drawPieChart,
    draw_doughnut: drawDoughnut,
    set_calendar_dates: setCalendarDates,
  };
};

CMS.dashboard_data = dashboardData();
