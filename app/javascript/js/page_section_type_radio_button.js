CMS = CMS || {};

const pageSectionForm = () => {
  const loads = ["handle_form_on_load"];
  const handleFormOnLoad = () => {
    const pgSectionForm = document.querySelector("#page_section_form");
    if (!pgSectionForm) return false;

    const optionTypeRadioButton = pgSectionForm.querySelectorAll(".js-cta-type-clicker");
    const megaMenuOptionTypeRadioButton = pgSectionForm.querySelectorAll(".js-url-type-clicker");

    if (optionTypeRadioButton.length > 0) {
      if (pgSectionForm.querySelector("input.js-cta-type-clicker:checked")) handlePageSectionRadioButton(pgSectionForm);

      optionTypeRadioButton.forEach((radioButton) => {
        radioButton.addEventListener("click", async () => {
          handlePageSectionRadioButton(pgSectionForm);
        });
      });
    }

    if (megaMenuOptionTypeRadioButton.length > 0) {
      handlePageSectionMegaMenuRadioButton(pgSectionForm);
      megaMenuOptionTypeRadioButton.forEach((radioButton) => {
        radioButton.addEventListener("click", async () => {
          handlePageSectionMegaMenuRadioButton(pgSectionForm);
        });
      });
    }
  };

  // clear all classes first then add one class
  const clearAllThenAddClass = (theTargetElement, theClass) => {
    if (!theTargetElement.classList.contains(theClass)) {
      theTargetElement.className = "";
      theTargetElement.classList.add(theClass);
      return;
    }
  };

  const handlePageSectionRadioButton = (pgSectionForm) => {
    const checkedValue = pgSectionForm.querySelector("input.js-cta-type-clicker:checked").value;
    const formFieldset = pgSectionForm.querySelector("fieldset#page-section-fields");

    switch (checkedValue) {
      case "none":
        clearAllThenAddClass(formFieldset, "option-none-selected");
        return;
      case "new_vehicles":
        clearAllThenAddClass(formFieldset, "new-vehicles-selected");
        return;
      case "internal_url_override":
        pgSectionForm.querySelector("input.js-link-text").placeholder = "e.g. /new-cars?vehicleType=New";
        clearAllThenAddClass(formFieldset, "internal-url-selected");
        return;
      case "external_url_override":
        pgSectionForm.querySelector("input.js-link-text").placeholder = "e.g. https://www.somewebsite.com.au/page";
        clearAllThenAddClass(formFieldset, "external-url-selected");
        return;
    }
  };

  const handlePageSectionMegaMenuRadioButton = (pgSectionForm) => {
    const checkedValue = pgSectionForm.querySelector("input.js-url-type-clicker:checked").value;
    const formFieldset = pgSectionForm.querySelector("fieldset#mega-menu-fields");

    switch (checkedValue) {
      case "none":
        clearAllThenAddClass(formFieldset, "mega-menu-none-selected");
        return;
      case "standard":
        clearAllThenAddClass(formFieldset, "mega-menu-standard-selected");
        return;
      case "image_enhanced":
        clearAllThenAddClass(formFieldset, "mega-menu-image-enhanced-selected");
        return;
      case "icon_grid":
        clearAllThenAddClass(formFieldset, "mega-menu-icon-grid-selected");
        return;
    }
  };

  return {
    loads: loads,
    handle_form_on_load: handleFormOnLoad,
  };
};

CMS.page_section_type_radio_button = pageSectionForm();
