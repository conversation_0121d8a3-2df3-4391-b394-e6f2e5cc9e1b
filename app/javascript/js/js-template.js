/**
 * The following template is just to illustrate the way we're creating and modularising
 * our javascript objects. It shows how we're utilising the revealing module pattern too.
 * '
 * Please use this template to structure you code accordingly.
 */

// importing any other plugin for example:
// import List from 'list.js'

// making sure the CMS object is loaded (DON'T need to import it)
CMS = CMS || {};

// Creating the desired object using the revealing module pattern
const template_object = () => {
  // populating the loads array with the function required to run in the "turbo:load" listener.
  const loads = [
    // "filterModels"
  ];

  const filterModels = () => { };

  // exposing public methods and attributes
  return {
    loads: loads, // loads should be public all the time.
    filterModels: filterModels, // key should be the same as in loads array
  };
};

// attaching the template_object object to the CMS object
CMS.template_object = template_object();
