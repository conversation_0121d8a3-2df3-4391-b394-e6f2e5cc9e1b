CMS = CMS || {};

const configureationFrom = () => {
  const loads = ["handle_form_on_load"];

  const handleFormOnLoad = () => {
    const pageForm = document.querySelector("#js-configuration-form");

    if (!pageForm) return false;

    const radioButtons = pageForm.querySelectorAll(".js-check-box-clickers");

    handleSelectForms(pageForm);

    radioButtons.forEach((radioButton) => {
      radioButton.addEventListener("click", async () => {
        handleSelectForms(pageForm);
      });
    });

    const stockVersionRadios = pageForm.querySelectorAll("input[name*='stock_version']");
    stockVersionRadios.forEach((radio) => {
      radio.addEventListener("change", () => {
        handleSelectForms(pageForm);
      });
    });
  };

  const handleSelectForms = (pageForm) => {
    const sourceRadio = pageForm.querySelector("input[name*='stock_source']:checked");
    const versionRadio = pageForm.querySelector("input[name*='stock_version']:checked");

    const checkedValue = sourceRadio ? sourceRadio.value : null;
    const stockVersion = versionRadio ? versionRadio.value : null;

    const versionWrapper = pageForm.querySelector("#js-hide-stock-version-radio-buttons");
    const versionHeader = pageForm.querySelector("#js-hide-stock-version-header");
    const presetWrapper = pageForm.querySelector("#js-choose-preset-wrapper");

    if (checkedValue === "stock_smartlists" || checkedValue === "none") {
      pageForm.querySelector("#js-hide-stock-collection-publish-setup").classList = "hidden";
      if (versionWrapper) versionWrapper.classList = "hidden";
      if (versionHeader) versionHeader.classList = "hidden";
      if (presetWrapper) presetWrapper.classList = "hidden";
    } else {
      pageForm.querySelector("#js-hide-stock-collection-publish-setup").classList = "col-wrapper-2";
      if (versionWrapper) versionWrapper.classList = "check-wrapper-hor";
      if (versionHeader) versionHeader.classList = "col-wrapper-2";

      if (presetWrapper) {
        if (stockVersion && stockVersion.toUpperCase() === "V5") {
          presetWrapper.classList.remove("hidden");
        } else {
          presetWrapper.classList.add("hidden");
        }
      }
    }
  };

  return {
    loads: loads,
    handle_form_on_load: handleFormOnLoad,
  };
};

CMS.configureationFrom = configureationFrom();
