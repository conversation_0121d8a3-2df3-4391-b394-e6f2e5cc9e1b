CMS = CMS || {};

const ctaConfigurationRadioButtons = () => {
  const loads = ["add_buttons_event_listeners"];

  const addButtonsEventListeners = () => {
    const radioButtons = document.querySelectorAll(".js-cta-type-clicker");

    if (null !== radioButtons) {
      radioButtons.forEach((button) => {
        button.addEventListener("click", toggleMediaTypeBlock);
      });
    }
  };

  const toggleMediaTypeBlock = (event) => {
    const assetType = event.target.dataset.assetType;
    const fieldName = event.target.name;

    const blocks = document.querySelectorAll(".js-cta-type-chooser");

    // If this toggle is for show_cta (details/listing)
    if (fieldName === "stock_setting_cta[show_cta]") {
      const wrapper = document.querySelector(".layouts-wrapper.js-cta-type-chooser");

      if (assetType === "details") {
        wrapper.classList.remove("is-not-visible");
        wrapper.classList.add("is-visible");
      } else {
        wrapper.classList.remove("is-visible");
        wrapper.classList.add("is-not-visible");
      }
      return; // No need to continue to logo_type logic
    }

    // If this toggle is for logo type (svg/image/icon)
    if (fieldName === "stock_setting_cta[logo_type]") {
      blocks.forEach((block) => {
        if (["svg", "image", "standard_icon"].includes(block.dataset.assetType)) {
          if (block.dataset.assetType === assetType) {
            block.classList.remove("is-not-visible");
            block.classList.add("is-visible");
          } else {
            block.classList.remove("is-visible");
            block.classList.add("is-not-visible");
          }
        }
      });
    }
  };

  return {
    loads: loads,
    add_buttons_event_listeners: addButtonsEventListeners,
  };
};

CMS.cta_configuration_radio_buttons = ctaConfigurationRadioButtons();
