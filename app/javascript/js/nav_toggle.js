import { getCookie } from "../utils/cookie";

// Toggles the collapsed class of a navigation wrapper and its associated button.
// Also saves the current state to a cookie for server-side rendering.
const navItems = [
  { wrapper: "nav3-wrapper", btn: "js-nav3BtnToggle", collapsedClass: "nav3Collapsed" },
  { wrapper: "nav4-wrapper", btn: "js-nav4BtnToggle", collapsedClass: "nav4Collapsed" },
];

// Helper to set a cookie for nav state - sets a session cookie that expires when the browser quits, unless deleting the cookie
function setNavCookie(name, value) {
  // Restore original direct cookie assignment for nav state (no SameSite, minimal attributes)
  if (!value) {
    // Use attributes Secure to mitigate CSRF risks and ensure cookies aren't sent in unintended contexts.
    // But only set Secure if the page is served over HTTPS.
    const isSecure = window.location.protocol === "https:";
    document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax${isSecure ? "; Secure" : ""
      }`;
    return;
  }
  document.cookie = `${name}=${value}; path=/`;
}

const toggleNav = (className, collapsedClass) => {
  // Attempt to find the navigation wrapper and button elements in the DOM
  const targetNavWrapper = document.querySelector(`.${className}`);
  // Find the button based on the className using the navItems array
  const navItem = navItems.find((navItem) => navItem.wrapper === className);
  // Use the nav object to get the button element
  const button = navItem ? document.getElementById(navItem.btn) : null;

  // If either element is missing, exit the function silently
  if (!targetNavWrapper || !button) {
    return;
  }

  // Toggle the collapsed class on both the wrapper and button
  targetNavWrapper.classList.toggle(collapsedClass);
  button.classList.toggle(collapsedClass);

  // Save the collapsed state to a cookie for persistence and server-side rendering
  const isCollapsed = targetNavWrapper.classList.contains(collapsedClass);
  setNavCookie(`${className}_collapsed`, isCollapsed ? "1" : "0");
};

// Sets the collapsed state for a nav and its button, and saves to a cookie.
const setNavState = (nav, collapsed) => {
  const targetNavWrapper = document.querySelector(`.${nav.wrapper}`);
  const button = document.getElementById(nav.btn);
  if (!targetNavWrapper || !button) return;
  targetNavWrapper.classList[collapsed ? "add" : "remove"](nav.collapsedClass);
  button.classList[collapsed ? "add" : "remove"](nav.collapsedClass);
  setNavCookie(`${nav.wrapper}_collapsed`, collapsed ? "1" : "0");
};

// Checks the cookie for the collapsed state of each navigation wrapper, and applies the correct collapsed class if necessary.
// Runs on page load to sync UI with saved state.
const syncStoredNavState = () => {
  navItems.forEach((nav) => {
    // Read the cookie value for this nav using getCookie utility
    const cookieValue = getCookie(`${nav.wrapper}_collapsed`);
    const isCollapsed = cookieValue === "1";
    setNavState(nav, isCollapsed);
  });
};

// Initialise navigation behaviour on page load (Turbolinks)
document.addEventListener("turbo:load", () => {
  // Sync navigation state with cookie
  syncStoredNavState();
  // Attach click handlers for each navigation toggle button
  navItems.forEach((nav) => {
    const button = document.getElementById(nav.btn);
    if (button) {
      button.addEventListener("click", () => toggleNav(nav.wrapper, nav.collapsedClass));
    }
  });
});

// Enable keyboard shortcuts to toggle navigation:
// - Cmd+Option+3 / Ctrl+Alt+3: Toggle nav3-wrapper
// - Cmd+Option+4 / Ctrl+Alt+4: Toggle nav4-wrapper
// - Cmd+Option+5 / Ctrl+Alt+5: Toggle both nav3-wrapper and nav4-wrapper together (expand/collapse all)
document.addEventListener("keydown", (event) => {
  // Mac: metaKey (Cmd) + altKey (Option)
  // Windows/Linux: ctrlKey + altKey
  const isMacCombo = event.metaKey && event.altKey && !event.shiftKey;
  const isWinCombo = event.ctrlKey && event.altKey && !event.shiftKey;
  if (isMacCombo || isWinCombo) {
    // Toggle nav3-wrapper with Cmd+Option+3 / Ctrl+Alt+3
    if (event.code === "Digit3") {
      toggleNav("nav3-wrapper", "nav3Collapsed");
      event.preventDefault();
    }
    // Toggle nav4-wrapper with Cmd+Option+4 / Ctrl+Alt+4
    if (event.code === "Digit4") {
      toggleNav("nav4-wrapper", "nav4Collapsed");
      event.preventDefault();
    }
    // Toggle both nav3-wrapper and nav4-wrapper with Cmd+Option+5 / Ctrl+Alt+5
    if (event.code === "Digit5") {
      const nav3 = document.querySelector(".nav3-wrapper");
      const nav4 = document.querySelector(".nav4-wrapper");
      const nav3Collapsed = nav3 && nav3.classList.contains("nav3Collapsed");
      const nav4Collapsed = nav4 && nav4.classList.contains("nav4Collapsed");
      const shouldExpand = nav3Collapsed || nav4Collapsed;
      navItems.forEach((nav) => setNavState(nav, !shouldExpand));
      event.preventDefault();
    }
  }
});
