import { getCookie } from "../utils/cookie";

// Helper to set a cookie for theme state using direct assignment (no SameSite, minimal attributes)
function setThemeCookie(name, value, { days } = {}) {
  if (value === "" || value === undefined || value === null || (typeof days === "number" && days < 0)) {
    document.cookie = `${name}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
    return;
  }
  let cookie = `${name}=${value}; path=/`;
  if (typeof days === "number") {
    const expires = new Date(Date.now() + days * 864e5).toUTCString();
    cookie += `; expires=${expires}`;
  }
  document.cookie = cookie;
}

// Register a global keyboard shortcut (Ctrl/Cmd + t) for toggling the theme drawer.
// Attach this only once (outside turbo:load) to avoid duplicate listeners on page navigation.
document.addEventListener("keydown", (event) => {
  // Mac: metaKey (Cmd) + altKey (Option) + t
  // Windows/Linux: ctrlKey + altKey + t
  const isMacCombo = event.metaKey && event.altKey && !event.ctrlKey && !event.shiftKey;
  const isWinCombo = event.ctrlKey && event.altKey && !event.metaKey && !event.shiftKey;
  if ((isMacCombo || isWinCombo) && event.code === "KeyT") {
    const themeDrawer = document.getElementById("js-themeSwitcherWrapper");
    if (themeDrawer) {
      themeDrawer.classList.toggle("drawer-open");
      // Prevent default browser behavior for the shortcut
      event.preventDefault();
    }
  }
});

// Close the theme drawer when clicking outside of it.
document.addEventListener("click", (event) => {
  const themeDrawer = document.getElementById("js-themeSwitcherWrapper");
  if (themeDrawer && themeDrawer.classList.contains("drawer-open") && !themeDrawer.contains(event.target)) {
    themeDrawer.classList.remove("drawer-open");
  }
});

// Theme initialisation and button logic, runs after each Turbolinks page load.
document.addEventListener("turbo:load", () => {
  const htmlElement = document.documentElement;
  // IDs of all theme switcher buttons for easy reference
  const buttonIds = [
    "js-themeSwitcherBtn0",
    "js-themeSwitcherBtn1",
    "js-themeSwitcherBtn2",
    "js-themeSwitcherBtn3",
    "js-themeSwitcherBtn4",
    "js-themeSwitcherBtn5",
  ];

  // Remove all theme-related data attributes from <html>
  function clearThemeAttributes() {
    htmlElement.removeAttribute("data-selected-theme");
    htmlElement.removeAttribute("data-dark-variant");
  }
  // Remove 'active' class from all theme switcher buttons
  function clearActiveButtons() {
    buttonIds.forEach((id) => {
      const btn = document.getElementById(id);
      if (btn) btn.classList.remove("active");
    });
  }

  // Map theme/variant to button ID for easy lookup
  function getActiveButtonId(theme, variant) {
    if (theme === "theme1") return "js-themeSwitcherBtn1";
    if (theme === "theme2") return "js-themeSwitcherBtn2";
    if (theme === "theme-dark" && variant === "variant1") return "js-themeSwitcherBtn4";
    if (theme === "theme-dark" && variant === "variant2") return "js-themeSwitcherBtn5";
    if (theme === "theme-dark") return "js-themeSwitcherBtn3";
    return "js-themeSwitcherBtn0";
  }

  // On page load, restore theme and variant from cookies if present
  const savedTheme = getCookie("selected_theme");
  const savedVariant = getCookie("selected_dark_variant");

  // Clear all theme state before applying new one
  clearThemeAttributes();
  clearActiveButtons();

  // If a theme was previously selected, restore it
  if (savedTheme) {
    htmlElement.setAttribute("data-selected-theme", savedTheme);
    // If dark theme variant is set, apply it as well
    if (savedTheme === "theme-dark" && savedVariant) {
      htmlElement.setAttribute("data-dark-variant", savedVariant);
    }
  }
  // Set active button based on theme/variant, or default
  const activeBtnId = getActiveButtonId(savedTheme, savedVariant);
  const activeBtn = document.getElementById(activeBtnId);
  if (activeBtn) activeBtn.classList.add("active");

  // Button Click Logic: Set theme and update cookies
  buttonIds.forEach((id) => {
    const btn = document.getElementById(id);
    if (!btn) return;
    btn.addEventListener("click", () => {
      // Clear all previous theme state and active buttons
      clearThemeAttributes();
      clearActiveButtons();
      btn.classList.add("active");
      switch (id) {
        case "js-themeSwitcherBtn1":
          // Theme 1: Set theme and clear any dark variant
          htmlElement.setAttribute("data-selected-theme", "theme1");
          setThemeCookie("selected_theme", "theme1", { days: 365 });
          setThemeCookie("selected_dark_variant", "", { days: -1 });
          break;
        case "js-themeSwitcherBtn2":
          // Theme 2: Set theme and clear any dark variant
          htmlElement.setAttribute("data-selected-theme", "theme2");
          setThemeCookie("selected_theme", "theme2", { days: 365 });
          setThemeCookie("selected_dark_variant", "", { days: -1 });
          break;
        case "js-themeSwitcherBtn3":
          // Dark Theme (default): Set theme and clear variant
          htmlElement.setAttribute("data-selected-theme", "theme-dark");
          setThemeCookie("selected_theme", "theme-dark", { days: 365 });
          setThemeCookie("selected_dark_variant", "", { days: -1 });
          break;
        case "js-themeSwitcherBtn4":
          // Dark Theme Variant 1
          htmlElement.setAttribute("data-selected-theme", "theme-dark");
          htmlElement.setAttribute("data-dark-variant", "variant1");
          setThemeCookie("selected_theme", "theme-dark", { days: 365 });
          setThemeCookie("selected_dark_variant", "variant1", { days: 365 });
          break;
        case "js-themeSwitcherBtn5":
          // Dark Theme Variant 2
          htmlElement.setAttribute("data-selected-theme", "theme-dark");
          htmlElement.setAttribute("data-dark-variant", "variant2");
          setThemeCookie("selected_theme", "theme-dark", { days: 365 });
          setThemeCookie("selected_dark_variant", "variant2", { days: 365 });
          break;
        case "js-themeSwitcherBtn0":
        default:
          // Default/reset: Clear all theme cookies
          setThemeCookie("selected_theme", "", { days: -1 });
          setThemeCookie("selected_dark_variant", "", { days: -1 });
      }
    });
  });
});
