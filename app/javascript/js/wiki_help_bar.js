CMS = CMS || {};

const wikiHelpBar = () => {
  // Array of module load hooks (for CMS loader integration)
  const loads = ["help_bar"];

  /**
   * Fetches wikis that match the current page's URL.
   * Returns an array of wiki objects from the server, or an empty array on error.
   */
  const fetchMatchingWikis = async () => {
    // Remove leading/trailing slashes from pathname for API call
    try {
      const response = await fetch("/matching_wikis?url=" + window.location.pathname.replace(/^\/|\/$/g, ""));
      return await response.json();
    } catch (err) {
      // Log error and return empty array if request fails
      console.log(err);
      return [];
    }
  };

  // Sidebar open/close helpers
  const closeWikiSidebar = (wikiSidebar, mainDiv) => {
    wikiSidebar.classList.remove("is-open");
    mainDiv.classList.remove("wiki-sidebar-open");
  };

  const openWikiSidebar = (wikiSidebar, mainDiv) => {
    wikiSidebar.classList.add("is-open");
    mainDiv.classList.add("wiki-sidebar-open");
  };

  const toggleWikiSidebar = (wikiSidebar, mainDiv) => {
    if (wikiSidebar.classList.contains("is-open")) {
      closeWikiSidebar(wikiSidebar, mainDiv);
    } else {
      openWikiSidebar(wikiSidebar, mainDiv);
    }
  };

  // Renders the wiki help bar, binds open/close events, and populates with matching wikis.
  // Returns false if sidebar or matching wikis are not found.
  const renderWikiHelpBar = async () => {
    // Find sidebar container; exit if not present
    const wikiSidebar = document.querySelector("#js_content_wiki_side_bar");
    if (!wikiSidebar) return false;

    // Find the content container for wiki articles
    const wikiContents = document.querySelector("#js_content_wiki_contents");

    // Fetch wikis relevant to the current page
    const matchingWikis = await fetchMatchingWikis();
    if (!matchingWikis.length) return false;

    // Main content wrapper (used to adjust layout when sidebar is open)
    const mainDiv = document.querySelector(".main");

    // Sidebar open/close buttons
    const openBtn = document.querySelector("#js_content_wiki_side_bar_open");
    const closeBtn = document.querySelector("#js_content_wiki_side_bar_close");

    // Bind click events for open/close buttons
    if (openBtn) {
      openBtn.addEventListener("click", () => openWikiSidebar(wikiSidebar, mainDiv));
    }
    if (closeBtn) {
      closeBtn.addEventListener("click", () => closeWikiSidebar(wikiSidebar, mainDiv));
    }

    // Keyboard shortcut: Cmd+Shift+L (Mac) or Ctrl+Shift+L (Windows)
    document.addEventListener("keydown", (event) => {
      const isMac = navigator.userAgent.includes("Mac");
      const key = event.key && event.key.toLowerCase();
      const isHelpShortcut =
        (isMac && event.metaKey && event.shiftKey && key === "l") ||
        (!isMac && event.ctrlKey && event.shiftKey && key === "l");
      if (isHelpShortcut) {
        event.preventDefault();
        toggleWikiSidebar(wikiSidebar, mainDiv);
      }
    });

    matchingWikis.forEach((wiki) => {
      $(wikiContents).append(`<h3>${wiki.title}</h3>${wiki.long_copy}<hr/>`);
    });
  };

  return {
    loads,
    help_bar: renderWikiHelpBar,
  };
};

CMS.wikiHelpBar = wikiHelpBar();
