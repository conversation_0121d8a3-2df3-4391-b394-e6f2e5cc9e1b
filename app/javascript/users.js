$(document).on("turbo:load", function () {
  "use strict";

  $(".permissions-list").on("change", "input[data-index]", function () {
    var index = this.dataset.index;
    var admin_checked = this.checked;
    var permissions_checkboxes = [
      document.getElementById("user_permissions_attributes_" + index + "_users"),
      document.getElementById("user_permissions_attributes_" + index + "_content"),
      document.getElementById("user_permissions_attributes_" + index + "_templates"),
      document.getElementById("user_permissions_attributes_" + index + "_dashboard"),
      document.getElementById("user_permissions_attributes_" + index + "_stock")
    ];
    // if admin_checked is true, we want to set disabled and checked to true. Vice versa when admin_checked is false
    permissions_checkboxes.forEach(function (element) {
      if (element !== null) {
        element.disabled = admin_checked;
        element.checked = admin_checked;
      }
    });
  });

  $(".js-auth-checkbox").on("change", function () {
    admin_auth_checkbox_change(this);
  });

  $(".js-auth-checkbox").each(function () {
    admin_auth_checkbox_change(this);
  });

  $("#js_account_reset_send").click(function (e) {
    var accountConfirmed = confirm("Are you sure you want to reset this user account? This action cannot be undone.");
    if (!accountConfirmed) {
      e.preventDefault();
      return false;
    } else {
      $(".toggle_user_status:first").addClass("active-false");
      $(".toggle_user_status:first").removeClass("active-true");
    }
  });

  // Register event listeners for every permissions-list div: `pre-inject` and `post-inject` of permissions auto-complete widget
  var permissions_lists = document.getElementsByClassName("permissions-list");
  if (permissions_lists.length > 0) {
    Array.from(permissions_lists).forEach(function (element) {
      if (element !== null) {
        element.addEventListener(
          "pre-inject",
          function (e) {
            permission_pre_injection(e);
          },
          false
        );
        element.addEventListener(
          "post-inject",
          function (e) {
            permission_post_injection(e);
          },
          false
        );
      }
    });
  }

  $(".permissions-list").on("click", ".js-add-fields", function (event) {
    event.preventDefault();

    var parent_permission_list = $(this).closest(".permissions-list")[0]; // native DOM element
    var time = new Date().getTime();
    var regexp = new RegExp($(this).data("id"), "g");
    var _event;

    // create and dispatch the event
    _event = new CustomEvent("pre-inject");
    parent_permission_list.dispatchEvent(_event);

    if (parent_permission_list.dataset.injectionReady === "true") {
      // insert the data-fields value (markup) into the closest/preceeding '.lr-item'
      $(this)
        .closest(".lr-item")
        .before(
          $(this)
            .data("fields")
            .replace(regexp, time)
        );
    }

    // create and dispatch the event
    _event = new CustomEvent("post-inject");
    parent_permission_list.dispatchEvent(_event);
  });

  // Register event listeners for every authorizations-list div: `pre-inject` and `post-inject` of authorizations auto-complete widget
  var authorizations_lists = document.getElementsByClassName("authorizations-list");
  if (authorizations_lists.length > 0) {
    Array.from(authorizations_lists).forEach(function (element) {
      if (element !== null) {
        element.addEventListener(
          "pre-inject",
          function (e) {
            authorization_pre_injection(e);
          },
          false
        );
        element.addEventListener(
          "post-inject",
          function (e) {
            authorization_post_injection(e);
          },
          false
        );
      }
    });
  }

  $(".authorizations-list").on("click", ".js-add-fields", function (event) {
    event.preventDefault();

    var parent_authorization_list = $(this).closest(".authorizations-list")[0]; // native DOM element
    var time = new Date().getTime();
    var regexp = new RegExp($(this).data("id"), "g");
    var _event;

    // create and dispatch the event
    _event = new CustomEvent("pre-inject");
    parent_authorization_list.dispatchEvent(_event);

    if (parent_authorization_list.dataset.injectionReady === "true") {
      // insert the data-fields value (markup) into the closest/preceeding '.lr-item'
      $(this)
        .closest(".lr-item")
        .before(
          $(this)
            .data("fields")
            .replace(regexp, time)
        );
    }

    // create and dispatch the event
    _event = new CustomEvent("post-inject");
    parent_authorization_list.dispatchEvent(_event);
  });

  $(".section-users").on("change", "#user_level", function () {
    var power_user = this.value !== "regular" && this.value !== "advanced";
    var access_wrapper = document.getElementById("access-wrapper-toggle");

    power_user === true ? access_wrapper.classList.add("no-access") : access_wrapper.classList.remove("no-access");
  });

  // Generic AJAX 'before' event handler for user actions
  $(
    ".toggle_user_status[data-remote], #invitation_send [data-remote], #invitation_cancel [data-remote], #password_reset_send [data-remote], #password_reset_cancel [data-remote]"
  ).on("ajax:before", function () {
    $(this).addClass("loading");
  });

  $(".toggle_user_status[data-remote]").on("ajax:success", function (event) {
    var detail = event.detail;
    var data = detail[0];
    // `data` is a JSON representation of the item
    $(this)
      .removeClass("loading active-true active-false")
      .addClass(function () {
        return data.user.active === true ? "active-true" : "active-false";
      });
    update_user_details(data.user);
  });

  // Generic AJAX 'success' event handler for user actions
  $(
    "#invitation_send [data-remote], #invitation_cancel [data-remote], #password_reset_send [data-remote], #password_reset_cancel [data-remote]"
  ).on("ajax:success", function (event) {
    var detail = event.detail;
    var data = detail[0];
    // `data` is a JSON representation of the item
    $(this).removeClass("loading");
    update_user_details(data.user);
  });
});

function admin_auth_checkbox_change(object) {
  var admin = object.dataset.admin;
  if (admin == 'true') {
    if (object.checked) {
      $("input:checkbox[name='" + object.name + "']").each(function () {
        if (this.dataset.admin == 'false') {
          this.checked = false;
          this.disabled = true;
        }
      });
    }
    else {
      $("input:checkbox[name='" + object.name + "']").each(function () {
        if (this.dataset.admin == 'false') {
          this.disabled = false;
        }
      });
    }
  }
}

function update_user_details(user) {
  "use strict";

  // Status
  $("#status_details").text(user.status);

  // Password Reset
  $("#password_reset_cancel").attr("class", function () {
    return user.password_reset_active === true ? "is-visible" : "is-not-visible";
  });
  $("#password_reset_send").attr("class", function () {
    return user.active === true ? "is-visible" : "is-not-visible";
  });
  $("#password_reset_send [data-remote]").text(function () {
    return user.password_reset_active === true ? "Reset User's Password Again" : "Reset User's Password";
  });
  $("#password_reset_details").text(user.password_reset_status);

  // Invitation
  $("#invitation_cancel").attr("class", function () {
    return user.invitation_active === true ? "is-visible" : "is-not-visible";
  });
  $("#invitation_send").attr("class", function () {
    return user.pending === true ? "is-visible" : "is-not-visible";
  });
  $("#invitation_send [data-remote]").text(function () {
    return user.invitation_active === true ? "Email New Invitation" : "Email Invitation";
  });
  $("#invitation_details").text(user.invitation_status);
}

function permission_pre_injection(e) {
  "use strict";

  var target_list = e.target;

  target_list.dataset.injectionReady =
    $(target_list)
      .find(".injected")
      .first().length === 0;
}

function permission_post_injection(e) {
  "use strict";

  var target_list = e.target;
  var injected = $(target_list)
    .find(".injected")
    .first();
  var autocomplete_field = injected.find(".autocomplete").first();
  var exception_list = [];

  autocomplete_field.focus();

  var autocomplete_config = {
    minLength: 2,
    focus: function (event, ui) {
      event.preventDefault();
      autocomplete_field.val(ui.item.label);
    },
    select: function (event, ui) {
      injected.find(".lr-text").text(ui.item.label);
      injected.find("input[name$='[permissionable_id]']").val(ui.item.value);
      injected.removeClass("injected");
      if (target_list.dataset.exceptions.length > 0) {
        exception_list = Array.from(target_list.dataset.exceptions.split(/\s*,\s*/));
      }
      exception_list.push(ui.item.value);
      target_list.dataset.exceptions = exception_list.toString();
      target_list.dataset.injectionReady = true;
      autocomplete_field.autocomplete("destroy");
      autocomplete_field.remove();
    },
    source: function (request, response) {
      $.get(
        autocomplete_field.data("source"),
        {
          term: request.term,
          exceptions: target_list.dataset.exceptions
        },
        function (data) {
          response(data);
        }
      );
    }
  };

  autocomplete_field.autocomplete(autocomplete_config);
  target_list.dataset.injectionReady = false;
}

function authorization_pre_injection(e) {
  "use strict";

  var target_list = e.target;

  target_list.dataset.injectionReady =
    $(target_list)
      .find(".injected")
      .first().length === 0;
}

function authorization_post_injection(e) {
  "use strict";

  var target_list = e.target;
  var injected = $(target_list)
    .find(".injected")
    .first();
  var autocomplete_field = injected.find(".autocomplete").first();
  var exception_list = [];

  autocomplete_field.focus();

  var autocomplete_config = {
    minLength: 2,
    focus: function (event, ui) {
      event.preventDefault();
      autocomplete_field.val(ui.item.label);
    },
    select: function (event, ui) {
      var target = injected.find(".lr-text");
      target.text(ui.item.label);
      var available_auths = target[0].dataset.authorizations;
      var model_type = target[0].dataset.modelType;
      var model_id = ui.item.value;
      var insert_html = "";
      var user_id = target_list.closest('.authorizations-list').dataset.userId;
      insert_html = "<a href='/users/" + user_id + "/" + model_type.toLowerCase() + "s/" + model_id + "/edit_authorizations' class='lr-action-wrapper'><svg class='lr-action-icon icon-pencil-edit-line'><use xlink:href='#svg-pencil-edit-line'></use></svg></a>";
      injected.find(".check-wrapper-hor")[0].innerHTML = insert_html;
      $(".js-lazy-auth-checkbox").on("change", function () {
        admin_auth_checkbox_change(this);
      });
      injected.removeClass("injected");
      if (target_list.dataset.exceptions.length > 0) {
        exception_list = Array.from(target_list.dataset.exceptions.split(/\s*,\s*/));
      }
      exception_list.push(ui.item.value);
      target_list.dataset.exceptions = exception_list.toString();
      target_list.dataset.injectionReady = true;
      autocomplete_field.autocomplete("destroy");
      autocomplete_field.remove();
    },
    source: function (request, response) {
      $.get(
        autocomplete_field.data("source"),
        {
          term: request.term,
          exceptions: target_list.dataset.exceptions
        },
        function (data) {
          response(data);
        }
      );
    }
  };

  autocomplete_field.autocomplete(autocomplete_config);
  target_list.dataset.injectionReady = false;
}
