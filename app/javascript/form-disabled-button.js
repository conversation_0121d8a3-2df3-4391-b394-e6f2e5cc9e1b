document.addEventListener("turbo:load", function () {
  "use strict";

  var trigger_forms = document.getElementsByClassName("js-form-disabled-button");

  if (trigger_forms.length > 0) {
    Array.from(trigger_forms).forEach(function (form) {
      if (form !== null) {
        form.elements.forEach((element) => {
          element.addEventListener(
            "input",
            function (e) {
              var button = e.target.form.getElementsByTagName("button")[0];
              var invalid = true;
              if (e.target.type == "checkbox") invalid = !e.target.checked;
              button.disabled = invalid;
            },
            false
          );
        });
      }
    });
  }
});
