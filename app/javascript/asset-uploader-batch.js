/*global add_visibility, remove_visibility, tmpl, StockFilter, image_lazy_loader, Rails */

document.addEventListener("turbo:load", function () {
  "use strict";

  const dealership_stock_page = document.querySelector(".dealership-stock");

  if (!dealership_stock_page) return;

  var batch_uploads_target = document.getElementById("stock_batch_uploads");
  var stock_filter_target = document.getElementById("stock_filter");

  // Stock Filter setup
  window.stock_filter = new StockFilter("stock_filter");
  // Batch Asset Uploader setup
  window.stock_uploader = new BatchAssetUploader("stock_batch_uploads");

  if (batch_uploads_target !== null) {
    window.stock_uploader.url = batch_uploads_target.dataset.url;
    window.stock_uploader.host = batch_uploads_target.dataset.host;
    window.stock_uploader.presigned_post_url = batch_uploads_target.dataset.presignedPostUrl;
    window.stock_uploader.pre_uploads_url = batch_uploads_target.dataset.preUploadsUrl;
    window.stock_uploader.attach_events();
  }

  if (stock_filter_target !== null && batch_uploads_target === null) {
    // manually trigger the stock filter, uploads are not active
    window.stock_uploader.pre_uploads_url = stock_filter_target.dataset.preUploadsUrl;
    window.stock_uploader.retrieve_current_stock();
  }
});

// Upload assets direct to S3 using jquery.fileupload plugin. See https://github.com/blueimp/jQuery-File-Upload
function BatchAssetUploader(init_attribute) {
  "use strict";

  this.attribute = init_attribute;
  this.form_data = {};
  this.url = "";
  this.host = "";
  this.pre_uploads_url;
  this.attribute_field = document.getElementById(this.attribute);
  this.accepted_file_types = /(\.|\/)(gif|jpe?g|png)$/i; // extension checking
  this.accepted_file_names = /([a-zA-Z\s\d]+)[_-]{1}([\d]+)\.(gif|jpe?g|png)$/i;
  this.accepted_file_types_string = "GIF, JPG and PNG";
  this.accepted_file_names_string = "Stock Number, followed by a '-' or a '_', followed by the position number";
  this.maximum_file_size_mb = 5;
  this.total_file_size = 0;
  this.error_count = 0;
  this.file_count = 0;
  this.current_stock = [];
  this.cms_queue = [];
  this.final_data = {
    error: false,
    files: {
      invalid: [],
      valid: [],
    },
    stock: {
      missing: [],
      invalid: [],
      valid: [],
      replacement: [],
    },
  };
  this.overall_progress_bar = document.getElementById("overall_progress");
  this.status = "ready";
  this.file_API_supported = !!("File" in window && "FileReader" in window && "FileList" in window && "Blob" in window);

  this.attach_events = function () {
    this.prepare_form();
    this.retrieve_current_stock();
    var $field = $(this.attribute_field);
    var _this = this;

    $field
      .fileupload({
        fileInput: $field,
        url: this.url,
        type: "POST",
        autoUpload: false,
        formData: {}, // overwritten later
        paramName: "file", // S3 does not like nested name fields i.e. name="user[avatar_url]"
        dataType: "XML", // S3 returns XML if success_action_status is set to 201
        // Callbacks - https://github.com/blueimp/jQuery-File-Upload/wiki/Options#callback-options
        add: function (e, data) {
          _this.create_file_instance(e, data);
        },
        submit: function (e, data) {
          _this.fix_request_content_type(e, data);
          _this.upload_callback_submit(e, data);
        },
        start: function () {
          _this.upload_callback_start();
        },
        progress: function (e, data) {
          _this.upload_callback_progress(e, data);
        },
        progressall: function (e, data) {
          _this.upload_callback_progressall(e, data);
        },
        done: function (e, data) {
          _this.queue_s3_response(e, data);
        },
        stop: function () {
          _this.save_s3_responses();
        },
      })
      .prop("disabled", !_this.file_API_supported);

    if (!_this.file_API_supported) {
      remove_visibility(document.getElementById("add-items-label"));
    }

    $("#upload-items").on("click", function () {
      if (_this.status === "ready") {
        _this.final_data.files.valid.forEach(function (file) {
          // submit each valid file
          file.data.submit();
        });
      }
    });
  };

  // callback invoked as soon as files are added to the fileupload widget - via file input selection, drag & drop or add API call
  // called once for each file
  this.create_file_instance = function (e, data) {
    var _this = this;
    var template_file = {
      position: 0,
      stock_id: "",
      status: "new",
      name: "",
      size: 0,
      error: false,
      errors: [],
      data: {},
      s3_url: "",
      s3_key: "",
      post_uploads_url: "",
      element: null,
    };

    if (data.originalFiles.indexOf(data.files[0]) === 0) {
      // current file is the first file
      this.generate_presigned_post();
      this.prepare_form();
    }

    $.each(data.files, function (index, file) {
      template_file.name = file.name;
      template_file.size = file.size;

      if (!index) {
        _this.validate_file(file, template_file);
        if (template_file.status === "pending") {
          _this.assign_to_stock(file, template_file);
        }

        template_file.error = template_file.errors.length > 0;

        if (template_file.error) {
          _this.final_data.files.invalid.push(template_file);
          _this.error_count += 1;
        } else {
          data.files[index].uploadName = _this.generate_upload_name(template_file.position, data.files[index].name);
          _this.total_file_size += file.size;
          template_file.data = data; // make data.submit() available
          _this.final_data.files.valid.push(template_file);
        }
      }
    });
    data.context = template_file;

    if (data.originalFiles.indexOf(data.files[0]) + 1 === data.originalFiles.length) {
      // current file is the last file
      this.final_data.error = this.error_count > 0;
      this.file_count = this.final_data.files.valid.length;
      this.prepare_form_for_uploading();
    }
  };

  this.generate_presigned_post = function () {
    var _this = this;
    // AJAX request to generate new S3 presigned post OR move this to `add` callback (above)
    // update the presigned_post key to avoid conflicts when submitting multiple uploads
    $.ajax({
      type: "GET",
      headers: { "X-CSRF-Token": Rails.csrfToken() },
      url: this.presigned_post_url,
      success: function (response_data) {
        _this.form_data = response_data;
      },
    });
  };

  this.upload_callback_start = function () {
    this.status = "working";
    remove_visibility(document.getElementById("replace-items-label"));
  };

  // callback for the submit event of each file upload
  this.fix_request_content_type = function (e, data) {
    // manually set the Content-Type of the item, otherwise it becomes "binary/octet-stream"
    window.stock_uploader.form_data["Content-Type"] = data.files[0].type;
    data.formData = window.stock_uploader.form_data;
  };

  // callback for the submit event of each file upload
  this.upload_callback_submit = function (e, data) {
    data.context.status = "uploading";
    data.context.element.classList.remove("status-pending");
    data.context.element.classList.add("status-" + data.context.status);
  };

  this.upload_callback_progress = function (e, data) {
    var current_progress_bar = data.context.element.getElementsByClassName("js-progress-bar")[0];
    add_visibility(current_progress_bar);
    current_progress_bar.value += data.progress().loaded;
    current_progress_bar.getElementsByClassName("js-progress-value")[0].innerHTML = parseInt(
      (data.progress().loaded / current_progress_bar.max) * 100,
      10
    );
  };

  this.upload_callback_progressall = function (e, data) {
    this.overall_progress_bar.value = data.loaded;
    this.overall_progress_bar.getElementsByClassName("js-progress-value")[0].innerHTML = parseInt(
      (data.loaded / data.total) * 100,
      10
    );
  };

  this.queue_s3_response = function (e, data) {
    var s3_location = $(data.jqXHR.responseXML).find("Location").text();
    var s3_bucket = $(data.jqXHR.responseXML).find("Bucket").text();
    var s3_key = $(data.jqXHR.responseXML).find("Key").text();
    var s3_etag = $(data.jqXHR.responseXML).find("ETag").text().replace(/"/g, "");

    data.context.s3_url = s3_location;
    data.context.s3_key = s3_key;
    data.context.s3_bucket = s3_bucket;
    data.context.s3_etag = s3_etag;

    this.cms_queue.push(data.context);
  };

  this.save_s3_responses = function () {
    var _this = this;
    var requests = [];

    this.final_data.stock.valid.forEach(function (stock) {
      var matching_uploads = [];
      var stock_uploads = [];
      matching_uploads = _this.cms_queue.filter(function (response) {
        return response.stock_id === stock.id;
      });
      matching_uploads.forEach(function (upload) {
        stock_uploads.push({
          type: "Stock::ImageUpload",
          position: upload.position,
          published: true,
          exported: true,
          export_position: upload.position,
          s3_location: upload.s3_url,
          s3_bucket: upload.s3_bucket,
          s3_key: upload.s3_key,
          s3_etag: upload.s3_etag,
        });
      });

      requests.push(
        $.ajax({
          type: "PUT",
          headers: { "X-CSRF-Token": Rails.csrfToken() },
          url: stock.post_uploads_url,
          // async: false, // request must be handled before moving on
          data: {
            stock: {
              destroy_existing_uploads: true,
              uploads_attributes: stock_uploads,
            },
          },
          success: function () {
            stock.upload_status = "completed";
            var stock_element = document.getElementById("pending_stock_upload_" + stock.id);
            stock_element.classList.remove("status-uploading");
            stock_element.classList.add("status-" + stock.upload_status);
          },
          dataType: "JSON",
        })
      );
    });

    $.when.apply($, requests).then(function () {
      // retrieve latest stock and update markup
      _this.retrieve_current_stock();
      // cleanup
      _this.status = "completed";
      var pending_uploads_wrapper = document.getElementById("pending_uploads_wrapper");
      pending_uploads_wrapper.classList.remove("pending");
      pending_uploads_wrapper.classList.add("completed");
      _this.prepare_form();
    });
  };

  this.retrieve_current_stock = function () {
    var _this = this;
    this.current_stock = [];

    $.ajax({
      type: "GET",
      headers: { "X-CSRF-Token": Rails.csrfToken() },
      url: this.pre_uploads_url,
      success: function (data) {
        _this.current_stock = data;
        window.stock_filter.register(data);
        BatchAssetUploader.update_stock_markup(data);
      },
    });
  };

  this.prepare_form = function () {
    // The template data will need to get reset if the user is adding "more" files
    this.final_data.files.invalid = [];
    this.final_data.files.valid = [];
    this.final_data.error = false;
    this.final_data.stock.missing = [];
    this.final_data.stock.invalid = [];
    this.final_data.stock.valid = [];
    this.final_data.stock.replacement = [];
    this.error_count = 0;
    this.total_file_size = 0;
    this.status = "ready";
    this.cms_queue = [];

    this.current_stock.forEach(function (stock) {
      stock.pending_uploads = [];
      stock.pending_uploads_size = 0;
      stock.upload_status = "new";
    });

    add_visibility(document.getElementById("add-items-label"));
    remove_visibility(document.getElementById("replace-items-label"));
    remove_visibility(document.getElementById("upload-items"));
    remove_visibility(this.overall_progress_bar);
  };

  this.prepare_form_for_uploading = function () {
    // The template data will get inserted, replacing any existing content
    var pending_uploads_wrapper = document.getElementById("pending_uploads_wrapper");
    pending_uploads_wrapper.classList.remove("completed");
    pending_uploads_wrapper.classList.add("pending");
    pending_uploads_wrapper.innerHTML = tmpl("batch_pending_uploads_list", this.final_data);
    pending_uploads_wrapper.classList.add("bu-visible");

    // add a click event to reveal the contents of pending_uploads_wrapper - height is fixed and overflow hidden by default
    // first remove any existing event listener
    var toggle_uploads_messages_element = document.getElementById("toggle_uploads_messages");
    toggle_uploads_messages_element.removeEventListener("click", BatchAssetUploader.toggle_uploads_messages, false);
    toggle_uploads_messages_element.addEventListener("click", BatchAssetUploader.toggle_uploads_messages, false);

    this.final_data.files.valid.forEach(function (file) {
      // point element attribute to actual DOM element, to track stock upload progress
      file.element = document.getElementById("pending_stock_upload_" + file.stock_id);
    });

    this.overall_progress_bar.max = 0;
    this.overall_progress_bar.value = 0;
    remove_visibility(document.getElementById("add-items-label"));
    add_visibility(document.getElementById("replace-items-label"));

    if (this.file_count > 0) {
      this.overall_progress_bar.max = this.total_file_size;
      add_visibility(document.getElementById("upload-items"));
      add_visibility(this.overall_progress_bar);
    } else {
      remove_visibility(document.getElementById("upload-items"));
      remove_visibility(this.overall_progress_bar);
    }
  };

  this.assign_to_stock = function (file, template_file) {
    var errors = [];
    var result = this.accepted_file_names.exec(file.name);

    if (result !== null) {
      var upload_stock_number = result[1];
      var upload_position = result[2];
      var matching_stock = this.current_stock.find(function (element) {
        return element.stock_number.toUpperCase() === upload_stock_number.toUpperCase();
      });
      if (matching_stock === undefined) {
        errors.push("No matching stock found for '" + file.name + "'.");
        template_file.status = "missing stock";
        if (!this.final_data.stock.missing.includes(upload_stock_number)) {
          this.final_data.stock.missing.push(upload_stock_number);
        }
      } else {
        if (matching_stock.allow_uploads === false) {
          errors.push("Uploads not allowed for '" + matching_stock.stock_number + "'.");
          template_file.status = "uploads disallowed";
          if (!this.final_data.stock.invalid.includes(matching_stock.stock_number)) {
            this.final_data.stock.invalid.push(matching_stock.stock_number);
          }
        } else {
          template_file.status = "pending";
          template_file.stock_id = matching_stock.id;
          template_file.position = parseInt(upload_position, 10);
          template_file.post_uploads_url = matching_stock.post_uploads_url;
          matching_stock.pending_uploads.push(template_file);
          matching_stock.pending_uploads.sort(function (a, b) {
            return a.position - b.position;
          });
          // set position to reflect order, not value of position from filename. Important for exports.
          matching_stock.pending_uploads.forEach(function (upload, index) {
            upload.position = index + 1;
          });
          matching_stock.pending_uploads_size += file.size;
          matching_stock.upload_status = "pending";
          if (!this.final_data.stock.valid.includes(matching_stock)) {
            this.final_data.stock.valid.push(matching_stock);
          }
          if (matching_stock.uploads_count > 0) {
            if (!this.final_data.stock.replacement.includes(matching_stock)) {
              this.final_data.stock.replacement.push(matching_stock);
            }
          }
        }
      }
    }
    template_file.errors = template_file.errors.concat(errors);
  };

  this.validate_file = function (file, template_file) {
    var errors = [];
    if (file.size > this.maximum_file_size_mb * 1000 * 1000) {
      errors.push(
        [
          "'",
          file.name,
          "' - File size is greater than ",
          this.maximum_file_size_mb,
          "MB. Please upload a smaller file.",
        ].join("")
      );
    }
    if (!this.accepted_file_types.test(file.type)) {
      errors.push(
        ["'", file.name, "' - File type not allowed. Allowed file types: ", this.accepted_file_types_string, "."].join()
      );
    }
    if (this.accepted_file_names.exec(file.name) === null) {
      errors.push("'" + file.name + "' - File name could not be parsed.");
    }
    if (!/^[a-zA-Z0-9_.-]+$/.test(file.name)) {
      errors.push("File name must not contain spaces or special characters.");
    }
    template_file.errors = template_file.errors.concat(errors);
    template_file.status = template_file.errors.length > 0 ? "error" : "pending";
  };

  this.generate_upload_name = function (index, current_name) {
    var return_val;
    var match_extension = /(?:\.([^.]+))?$/;
    var current_extension = match_extension.exec(current_name)[1];
    current_name = current_name.replace(match_extension, "");

    return_val =
      index +
      "-" +
      current_name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/(^-|-$)/g, "") +
      "." +
      current_extension.toLowerCase();

    return return_val;
  };
}

BatchAssetUploader.update_stock_markup = function (current_stock) {
  if (typeof current_stock != "object") {
    return;
  }
  current_stock.forEach(function (stock) {
    var thumbnail_element = document.getElementById("js-thumbnail-" + stock.id);
    var photo_summary_element = document.getElementById("js-photo-summary-" + stock.id);

    if (thumbnail_element !== null) {
      if (thumbnail_element.dataset.src !== stock.thumbnail) {
        thumbnail_element.dataset.src = stock.thumbnail;
      }
      thumbnail_element.addEventListener(
        "error",
        function () {
          this.src =
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABGCAIAAAC15KY+AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA9NJREFUeNrsmqFSI0EQho87qjCgokChgkIFhSIGDGBQoFDJA4AHPMSTBwgeUCjwRIEBhwEFBh7gvspfdE1NstxSt1d1x/0ttmZ7ema6v+npWVKMXV9ff7OUk+9GYFiGZViGZViGZQSGZViGZViGZVhGYFiGZViGZViGZQSGZViGZViGZVhGYFiGVU7u7+/b7fbR0VHlM49/yonMg0ajsbm5OTU19bfB6vf7+Fb5zGPl/zHk5OQEWDMzM9PT07w+PT09Pj7W6/Ver/efHMPPZRbPvb292LStra0/t43/ds26u7vjSSqFJg7g6+srZeLg4GB3d3dhYYGkQ9PtdpvNJq9iGqPolRlCA8uYhBmkpxH6IvsifXsgajMP7cvLy/X1dcxoh1mqPD8/l1mVmQWpFBAaXkmr/kB4XV1d3dnZ4ZyyNpqlpSXacuX09BRjIoQdwyl2tOXf4eGhcDOD9GdnZwxstVroi+xH6mmnmc48LEoBwTHcoIsnQ7CHL10swaL7+/sY064GFssoldRQ4vAETfTSxicacmttbY0zG/Xu6uqKXo0iKjiiZ28VJ3qcZgYi4ZUN15xMhX1rIMoa7VaRPk1/zQA4lpMbWp1Gp9PheXx8LEuWk2U1sHSOWD4yHOciNvXGCWU/041SGNwGOEoXV4RIqQJG8DzJJkWYCUAZRTCE97FenszNzQ17JUy8oscZfFBX6Csr8FoYOjFpug+ZWwpYl2YIUWVmMQl6eczRyNZlMwiMxNRJ4VW5XKTX0lpCy4WfwVG5HG6kfKuBpfRWHmVCnPid5XD6GgEU7WHoFXB2e6AkSZmEQwQdotJhH6lP9yO7kbBkTrZQy4WHKd9qbkNV9w+SjsQZVqafiDGcGhwVhGLBPgeXxrvUB8JADPi4w4BTo/AmJyeL9LqF01TCK00+nNThYXlY4+Wre4ZjOHFCQ2mnNnGmULLnqm5iwSR0Edjb2xsNDFS/VEq46dl51RS+dXX5qpzpllCFUt4N61NPMq/SI8mK3YFAVvd4meqO/PjlfamVnp+fV1ZWRuLXYhsbG7VaLRLk5eXl4uKCrsXFRcpKWulubm64GckChtA1MTGBfnl5+eHhQUOYZ3t7e3Z2li6Go8GeXhzAnt4i/e3tLfEzFWMzn1UW2UWwYqlrHfv5+XkdGpyp8s+dLymkJ6Djfqjso/TLCFWSvCabeKrw6fvWsEb/IhBfc5xKPlmzrxwfw7wKU8LiF5Tqf3X4SlLmQ8E/K/tnZcMyLMMyLIthGZZhGZZhGZbFsAzLsAzLsAzLYliGZViGZViGZTGs35KfAgwAOILnR0KQMNUAAAAASUVORK5CYII=";
          return true;
        },
        false
      );
      image_lazy_loader.unobserve(thumbnail_element);
      image_lazy_loader.observe(thumbnail_element);
    }

    if (photo_summary_element !== null) {
      if (photo_summary_element.innerHTML !== stock.photo_summary) {
        photo_summary_element.innerHTML = stock.photo_summary;
      }
    }

    if (stock.hasOwnProperty("export_setups")) {
      stock.export_setups.forEach(function (setup) {
        var current = document.getElementById("export-" + setup.id + "-" + stock.id);
        if (current) {
          current.checked = setup.available && setup.included;
          current.disabled = !setup.available;
        }
      });
    }
  });
};

document.addEventListener(
  "change",
  function (event) {
    if (event.target.matches(".js-stock-export-toggle")) {
      BatchAssetUploader.toggle_export_inclusion(event.target);
    }
  },
  false
);

BatchAssetUploader.toggle_export_inclusion = function (element) {
  if (element !== null) {
    if (element.disabled === false && element.dataset.toggle_url !== undefined) {
      $.ajax({
        type: "GET",
        headers: { "X-CSRF-Token": Rails.csrfToken() },
        url: element.dataset.toggle_url,
        success: function (data) {
          element.checked = data.stock.export_setup.available && data.stock.export_setup.included;
          element.disabled = !data.stock.export_setup.available;
        },
      });
    }
  }
};

BatchAssetUploader.toggle_uploads_messages = function () {
  var pending_uploads_wrapper = document.getElementById("pending_uploads_wrapper");
  var isCollapsed = pending_uploads_wrapper.getAttribute("data-collapsed") === "true";
  if (isCollapsed) {
    pending_uploads_wrapper.classList.add("bu-expanded");
    pending_uploads_wrapper.setAttribute("data-collapsed", "false");
  } else {
    pending_uploads_wrapper.classList.remove("bu-expanded");
    pending_uploads_wrapper.setAttribute("data-collapsed", "true");
  }
};
