/*
The jQuery UI date picker widget appends a shared element to the body ($.datepicker.dpDiv) which it expects will never leave the page, but Turbolinks removes that shared element when it re-renders.
We satisfy that expectation by removing the shared element from the page before Turbolinks caches the page (turbolinks:before-cache event), and appending it again before Turbolinks swaps the new body in during rendering (turbolinks:before-render event).

Additionally, returning to the cached version of a page that previously had date picker elements would result in those date pickers not being initialised again. We fix this issue by finding all initialised date picker inputs on the page and calling the date picker's destroy method before Turbolinks caches the page (turbolinks:before-cache event).

See https://github.com/turbolinks/turbolinks/issues/253 for this solution and https://github.com/turbolinks/turbolinks#full-list-of-events for the list of Turbolinks events.

We use similar logic to stop multiple Froala text editors appearing when using back/forward navigation.
*/
/* global tinymce */

document.addEventListener("turbolinks:before-cache", function () {
  "use strict";

  $.datepicker.dpDiv.remove();

  var datepicker_items = document.querySelectorAll("input.hasDatepicker");
  if (datepicker_items.length > 0) {
    Array.from(datepicker_items).forEach(function (element) {
      if (element !== null) {
        $(element).datepicker("destroy");
      }
    });
  }

  var wysiwyg_editors = document.querySelectorAll("textarea.text-editor");
  if (wysiwyg_editors.length > 0) {
    Array.from(wysiwyg_editors).forEach(function (element) {
      if (element !== null) {
        $(element).froalaEditor("destroy");
      }
    });
  }

  var tinymce_editors = document.querySelectorAll("textarea.tinymce");
  if (tinymce_editors.length > 0) {
    Array.from(tinymce_editors).forEach(function (element) {
      if (element !== null) {
        tinymce.remove(element);
      }
    });
  }
});

document.addEventListener("turbolinks:before-render", function (event) {
  "use strict";

  $.datepicker.dpDiv.appendTo(event.data.newBody);
});

var datePickerLoader = {
  loadDatePicker: function () {
    "use strict";

    // jquery-ui datepicker
    if ($(".datepicker").length > 0) {
      $(".datepicker").datepicker({
        // inline: true,
        // numberOfMonths: 2, // http://api.jqueryui.com/datepicker/#option-numberOfMonths
        firstDay: 1, // Monday
        dateFormat: "d MM, yy",
      });
    }
  },
};

var froalaLoader = {
  loadFroalaEditor: function () {
    "use strict";

    // froala text editor
    if ($(".text-editor").length > 0) {
      var options = {};
      var text_editor = $(".text-editor");

      if (text_editor.data("froalaHtmlUntouched") !== undefined) {
        options.htmlUntouched = text_editor.data("froalaHtmlUntouched");
      }
      if (text_editor.data("froalaEnter") !== undefined) {
        switch (text_editor.data("froalaEnter")) {
          case "BR":
            options.enter = $.FroalaEditor.ENTER_BR;
            break;
          case "P":
            options.enter = $.FroalaEditor.ENTER_P;
            break;
          case "DIV":
            options.enter = $.FroalaEditor.ENTER_DIV;
            break;
          default:
            break;
        }
      }
      text_editor.froalaEditor(options);
      text_editor.on("froalaEditor.image.error", function (e, editor, error, response) {
        var $popup = editor.popups.get("image.insert");
        var $layer = $popup.find(".fr-image-progress-bar-layer");
        $layer.find("h3").text(error.message);
      });
      text_editor.on("froalaEditor.video.error", function (e, editor, error, response) {
        var $popup = editor.popups.get("video.insert");
        var $layer = $popup.find(".fr-video-progress-bar-layer");
        $layer.find("h3").text(error.message);
      });
      text_editor.on("froalaEditor.video.linkError", function (e, editor, error, response) {
        var message = "Please provide a valid YouTube or Vimeo URL";
        var $popup = editor.popups.get("video.insert");
        if ($popup.is(":visible")) {
          var $layer = $popup.find(".fr-video-progress-bar-layer");
          $layer.find("h3").text(message);
        } else alert(message);
      });
    }
  },
};

var tinyMceLoader = {
  loadTinyMceEditor: function () {
    "use strict";

    // Determine if the current theme is dark mode by checking the <html> attribute
    var isDark = document.documentElement.getAttribute("data-selected-theme") === "theme-dark";

    // Base TinyMCE configuration object, shared by all editors
    var tinyMceBaseConfig = {
      selector: "textarea.tinymce", // Target all textareas with the 'tinymce' class
      plugins:
        // Enable a set of useful plugins for rich text editing
        "autoresize quickbars advlist autolink lists link image charmap preview anchor,searchreplace visualblocks code fullscreen insertdatetime media table code help wordcount fullscreen",
      max_height: 300, // Maximum editor height (in pixels)
      cache_suffix: "?v=6.1.0",
      height: 200, // Initial editor height (in pixels)
      menubar: false, // Hide the default menubar for a cleaner UI
      images_upload_url: "/content/editor_upload_image?editor=tinymce", // Server endpoint for image uploads
      style_formats: [
        // Define custom styles for the style_formats dropdown
        { title: "Paragraph", format: "p" },
        { title: "Heading 2", format: "h2" },
        { title: "Heading 3", format: "h3" },
        { title: "Heading 4", format: "h4" },
        { title: "Heading 5", format: "h5" },
      ],
      file_picker_types: "image media", // Allow image and media file pickers
      toolbar: [
        // Define the toolbar buttons and their order
        "undo redo bold italic underline superscript subscript strikethrough fontsize styles alignleft aligncenter alignright alignjustify hr bullist numlist inserttable link image media charmap code removeformat fullscreen",
      ],
      quickbars_insert_toolbar: "quickimage media", // Toolbar for quick insert actions
      quickbars_selection_toolbar: "bold italic underline | blocks | bullist numlist | blockquote quicklink", // Toolbar for text selection
      font_size_formats: "8px 10px 12px 14px 16px 18px 24px 36px 48px 60px 72px 96px", // Available font sizes
      // Dynamically set editor content background and text color based on theme
      // Use rgba for a transparent background in dark mode so it picks up the page background if it changes
      content_style: isDark
        ? "body { background-color: rgba(0,0,0,0.2); color: #fff; font-family: Helvetica, Arial, sans-serif; font-size: 14px; }"
        : "body { background-color: #fff; color: #222; font-family: Helvetica, Arial, sans-serif; font-size: 14px; }",
      skin: isDark ? "oxide-dark" : "oxide", // Use matching TinyMCE skin for theme
      content_css: isDark ? "dark" : "default", // Use matching TinyMCE content CSS for theme
      // Allow SVG and custom elements for richer content
      extended_valid_elements:
        "banner-web-component[*],clipPath[*],circle[*],defs[*],desc[*],ellipse[*],g[*],line[*],mask[*],path[*],polygon[*],rect[*],style[*],svg[*],symbol[*]",
      custom_elements: "banner-web-component",
      convert_urls: false, // Prevent TinyMCE from converting URLs
      image_dimensions: false, // Hide image dimension fields in dialogs
      setup: function (editor) {
        // Make the editor visible once initialised
        editor.on("init", function () {
          editor.getContainer().style.visibility = "visible";
        });
        // Mark the form as dirty when the editor content changes
        editor.on("change", function () {
          window.setFormDirty();
        });
      },
    };

    // Additional config for editors that require linebreak behavior
    var linebreakConfig = {
      selector: "textarea.tinymce.linebreak", // Target textareas with both 'tinymce' and 'linebreak' classes
      newline_behavior: "linebreak", // Use linebreaks instead of paragraphs on Enter
    };

    // Remove all existing TinyMCE editors before re-initialising.
    // This is necessary when changing skin or content_css (e.g., theme switch).
    tinymce.remove();

    // Initialise TinyMCE on all .tinymce editors if present
    if (document.querySelectorAll(".tinymce").length > 0) {
      tinymce.init(tinyMceBaseConfig);
    }
    // Initialise TinyMCE with linebreak config for .tinymce.linebreak editors
    if (document.querySelectorAll(".tinymce.linebreak").length > 0) {
      tinymce.init(Object.assign({}, tinyMceBaseConfig, linebreakConfig));
    }
    // If there are any read-only editors, set their mode accordingly
    if (document.querySelectorAll(".tinymce.readonly").length > 0) {
      tinymce.activeEditor.mode.set("readonly");
      tinymce.activeEditor.mode.set("design"); // Reset to design mode if needed
    }
  },
};

// Listen for theme changes and re-initialise TinyMCE
const observer = new MutationObserver(function () {
  tinyMceLoader.loadTinyMceEditor();
});
observer.observe(document.documentElement, { attributes: true, attributeFilter: ["data-selected-theme"] });

document.addEventListener("turbo:load", froalaLoader.loadFroalaEditor);
// On page load, initialise TinyMCE with the correct theme
document.addEventListener("turbo:load", tinyMceLoader.loadTinyMceEditor);
document.addEventListener("turbo:load", datePickerLoader.loadDatePicker);
