$(document).on("turbo:load", function () {
  "use strict";

  $(".toggle_status[data-remote]").on("ajax:before", function () {
    $(this).addClass("loading");
  });

  $(".toggle_status[data-remote]").on("ajax:success", function (event) {
    var detail = event.detail;
    var data = detail[0];
    // `data` is a JSON representation of the item
    $(this)
      .removeClass("loading active-true active-false")
      .addClass(function () {
        if (data && (data.active === "yes" || data.active === true)) {
          $(this)
            .find(".toggle_status_text")
            .first()
            .text("Yes");
          return "active-true";
        } else {
          $(this)
            .find(".toggle_status_text")
            .first()
            .text("No");
          return "active-false";
        }
      });
  });

  $(".toggle_template_banners[data-remote]").on("ajax:before", function () {
    $(this).addClass("loading");
  });

  $(".toggle_template_banners[data-remote]").on("ajax:success", function (event) {
    var detail = event.detail;
    var data = detail[0];
    // `data` is a JSON representation of the item
    // hide_template_banners is the boolean value, 'SHOW factory banners' is the visible toggle
    $(this)
      .removeClass("loading active-true active-false")
      .addClass(function () {
        if (data.hide_template_banners === true) {
          $(this)
            .find(".status-message")
            .first()
            .text("Factory Banners will not show");
          $("#banner_list_toggle")
            .removeClass("template-enabled-false template-enabled-true")
            .addClass("template-enabled-false");
          return "active-false";
        } else {
          $(this)
            .find(".status-message")
            .first()
            .text("Factory Banners will show");
          $("#banner_list_toggle")
            .removeClass("template-enabled-false template-enabled-true")
            .addClass("template-enabled-true");
          return "active-true";
        }
      });
  });

  $(".toggle_special[data-remote]").on("ajax:before", function () {
    $(this).addClass("loading");
  });

  $(".toggle_special[data-remote]").on("ajax:success", function (event) {
    var detail = event.detail;
    var data = detail[0];
    // `data` is a JSON representation of the item
    $(this)
      .removeClass("loading active-true active-false")
      .addClass(function () {
        if (data.featured === true) {
          return "active-true";
        } else {
          return "active-false";
        }
      });
  });
});
