document.addEventListener("turbo:load", function () {
  "use strict";

  // const images = document.getElementsByClassName(".js-lazy-load");
  const images = document.querySelectorAll("img.js-lazy-load");

  // images.forEach is not supported in IE11, images is a NodeList not an Array.
  // See: https://developer.mozilla.org/en-US/docs/Web/API/NodeList
  Array.prototype.forEach.call(images, function (image) {
    image_lazy_loader.observe(image);
  });
});

const config = {
  rootMargin: "0px 0px 50px 0px",
  threshold: 0
};

let image_lazy_loader = window.IntersectionObserver === undefined ? load_all_images() : load_visible_images();

function load_all_images() {
  var legacy_solution = {};
  legacy_solution.observe = function (image) {
    preloadImage(image);
  };
  legacy_solution.unobserve = function () { };
  return legacy_solution;
}

function load_visible_images() {
  return new IntersectionObserver(function (entries, self) {
    entries.forEach(function (entry) {
      if (entry.isIntersecting) {
        preloadImage(entry.target);
        // Observer has been passed as self to our callback
        self.unobserve(entry.target);
      }
    });
  }, config);
}

function preloadImage(img) {
  const src = img.dataset.src;
  if (!src || src === img.getAttribute("src")) {
    return;
  }
  img.src = src;
}
