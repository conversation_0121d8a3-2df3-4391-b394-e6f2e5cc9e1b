function ImageUploader(init_category_width, init_category_height) {
  "use strict";
  this.category_width = init_category_width;
  this.category_height = init_category_height;
  this.img = new Image();
  this.allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  this.maximum_file_size_mb = 5; // remember, base64 will add approx 33%

  this.additional_validations = function (file, callback) {
    var additional_errors = [];
    var _this = this;

    if ((this.allowed_types.indexOf(file.type) < 0) || (file.size > this.maximum_file_size_mb * 1000 * 1000)) {
      // file should not be read - either too large or not an acceptable type
      callback(additional_errors, '');
      return;
    }

    if (this.category_width === 0) {
      additional_errors.push('Image Category width not configured correctly.');
    }
    if (this.category_height === 0) {
      additional_errors.push('Image Category height not configured correctly.');
    }

    AssetUploader.read_file(file, function (status, base64) {
      if (status === 'error') {
        additional_errors.push('Sorry, we were unable to read your image. Please try again.');
        callback(additional_errors, base64);
        return;
      } else {
        _this.img.onload = function () {
          // access image dimensions
          if (_this.img.width < _this.category_width) {
            additional_errors.push('Image is too narrow (' + _this.img.width + 'px, should be at least ' + _this.category_width + 'px)');
          }
          if (_this.img.height < _this.category_height) {
            additional_errors.push('Image is too short (' + _this.img.height + 'px, should be at least ' + _this.category_height + 'px)');
          }
          callback(additional_errors, base64);
        };
        _this.img.src = base64;
      }
    });
  };

  this.post_validation_work = function (parent) {
    var attribute_image = document.getElementById(parent.attribute + '_image');
    this.image_cropper = new ImageCropper(this.img, this.category_width, this.category_height, parent.mime_type, function (cropped_base64) {
      parent.base64 = cropped_base64;
      parent.base64_field.value = cropped_base64;
      parent.attribute_link.setAttribute('href', cropped_base64);
      attribute_image.src = cropped_base64;
      parent.toggle_status('existing');
    });
  };

}
