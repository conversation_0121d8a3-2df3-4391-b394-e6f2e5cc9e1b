/*

Toggle the visibility of an element from a link. e.g.

```
  <a href="#brand-level-configuration" class="toggle-trigger">view</a>

  <div id="brand-level-configuration" class="is-not-visible">
    Content hidden by default
  </div>
```

On page load, elements with the class 'toggle-trigger' will have a click event listener (`toggle_visibility`) attached to them.
When `toggle_visibility` is fired it gets the href attribute value from the originating click event (e.g. `#brand-level-configuration`) and looks for an element with the same ID (without '#').
It then toggles the visibility of that element using the `is-visible` and `is-not-visible` classes.

*/

document.addEventListener("turbo:load", function () {
  "use strict";

  var trigger_list = document.getElementsByClassName("toggle-trigger");

  if (trigger_list.length > 0) {
    Array.from(trigger_list).forEach(function (element) {
      if (element !== null) {
        element.addEventListener(
          "click",
          function (e) {
            toggle_visibility(e);
          },
          false
        );
      }
    });
  }
});

function toggle_visibility(e) {
  "use strict";
  e.preventDefault();

  var toggle_target = e.target.getAttribute("href").split("#")[1];
  toggle_visibility_class(toggle_target);
}

function toggle_visibility_class(element_id) {
  var target_element = document.getElementById(element_id);

  if (target_element !== null) {
    if (target_element.classList.contains("is-visible")) {
      remove_visibility(target_element);
    } else {
      add_visibility(target_element);
    }
  }
}

function add_visibility(element) {
  element.classList.remove("is-not-visible");
  element.classList.add("is-visible");
}

function remove_visibility(element) {
  element.classList.remove("is-visible");
  element.classList.add("is-not-visible");
}
