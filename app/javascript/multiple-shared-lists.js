/*global Sortable,$, Rails */
/*
Drag and Drop between shared lists. The URL for Fetch submission must be in the separate element with class `js-multiple-shared-lists-url`:
Tag example of list:
  <ul class="list-recurring js-sortable-multiple-shared-list">
Each <li> child element must contain a data-id attribute:
  <li class="lr-item source" data-id="5">
On Fetch submission a `working` class is applied to the sortable-list. Sortable is disabled between Fetch request and success response.
*/

document.addEventListener('turbo:load', function () {
  'use strict';

  handleMultipleSharedLists();

  function handleMultipleSharedLists() {
    var sortableSharedLists = document.getElementsByClassName('js-sortable-multiple-shared-list');

    Array.from(sortableSharedLists).forEach(function (sortableList) {
      Sortable.create(sortableList, {
        group: 'multiple-shared-list',
        handle: '.sortable-handle',
        animation: 150,
        dataIdAttr: 'data-sorting',
        onChoose: function () {
          sortableList.style.opacity = 0.99;
        },
        onUnchoose: function () {
          sortableList.style.opacity = 1;
        },
        onUpdate: function (event) {
          var updatedItemsParams = calculateOrder(event.from);

          sendOrderData(updatedItemsParams);
        },
        onAdd: function (event) {
          var previousListParams = calculateOrder(event.from);
          var newListParams = calculateOrder(event.to);
          var updatedItemsParams = Object.assign({}, previousListParams, newListParams);
          var newSectionId = event.to.dataset['sectionId'];
          var draggableItemId = event.item.dataset['id'];

          sendOrderData(updatedItemsParams, newSectionId, draggableItemId);
        }
      });
    });
  }

  function calculateOrder(list) {
    var order = {};

    list.querySelectorAll('li.lr-item').forEach(function (element, index) {
      var itemId = element.dataset['id'];

      order[itemId] = { position: index + 1 }; // zero position avoidance
    });

    return order;
  }

  function sendOrderData(updatedItemsParams, newSectionId = '', draggableItemId = '') {
    var errorMessage = 'Sorry an error occurred, please try reloading the page and trying again.';
    var updatePositionUrl = document.querySelector('.js-multiple-shared-lists-url').dataset['url'];

    fetch(updatePositionUrl, {
      method: 'PUT',
      mode: 'cors',
      cache: 'no-cache',
      credentials: 'same-origin',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': Rails.csrfToken()
      },
      redirect: 'manual',
      referrer: 'no-referrer',
      body: JSON.stringify({
        updated_items: updatedItemsParams,
        draggable_item: {
          id: draggableItemId,
          section_id: newSectionId
        }
      })
    })
      .then(function (response) {
        if (response.type == 'opaqueredirect') {
          location.reload();
        }
      })
      .catch(error => {
        alert(errorMessage);
      });
  }
});
