/**
 * Retrieves the value of a cookie by name.
 *
 * @param {string} name - The name of the cookie to retrieve.
 * @returns {string} The cookie value if found, otherwise an empty string.
 */
export function getCookie(name) {
  return document.cookie.split("; ").reduce((result, cookieStr) => {
    const [cookieName, ...rest] = cookieStr.split("="); // Handles extra '=' in value
    const cookieValue = rest.join("="); // Rejoins rest of the value if '=' exists inside
    return cookieName === name ? decodeURIComponent(cookieValue) : result;
  }, "");
}
