/* global Turbolinks */
// Ask for confirmation before leaving the page if the form is dirty
// Save the original confirm method

const originalConfirm = window.confirm;

// Override the confirm method
window.askOnNavigation = function (message) {
  return new Promise((resolve, reject) => {
    // Create the modal elements
    const modal = document.createElement("div");
    modal.id = "js-modal";
    modal.classList.add("modal-bg-wrapper");

    const modalContentWrapper = document.createElement("div");
    modalContentWrapper.classList.add("modal-content-wrapper");

    const modalHeading = document.createElement("h2");
    modalHeading.classList.add("modal-content-text", "modal-content-heading");
    modalHeading.innerHTML =
      '<svg class="svg icon-size-35 icon-modal-alert"><use xlink:href="#svg-octagon-alert"></use></svg>' + message;

    // const modalSubHeading = document.createElement('h3');
    // modalSubHeading.classList.add('modal-content-text', 'modal-content-sub-heading');
    // modalSubHeading.textContent = 'Is this what you want?';

    const buttonWrapper = document.createElement("div");
    buttonWrapper.classList.add("imf-btn-wrapper");

    const cancelButton = document.createElement("button");
    cancelButton.id = "js-close-confirmation-modal";
    cancelButton.classList.add("imf-btn", "imf-btn-kill");
    cancelButton.textContent = "Stay on this page";
    cancelButton.addEventListener("click", () => {
      document.body.removeChild(modal);
      resolve(false);
    });

    const confirmButton = document.createElement("a");
    confirmButton.classList.add("imf-btn");
    confirmButton.id = "js-link-confirmation";
    confirmButton.textContent = "Leave this page";
    confirmButton.addEventListener("click", () => {
      document.body.removeChild(modal);
      resolve(true);
    });

    buttonWrapper.appendChild(cancelButton);
    buttonWrapper.appendChild(confirmButton);
    modalContentWrapper.appendChild(modalHeading);
    // modalContentWrapper.appendChild(modalSubHeading);
    modalContentWrapper.appendChild(buttonWrapper);
    modal.appendChild(modalContentWrapper);
    document.body.appendChild(modal);
  });
};

let isFormDirty = false;
let isFormSubmitting = false;

// Function to set form dirty state
function setFormDirty() {
  isFormDirty = true;
}

// Function to confirm navigation
function confirmNavigation(event) {
  if (isFormDirty && !isFormSubmitting) {
    const confirmationMessage = "You have unsaved changes. Do you really want to leave?";
    event.preventDefault(); // Prevent navigation initially
    window.askOnNavigation(confirmationMessage).then((result) => {
      if (result) {
        isFormDirty = false; // Reset dirty state if confirmed
        Turbolinks.visit(event.data.url); // Navigate to the destination page
      }
    });
  }
}

// Function to handle form submission
function handleFormSubmit() {
  isFormSubmitting = true;
}

// Function to attach event listeners
function attachEventListeners() {
  // Reset dirty state on new page load
  isFormDirty = false;
  isFormSubmitting = false;

  const excludedFormLabels = ["search", "filter", "sort", "reset"];

  // Attach change event listener to all form fields
  const forms = document.querySelectorAll("form");
  forms.forEach((form) => {
    let label = "";
    const submitButton = form.querySelector("[type='submit']");

    if (submitButton) {
      label = submitButton.innerText || submitButton.getAttribute("value");
    }

    if (label && excludedFormLabels.includes(label.toLowerCase())) {
      return;
    }

    const inputs = form.querySelectorAll("input, textarea, select");
    inputs.forEach((input) => {
      input.addEventListener("change", setFormDirty);
    });

    // Attach submit event listener to form
    form.addEventListener("submit", handleFormSubmit);
  });

  // Attach before-visit event for Turbolinks navigation
  document.addEventListener("turbolinks:before-visit", confirmNavigation);
}

// Function to detach event listeners
function detachEventListeners() {
  document.removeEventListener("turbolinks:before-visit", confirmNavigation);
}

// Event listeners for Turbolinks
document.addEventListener("turbo:load", attachEventListeners);
document.addEventListener("turbolinks:before-cache", detachEventListeners);
