FROM ruby:3.4

WORKDIR /dealer-drive-backend

RUN apt-get update -qq && apt-get install -yq --no-install-recommends \
  build-essential \
  gnupg2 \
  curl \
  less \
  git \
  libvips \
  libpq-dev \
  default-mysql-client \
  redis-tools \
  vim \
  libssl-dev \
  libghc-zlib-dev \
  libcurl4-gnutls-dev \
  libexpat1-dev \
  libgnutls30 \
  openssh-client \
  libvips42 \
  default-libmysqlclient-dev \
  && apt-get clean \
  && rm -rf /var/cache/apt/archives/* \
  && truncate -s 0 /var/log/*log \
  && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Add Node.js to sources list
RUN curl -sL https://deb.nodesource.com/setup_lts.x | bash -

# Install Node.js version that will enable installation of yarn
RUN apt-get install -y --no-install-recommends \
  nodejs \
  && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# set the timezone to Australia/Sydney
ENV TZ=Australia/Sydney
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Enforce cachebusting
ENV version=2

# Install global npm packages early
RUN npm install -g yarn sass esbuild

# Copy package files first for better caching
COPY package*.json yarn.lock* ./

# Install yarn dependencies with error handling
RUN yarn install --frozen-lockfile --non-interactive || \
    (echo "Yarn install failed, trying without frozen lockfile..." && yarn install --non-interactive)

# Copy the rest of the application
COPY ./ /dealer-drive-backend

# Copy Gemfile for Ruby dependencies
COPY Gemfile* ./
RUN gem install bundler foreman && bundle install

COPY . .

# Ensure bin/dev is executable if it exists
RUN if [ -f bin/dev ]; then chmod +x bin/dev; fi

# Set up GPG for non-interactive use
RUN echo 'export GPG_TTY=$(tty)' >> ~/.bashrc

EXPOSE 3000

CMD ["bin/rails", "server", "-b", "0.0.0.0"]