services:
  db:
    image: mysql:9.3
    restart: always
    environment:
      MYSQL_DATABASE: dealer-drive-backend_development
      MYSQL_ROOT_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql

  redis:
    image: redis:latest
    ports:
      - "6379:6379"

  web:
    build: .
    command: bash -c "rm -f tmp/pids/server.pid && bundle exec rails server -b 0.0.0.0"
    volumes:
      - .:/dealer-drive-backend
      - ~/.ssh:/root/.ssh:ro
      - ~/.gnupg:/root/.gnupg:ro
      - node_modules:/dealer-drive-backend/node_modules
    ports:
      - "3000:3000"
    environment:
      REDIS_URL: redis://redis:6379
      GPG_TTY: /dev/pts/0
      RAILS_ENV: development
      NODE_ENV: development
      RAILS_LOG_TO_STDOUT: "true"
    depends_on:
      - db
      - redis
    tty: true
    stdin_open: true
    # restart: unless-stopped

  css_watcher:
    build: .
    command: bash -c "yarn install --check-files && yarn build:css --watch"
    volumes:
      - .:/dealer-drive-backend
      - node_modules:/dealer-drive-backend/node_modules
    environment:
      NODE_ENV: development
    depends_on:
      - web
    restart: unless-stopped

  js_watcher:
    build: .
    command: bash -c "yarn install --check-files && yarn build --watch=forever"
    volumes:
      - .:/dealer-drive-backend  
      - node_modules:/dealer-drive-backend/node_modules
    environment:
      NODE_ENV: development
    depends_on:
      - web
    restart: unless-stopped

  sidekiq:
    build: .
    command: bundle exec sidekiq
    volumes:
      - .:/dealer-drive-backend
      - ~/.ssh:/root/.ssh:ro
      - ~/.gnupg:/root/.gnupg:ro
    environment:
      REDIS_URL: redis://redis:6379
      GPG_TTY: /dev/pts/0
      RAILS_ENV: development
    depends_on:
      - db
      - redis
    restart: unless-stopped

volumes:
  node_modules:
  db_data:
  